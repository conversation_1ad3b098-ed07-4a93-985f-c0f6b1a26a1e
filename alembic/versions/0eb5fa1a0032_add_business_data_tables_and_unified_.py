"""Add business data tables and unified tagging system

Revision ID: 0eb5fa1a0032
Revises: 
Create Date: 2025-07-17 22:12:18.821093

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '0eb5fa1a0032'
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('idx_role_name', table_name='roles')
    op.drop_index('idx_role_parent_id', table_name='roles')
    op.drop_index('ix_roles_id', table_name='roles')
    op.drop_table('roles')
    op.drop_index('idx_permission_action', table_name='permissions')
    op.drop_index('idx_permission_code', table_name='permissions')
    op.drop_index('idx_permission_module', table_name='permissions')
    op.drop_index('idx_permission_resource', table_name='permissions')
    op.drop_index('ix_permissions_id', table_name='permissions')
    op.drop_table('permissions')
    op.drop_index('idx_ur_role_id', table_name='user_roles')
    op.drop_index('idx_ur_user_id', table_name='user_roles')
    op.drop_index('ix_user_roles_id', table_name='user_roles')
    op.drop_table('user_roles')
    op.drop_index('idx_pol_operation_time', table_name='permission_operation_logs')
    op.drop_index('idx_pol_operator_id', table_name='permission_operation_logs')
    op.drop_index('idx_pol_target_id', table_name='permission_operation_logs')
    op.drop_index('ix_permission_operation_logs_id', table_name='permission_operation_logs')
    op.drop_table('permission_operation_logs')
    op.drop_index('idx_ai_model_default', table_name='ai_models')
    op.drop_index('idx_ai_model_performance', table_name='ai_models')
    op.drop_index('idx_ai_model_priority', table_name='ai_models')
    op.drop_index('idx_ai_model_provider', table_name='ai_models')
    op.drop_index('idx_ai_model_status', table_name='ai_models')
    op.drop_index('idx_ai_model_type', table_name='ai_models')
    op.drop_index('idx_ai_model_usage', table_name='ai_models')
    op.drop_index('ix_ai_models_id', table_name='ai_models')
    op.drop_table('ai_models')
    op.drop_index('idx_event_crawl_associations_event', table_name='event_crawl_associations')
    op.drop_index('idx_event_crawl_associations_rule', table_name='event_crawl_associations')
    op.drop_index('idx_event_crawl_associations_source', table_name='event_crawl_associations')
    op.drop_index('ix_event_crawl_associations_id', table_name='event_crawl_associations')
    op.drop_table('event_crawl_associations')
    op.drop_index('idx_rp_permission_id', table_name='role_permissions')
    op.drop_index('idx_rp_role_id', table_name='role_permissions')
    op.drop_index('ix_role_permissions_id', table_name='role_permissions')
    op.drop_table('role_permissions')
    op.drop_index('idx_model_metrics_date', table_name='model_metrics')
    op.drop_index('idx_model_metrics_model_date', table_name='model_metrics')
    op.drop_index('idx_model_metrics_performance', table_name='model_metrics')
    op.drop_index('idx_model_metrics_unique', table_name='model_metrics')
    op.drop_index('idx_model_metrics_window', table_name='model_metrics')
    op.drop_index('ix_model_metrics_id', table_name='model_metrics')
    op.drop_index('ix_model_metrics_model_id', table_name='model_metrics')
    op.drop_table('model_metrics')
    op.drop_index('idx_scheduler_next_scan', table_name='event_crawl_scheduler_status')
    op.drop_index('idx_scheduler_status', table_name='event_crawl_scheduler_status')
    op.drop_index('ix_event_crawl_scheduler_status_id', table_name='event_crawl_scheduler_status')
    op.drop_table('event_crawl_scheduler_status')
    op.create_foreign_key(None, 'data_processing_status', 'raw_data_records', ['raw_data_id'], ['id'], ondelete='CASCADE')
    op.create_foreign_key(None, 'economic_indicators', 'countries_regions', ['country_id'], ['id'])
    op.create_foreign_key(None, 'financial_events', 'countries_regions', ['country_id'], ['id'])
    op.alter_column('users', 'is_admin',
               existing_type=sa.BOOLEAN(),
               nullable=True,
               comment='是否是管理员',
               existing_server_default=sa.text('false'))
    op.drop_index('ix_users_is_admin', table_name='users')
    op.create_index('idx_user_admin', 'users', ['is_admin'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('idx_user_admin', table_name='users')
    op.create_index('ix_users_is_admin', 'users', ['is_admin'], unique=False)
    op.alter_column('users', 'is_admin',
               existing_type=sa.BOOLEAN(),
               nullable=False,
               comment=None,
               existing_comment='是否是管理员',
               existing_server_default=sa.text('false'))
    op.drop_constraint(None, 'financial_events', type_='foreignkey')
    op.drop_constraint(None, 'economic_indicators', type_='foreignkey')
    op.drop_constraint(None, 'data_processing_status', type_='foreignkey')
    op.create_table('event_crawl_scheduler_status',
    sa.Column('id', sa.BIGINT(), autoincrement=True, nullable=False, comment='调度器状态唯一标识符，自增主键'),
    sa.Column('scheduler_name', sa.VARCHAR(length=100), autoincrement=False, nullable=False, comment='调度器名称，标识不同的调度器实例'),
    sa.Column('last_scan_time', postgresql.TIMESTAMP(), autoincrement=False, nullable=True, comment='最后扫描时间，调度器最后一次扫描事件的时间戳'),
    sa.Column('next_scan_time', postgresql.TIMESTAMP(), autoincrement=False, nullable=True, comment='下次扫描时间，调度器下一次扫描事件的计划时间戳'),
    sa.Column('scan_interval_seconds', sa.INTEGER(), autoincrement=False, nullable=True, comment='扫描间隔，单位秒，调度器扫描频率'),
    sa.Column('processed_events_count', sa.INTEGER(), autoincrement=False, nullable=True, comment='已处理事件数，调度器处理的事件总数'),
    sa.Column('generated_tasks_count', sa.INTEGER(), autoincrement=False, nullable=True, comment='生成任务数，调度器生成的采集任务总数'),
    sa.Column('scheduler_status', sa.VARCHAR(length=20), autoincrement=False, nullable=True, comment='调度器状态：active活跃/paused暂停/stopped停止/error错误'),
    sa.Column('last_error_message', sa.TEXT(), autoincrement=False, nullable=True, comment='最后错误信息，调度器遇到的最后一个错误'),
    sa.Column('error_count', sa.INTEGER(), autoincrement=False, nullable=True, comment='错误次数，调度器累计的错误次数'),
    sa.Column('created_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True, comment='创建时间，调度器启动的时间戳'),
    sa.Column('updated_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True, comment='最后更新时间，调度器状态最后修改的时间戳'),
    sa.PrimaryKeyConstraint('id', name='event_crawl_scheduler_status_pkey'),
    sa.UniqueConstraint('scheduler_name', name='event_crawl_scheduler_status_scheduler_name_key')
    )
    op.create_index('ix_event_crawl_scheduler_status_id', 'event_crawl_scheduler_status', ['id'], unique=False)
    op.create_index('idx_scheduler_status', 'event_crawl_scheduler_status', ['scheduler_status'], unique=False)
    op.create_index('idx_scheduler_next_scan', 'event_crawl_scheduler_status', ['next_scan_time'], unique=False)
    op.create_table('model_metrics',
    sa.Column('id', sa.BIGINT(), autoincrement=True, nullable=False, comment='指标唯一标识符'),
    sa.Column('model_id', sa.BIGINT(), autoincrement=False, nullable=False, comment='关联的AI模型ID'),
    sa.Column('metric_date', postgresql.TIMESTAMP(), autoincrement=False, nullable=False, comment='指标日期'),
    sa.Column('time_window', sa.VARCHAR(length=20), autoincrement=False, nullable=True, comment='时间窗口：hour/day/week/month'),
    sa.Column('total_requests', sa.INTEGER(), autoincrement=False, nullable=True, comment='请求总数'),
    sa.Column('success_requests', sa.INTEGER(), autoincrement=False, nullable=True, comment='成功请求数'),
    sa.Column('failed_requests', sa.INTEGER(), autoincrement=False, nullable=True, comment='失败请求数'),
    sa.Column('avg_latency_ms', sa.INTEGER(), autoincrement=False, nullable=True, comment='平均延迟(毫秒)'),
    sa.Column('p95_latency_ms', sa.INTEGER(), autoincrement=False, nullable=True, comment='95分位延迟(毫秒)'),
    sa.Column('p99_latency_ms', sa.INTEGER(), autoincrement=False, nullable=True, comment='99分位延迟(毫秒)'),
    sa.Column('total_input_tokens', sa.BIGINT(), autoincrement=False, nullable=True, comment='输入token总数'),
    sa.Column('total_output_tokens', sa.BIGINT(), autoincrement=False, nullable=True, comment='输出token总数'),
    sa.Column('avg_input_tokens', sa.INTEGER(), autoincrement=False, nullable=True, comment='平均输入token数'),
    sa.Column('avg_output_tokens', sa.INTEGER(), autoincrement=False, nullable=True, comment='平均输出token数'),
    sa.Column('total_cost', sa.NUMERIC(precision=12, scale=4), autoincrement=False, nullable=True, comment='总成本'),
    sa.Column('avg_cost_per_request', sa.NUMERIC(precision=8, scale=4), autoincrement=False, nullable=True, comment='平均每次请求成本'),
    sa.Column('accuracy_score', sa.NUMERIC(precision=5, scale=4), autoincrement=False, nullable=True, comment='准确率评分'),
    sa.Column('quality_score', sa.NUMERIC(precision=5, scale=4), autoincrement=False, nullable=True, comment='质量评分'),
    sa.Column('user_satisfaction', sa.NUMERIC(precision=3, scale=2), autoincrement=False, nullable=True, comment='用户满意度'),
    sa.Column('timeout_errors', sa.INTEGER(), autoincrement=False, nullable=True, comment='超时错误数'),
    sa.Column('rate_limit_errors', sa.INTEGER(), autoincrement=False, nullable=True, comment='限流错误数'),
    sa.Column('api_errors', sa.INTEGER(), autoincrement=False, nullable=True, comment='API错误数'),
    sa.Column('other_errors', sa.INTEGER(), autoincrement=False, nullable=True, comment='其他错误数'),
    sa.Column('extra_metadata', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True, comment='扩展元数据'),
    sa.Column('created_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True, comment='创建时间'),
    sa.ForeignKeyConstraint(['model_id'], ['ai_models.id'], name='model_metrics_model_id_fkey', ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id', name='model_metrics_pkey')
    )
    op.create_index('ix_model_metrics_model_id', 'model_metrics', ['model_id'], unique=False)
    op.create_index('ix_model_metrics_id', 'model_metrics', ['id'], unique=False)
    op.create_index('idx_model_metrics_window', 'model_metrics', ['time_window'], unique=False)
    op.create_index('idx_model_metrics_unique', 'model_metrics', ['model_id', 'metric_date', 'time_window'], unique=True)
    op.create_index('idx_model_metrics_performance', 'model_metrics', ['avg_latency_ms', 'total_requests'], unique=False)
    op.create_index('idx_model_metrics_model_date', 'model_metrics', ['model_id', 'metric_date'], unique=False)
    op.create_index('idx_model_metrics_date', 'model_metrics', ['metric_date'], unique=False)
    op.create_table('role_permissions',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False, comment='关联ID'),
    sa.Column('role_id', sa.INTEGER(), autoincrement=False, nullable=False, comment='角色ID'),
    sa.Column('permission_id', sa.INTEGER(), autoincrement=False, nullable=False, comment='权限ID'),
    sa.Column('created_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True, comment='创建时间'),
    sa.ForeignKeyConstraint(['permission_id'], ['permissions.id'], name='role_permissions_permission_id_fkey', ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['role_id'], ['roles.id'], name='role_permissions_role_id_fkey', ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id', name='role_permissions_pkey'),
    sa.UniqueConstraint('role_id', 'permission_id', name='uk_role_permission')
    )
    op.create_index('ix_role_permissions_id', 'role_permissions', ['id'], unique=False)
    op.create_index('idx_rp_role_id', 'role_permissions', ['role_id'], unique=False)
    op.create_index('idx_rp_permission_id', 'role_permissions', ['permission_id'], unique=False)
    op.create_table('event_crawl_associations',
    sa.Column('id', sa.BIGINT(), autoincrement=True, nullable=False, comment='事件采集关联唯一标识符，自增主键'),
    sa.Column('event_id', sa.BIGINT(), autoincrement=False, nullable=False, comment='财经事件ID，外键引用financial_events表'),
    sa.Column('rule_id', sa.BIGINT(), autoincrement=False, nullable=False, comment='采集规则ID，外键引用event_driven_crawl_rules表'),
    sa.Column('source_id', sa.BIGINT(), autoincrement=False, nullable=False, comment='数据源ID，外键引用data_sources表'),
    sa.Column('scheduled_crawl_times', postgresql.ARRAY(postgresql.TIMESTAMP()), autoincrement=False, nullable=True, comment='计划采集时间数组，存储该事件的所有预定采集时间点'),
    sa.Column('actual_crawl_times', postgresql.ARRAY(postgresql.TIMESTAMP()), autoincrement=False, nullable=True, comment='实际采集时间数组，存储实际执行的采集时间点'),
    sa.Column('association_status', sa.VARCHAR(length=20), autoincrement=False, nullable=True, comment='关联状态：scheduled已调度/active进行中/completed已完成/cancelled已取消'),
    sa.Column('crawl_results', postgresql.JSONB(astext_type=sa.Text()), autoincrement=False, nullable=True, comment='采集结果摘要，存储采集任务的执行结果统计'),
    sa.Column('created_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True, comment='创建时间，关联建立的时间戳'),
    sa.Column('updated_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True, comment='最后更新时间，关联状态最后修改的时间戳'),
    sa.ForeignKeyConstraint(['event_id'], ['financial_events.id'], name='event_crawl_associations_event_id_fkey', ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id', name='event_crawl_associations_pkey')
    )
    op.create_index('ix_event_crawl_associations_id', 'event_crawl_associations', ['id'], unique=False)
    op.create_index('idx_event_crawl_associations_source', 'event_crawl_associations', ['source_id'], unique=False)
    op.create_index('idx_event_crawl_associations_rule', 'event_crawl_associations', ['rule_id'], unique=False)
    op.create_index('idx_event_crawl_associations_event', 'event_crawl_associations', ['event_id', 'association_status'], unique=False)
    op.create_table('ai_models',
    sa.Column('id', sa.BIGINT(), autoincrement=True, nullable=False, comment='模型唯一标识符'),
    sa.Column('model_name', sa.VARCHAR(length=100), autoincrement=False, nullable=False, comment='模型名称'),
    sa.Column('display_name', sa.VARCHAR(length=200), autoincrement=False, nullable=True, comment='显示名称'),
    sa.Column('description', sa.TEXT(), autoincrement=False, nullable=True, comment='模型描述'),
    sa.Column('model_type', sa.VARCHAR(length=50), autoincrement=False, nullable=False, comment='模型类型'),
    sa.Column('provider', sa.VARCHAR(length=50), autoincrement=False, nullable=False, comment='模型提供商'),
    sa.Column('model_version', sa.VARCHAR(length=50), autoincrement=False, nullable=False, comment='模型版本'),
    sa.Column('api_endpoint', sa.VARCHAR(length=500), autoincrement=False, nullable=True, comment='API接口地址'),
    sa.Column('api_key_name', sa.VARCHAR(length=100), autoincrement=False, nullable=True, comment='API密钥环境变量名'),
    sa.Column('model_params', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True, comment='模型配置参数'),
    sa.Column('max_tokens', sa.INTEGER(), autoincrement=False, nullable=True, comment='最大token数'),
    sa.Column('temperature', sa.NUMERIC(precision=3, scale=2), autoincrement=False, nullable=True, comment='温度参数'),
    sa.Column('top_p', sa.NUMERIC(precision=3, scale=2), autoincrement=False, nullable=True, comment='top_p参数'),
    sa.Column('frequency_penalty', sa.NUMERIC(precision=3, scale=2), autoincrement=False, nullable=True, comment='频率惩罚'),
    sa.Column('presence_penalty', sa.NUMERIC(precision=3, scale=2), autoincrement=False, nullable=True, comment='存在惩罚'),
    sa.Column('rate_limit_rpm', sa.INTEGER(), autoincrement=False, nullable=True, comment='每分钟请求数限制'),
    sa.Column('rate_limit_tpm', sa.INTEGER(), autoincrement=False, nullable=True, comment='每分钟token数限制'),
    sa.Column('timeout_seconds', sa.INTEGER(), autoincrement=False, nullable=True, comment='超时时间(秒)'),
    sa.Column('max_retries', sa.INTEGER(), autoincrement=False, nullable=True, comment='最大重试次数'),
    sa.Column('cost_per_1k_input_tokens', sa.NUMERIC(precision=10, scale=6), autoincrement=False, nullable=True, comment='每1K输入token成本'),
    sa.Column('cost_per_1k_output_tokens', sa.NUMERIC(precision=10, scale=6), autoincrement=False, nullable=True, comment='每1K输出token成本'),
    sa.Column('cost_per_request', sa.NUMERIC(precision=10, scale=6), autoincrement=False, nullable=True, comment='每次请求成本'),
    sa.Column('accuracy', sa.NUMERIC(precision=5, scale=4), autoincrement=False, nullable=True, comment='准确率'),
    sa.Column('latency_ms', sa.INTEGER(), autoincrement=False, nullable=True, comment='平均延迟(毫秒)'),
    sa.Column('availability', sa.NUMERIC(precision=5, scale=4), autoincrement=False, nullable=True, comment='可用性'),
    sa.Column('status', sa.VARCHAR(length=20), autoincrement=False, nullable=True, comment='模型状态'),
    sa.Column('is_default', sa.BOOLEAN(), autoincrement=False, nullable=True, comment='是否为默认模型'),
    sa.Column('priority', sa.INTEGER(), autoincrement=False, nullable=True, comment='优先级(1-10,数值越小优先级越高)'),
    sa.Column('total_requests', sa.BIGINT(), autoincrement=False, nullable=True, comment='总请求数'),
    sa.Column('success_requests', sa.BIGINT(), autoincrement=False, nullable=True, comment='成功请求数'),
    sa.Column('failed_requests', sa.BIGINT(), autoincrement=False, nullable=True, comment='失败请求数'),
    sa.Column('total_input_tokens', sa.BIGINT(), autoincrement=False, nullable=True, comment='总输入token数'),
    sa.Column('total_output_tokens', sa.BIGINT(), autoincrement=False, nullable=True, comment='总输出token数'),
    sa.Column('total_cost', sa.NUMERIC(precision=12, scale=4), autoincrement=False, nullable=True, comment='总成本'),
    sa.Column('created_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True, comment='创建时间'),
    sa.Column('updated_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True, comment='更新时间'),
    sa.Column('last_used_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True, comment='最后使用时间'),
    sa.PrimaryKeyConstraint('id', name='ai_models_pkey'),
    sa.UniqueConstraint('model_name', name='ai_models_model_name_key')
    )
    op.create_index('ix_ai_models_id', 'ai_models', ['id'], unique=False)
    op.create_index('idx_ai_model_usage', 'ai_models', ['total_requests', 'success_requests'], unique=False)
    op.create_index('idx_ai_model_type', 'ai_models', ['model_type'], unique=False)
    op.create_index('idx_ai_model_status', 'ai_models', ['status'], unique=False)
    op.create_index('idx_ai_model_provider', 'ai_models', ['provider'], unique=False)
    op.create_index('idx_ai_model_priority', 'ai_models', ['priority'], unique=False)
    op.create_index('idx_ai_model_performance', 'ai_models', ['accuracy', 'latency_ms'], unique=False)
    op.create_index('idx_ai_model_default', 'ai_models', ['is_default'], unique=False)
    op.create_table('permission_operation_logs',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False, comment='日志ID'),
    sa.Column('operator_id', sa.INTEGER(), autoincrement=False, nullable=False, comment='操作人ID'),
    sa.Column('operation_type', sa.VARCHAR(length=20), autoincrement=False, nullable=False, comment='操作类型：GRANT/REVOKE'),
    sa.Column('target_type', sa.VARCHAR(length=20), autoincrement=False, nullable=False, comment='目标类型：USER/ROLE'),
    sa.Column('target_id', sa.INTEGER(), autoincrement=False, nullable=False, comment='目标ID'),
    sa.Column('role_id', sa.INTEGER(), autoincrement=False, nullable=True, comment='角色ID'),
    sa.Column('permission_id', sa.INTEGER(), autoincrement=False, nullable=True, comment='权限ID'),
    sa.Column('operation_time', postgresql.TIMESTAMP(), autoincrement=False, nullable=True, comment='操作时间'),
    sa.Column('ip_address', sa.VARCHAR(length=45), autoincrement=False, nullable=True, comment='操作IP'),
    sa.ForeignKeyConstraint(['operator_id'], ['users.id'], name='permission_operation_logs_operator_id_fkey'),
    sa.PrimaryKeyConstraint('id', name='permission_operation_logs_pkey')
    )
    op.create_index('ix_permission_operation_logs_id', 'permission_operation_logs', ['id'], unique=False)
    op.create_index('idx_pol_target_id', 'permission_operation_logs', ['target_id'], unique=False)
    op.create_index('idx_pol_operator_id', 'permission_operation_logs', ['operator_id'], unique=False)
    op.create_index('idx_pol_operation_time', 'permission_operation_logs', ['operation_time'], unique=False)
    op.create_table('user_roles',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False, comment='关联ID'),
    sa.Column('user_id', sa.INTEGER(), autoincrement=False, nullable=False, comment='用户ID'),
    sa.Column('role_id', sa.INTEGER(), autoincrement=False, nullable=False, comment='角色ID'),
    sa.Column('created_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True, comment='创建时间'),
    sa.Column('created_by', sa.INTEGER(), autoincrement=False, nullable=True, comment='创建人ID'),
    sa.ForeignKeyConstraint(['role_id'], ['roles.id'], name='user_roles_role_id_fkey', ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], name='user_roles_user_id_fkey', ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id', name='user_roles_pkey'),
    sa.UniqueConstraint('user_id', 'role_id', name='uk_user_role')
    )
    op.create_index('ix_user_roles_id', 'user_roles', ['id'], unique=False)
    op.create_index('idx_ur_user_id', 'user_roles', ['user_id'], unique=False)
    op.create_index('idx_ur_role_id', 'user_roles', ['role_id'], unique=False)
    op.create_table('permissions',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False, comment='权限ID'),
    sa.Column('code', sa.VARCHAR(length=100), autoincrement=False, nullable=False, comment='权限编码'),
    sa.Column('name', sa.VARCHAR(length=100), autoincrement=False, nullable=False, comment='权限名称'),
    sa.Column('description', sa.TEXT(), autoincrement=False, nullable=True, comment='权限描述'),
    sa.Column('module', sa.VARCHAR(length=50), autoincrement=False, nullable=False, comment='所属模块'),
    sa.Column('resource', sa.VARCHAR(length=50), autoincrement=False, nullable=False, comment='资源类型'),
    sa.Column('action', sa.VARCHAR(length=50), autoincrement=False, nullable=False, comment='操作类型'),
    sa.Column('created_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True, comment='创建时间'),
    sa.Column('updated_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True, comment='更新时间'),
    sa.PrimaryKeyConstraint('id', name='permissions_pkey'),
    sa.UniqueConstraint('code', name='permissions_code_key')
    )
    op.create_index('ix_permissions_id', 'permissions', ['id'], unique=False)
    op.create_index('idx_permission_resource', 'permissions', ['resource'], unique=False)
    op.create_index('idx_permission_module', 'permissions', ['module'], unique=False)
    op.create_index('idx_permission_code', 'permissions', ['code'], unique=False)
    op.create_index('idx_permission_action', 'permissions', ['action'], unique=False)
    op.create_table('roles',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False, comment='角色ID'),
    sa.Column('name', sa.VARCHAR(length=50), autoincrement=False, nullable=False, comment='角色名称'),
    sa.Column('description', sa.TEXT(), autoincrement=False, nullable=True, comment='角色描述'),
    sa.Column('is_system', sa.BOOLEAN(), autoincrement=False, nullable=True, comment='是否系统内置角色'),
    sa.Column('parent_id', sa.INTEGER(), autoincrement=False, nullable=True, comment='父角色ID'),
    sa.Column('created_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True, comment='创建时间'),
    sa.Column('updated_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True, comment='更新时间'),
    sa.ForeignKeyConstraint(['parent_id'], ['roles.id'], name='roles_parent_id_fkey'),
    sa.PrimaryKeyConstraint('id', name='roles_pkey'),
    sa.UniqueConstraint('name', name='roles_name_key')
    )
    op.create_index('ix_roles_id', 'roles', ['id'], unique=False)
    op.create_index('idx_role_parent_id', 'roles', ['parent_id'], unique=False)
    op.create_index('idx_role_name', 'roles', ['name'], unique=False)
    # ### end Alembic commands ###
