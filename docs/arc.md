# FinSight 后端技术架构设计

## 1. 整体架构概览

### 1.1 架构风格
采用**微服务架构 + 事件驱动架构**，结合DDD（领域驱动设计）原则，确保系统的高可用性、可扩展性和可维护性。

### 1.2 核心设计原则
- **单一职责**：每个微服务专注于特定业务领域
- **数据一致性**：采用Saga模式处理分布式事务
- **高并发支持**：异步处理 + 消息队列 + 缓存策略
- **弹性设计**：熔断器、限流、降级机制
- **安全优先**：多层安全防护 + 数据加密

## 2. 微服务拆分设计

### 2.1 核心业务服务

#### 用户服务 (User Service)
```python
# 服务职责
- 用户注册/登录/认证
- 用户画像管理（分层、标签、偏好）
- 用户行为追踪
- 权限管理

# 技术栈
- FastAPI + SQLAlchemy + PostgreSQL
- Redis (会话缓存)
- JWT认证
```

#### 信息处理服务 (Information Processing Service)
```python
# 服务职责
- 信息采集调度
- 信息筛选与分类
- NLP处理与摘要生成
- 信息质量评估

# 技术栈
- FastAPI + Celery + Redis
- DeepSeek模型集成
- Elasticsearch (信息搜索)
- MinIO (文件存储)
```

#### 推送服务 (Push Service)
```python
# 服务职责
- 推送策略制定
- 多渠道推送执行
- 推送效果监控
- 推送历史管理

# 技术栈
- FastAPI + Kafka + Redis
- 多渠道SDK集成
- Prometheus监控
```

#### 数据采集服务 (Data Collection Service)
```python
# 服务职责
- 多源数据爬取
- 第三方API数据获取
- 数据清洗与去重
- 数据质量监控

# 技术栈
- Scrapy + FastAPI
- Apache Airflow (任务调度)
- MongoDB (原始数据存储)
```

### 2.2 基础设施服务

#### API网关 (Gateway Service)
```python
# 功能
- 路由转发
- 认证授权
- 限流熔断
- 日志审计

# 技术选型：Kong + Redis
```

#### 配置中心 (Config Service)
```python
# 功能
- 配置管理
- 动态配置更新
- 环境隔离
- 版本管理

# 技术选型：Consul + Vault
```

## 3. 数据架构设计

### 3.1 数据存储策略

#### 主数据库设计
```sql
-- 用户相关表
CREATE TABLE users (
    id BIGSERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    user_type SMALLINT NOT NULL, -- 1:小白型 2:进阶型 3:焦虑型
    risk_level SMALLINT NOT NULL, -- 1-5风险等级
    knowledge_level SMALLINT NOT NULL, -- 1-5知识水平
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 用户标签表
CREATE TABLE user_tags (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT REFERENCES users(id),
    tag_category VARCHAR(50) NOT NULL, -- core/derived
    tag_name VARCHAR(100) NOT NULL,
    tag_value TEXT,
    weight DECIMAL(3,2) DEFAULT 1.0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 信息内容表
CREATE TABLE information (
    id BIGSERIAL PRIMARY KEY,
    title VARCHAR(500) NOT NULL,
    content TEXT NOT NULL,
    summary TEXT,
    source_url VARCHAR(1000),
    source_type VARCHAR(50), -- news/report/social_media
    category VARCHAR(100), -- policy/company/market/industry
    keywords TEXT[], -- PostgreSQL数组类型
    importance_score DECIMAL(3,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processed_at TIMESTAMP
);

-- 推送记录表
CREATE TABLE push_records (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT REFERENCES users(id),
    information_id BIGINT REFERENCES information(id),
    push_channel VARCHAR(50), -- app/sms/wechat/email
    push_status VARCHAR(20), -- sent/delivered/opened/clicked
    push_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    opened_at TIMESTAMP,
    clicked_at TIMESTAMP
);
```

#### 缓存策略
```python
# Redis缓存设计
CACHE_KEYS = {
    'user_profile': 'user:profile:{user_id}',  # TTL: 1小时
    'user_tags': 'user:tags:{user_id}',        # TTL: 30分钟
    'hot_info': 'info:hot:{category}',         # TTL: 10分钟
    'push_strategy': 'push:strategy:{user_id}', # TTL: 1小时
    'rate_limit': 'rate:limit:{user_id}:{api}' # TTL: 1分钟
}
```

#### 搜索引擎设计
```json
// Elasticsearch索引映射
{
  "mappings": {
    "properties": {
      "title": {"type": "text", "analyzer": "ik_max_word"},
      "content": {"type": "text", "analyzer": "ik_max_word"},
      "summary": {"type": "text", "analyzer": "ik_max_word"},
      "category": {"type": "keyword"},
      "keywords": {"type": "keyword"},
      "importance_score": {"type": "float"},
      "created_at": {"type": "date"},
      "user_tags": {"type": "keyword"}
    }
  }
}
```

### 3.2 消息队列设计

#### Kafka Topic设计
```yaml
# 金融信息处理流
financial_data_raw:        # 原始数据采集
  partitions: 12
  replication: 3

financial_data_processed:  # 处理后数据
  partitions: 8
  replication: 3

user_behavior_events:      # 用户行为事件
  partitions: 16
  replication: 3

push_notifications:        # 推送通知
  partitions: 6
  replication: 3

system_alerts:            # 系统告警
  partitions: 3
  replication: 3
```

## 4. 核心服务详细设计

### 4.1 信息处理服务架构

```python
# 信息处理管道设计
from typing import List, Dict
from abc import ABC, abstractmethod

class InformationProcessor(ABC):
    @abstractmethod
    async def process(self, data: Dict) -> Dict:
        pass

class DataCollectionProcessor(InformationProcessor):
    """数据采集处理器"""
    async def process(self, data: Dict) -> Dict:
        # 1. 数据清洗
        cleaned_data = await self.clean_data(data)
        # 2. 去重检查
        if await self.is_duplicate(cleaned_data):
            return None
        # 3. 质量评估
        quality_score = await self.evaluate_quality(cleaned_data)
        return {**cleaned_data, 'quality_score': quality_score}

class NLPProcessor(InformationProcessor):
    """自然语言处理器"""
    async def process(self, data: Dict) -> Dict:
        # 1. 文本分析
        nlp_result = await self.deepseek_analyze(data['content'])
        # 2. 关键词提取
        keywords = await self.extract_keywords(data['content'])
        # 3. 摘要生成
        summary = await self.generate_summary(data['content'])
        
        return {
            **data,
            'nlp_analysis': nlp_result,
            'keywords': keywords,
            'summary': summary
        }

class ClassificationProcessor(InformationProcessor):
    """分类处理器"""
    async def process(self, data: Dict) -> Dict:
        # 1. 内容分类
        category = await self.classify_content(data)
        # 2. 重要性评分
        importance = await self.score_importance(data)
        # 3. 标签生成
        tags = await self.generate_tags(data)
        
        return {
            **data,
            'category': category,
            'importance_score': importance,
            'tags': tags
        }

# 处理管道
class ProcessingPipeline:
    def __init__(self):
        self.processors = [
            DataCollectionProcessor(),
            NLPProcessor(),
            ClassificationProcessor()
        ]
    
    async def execute(self, raw_data: Dict) -> Dict:
        result = raw_data
        for processor in self.processors:
            result = await processor.process(result)
            if result is None:  # 处理失败或过滤
                break
        return result
```

### 4.2 推送服务架构

```python
# 推送策略引擎
class PushStrategyEngine:
    def __init__(self, redis_client, user_service):
        self.redis = redis_client
        self.user_service = user_service
    
    async def generate_push_plan(self, user_id: int, information_id: int) -> Dict:
        # 1. 获取用户画像
        user_profile = await self.user_service.get_user_profile(user_id)
        
        # 2. 获取信息特征
        info_features = await self.get_information_features(information_id)
        
        # 3. 匹配度计算
        relevance_score = await self.calculate_relevance(
            user_profile, info_features
        )
        
        if relevance_score < 0.3:  # 低相关度，不推送
            return None
            
        # 4. 推送策略生成
        strategy = await self.generate_strategy(
            user_profile, info_features, relevance_score
        )
        
        return strategy
    
    async def generate_strategy(self, user_profile, info_features, score):
        strategy = {
            'channels': [],
            'timing': 'immediate',
            'priority': 'normal',
            'content_format': 'standard'
        }
        
        # 基于用户类型调整策略
        if user_profile['user_type'] == 1:  # 小白型
            strategy['channels'] = ['app', 'wechat']
            strategy['content_format'] = 'simplified'
        elif user_profile['user_type'] == 2:  # 进阶型
            strategy['channels'] = ['app', 'email']
            strategy['content_format'] = 'detailed'
        else:  # 焦虑型
            strategy['channels'] = ['app']
            strategy['timing'] = 'scheduled'  # 避免频繁打扰
            
        # 基于重要性调整优先级
        if info_features['importance_score'] > 0.8:
            strategy['priority'] = 'high'
            strategy['channels'].append('sms')
            
        return strategy

# 多渠道推送管理器
class MultiChannelPushManager:
    def __init__(self):
        self.channels = {
            'app': AppPushChannel(),
            'sms': SMSPushChannel(),
            'wechat': WeChatPushChannel(),
            'email': EmailPushChannel()
        }
    
    async def execute_push(self, user_id: int, content: Dict, strategy: Dict):
        tasks = []
        for channel_name in strategy['channels']:
            channel = self.channels[channel_name]
            task = asyncio.create_task(
                channel.send(user_id, content, strategy)
            )
            tasks.append(task)
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 记录推送结果
        await self.record_push_results(user_id, content['id'], results)
```

### 4.3 用户服务架构

```python
# 用户画像管理
class UserProfileManager:
    def __init__(self, db_session, redis_client):
        self.db = db_session
        self.redis = redis_client
    
    async def update_user_tags(self, user_id: int, behavior_data: Dict):
        """基于用户行为更新标签"""
        # 1. 行为分析
        behavior_analysis = await self.analyze_behavior(behavior_data)
        
        # 2. 标签权重调整
        tag_updates = await self.calculate_tag_updates(
            user_id, behavior_analysis
        )
        
        # 3. 批量更新标签
        async with self.db.begin():
            for tag_name, new_weight in tag_updates.items():
                await self.update_tag_weight(user_id, tag_name, new_weight)
        
        # 4. 更新缓存
        await self.invalidate_user_cache(user_id)
    
    async def classify_user_type(self, user_data: Dict) -> int:
        """用户类型分类算法"""
        features = {
            'investment_experience': user_data.get('experience_years', 0),
            'daily_decision_time': user_data.get('decision_time_minutes', 0),
            'risk_tolerance': user_data.get('risk_level', 1),
            'knowledge_score': user_data.get('knowledge_score', 0),
            'behavior_patterns': user_data.get('behavior_patterns', {})
        }
        
        # 规则引擎分类
        if (features['investment_experience'] < 1 and 
            features['knowledge_score'] < 30):
            return 1  # 小白型
        elif (features['investment_experience'] >= 2 and 
              features['knowledge_score'] >= 60):
            return 2  # 进阶型
        else:
            return 3  # 焦虑型
```

## 5. 性能优化设计

### 5.1 缓存策略

```python
# 多级缓存架构
class CacheManager:
    def __init__(self):
        self.l1_cache = {}  # 进程内缓存
        self.l2_cache = redis.Redis()  # Redis缓存
        self.l3_cache = None  # CDN缓存（可选）
    
    async def get(self, key: str):
        # L1缓存查找
        if key in self.l1_cache:
            return self.l1_cache[key]
        
        # L2缓存查找
        value = await self.l2_cache.get(key)
        if value:
            self.l1_cache[key] = value  # 写入L1缓存
            return value
        
        return None
    
    async def set(self, key: str, value, ttl: int = 300):
        # 写入所有缓存层
        self.l1_cache[key] = value
        await self.l2_cache.setex(key, ttl, value)
```

### 5.2 数据库优化

```sql
-- 核心索引设计
CREATE INDEX CONCURRENTLY idx_users_user_type ON users(user_type);
CREATE INDEX CONCURRENTLY idx_information_category_time ON information(category, created_at DESC);
CREATE INDEX CONCURRENTLY idx_push_records_user_status ON push_records(user_id, push_status);
CREATE INDEX CONCURRENTLY idx_user_tags_user_category ON user_tags(user_id, tag_category);

-- 分区表设计（按时间分区）
CREATE TABLE push_records_y2024m01 PARTITION OF push_records
FOR VALUES FROM ('2024-01-01') TO ('2024-02-01');
```

### 5.3 异步处理

```python
# Celery任务定义
from celery import Celery

app = Celery('finsight')

@app.task(bind=True, max_retries=3)
def process_information_task(self, raw_data: Dict):
    """异步信息处理任务"""
    try:
        pipeline = ProcessingPipeline()
        result = await pipeline.execute(raw_data)
        
        if result:
            # 存储处理结果
            await store_processed_information(result)
            # 触发推送任务
            trigger_push_task.delay(result['id'])
            
    except Exception as exc:
        # 重试机制
        if self.request.retries < self.max_retries:
            raise self.retry(exc=exc, countdown=60)
        else:
            # 记录失败日志
            logger.error(f"Processing failed: {exc}")

@app.task
def trigger_push_task(information_id: int):
    """触发推送任务"""
    # 获取目标用户列表
    target_users = get_target_users(information_id)
    
    # 批量创建推送任务
    for user_id in target_users:
        execute_push_task.delay(user_id, information_id)

@app.task(bind=True, max_retries=5)
def execute_push_task(self, user_id: int, information_id: int):
    """执行推送任务"""
    try:
        push_manager = MultiChannelPushManager()
        await push_manager.execute_push(user_id, information_id)
    except Exception as exc:
        raise self.retry(exc=exc, countdown=30)
```

## 6. 安全架构设计

### 6.1 认证授权

```python
# JWT认证实现
from jose import JWTError, jwt
from passlib.context import CryptContext

class AuthManager:
    def __init__(self):
        self.pwd_context = CryptContext(schemes=["bcrypt"])
        self.secret_key = os.getenv("JWT_SECRET_KEY")
        self.algorithm = "HS256"
        self.access_token_expire = 30  # 30分钟
        self.refresh_token_expire = 7  # 7天
    
    def create_access_token(self, data: dict) -> str:
        to_encode = data.copy()
        expire = datetime.utcnow() + timedelta(minutes=self.access_token_expire)
        to_encode.update({"exp": expire, "type": "access"})
        return jwt.encode(to_encode, self.secret_key, algorithm=self.algorithm)
    
    def create_refresh_token(self, data: dict) -> str:
        to_encode = data.copy()
        expire = datetime.utcnow() + timedelta(days=self.refresh_token_expire)
        to_encode.update({"exp": expire, "type": "refresh"})
        return jwt.encode(to_encode, self.secret_key, algorithm=self.algorithm)
    
    async def verify_token(self, token: str) -> dict:
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            return payload
        except JWTError:
            raise HTTPException(status_code=401, detail="Invalid token")
```

### 6.2 数据加密

```python
# 敏感数据加密
from cryptography.fernet import Fernet
import os

class DataEncryption:
    def __init__(self):
        self.key = os.getenv("ENCRYPTION_KEY").encode()
        self.cipher_suite = Fernet(self.key)
    
    def encrypt_sensitive_data(self, data: str) -> str:
        """加密敏感数据"""
        encrypted_data = self.cipher_suite.encrypt(data.encode())
        return encrypted_data.decode()
    
    def decrypt_sensitive_data(self, encrypted_data: str) -> str:
        """解密敏感数据"""
        decrypted_data = self.cipher_suite.decrypt(encrypted_data.encode())
        return decrypted_data.decode()
```

### 6.3 API安全

```python
# 限流中间件
class RateLimitMiddleware:
    def __init__(self, redis_client):
        self.redis = redis_client
    
    async def __call__(self, request: Request, call_next):
        client_ip = request.client.host
        user_id = getattr(request.state, 'user_id', None)
        
        # 构建限流key
        rate_limit_key = f"rate_limit:{client_ip}:{user_id}"
        
        # 检查限流
        current_requests = await self.redis.get(rate_limit_key)
        if current_requests and int(current_requests) > 100:  # 每分钟100次
            raise HTTPException(status_code=429, detail="Too many requests")
        
        # 增加计数
        await self.redis.incr(rate_limit_key)
        await self.redis.expire(rate_limit_key, 60)  # 60秒过期
        
        response = await call_next(request)
        return response
```

## 7. 监控告警设计

### 7.1 系统监控

```python
# Prometheus指标收集
from prometheus_client import Counter, Histogram, Gauge

# 业务指标
REQUEST_COUNT = Counter('api_requests_total', 'Total API requests', ['method', 'endpoint'])
REQUEST_DURATION = Histogram('api_request_duration_seconds', 'API request duration')
ACTIVE_USERS = Gauge('active_users_total', 'Total active users')
PUSH_SUCCESS_RATE = Gauge('push_success_rate', 'Push notification success rate')

# 信息处理指标
INFO_PROCESSED = Counter('information_processed_total', 'Total processed information')
AI_MODEL_LATENCY = Histogram('ai_model_latency_seconds', 'AI model processing latency')

class MetricsCollector:
    @staticmethod
    def record_request(method: str, endpoint: str, duration: float):
        REQUEST_COUNT.labels(method=method, endpoint=endpoint).inc()
        REQUEST_DURATION.observe(duration)
    
    @staticmethod
    def update_active_users(count: int):
        ACTIVE_USERS.set(count)
    
    @staticmethod
    def record_push_result(success: bool):
        if success:
            PUSH_SUCCESS_RATE.set(PUSH_SUCCESS_RATE._value._value + 0.01)
```

### 7.2 告警规则

```yaml
# Prometheus告警规则
groups:
  - name: finsight_alerts
    rules:
      - alert: HighErrorRate
        expr: rate(api_requests_total{status="500"}[5m]) > 0.1
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "High error rate detected"
      
      - alert: DatabaseConnectionIssue
        expr: up{job="postgresql"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Database connection lost"
      
      - alert: LowPushSuccessRate
        expr: push_success_rate < 0.8
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Push notification success rate is low"
```

## 8. 部署架构

### 8.1 容器化部署

```dockerfile
# Dockerfile示例
FROM python:3.11-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

COPY . .

EXPOSE 8000

CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### 8.2 Kubernetes部署

```yaml
# deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: finsight-api
spec:
  replicas: 3
  selector:
    matchLabels:
      app: finsight-api
  template:
    metadata:
      labels:
        app: finsight-api
    spec:
      containers:
      - name: api
        image: finsight/api:latest
        ports:
        - containerPort: 8000
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: db-secret
              key: url
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
---
apiVersion: v1
kind: Service
metadata:
  name: finsight-api-service
spec:
  selector:
    app: finsight-api
  ports:
  - port: 80
    targetPort: 8000
  type: LoadBalancer
```

## 9. 技术栈总结

### 9.1 核心技术栈
- **Web框架**: FastAPI
- **数据库**: PostgreSQL + Redis + MongoDB
- **消息队列**: Apache Kafka
- **搜索引擎**: Elasticsearch
- **任务队列**: Celery
- **容器化**: Docker + Kubernetes
- **监控**: Prometheus + Grafana
- **日志**: ELK Stack

### 9.2 开发工具链
- **版本控制**: Git + GitLab
- **CI/CD**: GitLab CI + ArgoCD
- **代码质量**: SonarQube
- **API文档**: Swagger/OpenAPI
- **负载测试**: Locust

这个架构设计确保了系统的高可用性、可扩展性和安全性，能够支撑FinSight产品的核心业务需求，同时为未来的扩展留下了足够的空间。