# 环境变量配置说明

## 基础配置

| 环境变量名 | 描述 | 默认值 | 必填 |
|------------|------|--------|------|
| ENVIRONMENT | 运行环境：development/testing/production | development | 是 |
| DEBUG | 是否开启调试模式 | true | 否 |
| DATABASE_URL | 数据库连接字符串 | - | 是 |
| REDIS_URL | Redis连接字符串 | redis://localhost:6379/0 | 否 |

## AI模型配置

### DeepSeek 配置
| 环境变量名 | 描述 | 默认值 | 必填 |
|------------|------|--------|------|
| DEEPSEEK_API_KEY | DeepSeek API密钥 | - | 是 |
| DEEPSEEK_API_BASE | DeepSeek API基础URL | https://api.deepseek.com/v1 | 否 |

### OpenAI 配置
| 环境变量名 | 描述 | 默认值 | 必填 |
|------------|------|--------|------|
| OPENAI_API_KEY | OpenAI API密钥 | - | 否 |
| OPENAI_API_BASE | OpenAI API基础URL | https://api.openai.com/v1 | 否 |

### 智谱 AI 配置
| 环境变量名 | 描述 | 默认值 | 必填 |
|------------|------|--------|------|
| ZHIPU_API_KEY | 智谱AI API密钥 | - | 否 |
| ZHIPU_API_BASE | 智谱AI API基础URL | https://open.bigmodel.cn/api/paas/v4 | 否 |

### 千问 配置
| 环境变量名 | 描述 | 默认值 | 必填 |
|------------|------|--------|------|
| QWEN_API_KEY | 千问 API密钥 | - | 否 |
| QWEN_API_BASE | 千问 API基础URL | https://dashscope.aliyuncs.com/api/v1 | 否 |

### 百度 ERNIE 配置
| 环境变量名 | 描述 | 默认值 | 必填 |
|------------|------|--------|------|
| BAIDU_API_KEY | 百度 API密钥 | - | 否 |
| BAIDU_SECRET_KEY | 百度 Secret密钥 | - | 否 |
| BAIDU_API_BASE | 百度 API基础URL | https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop | 否 |

### 本地模型配置
| 环境变量名 | 描述 | 默认值 | 必填 |
|------------|------|--------|------|
| LOCAL_LLM_API_BASE | 本地模型API基础URL | http://localhost:8000/v1 | 否 |

## 数据采集配置

| 环境变量名 | 描述 | 默认值 | 必填 |
|------------|------|--------|------|
| HTTP_PROXY | HTTP代理地址 | - | 否 |
| HTTPS_PROXY | HTTPS代理地址 | - | 否 |
| USE_PROXY | 是否使用代理 | false | 否 |
| CRAWL_DELAY_MIN | 爬虫最小延迟(秒) | 1 | 否 |
| CRAWL_DELAY_MAX | 爬虫最大延迟(秒) | 3 | 否 |
| MAX_CONCURRENT_REQUESTS | 最大并发请求数 | 10 | 否 |

## 推送服务配置

### 短信服务配置
| 环境变量名 | 描述 | 默认值 | 必填 |
|------------|------|--------|------|
| SMS_PROVIDER | 短信服务提供商 | tencent | 否 |
| TENCENT_SMS_SECRET_ID | 腾讯云短信SecretId | - | 否 |
| TENCENT_SMS_SECRET_KEY | 腾讯云短信SecretKey | - | 否 |
| TENCENT_SMS_SDK_APP_ID | 腾讯云短信AppId | - | 否 |

### 邮件服务配置
| 环境变量名 | 描述 | 默认值 | 必填 |
|------------|------|--------|------|
| EMAIL_PROVIDER | 邮件服务提供商 | sendgrid | 否 |
| SENDGRID_API_KEY | SendGrid API密钥 | - | 否 |
| SENDGRID_FROM_EMAIL | 发送邮件地址 | <EMAIL> | 否 |

### 微信推送配置
| 环境变量名 | 描述 | 默认值 | 必填 |
|------------|------|--------|------|
| WECHAT_APP_ID | 微信应用ID | - | 否 |
| WECHAT_APP_SECRET | 微信应用Secret | - | 否 |

## 安全配置

| 环境变量名 | 描述 | 默认值 | 必填 |
|------------|------|--------|------|
| JWT_SECRET_KEY | JWT密钥 | - | 是 |
| JWT_ALGORITHM | JWT算法 | HS256 | 否 |
| JWT_EXPIRE_MINUTES | JWT过期时间(分钟) | 30 | 否 |
| ENCRYPTION_KEY | 加密密钥 | - | 是 |

## 监控配置

| 环境变量名 | 描述 | 默认值 | 必填 |
|------------|------|--------|------|
| LOG_LEVEL | 日志级别 | INFO | 否 |
| LOG_FILE_PATH | 日志文件路径 | logs/app.log | 否 |
| ENABLE_METRICS | 是否启用监控指标 | true | 否 |
| METRICS_PORT | 监控指标端口 | 8080 | 否 |

## 其他配置

| 环境变量名 | 描述 | 默认值 | 必填 |
|------------|------|--------|------|
| UPLOAD_DIR | 上传目录 | uploads | 否 |
| MAX_FILE_SIZE | 最大文件大小(字节) | 10485760 | 否 |
| CACHE_TTL | 缓存过期时间(秒) | 3600 | 否 |
| ENABLE_DEBUG_ROUTES | 是否启用调试路由 | false | 否 |
| ENABLE_CORS | 是否启用CORS | true | 否 |

## 配置示例

创建 `.env` 文件并添加以下配置：

```env
# 基础配置
ENVIRONMENT=development
DEBUG=true
DATABASE_URL=postgresql://user:password@localhost:5432/finsight
REDIS_URL=redis://localhost:6379/0

# AI模型配置
DEEPSEEK_API_KEY=your_deepseek_api_key_here
OPENAI_API_KEY=your_openai_api_key_here
ZHIPU_API_KEY=your_zhipu_api_key_here

# 安全配置
JWT_SECRET_KEY=your_jwt_secret_key_here
ENCRYPTION_KEY=your_encryption_key_here

# 其他配置根据需要添加
```

## 环境变量优先级

1. 环境变量（最高优先级）
2. `.env` 文件
3. 默认值（最低优先级）

## 注意事项

1. 所有包含密钥的环境变量都不应该提交到版本控制系统
2. 生产环境建议使用环境变量而不是 `.env` 文件
3. 定期更换API密钥以保证安全性
4. 确保数据库连接字符串的安全性 