# 用户服务 API 文档

## 概述

用户服务提供了完整的用户管理功能，包括基于手机号的认证系统、用户资料管理、行为追踪、标签管理等功能。

### 基础认证机制

用户服务采用基于手机号+短信验证码的认证方式，摒弃了传统的用户名+密码登录模式，提供更安全、便捷的用户体验。

#### 认证流程

1. **发送短信验证码** - 用户输入手机号，系统发送6位数字验证码
2. **验证登录** - 用户输入验证码，系统验证后自动注册或登录
3. **获取令牌** - 验证成功后返回JWT访问令牌和刷新令牌
4. **令牌刷新** - 使用刷新令牌获取新的访问令牌

#### 安全特性

- **短信验证码限制**：单个手机号每分钟最多发送1次，每小时最多5次
- **验证码有效期**：5分钟内有效
- **尝试次数限制**：单个验证码最多尝试3次
- **JWT令牌**：访问令牌30分钟有效，刷新令牌7天有效
- **会话管理**：支持多设备登录，可查看和管理活跃会话

---

## API 接口详情

### 1. 身份认证

#### 1.1 发送短信验证码

**请求地址**
```
POST /api/v1/users/send-sms-code
```

**请求参数**
```json
{
    "phone": "13800138000",
    "purpose": "login"
}
```

**参数说明**
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| phone | string | 是 | 手机号（11位中国大陆手机号） |
| purpose | string | 否 | 验证码用途，默认为"login" |

**返回示例**
```json
{
    "message": "Verification code sent successfully",
    "expires_in": 300
}
```

**错误码**
- `400` - 手机号格式错误
- `429` - 发送频率过高
- `500` - 发送失败

---

#### 1.2 手机号登录

**请求地址**
```
POST /api/v1/users/phone-login
```

**请求参数**
```json
{
    "phone": "13800138000",
    "verification_code": "123456"
}
```

**参数说明**
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| phone | string | 是 | 手机号 |
| verification_code | string | 是 | 6位数字验证码 |

**返回示例**
```json
{
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "token_type": "bearer",
    "expires_in": 1800,
    "user": {
        "id": 1,
        "phone": "13800138000",
        "username": null,
        "email": null,
        "user_type": 1,
        "risk_level": 3,
        "knowledge_level": 1,
        "is_active": true,
        "is_verified": true,
        "first_login_at": "2024-01-01T10:00:00Z",
        "last_login_at": "2024-01-01T10:00:00Z",
        "created_at": "2024-01-01T10:00:00Z",
        "updated_at": "2024-01-01T10:00:00Z"
    }
}
```

**错误码**
- `400` - 参数格式错误
- `401` - 验证码错误或已过期
- `500` - 登录失败

---

#### 1.3 刷新访问令牌

**请求地址**
```
POST /api/v1/users/refresh-token
```

**请求参数**
```json
{
    "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

**返回示例**
```json
{
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "token_type": "bearer",
    "expires_in": 1800
}
```

**错误码**
- `401` - 刷新令牌无效或已过期
- `500` - 刷新失败

---

#### 1.4 用户登出

**请求地址**
```
POST /api/v1/users/logout
```

**请求头**
```
Authorization: Bearer <access_token>
```

**返回**
- 状态码：`204` - 登出成功

---

### 2. 用户资料管理

#### 2.1 获取当前用户信息

**请求地址**
```
GET /api/v1/users/me
```

**请求头**
```
Authorization: Bearer <access_token>
```

**返回示例**
```json
{
    "id": 1,
    "phone": "13800138000",
    "username": "张三",
    "email": "<EMAIL>",
    "user_type": 1,
    "risk_level": 3,
    "knowledge_level": 2,
    "is_active": true,
    "is_verified": true,
    "first_login_at": "2024-01-01T10:00:00Z",
    "last_login_at": "2024-01-01T12:00:00Z",
    "created_at": "2024-01-01T10:00:00Z",
    "updated_at": "2024-01-01T11:30:00Z"
}
```

---

#### 2.2 获取用户资料

**请求地址**
```
GET /api/v1/users/profile
```

**请求头**
```
Authorization: Bearer <access_token>
```

**返回示例**
同上 `/me` 接口

---

#### 2.3 更新用户资料

**请求地址**
```
PUT /api/v1/users/profile
```

**请求头**
```
Authorization: Bearer <access_token>
```

**请求参数**
```json
{
    "username": "李四",
    "email": "<EMAIL>",
    "user_type": 2,
    "risk_level": 4,
    "knowledge_level": 3
}
```

**参数说明**
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| username | string | 否 | 用户名（3-50字符） |
| email | string | 否 | 邮箱地址 |
| user_type | integer | 否 | 用户类型（1:小白型, 2:进阶型, 3:焦虑型） |
| risk_level | integer | 否 | 风险等级（1-5） |
| knowledge_level | integer | 否 | 知识水平（1-5） |

**返回示例**
返回更新后的用户信息

**错误码**
- `400` - 参数验证失败
- `401` - 未授权
- `500` - 更新失败

---

### 3. 用户标签管理

#### 3.1 创建用户标签

**请求地址**
```
POST /api/v1/users/tags
```

**请求头**
```
Authorization: Bearer <access_token>
```

**请求参数**
```json
{
    "tag_category": "core",
    "tag_name": "risk_preference",
    "tag_value": "conservative",
    "weight": 1.0
}
```

**参数说明**
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| tag_category | string | 是 | 标签分类（core/derived/behavior/preference） |
| tag_name | string | 是 | 标签名称 |
| tag_value | string | 否 | 标签值 |
| weight | float | 否 | 权重（0-5），默认1.0 |

**返回示例**
```json
{
    "id": 1,
    "user_id": 1,
    "tag_category": "core",
    "tag_name": "risk_preference",
    "tag_value": "conservative",
    "weight": 1.0,
    "created_at": "2024-01-01T10:00:00Z"
}
```

---

#### 3.2 获取用户标签列表

**请求地址**
```
GET /api/v1/users/tags?category=core
```

**请求头**
```
Authorization: Bearer <access_token>
```

**查询参数**
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| category | string | 否 | 过滤标签分类 |

**返回示例**
```json
[
    {
        "id": 1,
        "user_id": 1,
        "tag_category": "core",
        "tag_name": "risk_preference",
        "tag_value": "conservative",
        "weight": 1.0,
        "created_at": "2024-01-01T10:00:00Z"
    }
]
```

---

#### 3.3 更新用户标签

**请求地址**
```
PUT /api/v1/users/tags/{tag_id}
```

**请求头**
```
Authorization: Bearer <access_token>
```

**请求参数**
```json
{
    "tag_value": "aggressive",
    "weight": 2.0
}
```

**返回示例**
返回更新后的标签信息

---

#### 3.4 删除用户标签

**请求地址**
```
DELETE /api/v1/users/tags/{tag_id}
```

**请求头**
```
Authorization: Bearer <access_token>
```

**返回**
- 状态码：`204` - 删除成功

---

### 4. 用户行为追踪

#### 4.1 记录用户行为

**请求地址**
```
POST /api/v1/users/behaviors
```

**请求头**
```
Authorization: Bearer <access_token>
```

**请求参数**
```json
{
    "action_type": "view_article",
    "action_target": "dashboard",
    "action_details": {
        "article_id": "123",
        "duration": 30
    }
}
```

**参数说明**
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| action_type | string | 是 | 行为类型 |
| action_target | string | 否 | 行为目标 |
| action_details | object | 否 | 行为详情 |

**返回示例**
```json
{
    "id": 1,
    "user_id": 1,
    "action_type": "view_article",
    "action_target": "dashboard",
    "action_details": "{\"article_id\": \"123\", \"duration\": 30}",
    "ip_address": "127.0.0.1",
    "user_agent": "Mozilla/5.0...",
    "created_at": "2024-01-01T10:00:00Z"
}
```

---

#### 4.2 获取用户行为列表

**请求地址**
```
GET /api/v1/users/behaviors?action_type=login&limit=50
```

**请求头**
```
Authorization: Bearer <access_token>
```

**查询参数**
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| action_type | string | 否 | 过滤行为类型 |
| limit | integer | 否 | 返回条数限制，默认100 |

**返回示例**
```json
[
    {
        "id": 1,
        "user_id": 1,
        "action_type": "login",
        "action_target": "phone_login",
        "action_details": null,
        "ip_address": "127.0.0.1",
        "user_agent": "Mozilla/5.0...",
        "created_at": "2024-01-01T10:00:00Z"
    }
]
```

---

### 5. 会话管理

#### 5.1 获取活跃会话列表

**请求地址**
```
GET /api/v1/users/sessions
```

**请求头**
```
Authorization: Bearer <access_token>
```

**返回示例**
```json
[
    {
        "id": 1,
        "user_id": 1,
        "ip_address": "127.0.0.1",
        "user_agent": "Mozilla/5.0...",
        "is_active": true,
        "expires_at": "2024-01-08T10:00:00Z",
        "last_used_at": "2024-01-01T12:00:00Z",
        "created_at": "2024-01-01T10:00:00Z"
    }
]
```

---

#### 5.2 使会话失效

**请求地址**
```
DELETE /api/v1/users/sessions/{session_id}
```

**请求头**
```
Authorization: Bearer <access_token>
```

**返回**
- 状态码：`204` - 操作成功

---

### 6. 用户分析

#### 6.1 获取用户分析数据

**请求地址**
```
GET /api/v1/users/analytics
```

**请求头**
```
Authorization: Bearer <access_token>
```

**返回示例**
```json
{
    "user_id": 1,
    "total_behaviors": 25,
    "behavior_types": {
        "login": 5,
        "view_article": 15,
        "share": 3,
        "comment": 2
    },
    "active_sessions": 2,
    "tags_count": 8,
    "last_activity": "2024-01-01T12:00:00Z",
    "user_score": 85.5
}
```

---

## 数据模型

### 用户模型 (User)

| 字段 | 类型 | 说明 |
|------|------|------|
| id | integer | 用户ID |
| phone | string | 手机号（唯一） |
| username | string | 用户名（可选，唯一） |
| email | string | 邮箱（可选，唯一） |
| user_type | integer | 用户类型（1:小白型, 2:进阶型, 3:焦虑型） |
| risk_level | integer | 风险等级（1-5） |
| knowledge_level | integer | 知识水平（1-5） |
| is_active | boolean | 是否激活 |
| is_verified | boolean | 是否已验证 |
| first_login_at | datetime | 首次登录时间 |
| last_login_at | datetime | 最后登录时间 |
| created_at | datetime | 创建时间 |
| updated_at | datetime | 更新时间 |

### 用户标签模型 (UserTag)

| 字段 | 类型 | 说明 |
|------|------|------|
| id | integer | 标签ID |
| user_id | integer | 用户ID |
| tag_category | string | 标签分类 |
| tag_name | string | 标签名称 |
| tag_value | string | 标签值 |
| weight | decimal | 权重 |
| created_at | datetime | 创建时间 |

### 用户行为模型 (UserBehavior)

| 字段 | 类型 | 说明 |
|------|------|------|
| id | integer | 行为ID |
| user_id | integer | 用户ID |
| action_type | string | 行为类型 |
| action_target | string | 行为目标 |
| action_details | text | 行为详情（JSON格式） |
| ip_address | string | IP地址 |
| user_agent | string | 用户代理 |
| created_at | datetime | 创建时间 |

### 用户会话模型 (UserSession)

| 字段 | 类型 | 说明 |
|------|------|------|
| id | integer | 会话ID |
| user_id | integer | 用户ID |
| session_token | string | 会话令牌 |
| refresh_token | string | 刷新令牌 |
| ip_address | string | IP地址 |
| user_agent | string | 用户代理 |
| is_active | boolean | 是否活跃 |
| expires_at | datetime | 过期时间 |
| last_used_at | datetime | 最后使用时间 |
| created_at | datetime | 创建时间 |

---

## B端管理员接口

### 1. 发送管理员登录验证码

**请求地址**
```
POST /b/api/v1/admin/users/auth/send-code
```

**请求参数**
```json
{
    "phone": "13800138000",
    "purpose": "login"
}
```

**参数说明**
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| phone | string | 是 | 手机号（11位中国大陆手机号） |
| purpose | string | 否 | 验证码用途，默认为"login" |

**返回示例**
```json
{
    "message": "Verification code sent successfully"
}
```

**错误码**
- `400` - 手机号格式错误
- `403` - 用户不是管理员
- `404` - 用户不存在
- `429` - 发送频率过高
- `500` - 发送失败

---

### 2. 创建用户

**请求地址**
```
POST /b/api/v1/admin/users/
```

**请求头**
```
Authorization: Bearer <access_token>
```

**请求参数**
```json
{
    "phone": "13800138000",
    "username": "测试用户",
    "email": "<EMAIL>",
    "user_type": 1,
    "risk_level": 3,
    "knowledge_level": 2,
    "is_active": true,
    "is_verified": false,
    "is_admin": false
}
```

**参数说明**
| 参数 | 类型 | 必填 | 说明 | 默认值 |
|------|------|------|------|-------|
| phone | string | 是 | 手机号 | - |
| username | string | 否 | 用户名 | null |
| email | string | 否 | 邮箱 | null |
| user_type | integer | 否 | 用户类型（1:小白型, 2:进阶型, 3:焦虑型） | 1 |
| risk_level | integer | 否 | 风险等级（1-5） | 3 |
| knowledge_level | integer | 否 | 知识水平（1-5） | 1 |
| is_active | boolean | 否 | 是否激活 | true |
| is_verified | boolean | 否 | 是否已验证 | false |
| is_admin | boolean | 否 | 是否是管理员 | false |

**返回示例**
```json
{
    "id": 10,
    "phone": "13800138000",
    "username": "测试用户",
    "email": "<EMAIL>",
    "user_type": 1,
    "risk_level": 3,
    "knowledge_level": 2,
    "is_active": true,
    "is_verified": false,
    "first_login_at": null,
    "last_login_at": null,
    "created_at": "2024-07-04T11:30:00Z",
    "updated_at": "2024-07-04T11:30:00Z"
}
```

**错误码**
- `400` - 请求参数错误（例如手机号格式不正确、用户名格式不正确等）
- `403` - 权限不足，需要`user.create`权限
- `409` - 资源冲突（手机号、用户名或邮箱已存在）
- `500` - 服务器内部错误

---

### 3. 管理员登录

**请求地址**
```
POST /b/api/v1/admin/users/auth/login
```

**请求参数**
```json
{
    "phone": "13800138000",
    "verification_code": "123456"
}
```

**参数说明**
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| phone | string | 是 | 手机号 |
| verification_code | string | 是 | 6位数字验证码 |

**返回示例**
```json
{
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "token_type": "bearer",
    "expires_in": 1800,
    "user": {
        "id": 1,
        "phone": "13800138000",
        "username": "admin",
        "email": "<EMAIL>",
        "user_type": 2,
        "risk_level": 3,
        "knowledge_level": 5,
        "is_active": true,
        "is_verified": true,
        "is_admin": true,
        "first_login_at": "2024-01-01T10:00:00Z",
        "last_login_at": "2024-01-01T10:00:00Z",
        "created_at": "2024-01-01T10:00:00Z",
        "updated_at": "2024-01-01T10:00:00Z",
        "roles": [
            {
                "id": 1,
                "name": "超级管理员",
                "description": "系统超级管理员"
            }
        ],
        "permissions": [
            {
                "id": 1,
                "code": "user.list.read",
                "name": "查看用户列表",
                "module": "user",
                "resource": "list",
                "action": "read"
            }
        ]
    }
}
```

**字段说明**
| 字段 | 类型 | 说明 |
|------|------|------|
| access_token | string | 访问令牌 |
| token_type | string | 令牌类型，固定为"bearer" |
| expires_in | integer | 访问令牌过期时间（秒） |
| user | object | 用户信息 |

**错误码**
- `400` - 参数格式错误
- `401` - 验证码错误或已过期
- `403` - 用户不是管理员或账户已禁用
- `404` - 用户不存在
- `500` - 登录失败

---

### 4. 删除用户

**请求地址**
```
DELETE /b/api/v1/admin/users/{user_id}
```

**请求头**
```
Authorization: Bearer <access_token>
```

**参数说明**
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| user_id | integer | 是 | 用户ID（路径参数） |

**返回**
- 状态码：`204` - 删除成功，无响应内容

**错误码**
- `400` - 请求参数错误（例如尝试删除管理员账户或自己的账户）
- `403` - 权限不足，需要`user.delete`权限
- `404` - 用户不存在
- `500` - 服务器内部错误

---

## 错误处理

### 通用错误格式

```json
{
    "detail": "错误描述信息"
}
```

### 常见错误码

| 状态码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 201 | 创建成功 |
| 204 | 删除成功 |
| 400 | 请求参数错误 |
| 401 | 未授权或令牌无效 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 409 | 资源冲突 |
| 429 | 请求频率过高 |
| 500 | 服务器内部错误 |

---

## 使用示例

### JavaScript 示例

```javascript
// 发送验证码
const sendCode = async (phone) => {
    const response = await fetch('/api/v1/users/send-sms-code', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ phone })
    });
    return await response.json();
};

// 登录
const login = async (phone, verificationCode) => {
    const response = await fetch('/api/v1/users/phone-login', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            phone,
            verification_code: verificationCode
        })
    });
    const data = await response.json();
    
    // 保存令牌
    localStorage.setItem('access_token', data.access_token);
    localStorage.setItem('refresh_token', data.refresh_token);
    
    return data;
};

// B端管理员发送验证码
const sendAdminCode = async (phone) => {
    const response = await fetch('/b/api/v1/admin/users/auth/send-code', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
            phone,
            purpose: 'login'
        })
    });
    return await response.json();
};

// B端管理员登录
const adminLogin = async (phone, verificationCode) => {
    const response = await fetch('/b/api/v1/admin/users/auth/login', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            phone,
            verification_code: verificationCode
        })
    });
    const data = await response.json();
    
    // 保存令牌和过期时间
    if (data.access_token) {
        localStorage.setItem('admin_token', data.access_token);
        localStorage.setItem('admin_user', JSON.stringify(data.user));
        
        // 存储token过期时间（当前时间 + expires_in秒）
        if (data.expires_in) {
            const expiresAt = Date.now() + data.expires_in * 1000;
            localStorage.setItem('admin_token_expires_at', expiresAt.toString());
        }
    }
    
    return data;
};

// 获取用户信息
const getUserInfo = async () => {
    const token = localStorage.getItem('access_token');
    const response = await fetch('/api/v1/users/me', {
        headers: {
            'Authorization': `Bearer ${token}`
        }
    });
    return await response.json();
};

// 更新用户资料
const updateProfile = async (updateData) => {
    const token = localStorage.getItem('access_token');
    const response = await fetch('/api/v1/users/profile', {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(updateData)
    });
    return await response.json();
};
```

### Python 示例

```python
import requests

class UserAPIClient:
    def __init__(self, base_url):
        self.base_url = base_url
        self.access_token = None
        
    def send_verification_code(self, phone):
        """发送验证码"""
        response = requests.post(
            f"{self.base_url}/api/v1/users/send-sms-code",
            json={"phone": phone}
        )
        return response.json()
        
    def login(self, phone, verification_code):
        """手机号登录"""
        response = requests.post(
            f"{self.base_url}/api/v1/users/phone-login",
            json={
                "phone": phone,
                "verification_code": verification_code
            }
        )
        data = response.json()
        self.access_token = data.get("access_token")
        return data
        
    def get_user_info(self):
        """获取用户信息"""
        headers = {"Authorization": f"Bearer {self.access_token}"}
        response = requests.get(
            f"{self.base_url}/api/v1/users/me",
            headers=headers
        )
        return response.json()
        
    def update_profile(self, **kwargs):
        """更新用户资料"""
        headers = {
            "Authorization": f"Bearer {self.access_token}",
            "Content-Type": "application/json"
        }
        response = requests.put(
            f"{self.base_url}/api/v1/users/profile",
            json=kwargs,
            headers=headers
        )
        return response.json()

# 使用示例
client = UserAPIClient("https://api.example.com")

# 发送验证码
client.send_verification_code("13800138000")

# 登录
user_data = client.login("13800138000", "123456")
print(f"登录成功，用户ID: {user_data['user']['id']}")

# 获取用户信息
user_info = client.get_user_info()
print(f"用户名: {user_info['username']}")

# 更新资料
updated_user = client.update_profile(
    username="新用户名",
    knowledge_level=3
)
print(f"资料更新成功")
```

---

## 注意事项

1. **令牌管理**：访问令牌有效期较短，建议实现自动刷新机制
2. **错误处理**：客户端应妥善处理各种错误状态，特别是401错误需要重新登录
3. **频率限制**：发送验证码接口有频率限制，避免过于频繁的调用
4. **数据验证**：所有用户输入都会进行服务端验证，客户端也应进行前置验证提升用户体验
5. **隐私保护**：用户敏感信息已进行脱敏处理，客户端不应尝试获取完整手机号等敏感数据
6. **会话安全**：建议定期检查和清理不活跃的会话，提升账户安全性

---

## 版本说明

- **当前版本**：v1.0
- **最后更新**：2024-01-01
- **兼容性**：向后兼容
- **下个版本计划**：增加用户偏好设置、社交功能等

</rewritten_file> 