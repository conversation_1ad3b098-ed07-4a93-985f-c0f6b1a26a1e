# API接口分离指南

## 概述

本文档描述了FinSight系统中B端（管理端）和C端（用户端）API接口的分离设计和实现。

## 架构设计

### 1. 应用分离架构

系统采用多应用架构，将API分为三个部分：

- **主应用 (app)**: 传统单体API，保持向后兼容性
- **C端应用 (app_c)**: 面向用户的查询和基础操作API
- **B端应用 (app_b)**: 面向管理员的完整管理API

### 2. 路由分离

#### 2.1 标签分类服务分离

**C端路由 (`router.py`)**:
- 只包含查询接口（GET）
- 用户偏好查询（仅限本人）
- 标签推荐功能
- 无需管理员权限

**B端路由 (`admin_router.py`)**:
- 包含所有CRUD操作（POST、PUT、DELETE）
- 完整的管理功能
- 需要相应权限验证
- 数据统计和分析接口

### 3. 访问路径

#### 3.1 Swagger文档地址

- **C端文档**: `/c/docs`
- **B端文档**: `/b/docs`  
- **主应用文档**: `/docs`

#### 3.2 API前缀

```
C端API前缀: /c/api/v1/
B端API前缀: /b/api/v1/admin/
主应用API前缀: /api/v1/
```

## 权限设计

### 1. C端权限

C端接口主要是查询操作，权限控制相对宽松：

```python
# 无需认证的接口
- GET /tags/types
- GET /tags/categories 
- GET /tags
- GET /classifications/dimensions

# 需要用户认证的接口
- GET /my/classification-preferences  # 只能查看自己的偏好
- GET /recommendations/tags           # 个性化推荐
```

### 2. B端权限

B端接口需要管理员权限和细粒度权限控制：

```python
# 标签类型管理
- POST /tags/types          # require_permission("tag.type.create")
- PUT /tags/types/{id}      # require_permission("tag.type.update") 
- DELETE /tags/types/{id}   # require_permission("tag.type.delete")

# 标签管理
- POST /tags               # require_permission("tag.create")
- PUT /tags/{id}           # require_permission("tag.update")
- DELETE /tags/{id}        # require_permission("tag.delete")

# 分类管理
- POST /classifications/dimensions    # require_permission("classification.dimension.create")
- PUT /classifications/dimensions/{id} # require_permission("classification.dimension.update")
- DELETE /classifications/dimensions/{id} # require_permission("classification.dimension.delete")

# 数据分析
- GET /analytics/tags      # require_permission("tag.analytics.read")
- GET /analytics/users/{id}/tags # require_permission("user.tag.analytics.read")
```

## 接口功能对比

### 1. 标签类型接口

| 功能 | C端 | B端 | 权限要求 |
|------|-----|-----|----------|
| 获取类型列表 | ✅ | ✅ | 无 |
| 获取类型详情 | ✅ | ✅ | 无 |
| 创建类型 | ❌ | ✅ | tag.type.create |
| 更新类型 | ❌ | ✅ | tag.type.update |
| 删除类型 | ❌ | ✅ | tag.type.delete |

### 2. 标签接口

| 功能 | C端 | B端 | 权限要求 |
|------|-----|-----|----------|
| 获取标签列表 | ✅ | ✅ | 无 |
| 搜索标签 | ✅ | ✅ | 无 |
| 热门标签 | ✅ | ✅ | 无 |
| 标签详情 | ✅ | ✅ | 无 |
| 创建标签 | ❌ | ✅ | tag.create |
| 更新标签 | ❌ | ✅ | tag.update |
| 删除标签 | ❌ | ✅ | tag.delete |

### 3. 用户偏好接口

| 功能 | C端 | B端 | 权限要求 |
|------|-----|-----|----------|
| 查看我的偏好 | ✅ | ❌ | 用户认证 |
| 管理用户偏好 | ❌ | ✅ | user.preference.manage |
| 标签推荐 | ✅ | ❌ | 用户认证 |

## 技术实现

### 1. 路由文件结构

```
src/services/tag_classification_service/
├── router.py              # C端路由（查询接口）
├── admin_router.py        # B端路由（管理接口）
├── models.py              # 数据模型（共享）
├── schemas.py             # 数据传输对象（共享）
├── service.py             # 业务逻辑（共享）
└── dependencies.py        # 依赖注入（共享）
```

### 2. 主应用配置

```python
# src/main.py

# 创建三个FastAPI实例
app = FastAPI(...)      # 主应用
app_c = FastAPI(...)    # C端应用  
app_b = FastAPI(...)    # B端应用

# 注册路由
app_c.include_router(tag_classification_router, prefix="/api/v1/tags")
app_b.include_router(tag_classification_admin_router, prefix="/api/v1/admin/tags")

# 挂载子应用
app.mount("/c", app_c)
app.mount("/b", app_b)
```

### 3. 权限验证

使用权限服务提供的装饰器进行权限验证：

```python
from ..permission_service.dependencies import require_permission

@router.post("/tags")
async def create_tag(
    current_user: Annotated[User, Depends(require_permission("tag.create"))],
    # ... 其他参数
):
    """创建标签 - 需要tag.create权限"""
    pass
```

## 使用指南

### 1. 开发环境访问

启动应用后，可以通过以下地址访问：

- **主应用**: http://localhost:8000/
- **C端API**: http://localhost:8000/c/
- **B端API**: http://localhost:8000/b/

### 2. Swagger文档

- **C端文档**: http://localhost:8000/c/docs
- **B端文档**: http://localhost:8000/b/docs
- **主应用文档**: http://localhost:8000/docs

### 3. API调用示例

#### C端调用示例
```bash
# 获取标签列表
curl "http://localhost:8000/c/api/v1/tags"

# 搜索标签
curl "http://localhost:8000/c/api/v1/tags/search?q=金融"

# 获取我的偏好（需要认证）
curl -H "Authorization: Bearer <token>" \
     "http://localhost:8000/c/api/v1/my/classification-preferences"
```

#### B端调用示例
```bash
# 创建标签（需要权限）
curl -X POST \
     -H "Authorization: Bearer <admin_token>" \
     -H "Content-Type: application/json" \
     -d '{"name":"新标签","description":"标签描述"}' \
     "http://localhost:8000/b/api/v1/admin/tags"

# 获取标签统计
curl -H "Authorization: Bearer <admin_token>" \
     "http://localhost:8000/b/api/v1/admin/analytics/tags"
```

## 注意事项

### 1. 向后兼容性

主应用仍然保留所有原有路由，确保现有客户端不受影响。

### 2. 权限迁移

需要确保权限系统中已配置相应的权限编码：
- `tag.create`, `tag.update`, `tag.delete`
- `tag.type.create`, `tag.type.update`, `tag.type.delete` 
- `classification.dimension.create`, `classification.dimension.update`, `classification.dimension.delete`
- `user.preference.manage`
- `tag.analytics.read`

### 3. 测试覆盖

需要为B端和C端接口分别编写测试用例，确保功能正确性和权限控制有效性。

## 扩展指南

其他服务模块可以参考标签分类服务的分离模式：

1. 创建 `admin_router.py` 文件
2. 将管理功能移至B端路由
3. C端路由只保留查询和用户相关操作
4. 添加适当的权限验证
5. 在主应用中注册路由

这种分离模式提供了清晰的职责分离，便于维护和扩展。 