# FinSight Backend 运行指南

## 系统架构说明

FinSight Backend 采用微服务架构，主要包含以下组件：

- **主应用服务**：纯API服务，提供REST接口，处理用户请求
- **Celery工作进程**：处理所有异步任务（数据采集、数据处理）
- **Celery Beat调度器**：管理所有定时任务
- **Redis**：作为消息队列和缓存
- **PostgreSQL**：主数据库
- **MongoDB**：存储原始采集数据

**架构特点**：
- 主应用专注于API服务，不包含任何后台任务处理
- 所有数据采集和处理任务都通过Celery异步执行
- 完全的服务分离，便于独立扩展和部署

## 环境准备

### 1. 虚拟环境设置

首先确保您已经创建并激活了虚拟环境：

```bash
# 创建虚拟环境（如果还没有）
python -m venv venv/finsight

# 激活虚拟环境（必须在运行前执行）
source venv/finsight/bin/activate

# 安装依赖
pip install -r requirements.txt
```

### 2. 环境变量配置

项目支持多种环境配置，通过不同的`.env`文件管理：

#### 开发环境（Development）
```bash
# 复制示例配置文件
cp env_example.txt .env.develop

# 编辑开发环境配置
vim .env.develop
```

#### 测试环境（Testing）
```bash
# 创建测试环境配置
cp env_example.txt .env.test

# 编辑测试环境配置
vim .env.test
```

#### 生产环境（Production）
```bash
# 创建生产环境配置
cp env_example.txt .env.production

# 编辑生产环境配置
vim .env.production
```

## 启动方式

### 1. 完整系统启动（推荐）

#### 方式一：使用启动脚本
```bash
# 启动开发环境
./start_dev.sh
```

#### 方式二：手动启动完整系统
```bash
# 激活虚拟环境
source venv/finsight/bin/activate

# 设置环境变量
export ENVIRONMENT=development

# 1. 启动主应用服务
python -m src.main &

# 2. 启动Celery工作进程（数据采集和数据处理）
celery -A src.core.celery_app worker --loglevel=info --queues=data_collection,data_processing,celery --concurrency=4 &

# 3. 启动Celery Beat调度器（定时任务）
celery -A src.core.celery_app beat --loglevel=info &

# 等待所有服务启动
wait
```

### 2. 分离式启动（开发调试）

#### 启动主应用服务
```bash
# 激活虚拟环境
source venv/finsight/bin/activate

# 设置环境变量
export ENVIRONMENT=development

# 启动API服务
python -m src.main
```

#### 启动Celery工作进程
```bash
# 新开终端，激活虚拟环境
source venv/finsight/bin/activate

# 启动数据采集工作进程
celery -A src.core.celery_app worker --loglevel=info --queues=data_collection --concurrency=2 --hostname=worker-collection@%h

# 或启动数据处理工作进程
celery -A src.core.celery_app worker --loglevel=info --queues=data_processing --concurrency=2 --hostname=worker-processing@%h

# 或启动通用工作进程（处理所有队列）
celery -A src.core.celery_app worker --loglevel=info --queues=data_collection,data_processing,celery --concurrency=4
```

#### 启动Celery Beat调度器
```bash
# 新开终端，激活虚拟环境
source venv/finsight/bin/activate

# 启动定时任务调度器
celery -A src.core.celery_app beat --loglevel=info
```

### 3. 测试环境启动

```bash
# 激活虚拟环境
source venv/finsight/bin/activate

# 设置环境变量
export ENVIRONMENT=testing

# 启动主应用
python -m src.main &

# 启动Celery工作进程（测试环境使用同步执行）
celery -A src.core.celery_app worker --loglevel=info --queues=data_collection,data_processing,celery --concurrency=2 &

wait
```

### 4. 生产环境启动

```bash
# 激活虚拟环境
source venv/finsight/bin/activate

# 设置环境变量
export ENVIRONMENT=production

# 启动主应用（多进程）
gunicorn src.main:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8000 &

# 启动Celery工作进程（多实例）
celery -A src.core.celery_app worker --loglevel=info --queues=data_collection --concurrency=4 --hostname=worker-collection-1@%h &
celery -A src.core.celery_app worker --loglevel=info --queues=data_processing --concurrency=4 --hostname=worker-processing-1@%h &

# 启动Celery Beat调度器
celery -A src.core.celery_app beat --loglevel=info &

wait
```

### 5. 使用Uvicorn直接启动（仅API服务）

```bash
# 开发环境（带热重载）
uvicorn src.main:app --host 0.0.0.0 --port 8000 --reload

# 生产环境
uvicorn src.main:app --host 0.0.0.0 --port 8000 --workers 4
```

**注意**：使用Uvicorn直接启动只会启动API服务，不包含数据采集和处理功能。需要单独启动Celery工作进程。

### 6. 使用Docker启动

```bash
# 构建镜像
docker build -t finsight-backend .

# 运行容器
docker run -p 8000:8000 finsight-backend

# 或使用docker-compose
docker-compose up -d
```

## Celery任务管理

### 1. 任务队列说明

系统使用以下队列：

- **data_collection**：数据采集任务
  - 定时采集任务
  - 事件驱动采集任务
  - 手动触发采集任务
  - 数据源健康检查

- **data_processing**：数据处理任务
  - 原始数据处理
  - 内容分析和标签提取
  - 数据质量检查

- **celery**：通用任务队列
  - 系统维护任务
  - 清理任务

### 2. 监控Celery任务

#### 查看工作进程状态
```bash
# 查看活跃工作进程
celery -A src.core.celery_app inspect active

# 查看已注册任务
celery -A src.core.celery_app inspect registered

# 查看工作进程统计
celery -A src.core.celery_app inspect stats
```

#### 查看任务队列状态
```bash
# 查看队列长度
celery -A src.core.celery_app inspect active_queues

# 查看预定任务
celery -A src.core.celery_app inspect scheduled
```

#### 使用Flower监控（可选）
```bash
# 安装Flower
pip install flower

# 启动Flower监控界面
celery -A src.core.celery_app flower --port=5555

# 访问 http://localhost:5555 查看监控界面
```

### 3. 手动执行任务

#### 数据采集任务
```bash
# 执行单个数据源采集
python -c "
from src.services.data_collection_service.tasks import execute_crawl_task
result = execute_crawl_task.delay(data_source_id=1)
print(f'Task ID: {result.id}')
"

# 执行批量采集
python -c "
from src.services.data_collection_service.tasks import execute_batch_crawl
result = execute_batch_crawl.delay([1, 2, 3])
print(f'Task ID: {result.id}')
"
```

#### 数据处理任务
```bash
# 处理待处理数据
python -c "
from src.services.data_processing_service.tasks import process_pending_data_batch
result = process_pending_data_batch.delay()
print(f'Task ID: {result.id}')
"
```

### 4. 任务调度配置

系统自动执行以下定时任务：

- **数据采集调度**：每5分钟检查需要采集的数据源
- **数据处理**：每60秒处理待处理数据
- **健康检查**：每30分钟检查数据源健康状态
- **清理任务**：每2小时清理旧任务记录

## 环境变量说明

### 基础配置
- `ENVIRONMENT`: 环境类型（development/testing/production）
- `DEBUG`: 调试模式（true/false）
- `HOST`: 服务监听地址（默认：0.0.0.0）
- `PORT`: 服务端口（默认：8000）

### 数据库配置
- `DATABASE_URL`: 数据库连接URL
- `DATABASE_ENGINE`: 数据库引擎类型

### 安全配置
- `SECRET_KEY`: JWT密钥
- `ACCESS_TOKEN_EXPIRE_MINUTES`: 访问令牌过期时间

### 短信服务配置
- `SMS_PROVIDER`: 短信服务提供商
- `SMS_API_KEY`: 短信服务API密钥
- `SMS_SECRET_KEY`: 短信服务密钥

### CORS配置
- `CORS_ORIGINS`: 允许的跨域源

### Redis配置
- `REDIS_URL`: Redis连接URL（用于Celery消息队列）

### MongoDB配置
- `MONGODB_URL`: MongoDB连接URL（存储原始采集数据）
- `MONGODB_DATABASE`: MongoDB数据库名称

## 验证启动状态

### 1. 健康检查
访问：http://localhost:8000/health

期望响应：
```json
{
    "status": "ok",
    "app_name": "FinSight Backend",
    "version": "1.0.0",
    "environment": "development"
}
```

### 2. API文档
访问：http://localhost:8000/docs

### 3. 演示测试
```bash
python demo_test.py
```

## 常见问题排查

### 1. 虚拟环境问题
```bash
# 检查虚拟环境是否激活
which python

# 重新激活虚拟环境
source venv/finsight/bin/activate
```

### 2. 依赖问题
```bash
# 重新安装依赖
pip install -r requirements.txt

# 检查依赖版本
pip list
```

### 3. 端口占用
```bash
# 检查端口占用
lsof -i :8000

# 杀死占用进程
kill -9 <PID>
```

### 4. 数据库连接问题
```bash
# 检查数据库配置
cat .env.develop | grep DATABASE

# 测试数据库连接
python -c "from src.core.database import engine; print(engine.url)"
```

### 5. 权限问题
```bash
# 给启动脚本执行权限
chmod +x start_dev.sh
chmod +x start_dev_with_tests.sh
```

### 6. Celery相关问题

#### Celery工作进程无法启动
```bash
# 检查Redis连接
redis-cli ping

# 检查Celery配置
python -c "from src.core.celery_app import celery_app; print(celery_app.conf)"

# 清空Redis队列
redis-cli flushall
```

#### 任务执行失败
```bash
# 查看任务错误日志
celery -A src.core.celery_app events

# 查看失败任务
celery -A src.core.celery_app inspect failed

# 重启工作进程
pkill -f "celery worker"
celery -A src.core.celery_app worker --loglevel=info --queues=data_collection,data_processing,celery --concurrency=4
```

### 7. MongoDB连接问题
```bash
# 检查MongoDB连接
python -c "from src.core.mongodb import mongodb_manager; print(mongodb_manager.get_database())"

# 测试MongoDB写入
python -c "
from src.core.mongodb import mongodb_manager
db = mongodb_manager.get_database()
collection = db.test
result = collection.insert_one({'test': 'data'})
print(f'Inserted: {result.inserted_id}')
collection.delete_one({'_id': result.inserted_id})
print('Test completed')
"
```

## 日志和调试

### 1. 开启调试模式
在`.env`文件中设置：
```
DEBUG=true
LOG_LEVEL=debug
```

### 2. 查看日志
应用日志会输出到控制台，可以通过以下方式查看：
```bash
# 启动应用并保存日志
python -m src.main 2>&1 | tee app.log
```

### 3. 调试工具
```bash
# 使用调试工具
python debug_api.py
```

## 性能优化

### 1. 生产环境优化
```bash
# 使用多进程
uvicorn src.main:app --host 0.0.0.0 --port 8000 --workers 4

# 使用gunicorn
gunicorn src.main:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8000
```

### 2. 数据库连接池
在配置文件中优化数据库连接设置：
```
DATABASE_POOL_SIZE=20
DATABASE_MAX_OVERFLOW=30
``` 