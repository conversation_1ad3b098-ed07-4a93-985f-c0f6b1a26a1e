# SMS服务集成文档

## 概述

本文档描述了FinSight后端项目中的短信服务模块，该模块提供统一的短信发送和管理功能，支持基于业务名称的配置管理、发送频率限制和腾讯云短信集成。

## 功能特性

- ✅ 基于业务名称的短信配置管理
- ✅ 短信签名和模板管理
- ✅ 发送频率限制（每日/每分钟）
- ✅ 腾讯云短信服务集成
- ✅ 验证码短信发送
- ✅ 通知短信发送
- ✅ 营销短信发送
- ✅ 手机号格式验证
- ✅ 发送状态查询
- ✅ 自动故障转移（配置错误时使用模拟模式）
- ✅ 完整的错误处理和日志记录

## 数据库设计

### 短信签名表 (sms_signature)

```sql
CREATE TABLE `sms_signature` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '签名ID',
  `signature_name` varchar(100) NOT NULL COMMENT '签名名称',
  `sign_id` varchar(50) NOT NULL COMMENT '腾讯云签名ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_signature_name` (`signature_name`),
  UNIQUE KEY `uk_tencent_sign_id` (`sign_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='短信签名表（仅存储审核通过记录）';
```

### 短信模板表 (sms_template)

```sql
CREATE TABLE `sms_template` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '模板ID',
  `template_name` varchar(100) NOT NULL COMMENT '模板名称',
  `template_content` text NOT NULL COMMENT '模板内容',
  `template_params` json NOT NULL COMMENT '模板参数定义(JSON数组)',
  `template_type` tinyint(4) NOT NULL COMMENT '模板类型(1-验证码，2-通知，3-营销)',
  `template_id` varchar(50) NOT NULL COMMENT '腾讯云模板ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_template_name` (`template_name`),
  UNIQUE KEY `uk_tencent_template_id` (`template_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='短信模板表（仅存储审核通过记录）';
```

### 短信业务表 (sms_business)

```sql
CREATE TABLE `sms_business` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '业务ID',
  `signature_id` bigint(20) NOT NULL COMMENT '签名ID',
  `template_id` bigint(20) NOT NULL COMMENT '模板ID',
  `business_name` varchar(100) NOT NULL COMMENT '业务名称',
  `sms_type` tinyint(4) NOT NULL COMMENT '短信类型(1-验证码，2-通知，3-营销)',
  `daily_limit` int(11) DEFAULT 10 COMMENT '单用户每日发送限制',
  `minute_limit` int(11) DEFAULT 3 COMMENT '单用户每分钟发送限制',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_business_name` (`business_name`),
  KEY `idx_signature_id` (`signature_id`),
  KEY `idx_template_id` (`template_id`),
  CONSTRAINT `fk_sms_business_signature` FOREIGN KEY (`signature_id`) REFERENCES `sms_signature` (`id`),
  CONSTRAINT `fk_sms_business_template` FOREIGN KEY (`template_id`) REFERENCES `sms_template` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='短信业务表';
```

### 短信发送频率限制表 (sms_frequency_limit)

```sql
CREATE TABLE `sms_frequency_limit` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `business_id` bigint(20) NOT NULL COMMENT '业务ID',
  `phone_number` varchar(20) NOT NULL COMMENT '手机号',
  `daily_count` int(11) NOT NULL DEFAULT 0 COMMENT '当日发送次数',
  `minute_count` int(11) NOT NULL DEFAULT 0 COMMENT '当前分钟发送次数',
  `last_send_time` datetime DEFAULT NULL COMMENT '最后发送时间',
  `date_key` varchar(10) NOT NULL COMMENT '日期键(YYYYMMDD)',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_business_phone_date` (`business_id`,`phone_number`,`date_key`),
  KEY `idx_last_send_time` (`last_send_time`),
  CONSTRAINT `fk_sms_frequency_limit_business` FOREIGN KEY (`business_id`) REFERENCES `sms_business` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='短信发送频率限制表';
```

## 配置要求

### 环境变量

需要在环境变量文件（`.env.develop` 或 `.env.production`）中配置以下参数：

```bash
# 腾讯云短信服务基础配置
TENCENT_CLOUD_SECRET_ID=your-secret-id-here
TENCENT_CLOUD_SECRET_KEY=your-secret-key-here
TENCENT_CLOUD_SMS_SDK_APP_ID=your-sdk-app-id-here
TENCENT_CLOUD_SMS_REGION=ap-guangzhou

# 短信业务配置（基于业务名称）
TENCENT_CLOUD_SMS_LOGIN=登录验证
TENCENT_CLOUD_SMS_REGISTER=注册验证
TENCENT_CLOUD_SMS_RESET_PASSWORD=密码重置
```

### 配置说明

| 参数 | 说明 | 必需 |
|------|------|------|
| `TENCENT_CLOUD_SECRET_ID` | 腾讯云API密钥ID | 是 |
| `TENCENT_CLOUD_SECRET_KEY` | 腾讯云API密钥 | 是 |
| `TENCENT_CLOUD_SMS_SDK_APP_ID` | 短信应用的SDK AppID | 是 |
| `TENCENT_CLOUD_SMS_REGION` | 服务地域 | 可选，默认ap-guangzhou |
| `TENCENT_CLOUD_SMS_LOGIN` | 登录验证业务名称 | 可选，默认"登录验证" |
| `TENCENT_CLOUD_SMS_REGISTER` | 注册验证业务名称 | 可选，默认"注册验证" |
| `TENCENT_CLOUD_SMS_RESET_PASSWORD` | 密码重置业务名称 | 可选，默认"密码重置" |

## 代码集成

### 核心文件

1. **`src/services/sms_service/models.py`** - 数据库模型定义
2. **`src/services/sms_service/schemas.py`** - 数据传输对象定义
3. **`src/services/sms_service/tencent_client.py`** - 腾讯云短信客户端
4. **`src/services/sms_service/service.py`** - SMS服务核心业务逻辑
5. **`src/services/sms_service/migrations.py`** - 数据库迁移脚本
6. **`tests/test_sms_service.py`** - 单元测试

### 初始化数据库

首次使用前需要初始化SMS服务的数据库表和基础数据：

```python
from src.services.sms_service.migrations import run_migration

# 运行迁移，创建表结构和初始化数据
run_migration()
```

### 使用示例

#### 1. 发送验证码短信

```python
from src.services.sms_service import SmsService
from src.services.sms_service.schemas import SmsSendRequest

# 初始化服务（需要数据库会话）
sms_service = SmsService(db_session)

# 方式1：使用便捷方法发送验证码
result = sms_service.send_verification_code(
    phone="***********",
    code="123456",
    business_name="登录验证"  # 可选，默认使用配置中的TENCENT_CLOUD_SMS_LOGIN
)

# 方式2：使用通用发送方法
request = SmsSendRequest(
    business_name="登录验证",
    phone_numbers=["***********"],
    template_params=["123456", "5"]  # [验证码, 有效期（分钟）]
)
result = sms_service.send_sms(request)

if result.success:
    print("验证码发送成功")
else:
    print(f"发送失败: {result.message}")
```

#### 2. 发送通知短信

```python
request = SmsSendRequest(
    business_name="系统通知",
    phone_numbers=["***********", "***********"],
    template_params=["用户名", "操作内容", "2024-01-01 12:00:00"]
)

result = sms_service.send_sms(request)
```

#### 3. 查询业务配置

```python
# 获取业务配置
config = sms_service.get_business_config("登录验证")
if config:
    print(f"签名: {config.signature_name}")
    print(f"模板ID: {config.template_id}")
    print(f"每日限制: {config.daily_limit}")
    print(f"每分钟限制: {config.minute_limit}")

# 列出所有业务
businesses = sms_service.list_businesses()
for business in businesses:
    print(f"业务: {business.business_name}, 类型: {business.sms_type}")
```

#### 4. 获取服务状态

```python
status = sms_service.get_service_status()
print(f"服务状态: {'启用' if status.enabled else '禁用'}")
print(f"提供商: {status.provider}")
print(f"已加载业务数: {status.configuration_status['business_configs_loaded']}")
```

## 频率限制机制

SMS服务实现了严格的频率限制机制：

### 限制类型

1. **每日限制**：每个手机号在同一业务下每天的最大发送次数
2. **每分钟限制**：每个手机号在同一业务下每分钟的最大发送次数

### 限制检查流程

1. 根据业务名称获取配置的限制参数
2. 查询或创建该手机号的频率限制记录
3. 检查是否超过每日或每分钟限制
4. 发送成功后更新限制计数器

### 示例配置

```python
# 在数据库中配置业务限制
business = SmsBusiness(
    business_name="登录验证",
    daily_limit=10,    # 每天最多10条
    minute_limit=3,    # 每分钟最多3条
    # ... 其他配置
)
```

## 故障处理

### 自动故障转移

当腾讯云短信配置不完整或服务不可用时，系统会自动切换到模拟模式：

- 记录警告日志
- 返回模拟的成功响应
- 频率限制仍然生效

### 错误处理

1. **配置错误** - 自动切换模拟模式
2. **网络错误** - 抛出具体异常信息
3. **API限流** - 返回腾讯云错误码和消息
4. **参数错误** - 返回参数验证错误
5. **频率限制** - 返回相应的限制提示

### 日志记录

系统提供详细的日志记录：

```python
# 成功日志
logging.info("✅ 短信验证码发送成功: %s -> %s", phone, code)

# 错误日志  
logging.error("❌ 短信验证码发送失败: %s -> %s", phone, error_message)

# 警告日志
logging.warning("腾讯云短信配置不完整，将使用模拟模式")
```

## API接口

### SmsService 类

#### 主要方法

| 方法 | 描述 | 参数 | 返回值 |
|------|------|------|--------|
| `send_sms()` | 发送短信 | request: SmsSendRequest | SmsSendResponse |
| `send_verification_code()` | 发送验证码短信 | phone, code, business_name | SmsSendResponse |
| `get_business_config()` | 获取业务配置 | business_name | SmsBusinessConfig |
| `list_businesses()` | 列出所有业务 | 无 | List[SmsBusinessResponse] |
| `get_service_status()` | 获取服务状态 | 无 | SmsServiceStatus |
| `reload_business_configs()` | 重新加载业务配置 | 无 | bool |

#### 返回值格式

成功响应：
```python
SmsSendResponse(
    success=True,
    message="短信发送成功",
    request_id="xxx-xxx-xxx",
    send_status_set=[
        SmsSendStatusInfo(
            serial_no="xxx",
            phone_number="+86***********",
            fee=1,
            session_context="",
            code="Ok",
            message="send success",
            iso_code="CN"
        )
    ]
)
```

失败响应：
```python
SmsSendResponse(
    success=False,
    message="错误描述",
    error_code="InvalidParameter",
    error_message="详细错误信息"
)
```

## 业务配置管理

### 添加新业务

1. **创建签名**（如果需要新签名）
```python
signature = SmsSignature(
    signature_name="新签名",
    sign_id="tencent-sign-id"
)
db.add(signature)
db.commit()
```

2. **创建模板**
```python
template = SmsTemplate(
    template_name="新业务模板",
    template_content="您的{1}验证码是：{2}，有效期{3}分钟。",
    template_params=["业务名称", "验证码", "有效期"],
    template_type=SmsBusinessType.VERIFICATION,
    template_id="tencent-template-id"
)
db.add(template)
db.commit()
```

3. **创建业务**
```python
business = SmsBusiness(
    signature_id=signature.id,
    template_id=template.id,
    business_name="新业务名称",
    sms_type=SmsBusinessType.VERIFICATION,
    daily_limit=5,
    minute_limit=2
)
db.add(business)
db.commit()

# 重新加载配置
sms_service.reload_business_configs()
```

### 更新业务配置

```python
business = db.query(SmsBusiness).filter(
    SmsBusiness.business_name == "登录验证"
).first()

if business:
    business.daily_limit = 15  # 更新每日限制
    business.minute_limit = 5  # 更新每分钟限制
    db.commit()
    
    # 重新加载配置
    sms_service.reload_business_configs()
```

## 测试

### 运行单元测试

```bash
# 运行SMS服务测试
python -m pytest tests/test_sms_service.py -v

# 运行特定测试
python -m pytest tests/test_sms_service.py::TestSmsService::test_send_sms_success -v
```

### 测试覆盖范围

- 腾讯云客户端功能测试
- SMS服务业务逻辑测试
- 频率限制测试
- 错误处理测试
- 配置管理测试

## 迁移说明

### 从旧版本迁移

如果您的项目之前使用了基于 `TENCENT_CLOUD_SMS_SIGN_NAME` 和 `TENCENT_CLOUD_SMS_TEMPLATE_ID` 的配置，需要进行以下迁移：

1. **运行数据库迁移**
```bash
python -c "from src.services.sms_service.migrations import run_migration; run_migration()"
```

2. **更新环境变量**
```bash
# 移除旧配置
# TENCENT_CLOUD_SMS_SIGN_NAME=旧签名
# TENCENT_CLOUD_SMS_TEMPLATE_ID=旧模板ID

# 添加新配置
TENCENT_CLOUD_SMS_LOGIN=登录验证
TENCENT_CLOUD_SMS_REGISTER=注册验证
TENCENT_CLOUD_SMS_RESET_PASSWORD=密码重置
```

3. **更新代码引用**
```python
# 旧代码
from src.services.user_service.tencent_sms import TencentSmsService
sms_service = TencentSmsService()

# 新代码
from src.services.sms_service import SmsService
sms_service = SmsService(db_session)
```

## 最佳实践

1. **业务配置管理**
   - 为不同的短信用途创建独立的业务配置
   - 根据业务重要性设置合适的频率限制
   - 定期检查和更新模板内容

2. **错误处理**
   - 始终检查发送结果的 `success` 状态
   - 记录详细的错误日志用于故障排查
   - 实现重试机制处理临时网络问题

3. **性能优化**
   - 利用业务配置缓存减少数据库查询
   - 批量发送短信时控制并发数量
   - 定期清理过期的频率限制记录

4. **安全考虑**
   - 严格控制短信发送频率防止滥用
   - 验证手机号格式防止无效发送
   - 监控异常发送行为

## 故障排查

### 常见问题

1. **SMS服务未初始化**
   - 确保在创建UserService时传入了数据库会话
   - 检查数据库连接是否正常

2. **业务配置不存在**
   - 运行数据库迁移初始化基础数据
   - 检查业务名称是否正确

3. **频率限制触发**
   - 检查业务配置的限制参数
   - 清理测试期间的频率限制记录

4. **腾讯云配置错误**
   - 验证环境变量配置
   - 检查腾讯云控制台中的签名和模板状态 