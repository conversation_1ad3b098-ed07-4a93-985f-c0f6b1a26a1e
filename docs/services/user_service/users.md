# 用户管理模块数据库设计

## 1. 用户基础信息表 (users)

```sql
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL COMMENT '用户名',
    mobile VARCHAR(20) UNIQUE COMMENT '手机号',
    email VARCHAR(100) UNIQUE COMMENT '邮箱',
    password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希',
    nickname VARCHAR(50) COMMENT '昵称',
    avatar_url VARCHAR(500) COMMENT '头像URL',
    gender TINYINT DEFAULT 0 COMMENT '性别：0-未知，1-男，2-女',
    birth_date DATE COMMENT '出生日期',
    register_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '注册时间',
    last_login_time TIMESTAMP COMMENT '最后登录时间',
    status TINYINT DEFAULT 1 COMMENT '状态：0-禁用，1-正常',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_mobile (mobile),
    INDEX idx_email (email),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户基础信息表';
```

## 2. 用户分层信息表 (user_segments)

```sql
CREATE TABLE user_segments (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    segment_type ENUM('beginner', 'advanced', 'anxious') NOT NULL COMMENT '用户分层类型：小白型、进阶型、焦虑型',
    risk_level TINYINT DEFAULT 1 COMMENT '风险等级：1-保守，2-稳健，3-积极，4-激进',
    knowledge_level TINYINT DEFAULT 1 COMMENT '知识水平：1-初级，2-中级，3-高级',
    investment_experience TINYINT DEFAULT 0 COMMENT '投资经验年限',
    event_sensitivity DECIMAL(3,2) DEFAULT 0.50 COMMENT '事件敏感度：0-1之间',
    segment_score DECIMAL(5,2) COMMENT '分层得分',
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY uk_user_segment (user_id),
    INDEX idx_segment_type (segment_type),
    INDEX idx_risk_level (risk_level)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户分层信息表';
```

## 3. 用户标签表 (user_tags)

```sql
CREATE TABLE user_tags (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    tag_type ENUM('core', 'derived') NOT NULL COMMENT '标签类型：核心标签、衍生标签',
    tag_category VARCHAR(50) NOT NULL COMMENT '标签分类：风险等级、知识水平、认知偏差等',
    tag_key VARCHAR(100) NOT NULL COMMENT '标签键',
    tag_value VARCHAR(500) COMMENT '标签值',
    confidence_score DECIMAL(3,2) DEFAULT 0.00 COMMENT '置信度：0-1之间',
    is_system_generated BOOLEAN DEFAULT TRUE COMMENT '是否系统生成',
    update_frequency ENUM('T+0', 'daily', 'weekly', 'monthly') DEFAULT 'daily' COMMENT '更新频率',
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY uk_user_tag (user_id, tag_category, tag_key),
    INDEX idx_tag_type (tag_type),
    INDEX idx_tag_category (tag_category)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户标签表';
```

## 4. 用户偏好设置表 (user_preferences)

```sql
CREATE TABLE user_preferences (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    investment_fields JSON COMMENT '投资领域偏好：["stock", "fund", "forex"]',
    market_focus JSON COMMENT '关注市场：["A_stock", "HK_stock", "US_stock"]',
    interested_industries JSON COMMENT '感兴趣行业：["technology", "consumer", "finance"]',
    push_channels JSON COMMENT '推送渠道偏好：["app", "sms", "email", "wechat"]',
    push_time_slots JSON COMMENT '推送时间段：[{"start": "08:00", "end": "09:00"}]',
    push_frequency ENUM('high', 'medium', 'low') DEFAULT 'medium' COMMENT '推送频率偏好',
    language_preference VARCHAR(10) DEFAULT 'zh-CN' COMMENT '语言偏好',
    notification_settings JSON COMMENT '通知设置',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY uk_user_preference (user_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户偏好设置表';
```

## 5. 用户行为日志表 (user_behavior_logs)

```sql
CREATE TABLE user_behavior_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    behavior_type ENUM('login', 'logout', 'browse', 'search', 'click', 'share', 'feedback') NOT NULL COMMENT '行为类型',
    target_type VARCHAR(50) COMMENT '目标类型：article, analysis, notification',
    target_id BIGINT COMMENT '目标ID',
    behavior_data JSON COMMENT '行为数据',
    session_id VARCHAR(100) COMMENT '会话ID',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_behavior (user_id, behavior_type),
    INDEX idx_target (target_type, target_id),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户行为日志表';
```

## 6. 用户反馈表 (user_feedback)

```sql
CREATE TABLE user_feedback (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    feedback_type ENUM('useful', 'useless', 'interested', 'not_interested', 'suggestion', 'complaint') NOT NULL COMMENT '反馈类型',
    target_type VARCHAR(50) COMMENT '反馈目标类型',
    target_id BIGINT COMMENT '反馈目标ID',
    rating TINYINT COMMENT '评分：1-5',
    content TEXT COMMENT '反馈内容',
    status ENUM('pending', 'processing', 'resolved', 'closed') DEFAULT 'pending' COMMENT '处理状态',
    response TEXT COMMENT '回复内容',
    response_time TIMESTAMP COMMENT '回复时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_feedback (user_id, feedback_type),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户反馈表';
```

## 7. 用户认证令牌表 (user_tokens)

```sql
CREATE TABLE user_tokens (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    token_type ENUM('access', 'refresh', 'reset_password') NOT NULL COMMENT '令牌类型',
    token_hash VARCHAR(255) NOT NULL COMMENT '令牌哈希',
    expires_at TIMESTAMP NOT NULL COMMENT '过期时间',
    is_revoked BOOLEAN DEFAULT FALSE COMMENT '是否已撤销',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_token_hash (token_hash),
    INDEX idx_user_token (user_id, token_type),
    INDEX idx_expires_at (expires_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户认证令牌表';
```

## 索引策略

1. **主键索引**：所有表都使用自增主键
2. **唯一索引**：用户名、手机号、邮箱等唯一字段
3. **外键索引**：关联用户ID的字段
4. **查询索引**：基于查询频率和条件创建复合索引
5. **时间索引**：按时间范围查询的字段

## 分库分表策略

### 分表策略
- **用户行为日志表**: 按月分表，保留12个月数据
- **用户反馈表**: 按年分表

### 分表规则
```sql
-- 用户行为日志按月分表示例
CREATE TABLE user_behavior_logs_202501 LIKE user_behavior_logs;
CREATE TABLE user_behavior_logs_202502 LIKE user_behavior_logs;
```

## 数据安全

1. **敏感数据加密**：密码、手机号使用AES-256加密
2. **行级访问控制**：用户只能访问自己的数据
3. **审计日志**：记录所有数据变更操作
4. **数据备份**：每日全量备份，实时增量备份