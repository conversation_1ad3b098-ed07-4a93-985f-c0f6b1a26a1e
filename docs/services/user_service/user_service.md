# 用户服务文档

## 1. 服务概述

用户服务是FinSight系统的核心服务之一，负责用户管理、认证授权、用户画像构建等功能。

### 1.1 主要职责
- 用户注册、登录、认证
- 手机号+验证码登录（首次登录即注册）
- 用户画像管理（分层、标签、偏好）
- 用户行为追踪
- 权限管理
- 会话管理

### 1.2 技术栈
- **框架**: FastAPI + SQLAlchemy + PostgreSQL
- **缓存**: Redis (会话缓存)
- **认证**: JWT认证
- **数据库**: PostgreSQL
- **短信服务**: 模拟短信发送（可扩展真实SMS服务）

### 1.3 架构设计
- **分层架构**: Router层 → Service层 → Model层
- **依赖注入**: FastAPI的Depends机制实现组件解耦
- **会话管理**: 数据库持久化 + Redis缓存双重保障
- **安全机制**: JWT + 会话追踪 + 行为审计

## 2. 数据模型

### 2.1 用户基础信息表 (users)
```sql
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE,
    email VARCHAR(100) UNIQUE,
    phone VARCHAR(20) UNIQUE NOT NULL,
    password_hash VARCHAR(255),
    user_type SMALLINT NOT NULL DEFAULT 1, -- 1:小白型 2:进阶型 3:焦虑型
    risk_level SMALLINT NOT NULL DEFAULT 3, -- 1-5风险等级
    knowledge_level SMALLINT NOT NULL DEFAULT 1, -- 1-5知识水平
    is_active BOOLEAN DEFAULT TRUE,
    is_verified BOOLEAN DEFAULT FALSE,
    first_login_at TIMESTAMP,
    last_login_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 2.2 用户会话表 (user_sessions)
```sql
CREATE TABLE user_sessions (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id),
    session_token VARCHAR(255) UNIQUE NOT NULL, -- 当前有效的访问令牌
    refresh_token VARCHAR(255) UNIQUE NOT NULL, -- 刷新令牌
    ip_address VARCHAR(45),
    user_agent TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    expires_at TIMESTAMP NOT NULL,
    last_used_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 最后使用时间
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 2.3 短信验证码表 (sms_verification_codes)
```sql
CREATE TABLE sms_verification_codes (
    id SERIAL PRIMARY KEY,
    phone VARCHAR(20) NOT NULL,
    code VARCHAR(10) NOT NULL,
    purpose VARCHAR(20) NOT NULL, -- login/register/reset_password
    is_used BOOLEAN DEFAULT FALSE,
    attempts INTEGER DEFAULT 0,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 2.4 用户标签表 (user_tags)
```sql
CREATE TABLE user_tags (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id),
    tag_category VARCHAR(50) NOT NULL, -- core/derived
    tag_name VARCHAR(100) NOT NULL,
    tag_value TEXT,
    weight DECIMAL(3,2) DEFAULT 1.0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 3. 底层协议逻辑

### 3.1 JWT认证机制

#### 3.1.1 Token结构
```yaml
AccessToken:
  header:
    alg: HS256
    typ: JWT
  payload:
    sub: "用户ID"
    phone: "用户手机号"
    user_type: "用户类型"
    type: "access"
    exp: "过期时间戳"
    iat: "签发时间戳"

RefreshToken:
  header:
    alg: HS256
    typ: JWT
  payload:
    sub: "用户ID"
    phone: "用户手机号"
    user_type: "用户类型"
    type: "refresh"
    exp: "过期时间戳"
    iat: "签发时间戳"
```

#### 3.1.2 认证流程
```mermaid
sequenceDiagram
    participant C as Client
    participant A as API Gateway
    participant U as User Service
    participant R as Redis
    participant D as Database

    C->>A: 请求 + Authorization Header
    A->>U: 转发请求
    U->>U: 解析JWT Token
    U->>D: 查询用户信息
    D-->>U: 返回用户数据
    U->>R: 检查会话缓存
    R-->>U: 返回会话状态
    U-->>A: 认证结果
    A-->>C: 响应
```

### 3.2 会话管理机制

#### 3.2.1 会话生命周期
```yaml
会话创建:
  - 用户登录成功时创建
  - 同时写入数据库和Redis
  - 数据库存储完整会话信息
  - Redis存储热点数据(30分钟TTL)

会话刷新:
  - 使用refresh_token刷新access_token
  - 更新数据库中的session_token
  - 更新Redis缓存
  - 记录last_used_at时间

会话失效:
  - 主动注销
  - Token过期
  - 并发会话超限
  - 异常行为检测
```

#### 3.2.2 双重存储策略
```yaml
数据库存储:
  - 完整的会话信息
  - 持久化保存
  - 支持复杂查询
  - 审计追踪

Redis缓存:
  - 热点数据快速访问
  - 自动过期清理
  - 高并发支持
  - 临时状态存储
```

### 3.3 短信验证码机制

#### 3.3.1 验证码生成与存储
```yaml
生成规则:
  - 6位随机数字
  - 5分钟有效期
  - 每个手机号同时只能有一个有效验证码

存储策略:
  - Redis主要存储: sms:code:{phone}:{purpose}
  - 数据库备份存储: sms_verification_codes表
  - 防重放机制: 使用后立即标记为已使用

验证逻辑:
  - 检查验证码是否存在且有效
  - 检查尝试次数(最多3次)
  - 验证成功后清除验证码
```

## 4. API 接口

### 4.1 认证相关接口

#### 4.1.1 发送短信验证码
- **POST** `/api/v1/users/send-sms-code`
- **描述**: 发送短信验证码
- **认证**: 无需认证
- **请求体**: SendSmsCodeRequest
- **响应**: SendSmsCodeResponse

**请求示例**:
```json
{
    "phone": "13800138000",
    "purpose": "login"
}
```

**响应示例**:
```json
{
    "message": "Verification code sent successfully",
    "expires_in": 300
}
```

**底层逻辑**:
1. 验证手机号格式
2. 检查发送频率限制
3. 生成6位随机验证码
4. 存储到Redis和数据库
5. 模拟发送短信(实际环境中调用短信服务)

#### 4.1.2 手机号+验证码登录
- **POST** `/api/v1/users/phone-login`
- **描述**: 手机号+验证码登录（首次登录即注册）
- **认证**: 无需认证
- **请求体**: PhoneLoginRequest
- **响应**: TokenResponse

**请求示例**:
```json
{
    "phone": "13800138000",
    "verification_code": "123456"
}
```

**响应示例**:
```json
{
    "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "token_type": "bearer",
    "expires_in": 1800,
    "user": {
        "id": 1,
        "phone": "13800138000",
        "username": null,
        "email": null,
        "user_type": 1,
        "risk_level": 3,
        "knowledge_level": 1,
        "is_active": true,
        "is_verified": true,
        "first_login_at": "2024-01-01T10:00:00Z",
        "last_login_at": "2024-01-01T10:00:00Z",
        "created_at": "2024-01-01T10:00:00Z",
        "updated_at": "2024-01-01T10:00:00Z"
    }
}
```

**底层逻辑**:
1. 验证验证码有效性
2. 检查用户是否存在，不存在则自动注册
3. 生成JWT访问令牌和刷新令牌
4. 创建用户会话记录
5. 存储会话到数据库和Redis
6. 记录用户行为日志
7. 返回令牌和用户信息

#### 4.1.3 刷新访问令牌 ⭐ **已修复**
- **POST** `/api/v1/users/refresh-token`
- **描述**: 使用刷新令牌获取新的访问令牌
- **认证**: 需要有效的refresh_token
- **请求体**: RefreshTokenRequest
- **响应**: TokenResponse

**请求示例**:
```json
{
    "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

**响应示例**:
```json
{
    "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "token_type": "bearer",
    "expires_in": 1800
}
```

**修复说明**:
- ✅ 现在会正确更新数据库中的user_sessions表
- ✅ 现在会正确更新Redis中的session缓存
- ✅ 会记录last_used_at时间戳
- ✅ 支持更新客户端IP和User-Agent信息

**底层逻辑**:
1. 验证refresh_token的有效性和签名
2. 从数据库查找对应的活跃会话
3. 验证会话状态(is_active=true, 未过期)
4. 生成新的access_token
5. **更新数据库中的session_token字段**
6. **更新Redis缓存中的会话数据**
7. **记录last_used_at时间戳**
8. 返回新的访问令牌

### 4.2 用户信息接口

#### 4.2.1 获取用户信息(需验证)
- **GET** `/api/v1/users/profile`
- **描述**: 获取当前用户信息（需要用户已验证）
- **认证**: 需要JWT Token，且用户必须已验证 ⭐ **认证机制确认**
- **请求头**: `Authorization: Bearer <access_token>`
- **响应**: UserResponse

**认证机制说明**:
- ✅ 必须在请求头中提供有效的JWT Token
- ✅ Token格式: `Authorization: Bearer <access_token>`
- ✅ 用户必须是活跃状态(is_active=true)
- ✅ 用户必须是已验证状态(is_verified=true)

**依赖链**:
```python
get_current_active_user -> get_current_user -> HTTPBearer认证
```

**底层逻辑**:
1. HTTPBearer从Authorization头提取Token
2. 验证JWT Token的有效性和签名
3. 从Token中提取用户ID
4. 查询数据库获取用户信息
5. 检查用户is_active状态
6. 检查用户is_verified状态
7. 返回用户信息

#### 4.2.2 获取用户信息(无需验证)
- **GET** `/api/v1/users/me`
- **描述**: 获取当前用户信息（无需验证状态）
- **认证**: 需要JWT Token
- **请求头**: `Authorization: Bearer <access_token>`
- **响应**: UserResponse

#### 4.2.3 更新用户信息
- **PUT** `/api/v1/users/profile`
- **描述**: 更新用户信息
- **认证**: 需要JWT Token，且用户必须已验证
- **请求头**: `Authorization: Bearer <access_token>`
- **请求体**: UserProfileUpdate
- **响应**: UserResponse

### 4.3 会话管理接口

#### 4.3.1 获取活跃会话
- **GET** `/api/v1/users/sessions`
- **描述**: 获取用户的所有活跃会话
- **认证**: 需要JWT Token
- **响应**: List[UserSessionResponse]

#### 4.3.2 注销会话
- **DELETE** `/api/v1/users/sessions/{session_id}`
- **描述**: 注销指定会话
- **认证**: 需要JWT Token
- **响应**: 204 No Content

#### 4.3.3 用户注销
- **POST** `/api/v1/users/logout`
- **描述**: 注销当前会话
- **认证**: 需要JWT Token
- **响应**: 204 No Content

## 5. 业务逻辑

### 5.1 手机号+验证码登录流程
```mermaid
sequenceDiagram
    participant U as User
    participant C as Client
    participant A as API
    participant S as SMS Service
    participant R as Redis
    participant D as Database

    U->>C: 输入手机号
    C->>A: POST /send-sms-code
    A->>S: 发送验证码
    A->>R: 存储验证码(5分钟)
    A->>D: 备份验证码记录
    A-->>C: 发送成功

    U->>C: 输入验证码
    C->>A: POST /phone-login
    A->>R: 验证验证码
    A->>D: 查找/创建用户
    A->>A: 生成JWT Tokens
    A->>D: 创建会话记录
    A->>R: 缓存会话(30分钟)
    A-->>C: 返回Token和用户信息
```

### 5.2 Token刷新流程 ⭐ **已修复**
```mermaid
sequenceDiagram
    participant C as Client
    participant A as API
    participant D as Database
    participant R as Redis

    C->>A: POST /refresh-token
    A->>A: 验证refresh_token
    A->>D: 查找对应会话
    A->>A: 生成新access_token
    A->>D: 更新session_token
    A->>D: 更新last_used_at
    A->>R: 更新会话缓存
    A-->>C: 返回新access_token
```

### 5.3 用户类型分类
根据以下维度对用户进行分类：
1. **投资经验** (investment_experience)
2. **决策时间** (daily_decision_time)
3. **风险承受能力** (risk_tolerance)
4. **知识水平** (knowledge_score)
5. **行为模式** (behavior_patterns)

### 5.4 分类规则
- **小白型 (Type 1)**: 投资经验 < 1年 且 知识评分 < 30
- **进阶型 (Type 2)**: 投资经验 >= 2年 且 知识评分 >= 60
- **焦虑型 (Type 3)**: 其他情况

## 6. 安全设计

### 6.1 密码安全
- 使用bcrypt进行密码哈希
- 强密码策略验证
- 密码重置机制

### 6.2 JWT 认证
- Access Token: 30分钟有效期
- Refresh Token: 7天有效期
- Token自动续期机制
- **会话状态同步**: 数据库和Redis双重保障

### 6.3 短信验证码安全
- 验证码6位数字随机生成
- 5分钟有效期
- 最多尝试3次验证
- 防止重复发送机制

### 6.4 会话管理安全
- Redis存储会话信息
- 数据库持久化会话记录
- 闲置超时自动登出
- 并发会话控制
- **会话一致性**: Token刷新时同步更新所有存储

## 7. 缓存策略

### 7.1 缓存键设计
```python
CACHE_KEYS = {
    'user_profile': 'user:profile:{user_id}',     # TTL: 1小时
    'user_tags': 'user:tags:{user_id}',           # TTL: 30分钟
    'user_session': 'session:{session_id}',      # TTL: 30分钟
    'sms_code': 'sms:code:{phone}:{purpose}',    # TTL: 5分钟
}
```

### 7.2 缓存更新策略
- 用户信息变更时主动失效缓存
- 用户标签变更时更新相关缓存
- **Token刷新时同步更新会话缓存** ⭐ **已修复**
- 定期清理过期会话
- 验证码使用后立即失效

## 8. 错误处理

### 8.1 HTTP状态码规范
```yaml
200: 请求成功
201: 资源创建成功
204: 操作成功，无返回内容
400: 请求参数错误
401: 认证失败或Token无效
403: 权限不足或资源被禁止访问
404: 资源不存在
409: 资源冲突
422: 请求参数验证失败
500: 服务器内部错误
```

### 8.2 错误响应格式
```json
{
    "detail": "错误描述信息",
    "error_code": "ERROR_CODE",
    "timestamp": "2024-01-01T10:00:00Z"
}
```

## 9. 监控指标

### 9.1 业务指标
- 用户注册数量
- 用户活跃度
- 登录成功率
- 短信验证码发送量
- 短信验证码验证成功率
- **Token刷新成功率** ⭐ **新增监控**
- **会话一致性监控** ⭐ **新增监控**
- 密码重置频率

### 9.2 技术指标
- API响应时间
- 数据库连接数
- 缓存命中率
- JWT Token验证成功率
- **Redis-数据库同步成功率** ⭐ **新增监控**
- 短信服务可用性

## 10. 部署说明

### 10.1 环境变量
```bash
# JWT配置
JWT_SECRET_KEY=your-secret-key
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30
JWT_REFRESH_TOKEN_EXPIRE_DAYS=7

# 数据库配置
DATABASE_URL=postgresql://user:pass@host:port/db

# Redis配置
REDIS_URL=redis://host:port/db

# 短信服务配置
SMS_PROVIDER=aliyun
SMS_ACCESS_KEY_ID=your-access-key
SMS_ACCESS_KEY_SECRET=your-secret
```

### 10.2 数据库迁移
```bash
# 启动应用时会自动创建数据库表
python -m src.main

# 或者使用 Alembic 进行数据库迁移
alembic revision --autogenerate -m "Create user tables"
alembic upgrade head
```

### 10.3 测试运行
```bash
# 安装依赖
pip install -r requirements.txt

# 激活虚拟环境
source venv/finsight/bin/activate

# 配置环境变量
cp env_example.txt .env.develop

# 运行测试
pytest tests/test_user_service.py -v

# 启动应用
python -m src.main
```

## 11. API 使用示例

### 11.1 完整登录流程示例

```bash
# 1. 发送验证码
curl -X POST "http://localhost:8000/api/v1/users/send-sms-code" \
     -H "Content-Type: application/json" \
     -d '{"phone": "13800138000", "purpose": "login"}'

# 2. 使用验证码登录
curl -X POST "http://localhost:8000/api/v1/users/phone-login" \
     -H "Content-Type: application/json" \
     -d '{"phone": "13800138000", "verification_code": "123456"}'

# 3. 使用令牌获取用户信息
curl -X GET "http://localhost:8000/api/v1/users/profile" \
     -H "Authorization: Bearer YOUR_ACCESS_TOKEN"

# 4. 刷新访问令牌 ⭐ 现在会正确更新会话状态
curl -X POST "http://localhost:8000/api/v1/users/refresh-token" \
     -H "Content-Type: application/json" \
     -H "User-Agent: MyApp/1.0" \
     -d '{"refresh_token": "YOUR_REFRESH_TOKEN"}'

# 5. 使用新令牌继续访问
curl -X GET "http://localhost:8000/api/v1/users/profile" \
     -H "Authorization: Bearer NEW_ACCESS_TOKEN"
```

### 11.2 错误处理示例

```json
{
    "detail": "Invalid phone number or verification code"
}
```


## 12. 扩展功能

### 12.1 真实短信服务集成
当前使用模拟短信服务，可扩展集成以下短信服务提供商：
- 阿里云短信服务
- 腾讯云短信服务
- 七牛云短信服务

### 12.2 用户标签系统
计划实现完整的用户标签管理功能：
- 核心标签和衍生标签
- 标签权重系统
- 标签推荐算法
- 标签分析报告

### 12.3 高级会话管理
- 设备指纹识别
- 异地登录检测
- 会话风险评估
- 智能会话管理 