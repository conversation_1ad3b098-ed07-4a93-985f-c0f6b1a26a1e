# 数据处理模块数据库设计文档目录

本目录包含了FinSight后端数据处理模块的完整数据库设计文档，所有涉及数据表的字段都包含了详细的注释说明。

## 📁 文档结构

### 1. 数据采集服务数据库设计
**文件**: `DATA_COLLECTION_DATABASE_DESIGN.md`

- **用途**: 定义数据采集服务的完整数据库架构
- **涵盖内容**:
  - 数据源配置表 (data_sources) - 管理各种数据源的配置信息
  - 数据源模板表 (source_templates) - 预设的数据源配置模板
  - 采集任务表 (crawl_tasks) - 采集任务的调度和状态管理
  - 原始数据记录表 (raw_data_records) - 采集到的原始数据记录
  - MongoDB和Redis的配合使用方案
  - 完整的索引优化策略

### 2. 数据源配置示例
**文件**: `DATA_SOURCE_CONFIG_EXAMPLES.md`

- **用途**: 提供各种数据源类型的详细配置示例
- **涵盖内容**:
  - 新闻网站数据源配置 (静态HTML和动态JavaScript)
  - 社交媒体数据源配置 (微博、Twitter等)
  - 研报平台数据源配置
  - API接口数据源配置
  - RSS源配置
  - 反爬虫和代理配置示例

### 3. 数据库性能基准测试
**文件**: `DATABASE_PERFORMANCE_BENCHMARK.md`

- **用途**: 定义数据库性能测试标准和优化方案
- **涵盖内容**:
  - PostgreSQL性能基准指标和测试脚本
  - MongoDB性能基准指标和测试脚本
  - Redis性能基准指标和测试脚本
  - Elasticsearch性能基准指标和测试脚本
  - 负载测试方案和性能优化建议
  - 监控指标和告警设置

### 4. 信息分类与标签系统设计
**文件**: `INFORMATION_CLASSIFICATION_TAGGING_SYSTEM.md`

- **用途**: 定义内容分类和标签系统的数据库架构
- **涵盖内容**:
  - 分类维度表 (classification_dimensions) - 定义分类的维度框架
  - 分类值表 (classification_values) - 具体的分类值定义
  - 内容分类关联表 (content_classifications) - 内容与分类的关联
  - 标签表 (tags) - 灵活的标签系统
  - 内容标签关联表 (content_tags) - 内容与标签的关联
  - 实体识别和用户画像匹配功能

## 🔧 使用说明

### 数据表字段注释规范
所有数据表字段都采用了详细的 `COMMENT` 注释，包含：
- **字段用途**: 说明字段的具体作用
- **数据类型说明**: 详细的数据类型和取值范围
- **业务逻辑**: 字段在业务流程中的意义
- **关联关系**: 外键关系和数据关联说明
- **约束条件**: 唯一性、检查约束等说明

### 索引设计原则
- **查询优化**: 基于实际查询场景设计的复合索引
- **性能平衡**: 在查询性能和写入性能之间找到平衡
- **分区策略**: 对大表采用分区策略提升性能
- **监控覆盖**: 所有关键索引都有对应的监控指标

### 数据完整性保证
- **外键约束**: 确保数据关联的完整性
- **检查约束**: 保证数据值的有效性
- **唯一约束**: 防止重复数据的产生
- **默认值**: 为关键字段提供合理的默认值

## 🚀 快速开始

1. **环境准备**: 确保已安装PostgreSQL 15+、MongoDB 7.0+、Redis 7.2+
2. **数据库创建**: 根据各文档中的SQL脚本创建数据库结构
3. **初始数据**: 使用文档中提供的初始数据脚本
4. **索引优化**: 执行所有推荐的索引创建语句
5. **性能测试**: 使用性能基准测试文档验证系统性能

## 📈 性能指标

### 目标性能基准
- **PostgreSQL**: 插入>=10K rows/sec, 查询<=100ms
- **MongoDB**: 写入>=15K docs/sec, 读取>=20K docs/sec  
- **Redis**: 读取>=100K ops/sec, 写入>=80K ops/sec
- **Elasticsearch**: 索引>=8K docs/sec, 搜索<=100ms

### 扩展性考量
- **水平扩展**: 支持数据库集群部署
- **垂直扩展**: 支持硬件资源的动态调整
- **负载均衡**: 读写分离和负载分布
- **数据分片**: 大表的自动分片策略

## 🔍 相关文档

- [项目架构文档](../../arc.md)
- [API接口文档](../../api/)
- [部署文档](../../deployment/)
- [运行指南](../../RUN_GUIDE.md)

## 📝 更新日志

- **2024-01**: 完成所有表字段的详细注释
- **2024-01**: 整理文档结构，统一放置在database目录
- **2024-01**: 添加性能基准测试文档
- **2024-01**: 完善标签系统设计

---

> 📌 **注意**: 所有数据库变更都应该通过迁移脚本进行，确保开发、测试、生产环境的一致性。 