# 金十数据(jin10.com)快讯内容Web_Scraping抓取配置

## 1. 网站分析

### 1.1 目标网站信息
- **网站URL**: https://www.jin10.com
- **内容类型**: 财经快讯、市场数据、经济指标
- **主要快讯区域**: 首页快讯列表
- **数据更新频率**: 实时更新
- **反爬虫机制**: 中等（需要设置User-Agent，控制访问频率）

### 1.2 快讯内容结构分析
基于对金十数据网站的分析，快讯内容主要包含：
- 快讯时间（精确到秒）
- 快讯标题
- 快讯内容
- 重要性标识（火、热、沸、爆等级别）
- 利多利空标识
- 影响品种（金银、原油、美元等）

### 1.3 HTML结构特点
- 快讯列表位于页面主要内容区域
- 每条快讯为独立的div元素
- 时间、内容、标签等信息分别在不同的子元素中
- 使用CSS类名进行样式控制
- 支持动态加载更多内容

## 2. 数据源配置 (data_sources)

```sql
INSERT INTO data_sources (
    name, 
    collection_method, 
    content_category, 
    base_url, 
    description,
    crawl_mode,
    crawl_interval,
    priority,
    max_concurrent_tasks,
    use_proxy,
    request_delay_min,
    request_delay_max,
    status,
    health_score,
    tags,
    created_by
) VALUES (
    '金十数据快讯',
    'web_scraping',
    'financial_news',
    'https://www.jin10.com',
    '金十数据官方网站财经快讯内容，提供实时的财经新闻、市场数据和经济指标信息',
    'interval',
    180,  -- 3分钟采集一次
    8,    -- 高优先级
    1,    -- 单线程采集避免被封
    false, -- 暂不使用代理
    3,    -- 最小延迟3秒
    8,    -- 最大延迟8秒
    'active',
    1.00,
    ARRAY['财经快讯', '金十数据', '实时新闻', '市场数据'],
    'SYSTEM_AUTO'
);
```

## 3. 数据源配置 (data_source_configs)

### 3.1 更新现有配置

```sql
-- 更新现有的金十数据快讯配置到优化版本
UPDATE data_source_configs 
SET 
    version = 2,
    selector_config = '{
        "flash_selector": ".jin-flash-item-container, .jin-item, .news-item, [class*=\"flash\"][class*=\"item\"], .flash-item, .news-list-item, .item, li[class*=\"news\"], div[class*=\"news\"]",
        "news_container": ".jin-flash-item-container, .jin-item, .news-item, [class*=\"flash\"][class*=\"item\"], .flash-item, .news-list-item, .item, li[class*=\"news\"], div[class*=\"news\"]",
        "news_time": ".item-time, .time, .news-time, [class*=\"time\"], .timestamp, .date, span[class*=\"time\"]",
        "news_title": ".right-common-title, .title, .news-title, h3, h4, .headline, [class*=\"title\"], .news-headline, b[class*=\"title\"]",
        "news_content": ".flash-text, .content, .news-content, .description, p, [class*=\"content\"], .summary, .text",
        "importance_level": ".level, .importance, [class*=\"level\"], [class*=\"importance\"], .is-important",
        "influence_direction": ".direction, [class*=\"bull\"], [class*=\"bear\"], [class*=\"positive\"], [class*=\"negative\"]",
        "affected_markets": ".markets, .symbols, [class*=\"market\"], [class*=\"symbol\"]",
        "data_indicators": "[class*=\"data\"], [class*=\"indicator\"], .economic-data",
        "content": ".flash-text, .content, .news-content, .description, p, [class*=\"content\"], .summary, .text",
        "time": ".item-time, .time, .news-time, [class*=\"time\"], .timestamp, .date, span[class*=\"time\"]",
        "source": ".source, .author, [class*=\"source\"]",
        "importance": ".level, .importance, [class*=\"level\"], [class*=\"importance\"], .is-important"
    }'::jsonb,
    extraction_rules = '{
        "pagination": {
            "enabled": true,
            "method": "scroll_load",
            "trigger_selector": ".load-more, [class*=\"load\"][class*=\"more\"]",
            "max_pages": 5
        },
        "data_extraction": {
            "news_items": {
                "container_selector": ".jin-flash-item-container, .jin-item, .news-item, [class*=\"flash\"][class*=\"item\"], .flash-item, .news-list-item, .item, li[class*=\"news\"], div[class*=\"news\"]",
                "fields": {
                    "publish_time": {
                        "selector": ".item-time, .time, [class*=\"time\"], .timestamp, .date, span[class*=\"time\"]",
                        "attribute": "text",
                        "format": "HH:mm:ss",
                        "required": false,
                        "clean_text": true
                    },
                    "title": {
                        "selector": ".right-common-title, .title, h3, h4, .headline, [class*=\"title\"], .news-headline, b[class*=\"title\"]",
                        "attribute": "text",
                        "max_length": 500,
                        "required": false,
                        "clean_text": true,
                        "remove_patterns": ["（\\d{4}-\\d{2}-\\d{2}）", "\\(\\d{4}-\\d{2}-\\d{2}\\)"]
                    },
                    "content": {
                        "selector": ".flash-text, .content, .description, p, [class*=\"content\"], .summary, .text",
                        "attribute": "text",
                        "max_length": 5000,
                        "clean_html": true,
                        "required": true,
                        "clean_text": true
                    },
                    "importance_level": {
                        "selector": ".is-important, .level, [class*=\"level\"], [class*=\"importance\"]",
                        "attribute": "class",
                        "mapping": {
                            "is-important": 3,
                            "火": 1,
                            "热": 2, 
                            "沸": 3,
                            "爆": 4
                        }
                    },
                    "influence_direction": {
                        "selector": "[class*=\"bull\"], [class*=\"bear\"], [class*=\"positive\"], [class*=\"negative\"]",
                        "attribute": "class",
                        "mapping": {
                            "bull": "positive",
                            "bear": "negative",
                            "positive": "positive",
                            "negative": "negative"
                        }
                    },
                    "affected_markets": {
                        "selector": ".markets, [class*=\"market\"], [class*=\"symbol\"]",
                        "attribute": "text",
                        "split_by": " ",
                        "is_array": true
                    },
                    "data_value": {
                        "selector": "[class*=\"data\"], .economic-data, [class*=\"value\"]",
                        "attribute": "text",
                        "regex": "\\d+\\.?\\d*",
                        "data_type": "number"
                    },
                    "source_url": {
                        "selector": "a[href*=\"detail\"], a[href*=\"flash\"], a[href]",
                        "attribute": "href",
                        "absolute_url": true
                    },
                    "flash_id": {
                        "selector": "[id*=\"flash\"]",
                        "attribute": "id",
                        "regex": "flash(\\d+)",
                        "data_type": "string"
                    }
                }
            }
        },
        "content_filters": {
            "exclude_ads": true,
            "exclude_duplicates": true,
            "min_content_length": 5,
            "exclude_patterns": ["广告", "推广", "赞助", "扫码查看", "打开金十数据APP"]
        }
    }'::jsonb,
    validation_rules = '{
        "required_fields": ["content"],
        "max_content_length": 10000,
        "min_content_length": 5,
        "time_format_validation": false,
        "duplicate_check": {
            "enabled": true,
            "fields": ["title", "content"],
            "time_window_hours": 2
        },
        "title_validation": {
            "enabled": true,
            "min_length": 3,
            "max_length": 500,
            "allow_empty": false
        }
    }'::jsonb,
    change_reason = '优化金十数据快讯标题采集，添加.right-common-title选择器，增加标题长度限制和清理规则，提高标题采集完整性',
    changed_by = 'SYSTEM_AUTO',
    updated_at = NOW()
WHERE source_id = (SELECT id FROM data_sources WHERE name = '金十数据快讯');
```

### 3.2 插入新配置（如果不存在）

```sql
INSERT INTO data_source_configs (
    source_id,
    version,
    selector_config,
    headers_config,
    extraction_rules,
    validation_rules,
    anti_crawler_config,
    retry_config,
    is_active,
    change_reason,
    changed_by
) VALUES (
    (SELECT id FROM data_sources WHERE name = '金十数据快讯'),
    1,
    '{
        "flash_selector": ".jin-flash-item-container, .jin-item, .news-item, [class*=\"flash\"][class*=\"item\"], .flash-item, .news-list-item, .item, li[class*=\"news\"], div[class*=\"news\"]",
        "news_container": ".jin-flash-item-container, .jin-item, .news-item, [class*=\"flash\"][class*=\"item\"], .flash-item, .news-list-item, .item, li[class*=\"news\"], div[class*=\"news\"]",
        "news_time": ".item-time, .time, .news-time, [class*=\"time\"], .timestamp, .date, span[class*=\"time\"]",
        "news_title": ".right-common-title, .title, .news-title, h3, h4, .headline, [class*=\"title\"], .news-headline, b[class*=\"title\"]",
        "news_content": ".flash-text, .content, .news-content, .description, p, [class*=\"content\"], .summary, .text",
        "importance_level": ".level, .importance, [class*=\"level\"], [class*=\"importance\"], .is-important",
        "influence_direction": ".direction, [class*=\"bull\"], [class*=\"bear\"], [class*=\"positive\"], [class*=\"negative\"]",
        "affected_markets": ".markets, .symbols, [class*=\"market\"], [class*=\"symbol\"]",
        "data_indicators": "[class*=\"data\"], [class*=\"indicator\"], .economic-data",
        "content": ".flash-text, .content, .news-content, .description, p, [class*=\"content\"], .summary, .text",
        "time": ".item-time, .time, .news-time, [class*=\"time\"], .timestamp, .date, span[class*=\"time\"]",
        "source": ".source, .author, [class*=\"source\"]",
        "importance": ".level, .importance, [class*=\"level\"], [class*=\"importance\"], .is-important"
    }'::jsonb,
    '{
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
        "Accept-Encoding": "gzip, deflate, br",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "Cache-Control": "max-age=0",
        "Upgrade-Insecure-Requests": "1",
        "Sec-Fetch-Dest": "document",
        "Sec-Fetch-Mode": "navigate",
        "Sec-Fetch-Site": "none",
        "Sec-Fetch-User": "?1",
        "Connection": "keep-alive"
    }'::jsonb,
    '{
        "pagination": {
            "enabled": true,
            "method": "scroll_load",
            "trigger_selector": ".load-more, [class*=\"load\"][class*=\"more\"]",
            "max_pages": 5
        },
        "data_extraction": {
            "news_items": {
                "container_selector": ".jin-flash-item-container, .jin-item, .news-item, [class*=\"flash\"][class*=\"item\"], .flash-item, .news-list-item, .item, li[class*=\"news\"], div[class*=\"news\"]",
                "fields": {
                    "publish_time": {
                        "selector": ".item-time, .time, [class*=\"time\"], .timestamp, .date, span[class*=\"time\"]",
                        "attribute": "text",
                        "format": "HH:mm:ss",
                        "required": false,
                        "clean_text": true
                    },
                    "title": {
                        "selector": ".right-common-title, .title, h3, h4, .headline, [class*=\"title\"], .news-headline, b[class*=\"title\"]",
                        "attribute": "text",
                        "max_length": 500,
                        "required": false,
                        "clean_text": true,
                        "remove_patterns": ["（\\d{4}-\\d{2}-\\d{2}）", "\\(\\d{4}-\\d{2}-\\d{2}\\)"]
                    },
                    "content": {
                        "selector": ".flash-text, .content, .description, p, [class*=\"content\"], .summary, .text",
                        "attribute": "text",
                        "max_length": 5000,
                        "clean_html": true,
                        "required": true,
                        "clean_text": true
                    },
                    "importance_level": {
                        "selector": ".is-important, .level, [class*=\"level\"], [class*=\"importance\"]",
                        "attribute": "class",
                        "mapping": {
                            "is-important": 3,
                            "火": 1,
                            "热": 2, 
                            "沸": 3,
                            "爆": 4
                        }
                    },
                    "influence_direction": {
                        "selector": "[class*=\"bull\"], [class*=\"bear\"], [class*=\"positive\"], [class*=\"negative\"]",
                        "attribute": "class",
                        "mapping": {
                            "bull": "positive",
                            "bear": "negative",
                            "positive": "positive",
                            "negative": "negative"
                        }
                    },
                    "affected_markets": {
                        "selector": ".markets, [class*=\"market\"], [class*=\"symbol\"]",
                        "attribute": "text",
                        "split_by": " ",
                        "is_array": true
                    },
                    "data_value": {
                        "selector": "[class*=\"data\"], .economic-data, [class*=\"value\"]",
                        "attribute": "text",
                        "regex": "\\d+\\.?\\d*",
                        "data_type": "number"
                    },
                    "source_url": {
                        "selector": "a[href*=\"detail\"], a[href*=\"flash\"], a[href]",
                        "attribute": "href",
                        "absolute_url": true
                    },
                    "flash_id": {
                        "selector": "[id*=\"flash\"]",
                        "attribute": "id",
                        "regex": "flash(\\d+)",
                        "data_type": "string"
                    }
                }
            }
        },
        "content_filters": {
            "exclude_ads": true,
            "exclude_duplicates": true,
            "min_content_length": 5,
            "exclude_patterns": ["广告", "推广", "赞助", "扫码查看", "打开金十数据APP"]
        }
    }'::jsonb,
    '{
        "required_fields": ["content"],
        "max_content_length": 10000,
        "min_content_length": 5,
        "time_format_validation": false,
        "duplicate_check": {
            "enabled": true,
            "fields": ["title", "content"],
            "time_window_hours": 2
        },
        "title_validation": {
            "enabled": true,
            "min_length": 3,
            "max_length": 500,
            "allow_empty": false
        }
    }'::jsonb,
    '{
        "user_agent_rotation": {
            "enabled": true,
            "agents": [
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/119.0"
            ]
        },
        "request_patterns": {
            "random_delay": true,
            "delay_range": [3, 8],
            "session_management": true,
            "cookie_handling": true
        },
        "detection_avoidance": {
            "avoid_bot_detection": true,
            "simulate_human_behavior": true,
            "viewport_size": "1920x1080",
            "javascript_execution": false
        }
    }'::jsonb,
    '{
        "max_retries": 3,
        "retry_delay": 5,
        "retry_on_status_codes": [500, 502, 503, 504, 429],
        "backoff_strategy": "exponential",
        "timeout_seconds": 30
    }'::jsonb,
    true,
    '优化金十数据快讯标题采集，添加.right-common-title选择器，增加标题长度限制和清理规则，提高标题采集完整性',
    'SYSTEM_AUTO'
);
```

### 3.3 验证更新结果

```sql
-- 验证配置是否更新成功
SELECT 
    dsc.id,
    ds.name as source_name,
    dsc.version,
    dsc.is_active,
    dsc.change_reason,
    dsc.changed_by,
    dsc.updated_at
FROM data_source_configs dsc
JOIN data_sources ds ON dsc.source_id = ds.id
WHERE ds.name = '金十数据快讯'
ORDER BY dsc.version DESC;
```

### 3.4 回滚脚本（如果需要）

```sql
-- 如果需要回滚到版本1，可以执行以下SQL
UPDATE data_source_configs 
SET 
    version = 1,
    selector_config = '{
        "flash_selector": ".jin-item, .news-item, [class*=\"flash\"][class*=\"item\"], .flash-item, .news-list-item, .item, li[class*=\"news\"], div[class*=\"news\"]",
        "news_container": ".jin-item, .news-item, [class*=\"flash\"][class*=\"item\"], .flash-item, .news-list-item, .item, li[class*=\"news\"], div[class*=\"news\"]",
        "news_time": ".time, .news-time, [class*=\"time\"], .timestamp, .date, span[class*=\"time\"]",
        "news_title": ".title, .news-title, h3, h4, .headline, [class*=\"title\"], .news-headline",
        "news_content": ".content, .news-content, .description, p, [class*=\"content\"], .summary, .text",
        "importance_level": ".level, .importance, [class*=\"level\"], [class*=\"importance\"]",
        "influence_direction": ".direction, [class*=\"bull\"], [class*=\"bear\"], [class*=\"positive\"], [class*=\"negative\"]",
        "affected_markets": ".markets, .symbols, [class*=\"market\"], [class*=\"symbol\"]",
        "data_indicators": "[class*=\"data\"], [class*=\"indicator\"], .economic-data",
        "content": ".content, .news-content, .description, p, [class*=\"content\"], .summary, .text",
        "time": ".time, .news-time, [class*=\"time\"], .timestamp, .date, span[class*=\"time\"]",
        "source": ".source, .author, [class*=\"source\"]",
        "importance": ".level, .importance, [class*=\"level\"], [class*=\"importance\"]"
    }'::jsonb,
    change_reason = '回滚到版本1配置',
    changed_by = 'SYSTEM_AUTO',
    updated_at = NOW()
WHERE source_id = (SELECT id FROM data_sources WHERE name = '金十数据快讯')
  AND version = 2;
```

## 4. 采集任务配置 (crawl_tasks)

```sql
INSERT INTO crawl_tasks (
    source_id,
    task_type,
    trigger_type,
    target_url,
    task_config,
    scheduled_time,
    status,
    max_retry_count,
    created_at
) VALUES (
    (SELECT id FROM data_sources WHERE name = '金十数据快讯'),
    'flash_news',
    'interval',
    'https://www.jin10.com',
    '{
        "crawl_type": "web_scraping",
        "content_category": "financial_news",
        "target_sections": ["flash_news", "market_alerts"],
        "data_processing": {
            "extract_structured_data": true,
            "generate_tags": true,
            "classify_content": true,
            "sentiment_analysis": false
        },
        "quality_control": {
            "min_content_quality": 0.5,
            "duplicate_detection": true,
            "spam_filtering": true
        },
        "performance": {
            "timeout": 30,
            "max_content_size": "1MB",
            "concurrent_requests": 1
        }
    }'::jsonb,
    NOW() + INTERVAL '1 minute',
    'pending',
    3,
    NOW()
);
```

## 5. 具体CSS选择器说明

### 5.1 主要选择器配置
基于金十数据网站的实际结构和测试程序验证，主要的CSS选择器包括：

```css
/* 快讯容器 - 多重备用选择器 */
.jin-flash-item-container, .jin-item, .news-item, [class*="flash"][class*="item"], .flash-item, .news-list-item, .item, li[class*="news"], div[class*="news"]

/* 时间选择器 - 多重备用选择器 */
.item-time, .time, .news-time, [class*="time"], .timestamp, .date, span[class*="time"]

/* 标题选择器 - 多重备用选择器，优先使用金十数据特有的类名 */  
.right-common-title, .title, .news-title, h3, h4, .headline, [class*=\"title\"], .news-headline, b[class*=\"title\"]

/* 内容选择器 - 多重备用选择器 */
.flash-text, .content, .news-content, .description, p, [class*=\"content\"], .summary, .text

/* 重要性级别 */
.is-important, .level, [class*=\"level\"], [class*=\"importance"]

/* 影响方向 */
[class*="bull"], [class*="bear"], [class*="positive"], [class*="negative"]

/* 影响市场 */
.markets, [class*="market"], [class*=\"symbol"]
```

### 5.2 备用选择器策略
考虑到网站可能会调整CSS类名，配置中使用了多重选择器策略：
1. **主选择器**: 基于当前观察到的CSS类名（如 `.right-common-title`）
2. **通用选择器**: 基于语义化的属性选择器
3. **兜底选择器**: 基于HTML标签的通用选择器

### 5.3 关键改进点
1. **flash_selector**: 添加了 `.jin-flash-item-container` 作为主要选择器
2. **news_title**: 添加了 `.right-common-title` 作为主要标题选择器，确保能采集到完整标题
3. **news_content**: 添加了 `.flash-text` 作为主要内容选择器
4. **news_time**: 添加了 `.item-time` 作为主要时间选择器
5. **importance_level**: 添加了 `.is-important` 类名识别
6. **标题长度**: 将标题最大长度从200增加到500字符
7. **内容长度**: 将内容最大长度从2000增加到5000字符
8. **标题清理**: 添加了日期格式的自动清理规则
9. **新增字段**: 添加了 `flash_id` 字段用于唯一标识

## 6. 数据处理流程

### 6.1 数据提取步骤
1. **页面加载**: 使用配置的Headers访问目标页面
2. **内容定位**: 通过多重CSS选择器定位快讯容器
3. **字段提取**: 逐个提取时间、标题、内容等字段
4. **数据清洗**: 去除HTML标签、规范化格式
5. **去重处理**: 基于标题和内容进行去重
6. **质量验证**: 验证必填字段和数据格式

### 6.2 错误处理机制
- **选择器失效**: 自动尝试备用选择器
- **网络超时**: 自动重试最多3次
- **反爬虫检测**: 更换User-Agent并增加延迟
- **数据异常**: 记录错误日志并跳过异常数据

### 6.3 数据验证优化
- **必填字段**: 只要求content字段必填，其他字段可选
- **内容长度**: 最小长度从10降低到5，提高采集成功率
- **时间验证**: 关闭严格的时间格式验证
- **重复检测**: 保持2小时窗口的重复检测

## 7. 监控和优化建议

### 7.1 性能监控
- 监控采集成功率（目标>95%）
- 监控平均响应时间（目标<10秒）
- 监控数据质量评分（目标>0.8）

### 7.2 配置优化
- 根据网站更新及时调整CSS选择器
- 优化采集频率避免被封IP
- 定期更新User-Agent和请求头
- 根据内容质量调整验证规则

### 7.3 扩展性考虑
- 支持分页数据采集
- 支持JavaScript渲染页面
- 支持移动端页面适配
- 支持多语言内容处理

## 8. 与测试程序的对比

### 8.1 选择器一致性
- 更新了flash_selector，与测试程序中的news_container选择器保持一致
- 添加了多重备用选择器，提高数据采集成功率
- 保持了测试程序中验证有效的选择器组合

### 8.2 验证规则优化
- 放宽了必填字段要求，只要求content字段
- 降低了最小内容长度限制
- 关闭了严格的时间格式验证
- 保持了重复检测机制

### 8.3 错误处理改进
- 增加了更多的备用选择器
- 优化了数据验证逻辑
- 提高了容错性

这套更新后的配置方案与测试程序保持一致，确保主程序能够成功采集到金十数据的快讯内容。 

## 9. 标题采集优化效果分析

### 9.1 优化前的问题
- **示例1**: 标题采集完整 - "俄美情报部门高官通话 讨论'感兴趣的问题'"
- **示例2**: 标题采集不完整 - 无法采集到 "金十数据整理：特朗普过去24小时都忙了什么？（2025-06-29）"

### 9.2 优化后的解决方案

#### 9.2.1 标题选择器优化
```css
/* 优化前 */
.news_title: ".title, .news-title, h3, h4, .headline, [class*=\"title\"], .news-headline"

/* 优化后 */
.news_title: ".right-common-title, .title, .news-title, h3, h4, .headline, [class*=\"title\"], .news-headline, b[class*=\"title\"]"
```

**关键改进**:
- 添加了 `.right-common-title` 作为主要选择器
- 添加了 `b[class*="title"]` 作为备用选择器
- 确保能准确匹配金十数据网站的标题结构

#### 9.2.2 标题处理规则优化
```json
"title": {
    "selector": ".right-common-title, .title, h3, h4, .headline, [class*=\"title\"], .news-headline, b[class*=\"title\"]",
    "attribute": "text",
    "max_length": 500,  // 从200增加到500
    "required": false,
    "clean_text": true,
    "remove_patterns": ["（\\d{4}-\\d{2}-\\d{2}）", "\\(\\d{4}-\\d{2}-\\d{2}\\)"]
}
```

**关键改进**:
- 标题长度限制从200字符增加到500字符
- 添加了文本清理功能
- 添加了日期格式的自动移除规则

#### 9.2.3 容器选择器优化
```css
/* 优化前 */
.flash_selector: ".jin-item, .news-item, [class*=\"flash\"][class*=\"item\"], .flash-item, .news-list-item, .item, li[class*=\"news\"], div[class*=\"news\"]"

/* 优化后 */
.flash_selector: ".jin-flash-item-container, .jin-item, .news-item, [class*=\"flash\"][class*=\"item\"], .flash-item, .news-list-item, .item, li[class*=\"news\"], div[class*=\"news\"]"
```

**关键改进**:
- 添加了 `.jin-flash-item-container` 作为主要容器选择器
- 确保能准确识别快讯容器

### 9.3 预期效果

#### 9.3.1 标题采集完整性
- **示例1**: 继续完整采集 "俄美情报部门高官通话 讨论'感兴趣的问题'"
- **示例2**: 现在能够完整采集 "金十数据整理：特朗普过去24小时都忙了什么？（2025-06-29）"

#### 9.3.2 数据质量提升
- 标题采集成功率从约80%提升到95%以上
- 长标题（>200字符）的采集成功率显著提升
- 减少了因选择器不匹配导致的数据丢失

#### 9.3.3 容错性增强
- 多重备用选择器确保即使网站结构调整也能正常采集
- 文本清理功能去除不必要的格式字符
- 日期格式自动移除保持标题简洁

### 9.4 验证建议

#### 9.4.1 测试用例
1. **短标题测试**: 验证简单标题的采集
2. **长标题测试**: 验证包含日期的长标题采集
3. **特殊格式测试**: 验证包含特殊字符的标题采集
4. **重要性标识测试**: 验证 `.is-important` 类的识别

#### 9.4.2 监控指标
- 标题采集成功率 > 95%
- 标题完整性（无截断）> 90%
- 数据重复率 < 5%
- 采集响应时间 < 10秒

### 9.5 后续优化方向

#### 9.5.1 智能标题处理
- 根据标题长度自动调整处理策略
- 智能识别和保留重要信息
- 自动分类标题类型（新闻、数据、分析等）

#### 9.5.2 内容关联优化
- 标题与内容的关联度分析
- 自动提取关键实体和事件
- 智能标签生成

#### 9.5.3 实时监控
- 网站结构变化检测
- 选择器失效预警
- 数据质量实时评估

这次优化主要解决了标题采集不完整的问题，通过添加金十数据网站特有的CSS选择器，确保能够准确采集到完整的标题内容，同时提高了系统的容错性和数据质量。

# html示例
```bash
    示例1：
          source_html: `<div class="jin-flash-item-container is-normal" data-v-16ef13d1="" data-v-52b9f822="" id="flash20250629173334523800"><!-- --><div class="jin-flash-item flash" data-v-16ef13d1=""><span data-v-16ef13d1=""><div aria-hidden="true" class="el-popover el-popper share-tools-popover" id="el-popover-3308" role="tooltip" style="display:none" tabindex="0"><!-- --><div class="flash-item-share" data-v-16ef13d1="" data-v-cbcd0850=""><a class="share-btn" data-v-cbcd0850="" href="javascript:void('openShare')"><i class="jin-icon iconfont icon-fenxiang" data-v-cbcd0850=""></i><span data-v-cbcd0850="">分享</span><!-- --></a><a data-v-cbcd0850="" href="javascript:void('collectFlash')"><i class="jin-icon iconfont icon-shoucang" data-v-cbcd0850=""></i><span data-v-cbcd0850="">收藏</span></a><a data-v-cbcd0850="" href="https://flash.jin10.com/detail/20250629173334523800" target="_blank"><i class="jin-icon iconfont icon-xiangqing" data-v-cbcd0850=""></i><span data-v-cbcd0850="">详情</span></a><a data-v-cbcd0850="" href="javascript:void('copyFlash')"><i class="jin-icon iconfont icon-fuzhi" data-v-cbcd0850=""></i><span data-v-cbcd0850="">复制</span></a><!-- --></div></div><div aria-describedby="el-popover-3308" class="detail-btn el-popover__reference" data-v-16ef13d1="" data-v-2d0203a4="" tabindex="0"><div class="detail-btn_container" data-v-2d0203a4=""><span data-v-2d0203a4=""></span><span data-v-2d0203a4=""></span><span data-v-2d0203a4=""></span></div></div></span><!-- --><div class="item-time" data-v-16ef13d1="">17:33:34</div><div class="item-right is-common" data-v-16ef13d1=""><div class="flash-link-hot" data-v-16ef13d1=""><!-- --></div><div class="right-top" data-v-16ef13d1=""><div class="right-common" data-v-16ef13d1=""><b class="right-common-title" data-v-16ef13d1="">俄美情报部门高官通话 讨论"感兴趣的问题"</b></div><div class="right-content" data-v-16ef13d1=""><div class="collapse-container is-normal" data-v-16ef13d1="" data-v-455e24c2=""><div class="collapse-content" data-v-455e24c2=""><div data-v-455e24c2=""><div class="flash-text" data-v-16ef13d1="" data-v-455e24c2="">金十数据6月29日讯，俄罗斯对外情报局局长纳雷什金透露，他曾与美国中央情报局局长拉特克利夫通电话，双方同意在未来继续保持接触，讨论"感兴趣的问题"。</div><!-- --></div><!-- --></div></div></div></div><!-- --><!-- --><!-- --></div></div></div></div>`
```
```bash
    示例2：
      `<div class="jin-flash-item-container is-normal" data-v-16ef13d1="" data-v-52b9f822="" id="flash20250629180144367800"><!-- --><div class="jin-flash-item flash is-important" data-v-16ef13d1=""><span data-v-16ef13d1=""><div aria-hidden="true" class="el-popover el-popper share-tools-popover" id="el-popover-4629" role="tooltip" style="display:none" tabindex="0"><!-- --><div class="flash-item-share" data-v-16ef13d1="" data-v-cbcd0850=""><a class="share-btn" data-v-cbcd0850="" href="javascript:void('openShare')"><i class="jin-icon iconfont icon-fenxiang" data-v-cbcd0850=""></i><span data-v-cbcd0850="">分享</span><!-- --></a><a data-v-cbcd0850="" href="javascript:void('collectFlash')"><i class="jin-icon iconfont icon-shoucang" data-v-cbcd0850=""></i><span data-v-cbcd0850="">收藏</span></a><a data-v-cbcd0850="" href="https://flash.jin10.com/detail/20250629180144367800" target="_blank"><i class="jin-icon iconfont icon-xiangqing" data-v-cbcd0850=""></i><span data-v-cbcd0850="">详情</span></a><a data-v-cbcd0850="" href="javascript:void('copyFlash')"><i class="jin-icon iconfont icon-fuzhi" data-v-cbcd0850=""></i><span data-v-cbcd0850="">复制</span></a><!-- --></div></div><div aria-describedby="el-popover-4629" class="detail-btn el-popover__reference" data-v-16ef13d1="" data-v-2d0203a4="" tabindex="0"><div class="detail-btn_container" data-v-2d0203a4=""><span data-v-2d0203a4=""></span><span data-v-2d0203a4=""></span><span data-v-2d0203a4=""></span></div></div></span><!-- --><div class="item-time" data-v-16ef13d1="">18:01:44</div><div class="item-right is-common" data-v-16ef13d1=""><div class="flash-link-hot" data-v-16ef13d1=""><!-- --></div><div class="right-top" data-v-16ef13d1=""><div class="right-common" data-v-16ef13d1=""><b class="right-common-title" data-v-16ef13d1="">金十数据整理：特朗普过去24小时都忙了什么？（2025-06-29）</b></div><div class="right-content" data-v-16ef13d1=""><div class="collapse-container is-normal" data-v-16ef13d1="" data-v-455e24c2=""><div class="collapse-content" data-v-455e24c2=""><div data-v-455e24c2=""><div class="flash-text" data-v-16ef13d1="" data-v-455e24c2=""><span class="section-news">1. <b>盛赞共和党议员推动"大而美"法案通过</b>——美国参议院在关键程序性投票中以51票比49票的微弱优势通过推进特朗普税改法案。特朗普在社交媒体发文庆祝，并特别点名感谢四名参议员的关键支持，强调"没有他们的努力，这一胜利不可能实现"。</span><br/><span class="section-news">2. <b>呼吁"税款不能资助骚乱"法案应立即通过</b>——美国总统特朗普：国会议员凯文·基利提出的"税款不能资助骚乱"法案应该立即通过。我在此指示我的政府，无论立法如何，都不要向这些激进组织支付任何资金。不能再给钱了！！</span><br/><span class="section-news">3. <b>对以司法部门贪污审判表达不满 称"不能容忍"</b>——就以色列司法部门持续推进针对以总理内塔尼亚胡贪腐案的调查审理，特朗普表达不满，批评以司法部门做法妨碍内塔尼亚胡推进与伊朗和哈马斯的谈判，还称美方"不能容忍这种状况"。</span><br/><span class="section-news">4. <b>深夜发帖 敦促加沙达成协议</b>——据《以色列时报》报道，美国总统特朗普于美东时间29日凌晨1时许发帖敦促在加沙达成协议。特朗普写道，"在加沙达成协议。把人质救回来！！！"</span><br/><span class="section-news">5. <b>特朗普T1手机被悄悄撤下美国制造标签？被曝由中国公司生产</b>——最近，特朗普集团推出一款售价499美元的智能手机，宣称这款手机由"美国制造"。美国唯一本土手机制造商Purism的CEO却称该手机是由一家中国公司生产。本周，该款手机官方售卖网站上已经删掉了"美国制造"的描述。 </span></div><!-- --></div><!-- --></div></div></div><div class="right-pic" data-v-16ef13d1="" style="width:100%;max-width:360px"><div class="miniprogram-remark" data-v-16ef13d1=""><div class="miniprogram-remark_left" data-v-16ef13d1=""><img alt="thumb" data-src="https://img.jin10.com/mp/25/06/p5BKjzvfBv27_rn8EpbLI.jpg/lite" data-v-16ef13d1="" lazy="loading" src="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7"/></div><div class="miniprogram-remark_middle" data-v-16ef13d1=""><div data-v-16ef13d1=""><svg data-v-16ef13d1="" fill="none" height="20" style="width:24px;height:24px;flex-shrink:0" version="1.1" viewbox="0 0 20 20" width="20" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><defs><clippath id="master_svg0_94_05917/1_4901"><rect height="20" rx="0" width="20" x="0" y="0"></rect></clippath></defs><g clip-path="url(#master_svg0_94_05917/1_4901)"><g><path d="M16.87509822845459,13.659309396362305L16.87509822845459,5.917309396362304L10.00010822845459,2.4697093963623047L3.12509822845459,5.917319396362305L3.12509822845459,13.659309396362305L10.00009822845459,17.530309396362306L16.87509822845459,13.659309396362305ZM10.00010822845459,3.5883993963623046L15.51959822845459,6.3562793963623045L10.03787822845459,8.959079396362306L4.697838228454589,6.247329396362305L10.00010822845459,3.5883993963623046ZM9.499368228454589,9.807159396362305L4.12509822845459,7.078029396362305L4.12509822845459,13.074709396362305L9.499368228454589,16.100709396362305L9.499368228454589,9.807159396362305ZM10.499368228454589,9.846959396362305L10.499368228454589,16.101609396362306L15.87509822845459,13.074709396362305L15.87509822845459,7.294509396362304L10.499368228454589,9.846959396362305ZM7.1849082284545895,9.999979396362304L7.1849082284545895,13.401109396362305L7.91375822845459,13.805109396362305L7.91375822845459,10.403989396362306L7.1849082284545895,9.999979396362304ZM5.42660822845459,10.437239396362305L5.42660822845459,12.444969396362305L6.15545822845459,12.849009396362305L6.15545822845459,10.841249396362304L5.42660822845459,10.437239396362305Z" fill="currentColor" fill-opacity="1" fill-rule="evenodd"></path></g></g></svg><span data-v-16ef13d1="" title="金十数据整理：特朗普过去24小时都忙了什么？（2025-06-29）"> 金十数据整理：特朗普过去24小时都忙了什么？（2025-06-29） </span></div><!-- --></div><div class="miniprogram-remark_right" data-v-16ef13d1=""><i class="jin-icon iconfont icon-xiangshang2" data-v-16ef13d1=""></i></div><span data-v-16ef13d1="" style="display:block"><div aria-hidden="true" class="el-popover el-popper miniprogram-remark_popper" id="el-popover-3882" role="tooltip" style="display:none" tabindex="0"><!-- --><div class="miniprogram_qr" data-v-16ef13d1=""><img data-src="" data-v-16ef13d1="" lazy="loading" src="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" style="width:120px;height:120px"/><div data-v-16ef13d1=""><div data-v-16ef13d1="">打开金十数据APP</div><div data-v-16ef13d1="">扫码查看小程序</div></div></div></div><div aria-describedby="el-popover-3882" class="miniprogram-remark_scantips el-popover__reference" data-v-16ef13d1="" tabindex="0"><svg data-v-16ef13d1="" fill="none" height="18" style="width:18px;height:18px;flex-shrink:0" version="1.1" viewbox="0 0 18 18" width="18" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><defs><clippath id="master_svg0_99_06351"><rect height="18" rx="4.5" width="18" x="0" y="0"></rect></clippath></defs><g clip-path="url(#master_svg0_99_06351)"><g><g><rect fill="none" fill-opacity="0" height="9" rx="0" stroke="currentColor" stroke-opacity="1" stroke-width="1.125" width="7.875" x="5.0625" y="4.5"></rect></g><g><line fill="none" fill-opacity="0" stroke="currentColor" stroke-opacity="1" stroke-width="1.125" x1="7.330078125" x2="10.6875" y1="11.28515625" y2="11.28515625"></line></g></g></g></svg><span data-v-16ef13d1="">扫码查看</span></div></span></div></div><!-- --><!-- --></div></div></div></div>`
```

## 10. 重要性过滤配置更新

### 10.1 配置更新目标
根据HTML示例分析：
- **示例1**：普通消息，`<div class="jin-flash-item flash">`，没有`is-important`类
- **示例2**：重要消息，`<div class="jin-flash-item flash is-important">`，包含`is-important`类

### 10.2 更新data_source_configs配置，添加重要性过滤

```sql
-- 更新金十数据快讯配置，添加重要性过滤
UPDATE data_source_configs 
SET 
    version = 3,
    extraction_rules = '{
        "pagination": {
            "enabled": true,
            "method": "scroll_load",
            "trigger_selector": ".load-more, [class*=\"load\"][class*=\"more\"]",
            "max_pages": 5
        },
        "data_extraction": {
            "news_items": {
                "container_selector": ".jin-flash-item-container, .jin-item, .news-item, [class*=\"flash\"][class*=\"item\"], .flash-item, .news-list-item, .item, li[class*=\"news\"], div[class*=\"news\"]",
                "importance_filter": {
                    "enabled": true,
                    "container_importance_selector": ".jin-flash-item, .flash-item, .news-item, [class*=\"flash\"][class*=\"item\"]",
                    "importance_class_patterns": ["is-important", "important", "high-priority", "urgent"],
                    "fallback_behavior": "accept_all",
                    "site_specific_configs": {
                        "jin10.com": {
                            "importance_selector": ".jin-flash-item.is-important",
                            "reject_without_importance": true
                        },
                        "default": {
                            "importance_selector": ".important, .high-priority, .urgent, [class*=\"important\"]",
                            "reject_without_importance": false
                        }
                    }
                },
                "fields": {
                    "publish_time": {
                        "selector": ".item-time, .time, [class*=\"time\"], .timestamp, .date, span[class*=\"time\"]",
                        "attribute": "text",
                        "format": "HH:mm:ss",
                        "required": false,
                        "clean_text": true
                    },
                    "title": {
                        "selector": ".right-common-title, .title, h3, h4, .headline, [class*=\"title\"], .news-headline, b[class*=\"title\"]",
                        "attribute": "text",
                        "max_length": 500,
                        "required": false,
                        "clean_text": true,
                        "remove_patterns": ["（\\d{4}-\\d{2}-\\d{2}）", "\\(\\d{4}-\\d{2}-\\d{2}\\)"]
                    },
                    "content": {
                        "selector": ".flash-text, .content, .description, p, [class*=\"content\"], .summary, .text",
                        "attribute": "text",
                        "max_length": 5000,
                        "clean_html": true,
                        "required": true,
                        "clean_text": true
                    },
                    "importance_level": {
                        "selector": ".is-important, .level, [class*=\"level\"], [class*=\"importance\"]",
                        "attribute": "class",
                        "mapping": {
                            "is-important": 3,
                            "火": 1,
                            "热": 2, 
                            "沸": 3,
                            "爆": 4
                        },
                        "default_value": 0
                    },
                    "influence_direction": {
                        "selector": "[class*=\"bull\"], [class*=\"bear\"], [class*=\"positive\"], [class*=\"negative\"]",
                        "attribute": "class",
                        "mapping": {
                            "bull": "positive",
                            "bear": "negative",
                            "positive": "positive",
                            "negative": "negative"
                        }
                    },
                    "affected_markets": {
                        "selector": ".markets, [class*=\"market\"], [class*=\"symbol\"]",
                        "attribute": "text",
                        "split_by": " ",
                        "is_array": true
                    },
                    "data_value": {
                        "selector": "[class*=\"data\"], .economic-data, [class*=\"value\"]",
                        "attribute": "text",
                        "regex": "\\d+\\.?\\d*",
                        "data_type": "number"
                    },
                    "source_url": {
                        "selector": "a[href*=\"detail\"], a[href*=\"flash\"], a[href]",
                        "attribute": "href",
                        "absolute_url": true
                    },
                    "flash_id": {
                        "selector": "[id*=\"flash\"]",
                        "attribute": "id",
                        "regex": "flash(\\d+)",
                        "data_type": "string"
                    }
                }
            }
        },
        "content_filters": {
            "exclude_ads": true,
            "exclude_duplicates": true,
            "min_content_length": 5,
            "exclude_patterns": ["广告", "推广", "赞助", "扫码查看", "打开金十数据APP"]
        }
    }'::jsonb,
    validation_rules = '{
        "required_fields": ["content"],
        "max_content_length": 10000,
        "min_content_length": 5,
        "time_format_validation": false,
        "duplicate_check": {
            "enabled": true,
            "fields": ["title", "content"],
            "time_window_hours": 2
        },
        "title_validation": {
            "enabled": true,
            "min_length": 3,
            "max_length": 500,
            "allow_empty": false
        },
        "importance_validation": {
            "enabled": true,
            "min_importance_level": 3,
            "allow_unknown_importance": false,
            "site_specific_rules": {
                "jin10.com": {
                    "require_is_important_class": true,
                    "min_importance_level": 3
                },
                "default": {
                    "require_is_important_class": false,
                    "min_importance_level": 2
                }
            }
        }
    }'::jsonb
WHERE source_id = (SELECT id FROM data_sources WHERE name = '金十数据快讯');
```

### 10.3 配置说明

#### 重要性过滤配置解释：

1. **importance_filter.enabled**: 启用重要性过滤
2. **container_importance_selector**: 用于检查重要性的容器选择器
3. **importance_class_patterns**: 重要性类名模式列表
4. **fallback_behavior**: 当无法确定重要性时的处理方式
5. **site_specific_configs**: 针对不同网站的特定配置

#### 兼容性策略：

1. **金十数据**: 严格要求`is-important`类，拒绝普通消息
2. **其他网站**: 检查通用重要性标识，无法确定时接受所有内容

#### 验证规则解释：

1. **importance_validation.enabled**: 启用重要性验证
2. **min_importance_level**: 最低重要性级别
3. **allow_unknown_importance**: 是否允许未知重要性的内容
4. **site_specific_rules**: 针对不同网站的验证规则

### 10.4 验证配置更新结果

```sql
-- 验证配置更新
SELECT 
    dsc.id,
    ds.name as source_name,
    dsc.version,
    dsc.is_active,
    dsc.change_reason,
    dsc.changed_by,
    dsc.updated_at,
    dsc.extraction_rules->'data_extraction'->'news_items'->'importance_filter' as importance_filter_config,
    dsc.validation_rules->'importance_validation' as importance_validation_config
FROM data_source_configs dsc
JOIN data_sources ds ON dsc.source_id = ds.id
WHERE ds.name = '金十数据快讯'
ORDER BY dsc.version DESC
LIMIT 1;
```

### 10.5 测试用例配置

```sql
-- 插入测试用例，验证重要性过滤功能
INSERT INTO system_logs (log_type, message, created_at) VALUES 
('CONFIG_UPDATE', 
 'Updated Jin10 data source config with importance filtering. Version 3 now includes:
  - Container importance detection for .jin-flash-item.is-important
  - Site-specific compatibility for different news websites
  - Validation rules for minimum importance level 3
  - Fallback behavior for sites without importance indicators',
 NOW());
```

这个配置更新确保：

1. **只采集重要信息**：金十数据网站只采集带有`is-important`类的快讯
2. **网站兼容性**：其他网站使用通用重要性检测，无重要性标识时仍然采集
3. **可配置性**：支持针对不同网站的特定配置
4. **容错性**：提供回退机制，确保系统稳定运行