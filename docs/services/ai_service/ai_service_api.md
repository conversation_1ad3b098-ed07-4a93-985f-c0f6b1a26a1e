# AI模型管理服务API文档

## 概述

AI模型管理服务提供了完整的AI模型配置、监控和管理功能，支持多种大语言模型的统一管理。

## 接口权限

所有B端接口都需要管理员权限，具体权限要求如下：

| 权限名称 | 说明 |
|---------|------|
| `ai_model.model.create` | 创建AI模型 |
| `ai_model.model.read` | 读取AI模型详情 |
| `ai_model.model.update` | 更新AI模型 |
| `ai_model.model.delete` | 删除AI模型 |
| `ai_model.list.read` | 读取AI模型列表 |
| `ai_model.status.update` | 更新模型状态 |
| `ai_model.default.update` | 设置默认模型 |
| `ai_model.metrics.create` | 创建模型指标 |
| `ai_model.metrics.read` | 读取模型指标 |
| `ai_model.analytics.read` | 读取系统分析数据 |

## 基础URL

B端管理接口：`/b/api/v1/admin/ai`

## 主要接口

### 1. 模型管理

#### 创建AI模型
```http
POST /b/api/v1/admin/ai/models
Content-Type: application/json
Authorization: Bearer <token>

{
    "model_name": "deepseek-chat",
    "display_name": "DeepSeek聊天模型",
    "description": "DeepSeek公司开发的大语言模型",
    "model_type": "llm",
    "provider": "deepseek",
    "model_version": "v1.0",
    "api_endpoint": "https://api.deepseek.com/v1/chat/completions",
    "api_key_name": "DEEPSEEK_API_KEY",
    "model_config": {
        "context_length": 32768,
        "supports_streaming": true
    },
    "max_tokens": 4096,
    "temperature": 0.7,
    "top_p": 0.9,
    "rate_limit_rpm": 60,
    "rate_limit_tpm": 100000,
    "cost_per_1k_input_tokens": 0.0014,
    "cost_per_1k_output_tokens": 0.0028,
    "is_default": true,
    "priority": 1
}
```

#### 获取模型列表
```http
GET /b/api/v1/admin/ai/models?page=1&page_size=20&model_type=llm&status=active
Authorization: Bearer <token>
```

#### 获取模型详情
```http
GET /b/api/v1/admin/ai/models/{model_id}
Authorization: Bearer <token>
```

#### 更新模型配置
```http
PUT /b/api/v1/admin/ai/models/{model_id}
Content-Type: application/json
Authorization: Bearer <token>

{
    "display_name": "DeepSeek聊天模型-升级版",
    "model_version": "v1.1",
    "temperature": 0.8,
    "cost_per_1k_input_tokens": 0.001
}
```

#### 删除模型
```http
DELETE /b/api/v1/admin/ai/models/{model_id}
Authorization: Bearer <token>
```

### 2. 模型状态管理

#### 更新模型状态
```http
PATCH /b/api/v1/admin/ai/models/{model_id}/status
Content-Type: application/json
Authorization: Bearer <token>

{
    "status": "active"  // active, inactive, testing, deprecated
}
```

#### 设置默认模型
```http
PATCH /b/api/v1/admin/ai/models/{model_id}/default
Content-Type: application/json
Authorization: Bearer <token>

{
    "is_default": true
}
```

#### 获取默认模型
```http
GET /b/api/v1/admin/ai/models/type/llm/default
Authorization: Bearer <token>
```

#### 获取可用模型
```http
GET /b/api/v1/admin/ai/models/type/llm/available
Authorization: Bearer <token>
```

### 3. 批量操作

#### 批量更新状态
```http
POST /b/api/v1/admin/ai/models/batch/status
Content-Type: application/json
Authorization: Bearer <token>

{
    "model_ids": [1, 2, 3],
    "operation": "activate"  // activate, deactivate, deprecate
}
```

#### 批量删除模型
```http
POST /b/api/v1/admin/ai/models/batch/delete
Content-Type: application/json
Authorization: Bearer <token>

{
    "model_ids": [4, 5, 6],
    "operation": "delete"
}
```

### 4. 模型指标

#### 创建模型指标
```http
POST /b/api/v1/admin/ai/models/{model_id}/metrics
Content-Type: application/json
Authorization: Bearer <token>

{
    "metric_date": "2024-01-15T10:00:00Z",
    "time_window": "hour",
    "total_requests": 150,
    "success_requests": 145,
    "failed_requests": 5,
    "avg_latency_ms": 800,
    "total_input_tokens": 50000,
    "total_output_tokens": 25000,
    "total_cost": 12.50,
    "accuracy_score": 0.95
}
```

#### 获取模型指标
```http
GET /b/api/v1/admin/ai/models/{model_id}/metrics?time_window=day&days=7
Authorization: Bearer <token>
```

#### 获取模型性能
```http
GET /b/api/v1/admin/ai/models/{model_id}/performance?days=7
Authorization: Bearer <token>
```

### 5. 系统分析

#### 获取系统信息
```http
GET /b/api/v1/admin/ai/system/info
Authorization: Bearer <token>
```

响应示例：
```json
{
    "total_models": 5,
    "active_models": 4,
    "default_models": {
        "llm": "deepseek-chat",
        "classification": "bert-base",
        "sentiment": "roberta-sentiment"
    },
    "provider_distribution": {
        "deepseek": 2,
        "openai": 2,
        "local": 1
    },
    "type_distribution": {
        "llm": 3,
        "classification": 1,
        "sentiment": 1
    },
    "total_requests_today": 1250,
    "total_cost_today": 45.67
}
```

#### 模型对比
```http
POST /b/api/v1/admin/ai/models/compare
Content-Type: application/json
Authorization: Bearer <token>

{
    "model_ids": [1, 2, 3],
    "time_range": "7d"  // 1d, 7d, 30d, 90d
}
```

## 支持的模型类型

| 类型 | 说明 | 用途 |
|------|------|------|
| `llm` | 大语言模型 | 文本生成、对话、摘要等 |
| `classification` | 分类模型 | 内容分类、情感分析等 |
| `ner` | 命名实体识别 | 实体提取、信息抽取 |
| `sentiment` | 情感分析 | 情感识别、观点挖掘 |
| `embedding` | 嵌入模型 | 向量化、相似度计算 |
| `rerank` | 重排序模型 | 搜索结果排序优化 |

## 支持的模型提供商

| 提供商 | 说明 |
|--------|------|
| `deepseek` | DeepSeek公司模型 |
| `openai` | OpenAI公司模型 |
| `anthropic` | Anthropic公司模型 |
| `zhipu` | 智谱AI模型 |
| `qwen` | 阿里通义千问 |
| `baidu` | 百度文心一言 |
| `local` | 本地部署模型 |
| `custom` | 自定义模型 |

## 模型状态

| 状态 | 说明 |
|------|------|
| `active` | 激活状态，可正常使用 |
| `inactive` | 停用状态，暂时不可用 |
| `testing` | 测试状态，仅测试环境使用 |
| `deprecated` | 已废弃，建议替换 |

## 使用示例

### Python SDK示例

```python
import requests
from typing import Dict, Any

class AIModelClient:
    def __init__(self, base_url: str, token: str):
        self.base_url = base_url
        self.headers = {
            'Authorization': f'Bearer {token}',
            'Content-Type': 'application/json'
        }
    
    def create_model(self, model_data: Dict[str, Any]) -> Dict[str, Any]:
        """创建AI模型"""
        response = requests.post(
            f"{self.base_url}/models",
            json=model_data,
            headers=self.headers
        )
        response.raise_for_status()
        return response.json()
    
    def get_models(self, **filters) -> Dict[str, Any]:
        """获取模型列表"""
        response = requests.get(
            f"{self.base_url}/models",
            params=filters,
            headers=self.headers
        )
        response.raise_for_status()
        return response.json()
    
    def get_default_model(self, model_type: str) -> Dict[str, Any]:
        """获取默认模型"""
        response = requests.get(
            f"{self.base_url}/models/type/{model_type}/default",
            headers=self.headers
        )
        response.raise_for_status()
        return response.json()

# 使用示例
client = AIModelClient("http://localhost:8000/b/api/v1/admin/ai", "your-token")

# 创建模型
model_data = {
    "model_name": "gpt-4",
    "model_type": "llm",
    "provider": "openai",
    "model_version": "gpt-4-0613",
    "is_default": True
}
new_model = client.create_model(model_data)

# 获取模型列表
models = client.get_models(model_type="llm", status="active")

# 获取默认LLM模型
default_llm = client.get_default_model("llm")
```

## 错误码

| 错误码 | 说明 |
|--------|------|
| 400 | 请求参数错误 |
| 401 | 未授权，需要登录 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 409 | 资源冲突（如模型名称重复） |
| 500 | 服务器内部错误 |

## 注意事项

1. **权限管理**：所有接口都需要相应的权限，请确保用户具备必要的权限
2. **模型命名**：模型名称必须唯一，只能包含字母、数字、下划线、连字符和点号
3. **默认模型**：每种类型只能有一个默认模型，设置新的默认模型会自动取消原有默认模型
4. **成本管理**：设置合理的成本参数，便于成本监控和控制
5. **限流配置**：根据API提供商的限制合理设置请求频率限制
6. **监控指标**：定期查看模型性能指标，及时调整配置

## 最佳实践

1. **模型配置**：
   - 为不同用途配置不同的模型参数
   - 设置合理的重试次数和超时时间
   - 定期更新成本参数

2. **性能监控**：
   - 定期查看模型性能指标
   - 设置告警阈值
   - 分析失败原因并优化

3. **成本控制**：
   - 监控每日成本
   - 设置成本预算告警
   - 优化模型使用策略

4. **安全管理**：
   - 定期轮换API密钥
   - 限制模型访问权限
   - 记录操作日志 