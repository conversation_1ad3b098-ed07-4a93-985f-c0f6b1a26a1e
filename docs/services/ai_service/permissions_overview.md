# AI 服务权限概览文档

## 📖 文档说明

本文档整理了 AI 模型管理服务的完整权限信息，包括权限定义、接口权限映射和角色权限配置。

## 🎯 权限模块定义

AI 服务采用基于 RBAC 的权限控制，权限编码格式为：`ai_model.{resource}.{action}`

### 权限分类

1. **模型管理权限** - 管理 AI 模型的基本操作
2. **列表查看权限** - 查看模型列表和统计信息
3. **状态管理权限** - 管型模型状态和默认设置
4. **指标管理权限** - 管理模型性能指标
5. **数据分析权限** - 查看分析报告和系统信息

## 📝 完整权限列表

### AI 模型管理权限 (ai_model module)

| 权限编码 | 权限名称 | 权限描述 | 资源 | 操作 |
|----------|----------|----------|------|------|
| `ai_model.model.create` | 创建 AI 模型 | 创建新的 AI 模型配置 | model | create |
| `ai_model.model.read` | 查看 AI 模型 | 查看 AI 模型详细信息 | model | read |
| `ai_model.model.update` | 更新 AI 模型 | 修改 AI 模型配置和使用统计 | model | update |
| `ai_model.model.delete` | 删除 AI 模型 | 删除指定的 AI 模型 | model | delete |
| `ai_model.list.read` | 查看模型列表 | 查看 AI 模型列表和可用模型 | list | read |
| `ai_model.status.update` | 更新模型状态 | 修改模型状态（激活/停用/测试/废弃）和批量状态更新 | status | update |
| `ai_model.default.update` | 设置默认模型 | 设置或取消模型为默认模型 | default | update |
| `ai_model.metrics.create` | 创建模型指标 | 为指定模型创建指标记录 | metrics | create |
| `ai_model.metrics.read` | 查看模型指标 | 获取指定模型的指标数据 | metrics | read |
| `ai_model.analytics.read` | 查看分析数据 | 获取系统信息、模型性能和对比分析 | analytics | read |

## 🚀 API 接口权限映射

### AI 模型管理接口

| HTTP 方法 | 接口路径 | 接口名称 | 权限要求 | 接口描述 |
|-----------|----------|----------|----------|----------|
| POST | `/models` | create_ai_model | `ai_model.model.create` | 创建新的AI模型 |
| GET | `/models` | list_ai_models | `ai_model.list.read` | 获取AI模型列表 |
| GET | `/models/{model_id}` | get_ai_model | `ai_model.model.read` | 获取AI模型详情 |
| GET | `/models/by-name/{model_name}` | get_ai_model_by_name | `ai_model.model.read` | 通过名称获取AI模型 |
| PUT | `/models/{model_id}` | update_ai_model | `ai_model.model.update` | 更新AI模型配置 |
| DELETE | `/models/{model_id}` | delete_ai_model | `ai_model.model.delete` | 删除AI模型 |

### 模型类型和状态管理接口

| HTTP 方法 | 接口路径 | 接口名称 | 权限要求 | 接口描述 |
|-----------|----------|----------|----------|----------|
| GET | `/models/type/{model_type}/default` | get_default_model | `ai_model.model.read` | 获取指定类型的默认模型 |
| GET | `/models/type/{model_type}/available` | get_available_models | `ai_model.list.read` | 获取指定类型的所有可用模型 |
| PATCH | `/models/{model_id}/status` | update_model_status | `ai_model.status.update` | 更新模型状态 |
| PATCH | `/models/{model_id}/default` | set_default_model | `ai_model.default.update` | 设置默认模型 |
| PATCH | `/models/{model_id}/usage` | update_usage_stats | `ai_model.model.update` | 更新使用统计 |

### 批量操作接口

| HTTP 方法 | 接口路径 | 接口名称 | 权限要求 | 接口描述 |
|-----------|----------|----------|----------|----------|
| POST | `/models/batch/status` | batch_update_status | `ai_model.status.update` | 批量更新模型状态 |
| POST | `/models/batch/delete` | batch_delete_models | `ai_model.model.delete` | 批量删除模型 |

### 系统信息和分析接口

| HTTP 方法 | 接口路径 | 接口名称 | 权限要求 | 接口描述 |
|-----------|----------|----------|----------|----------|
| GET | `/system/info` | get_system_info | `ai_model.analytics.read` | 获取系统模型统计信息 |
| POST | `/models/compare` | compare_models | `ai_model.analytics.read` | 对比多个模型的性能 |

### 模型指标管理接口

| HTTP 方法 | 接口路径 | 接口名称 | 权限要求 | 接口描述 |
|-----------|----------|----------|----------|----------|
| POST | `/models/{model_id}/metrics` | create_model_metrics | `ai_model.metrics.create` | 创建模型指标记录 |
| GET | `/models/{model_id}/metrics` | get_model_metrics | `ai_model.metrics.read` | 获取模型指标数据 |
| GET | `/models/{model_id}/performance` | get_model_performance | `ai_model.analytics.read` | 获取模型性能概览 |

### 健康检查接口

| HTTP 方法 | 接口路径 | 接口名称 | 权限要求 | 接口描述 |
|-----------|----------|----------|----------|----------|
| GET | `/health` | health_check | 无 | AI服务健康检查 |

## 👥 角色权限配置建议

### 建议的角色权限分配

| 角色名称 | 英文名称 | AI 模型权限 | 权限说明 |
|----------|----------|-------------|----------|
| 系统管理员 | Admin | 全部权限 | 拥有所有 AI 模型管理权限 |
| 数据管理员 | DataAdmin | `ai_model.*` | 拥有完整的 AI 模型管理权限 |
| 数据分析师 | Analyst | `ai_model.model.read`<br/>`ai_model.list.read`<br/>`ai_model.metrics.read`<br/>`ai_model.analytics.read` | 只能查看模型信息、指标和分析数据 |
| AI 工程师 | AIEngineer | `ai_model.model.*`<br/>`ai_model.list.read`<br/>`ai_model.status.update`<br/>`ai_model.metrics.*`<br/>`ai_model.analytics.read` | 模型管理和指标管理权限，但不能设置默认模型 |
| 普通用户 | User | `ai_model.list.read`<br/>`ai_model.analytics.read` | 只能查看模型列表和基本分析信息 |

### 推荐的扩展角色

建议增加专门的 AI 相关角色：

```json
{
  "name": "AIEngineer",
  "description": "AI 工程师，负责模型训练和调优",
  "is_system": true,
  "permissions": [
    "ai_model.model.*",
    "ai_model.list.read",
    "ai_model.status.update",
    "ai_model.metrics.*",
    "ai_model.analytics.read"
  ]
}
```

## 🔧 权限初始化配置

### 权限迁移配置

需要在 `src/services/permission_service/migrations.py` 中添加以下权限定义：

```python
# AI 模型管理权限
{"code": "ai_model.model.create", "name": "创建AI模型", "module": "ai_model", "resource": "model", "action": "create"},
{"code": "ai_model.model.read", "name": "查看AI模型", "module": "ai_model", "resource": "model", "action": "read"},
{"code": "ai_model.model.update", "name": "更新AI模型", "module": "ai_model", "resource": "model", "action": "update"},
{"code": "ai_model.model.delete", "name": "删除AI模型", "module": "ai_model", "resource": "model", "action": "delete"},
{"code": "ai_model.list.read", "name": "查看模型列表", "module": "ai_model", "resource": "list", "action": "read"},
{"code": "ai_model.status.update", "name": "更新模型状态", "module": "ai_model", "resource": "status", "action": "update"},
{"code": "ai_model.default.update", "name": "设置默认模型", "module": "ai_model", "resource": "default", "action": "update"},
{"code": "ai_model.metrics.create", "name": "创建模型指标", "module": "ai_model", "resource": "metrics", "action": "create"},
{"code": "ai_model.metrics.read", "name": "查看模型指标", "module": "ai_model", "resource": "metrics", "action": "read"},
{"code": "ai_model.analytics.read", "name": "查看分析数据", "module": "ai_model", "resource": "analytics", "action": "read"},
```

### 角色权限配置更新

在现有角色中添加 AI 模型权限：

```python
# 更新 DataAdmin 角色权限
"DataAdmin": {
    "permissions": [
        # ... 现有权限 ...
        "ai_model.*"  # 添加所有 AI 模型权限
    ]
},

# 更新 Analyst 角色权限
"Analyst": {
    "permissions": [
        # ... 现有权限 ...
        "ai_model.model.read",
        "ai_model.list.read", 
        "ai_model.metrics.read",
        "ai_model.analytics.read"
    ]
}
```

## 📋 权限检查清单

### 开发者检查清单

- [ ] 所有 API 接口都已配置正确的权限验证
- [ ] 权限编码遵循统一的命名规范
- [ ] 敏感操作（删除、批量操作）使用了适当的权限级别
- [ ] 权限已在迁移文件中正确定义
- [ ] 角色权限配置符合业务需求
- [ ] 测试覆盖了所有权限验证场景

### 权限审计要点

1. **最小权限原则** - 确保每个角色只拥有必要的权限
2. **权限隔离** - 不同业务模块的权限相互独立
3. **敏感操作保护** - 删除、批量操作等需要高级权限
4. **审计日志** - 重要操作需要记录审计日志
5. **权限继承** - 角色层级的权限继承关系合理

## 🔗 相关文档

- [RBAC设计文档](../role_permission/rbac_design.md) - 整体权限系统设计
- [权限系统概览](../role_permission/permissions_overview.md) - 全局权限信息
- [AI服务架构文档](./architecture.md) - AI服务整体架构
- [API接口文档](./api_documentation.md) - 详细的API接口说明

---

**注意**: 本文档基于当前的 AI 服务实现整理，如有权限需求变更，请及时更新本文档和相关配置。 