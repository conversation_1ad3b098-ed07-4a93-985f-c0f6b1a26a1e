# API密钥配置说明

## 概述

AI服务模块使用 `api_key_name` 字段来动态管理不同AI提供商的API密钥。这种设计提供了以下优势：

1. **灵活性**：支持多个AI提供商和模型
2. **安全性**：API密钥通过环境变量管理，不在代码中硬编码
3. **可扩展性**：新增AI提供商时无需修改代码结构

## `api_key_name` 字段说明

### 数据库字段
```sql
api_key_name VARCHAR(100) COMMENT "API密钥环境变量名"
```

### 使用方式

1. **数据库中存储环境变量名**
   ```python
   ai_model = AIModel(
       model_name="deepseek-chat",
       api_key_name="DEEPSEEK_API_KEY",  # 环境变量名
       ...
   )
   ```

2. **环境变量中设置实际密钥**
   ```bash
   # .env.develop 文件中
   DEEPSEEK_API_KEY=sk-xxxxxxxxxxxxxxxxx
   ```

3. **代码中动态获取**
   ```python
   from src.services.ai_service.utils import AIServiceUtils
   
   # 从模型配置获取API密钥
   api_key = AIServiceUtils.get_api_key_from_env(ai_model.api_key_name)
   ```

## 支持的API提供商

### DeepSeek
- **环境变量名**: `DEEPSEEK_API_KEY`
- **示例值**: `sk-xxxxxxxxxxxxxxxxx`
- **API文档**: https://platform.deepseek.com/docs

### OpenAI
- **环境变量名**: `OPENAI_API_KEY`
- **示例值**: `sk-xxxxxxxxxxxxxxxxx`
- **API文档**: https://platform.openai.com/docs

### 百度文心一言
- **环境变量名**: `BAIDU_API_KEY`, `BAIDU_SECRET_KEY`
- **示例值**: API Key 和 Secret Key
- **API文档**: https://cloud.baidu.com/doc/WENXINWORKSHOP

### 阿里云通义千问
- **环境变量名**: `ALIYUN_API_KEY`
- **示例值**: `sk-xxxxxxxxxxxxxxxxx`
- **API文档**: https://help.aliyun.com/zh/dashscope

### 腾讯云混元
- **环境变量名**: `TENCENT_SECRET_ID`, `TENCENT_SECRET_KEY`
- **示例值**: SecretId 和 SecretKey
- **API文档**: https://cloud.tencent.com/document/product/1729

### 智谱AI
- **环境变量名**: `ZHIPU_API_KEY`
- **示例值**: `xxxxxxxxxxxxxxxxx.xxxxxxxx`
- **API文档**: https://open.bigmodel.cn/doc

### Anthropic Claude
- **环境变量名**: `ANTHROPIC_API_KEY`
- **示例值**: `sk-ant-xxxxxxxxxxxxxxxxx`
- **API文档**: https://docs.anthropic.com/claude/reference

### Google Gemini
- **环境变量名**: `GOOGLE_API_KEY`
- **示例值**: `AIxxxxxxxxxxxxxxxxx`
- **API文档**: https://ai.google.dev/docs

## 配置示例

### 1. 配置数据库记录
```python
# 添加DeepSeek模型配置
deepseek_model = AIModel(
    model_name="deepseek-chat",
    display_name="DeepSeek Chat",
    description="DeepSeek的对话模型",
    model_type=ModelType.LLM,
    provider=ModelProvider.DEEPSEEK,
    api_endpoint="https://api.deepseek.com/v1/chat/completions",
    api_key_name="DEEPSEEK_API_KEY",  # 关键字段
    model_config={
        "context_length": 32768,
        "max_tokens": 4096,
        "temperature": 0.7
    }
)

# 添加OpenAI模型配置
openai_model = AIModel(
    model_name="gpt-3.5-turbo",
    display_name="GPT-3.5 Turbo",
    description="OpenAI的GPT-3.5模型",
    model_type=ModelType.LLM,
    provider=ModelProvider.OPENAI,
    api_endpoint="https://api.openai.com/v1/chat/completions",
    api_key_name="OPENAI_API_KEY",  # 关键字段
    model_config={
        "context_length": 4096,
        "max_tokens": 1024,
        "temperature": 0.7
    }
)
```

### 2. 配置环境变量
```bash
# .env.develop 文件
DEEPSEEK_API_KEY=sk-1234567890abcdef1234567890abcdef
OPENAI_API_KEY=sk-abcdef1234567890abcdef1234567890
BAIDU_API_KEY=your_baidu_api_key
BAIDU_SECRET_KEY=your_baidu_secret_key
```

### 3. 使用API密钥
```python
from src.services.ai_service.utils import AIServiceUtils

# 方式1：直接通过环境变量名获取
api_key = AIServiceUtils.get_api_key_from_env("DEEPSEEK_API_KEY")

# 方式2：通过模型配置获取
model = ai_service.get_model_by_name("deepseek-chat")
if model:
    api_key = AIServiceUtils.get_api_key_from_env(model.api_key_name)
    
# 方式3：验证模型配置完整性
is_valid = AIServiceUtils.validate_model_config(model)
```

## 安全最佳实践

### 1. 环境变量安全
- 永远不要在代码中硬编码API密钥
- 使用 `.env` 文件管理本地开发环境密钥
- 生产环境使用密钥管理系统（如AWS Secrets Manager）

### 2. 权限控制
- 为不同环境使用不同的API密钥
- 定期轮换API密钥
- 监控API密钥使用情况

### 3. 错误处理
- 验证API密钥格式
- 处理密钥过期或无效的情况
- 记录安全相关的错误日志

### 4. 配置验证
```python
# 验证配置完整性
def validate_api_configuration():
    """验证API配置完整性"""
    required_keys = [
        "DEEPSEEK_API_KEY",
        "OPENAI_API_KEY",
        # 添加其他必需的密钥
    ]
    
    missing_keys = []
    for key in required_keys:
        if not os.getenv(key):
            missing_keys.append(key)
    
    if missing_keys:
        logger.warning(f"缺少环境变量: {missing_keys}")
        return False
    
    return True
```

## 故障排查

### 常见问题

1. **API密钥未设置**
   - 错误信息：`环境变量 DEEPSEEK_API_KEY 未设置或为空`
   - 解决方案：在 `.env` 文件中添加对应的环境变量

2. **API密钥格式错误**
   - 错误信息：`API密钥格式无效`
   - 解决方案：检查API密钥格式是否正确

3. **API调用失败**
   - 错误信息：`API调用失败: 401 Unauthorized`
   - 解决方案：检查API密钥是否有效且有足够权限

### 调试方法
```python
# 调试API密钥配置
from src.services.ai_service.utils import AIServiceUtils

# 检查环境变量
import os
print(f"DEEPSEEK_API_KEY exists: {bool(os.getenv('DEEPSEEK_API_KEY'))}")

# 验证密钥获取
api_key = AIServiceUtils.get_api_key_from_env("DEEPSEEK_API_KEY")
print(f"API Key retrieved: {bool(api_key)}")

# 验证模型配置
model = ai_service.get_model_by_name("deepseek-chat")
if model:
    is_valid = AIServiceUtils.validate_model_config(model)
    print(f"Model config valid: {is_valid}")
```

## 扩展新的AI提供商

当需要添加新的AI提供商时，按以下步骤操作：

1. **添加环境变量**
   ```bash
   NEW_PROVIDER_API_KEY=your_api_key_here
   ```

2. **创建模型记录**
   ```python
   new_model = AIModel(
       model_name="new-provider-model",
       api_key_name="NEW_PROVIDER_API_KEY",
       # 其他配置...
   )
   ```

3. **更新工具类**（如果需要特殊处理）
   ```python
   def validate_new_provider_key(api_key: str) -> bool:
       """验证新提供商的API密钥格式"""
       # 自定义验证逻辑
       return True
   ```

这种设计确保了系统的可扩展性和安全性，同时保持了配置的灵活性。 