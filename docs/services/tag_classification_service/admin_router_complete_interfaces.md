# 标签分类管理端完整接口文档

## 概述

本文档详细记录了标签分类服务管理端API的完整接口列表，包括所有分类维度和分类值的增删改查接口。

## 接口总览

### 📊 接口统计
- **总接口数量**: 24个
- **TagClassification管理**: 6个接口
- **Tag管理**: 5个接口
- **ClassificationDimension管理**: 5个接口
- **ClassificationValue管理**: 5个接口
- **统计分析**: 3个接口

## 详细接口列表

### 1. 标签分类管理接口 (TagClassification)

| 序号 | 方法 | 路径 | 权限 | 描述 |
|------|------|------|------|------|
| 1 | GET | `/tags/classifications` | `tag.classification.read` | 获取标签分类列表 |
| 2 | GET | `/tags/classifications/{id}` | `tag.classification.read` | 获取标签分类详情 |
| 3 | POST | `/tags/classifications` | `tag.classification.create` | 创建标签分类 |
| 4 | PUT | `/tags/classifications/{id}` | `tag.classification.update` | 更新标签分类 |
| 5 | DELETE | `/tags/classifications/{id}` | `tag.classification.delete` | 删除标签分类 |
| 6 | GET | `/tags/classifications/tree` | `tag.classification.read` | 获取标签分类树 |

### 2. 标签管理接口 (Tag)

| 序号 | 方法 | 路径 | 权限 | 描述 |
|------|------|------|------|------|
| 7 | GET | `/tags` | `tag.tag.read` | 获取标签列表 |
| 8 | GET | `/tags/{id}` | `tag.tag.read` | 获取标签详情 |
| 9 | POST | `/tags` | `tag.tag.create` | 创建标签 |
| 10 | PUT | `/tags/{id}` | `tag.tag.update` | 更新标签 |
| 11 | DELETE | `/tags/{id}` | `tag.tag.delete` | 删除标签 |

### 3. 分类维度管理接口 (ClassificationDimension)

| 序号 | 方法 | 路径 | 权限 | 描述 |
|------|------|------|------|------|
| 12 | GET | `/classifications/dimensions` | `classification.dimension.read` | 获取分类维度列表 |
| 13 | GET | `/classifications/dimensions/{id}` | `classification.dimension.read` | 获取分类维度详情 |
| 14 | POST | `/classifications/dimensions` | `classification.dimension.create` | 创建分类维度 |
| 15 | PUT | `/classifications/dimensions/{id}` | `classification.dimension.update` | 更新分类维度 |
| 16 | DELETE | `/classifications/dimensions/{id}` | `classification.dimension.delete` | 删除分类维度 |

### 4. 分类值管理接口 (ClassificationValue)

| 序号 | 方法 | 路径 | 权限 | 描述 |
|------|------|------|------|------|
| 17 | GET | `/classifications/values` | `classification.value.read` | 获取分类值列表 |
| 18 | GET | `/classifications/values/{id}` | `classification.value.read` | 获取分类值详情 |
| 19 | POST | `/classifications/values` | `classification.value.create` | 创建分类值 |
| 20 | PUT | `/classifications/values/{id}` | `classification.value.update` | 更新分类值 |
| 21 | DELETE | `/classifications/values/{id}` | `classification.value.delete` | 删除分类值 |

### 5. 统计分析接口

| 序号 | 方法 | 路径 | 权限 | 描述 |
|------|------|------|------|------|
| 22 | GET | `/tags/statistics` | `tag.tag.read` | 获取标签统计信息 |
| 23 | GET | `/tags/popular` | `tag.tag.read` | 获取热门标签 |
| 24 | GET | `/tags/trending` | `tag.tag.read` | 获取趋势标签 |

## 权限体系

### 权限分类
1. **标签分类权限**: `tag.classification.*`
2. **标签权限**: `tag.tag.*`
3. **分类维度权限**: `classification.dimension.*`
4. **分类值权限**: `classification.value.*`

### 权限操作类型
- **read**: 读取权限
- **create**: 创建权限
- **update**: 更新权限
- **delete**: 删除权限

## 接口特性

### 1. 统一的错误处理
- **401 Unauthorized**: 用户未认证
- **403 Forbidden**: 权限不足
- **404 Not Found**: 资源不存在
- **422 Unprocessable Entity**: 参数验证失败
- **500 Internal Server Error**: 服务器内部错误

### 2. 标准化的响应格式
- **列表接口**: 返回 `PageResponse` 格式，包含分页信息
- **详情接口**: 返回对应的 Response Schema
- **创建接口**: 返回创建后的对象，状态码 201
- **更新接口**: 返回更新后的对象
- **删除接口**: 返回 204 状态码，无内容

### 3. 查询参数支持
- **分页参数**: `page`, `size`
- **过滤参数**: `is_active`, `search`, `classification_type`, `domain`
- **关联参数**: `parent_id`, `classification_id`, `dimension_id`

## 测试验证

### 1. 接口存在性测试
✅ 所有24个接口都已验证存在并正确注册

### 2. 权限保护测试
✅ 所有接口都有适当的权限保护，未授权访问返回401/403

### 3. CRUD完整性测试
✅ 所有资源都支持完整的增删改查操作

## 使用示例

### 创建分类维度
```bash
POST /api/v1/admin/classifications/dimensions
{
  "dimension_name": "内容类型",
  "dimension_code": "content_type",
  "description": "内容分类维度"
}
```

### 创建分类值
```bash
POST /api/v1/admin/classifications/values
{
  "value_name": "新闻",
  "value_code": "news",
  "dimension_id": 1,
  "description": "新闻类型内容"
}
```

### 创建标签分类
```bash
POST /api/v1/admin/tags/classifications
{
  "classification_code": "financial.news",
  "classification_name": "财经新闻",
  "classification_type": "category",
  "domain": "financial",
  "description": "财经新闻相关标签分类"
}
```

### 创建标签
```bash
POST /api/v1/admin/tags
{
  "tag_name": "央行政策",
  "tag_code": "central_bank_policy",
  "tag_slug": "central-bank-policy",
  "classification_id": 1,
  "description": "央行政策相关标签"
}
```

## 技术实现

### 1. 依赖注入
```python
current_user: Annotated[User, Depends(require_permission("specific.permission"))]
```

### 2. 服务层调用
```python
service: Annotated[ServiceClass, Depends(get_service)]
```

### 3. 数据验证
```python
data: CreateSchema  # 自动验证请求数据
```

## 部署注意事项

1. **权限配置**: 确保为用户分配了适当的权限
2. **数据库迁移**: 确保数据库结构与新接口兼容
3. **API文档**: 更新API文档以反映新接口
4. **前端适配**: 前端代码需要适配新的接口结构

## 后续扩展

1. **批量操作**: 支持批量创建、更新、删除
2. **导入导出**: 支持数据的导入和导出功能
3. **版本控制**: 支持数据变更的版本控制
4. **审计日志**: 记录所有管理操作的详细日志

## 总结

✅ **完整性**: 所有资源都有完整的CRUD接口
✅ **安全性**: 所有接口都有细粒度的权限控制
✅ **一致性**: 统一的接口设计和错误处理
✅ **可扩展性**: 支持未来功能的扩展
✅ **可测试性**: 完整的测试覆盖

标签分类管理端API现在提供了完整的管理功能，支持企业级的标签分类体系管理需求。
