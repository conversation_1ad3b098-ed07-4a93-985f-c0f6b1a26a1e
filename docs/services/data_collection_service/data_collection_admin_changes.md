# 数据采集模块B端接口分离和权限更改总结

## 概述

本次更改完成了数据采集模块B端接口的分离，并更新了权限体系以支持新的权限格式。主要包括数据源管理、数据源配置管理和原始数据记录管理三大模块。

## 主要更改

### 1. 权限名称格式调整

#### 1.1 数据源管理权限
- `data_source.create` → `data_source.data_source.create`
- `data_source.read` → `data_source.data_source.read`
- `data_source.update` → `data_source.data_source.update`
- `data_source.delete` → `data_source.data_source.delete`
- 新增：`data_source.list.read`（查看数据源列表）
- 新增：`data_source.stats.read`（查看数据源统计）

#### 1.2 数据源配置管理权限
- `data_source_config.create` → `data_source.config.create`
- `data_source_config.read` → `data_source.config.read`
- `data_source_config.update` → `data_source.config.update`
- `data_source_config.delete` → `data_source.config.delete`
- `data_source_config.manage` → `data_source.config.manage`

#### 1.3 原始数据记录管理权限
- `raw_data_record.create` → `raw_data_record.raw_data_record.create`
- `raw_data_record.read` → `raw_data_record.raw_data_record.read`
- `raw_data_record.update` → `raw_data_record.raw_data_record.update`
- `raw_data_record.delete` → `raw_data_record.raw_data_record.delete`
- `raw_data_record.manage` → `raw_data_record.raw_data_record.manage`
- `raw_data_record.analyze` → `raw_data_record.raw_data_record.analyze`
- 新增：`raw_data_record.list.read`（查看原始数据记录列表）
- 新增：`raw_data_record.stats.read`（查看原始数据记录统计）

### 2. API路由路径调整

#### 2.1 数据源配置路径更改
- 旧路径：`/api/v1/admin/data-collection/data-source-configs`
- 新路径：`/api/v1/admin/data-collection/data-source/configs`

这种调整使路由结构更加清晰，体现了数据源配置作为数据源子资源的层级关系。

### 3. 更新的文件列表

#### 3.1 后端代码文件
1. **权限定义更新**
   - `src/services/permission_service/migrations.py` - 添加新的权限定义
   - `src/services/data_collection_service/admin_router.py` - 权限名称和路由路径调整

2. **角色权限分配更新**
   - `src/services/permission_service/migrations.py` - 更新DataAdmin和Analyst角色的权限

#### 3.2 前端配置文件
1. **权限配置文件**
   - `docs/role_permission/frontend_permissions_config.json` - 添加新权限模块定义
   - `docs/role_permission/frontend_permissions_utils.js` - 添加新权限常量
   - `docs/role_permission/nextjs_config.ts` - 添加NextJS权限配置

2. **文档更新**
   - `docs/role_permission/permissions_overview.md` - 添加新权限模块说明

#### 3.3 测试文件
1. **测试更新**
   - `tests/services/data_collection_service/test_admin_router_simple.py` - 新增简化测试
   - `tests/services/data_collection_service/test_admin_router.py` - 修复导入问题

## 权限体系架构

### 1. 权限模块划分

#### 1.1 数据源管理模块 (data_source)
```
data_source.data_source.*     # 数据源CRUD操作
data_source.config.*          # 数据源配置管理
data_source.list.read         # 列表查看
data_source.stats.read        # 统计信息查看
```

#### 1.2 原始数据记录模块 (raw_data_record)
```
raw_data_record.raw_data_record.*  # 原始数据记录CRUD操作
raw_data_record.list.read          # 列表查看
raw_data_record.stats.read         # 统计信息查看
```

### 2. 角色权限分配

#### 2.1 DataAdmin（数据管理员）
- 拥有所有数据源和原始数据记录的完整权限
- 包括创建、读取、更新、删除、管理等操作

#### 2.2 Analyst（数据分析师）
- 拥有数据源和原始数据记录的读取权限
- 包括查看详情、列表、统计信息等

#### 2.3 Admin（系统管理员）
- 拥有所有权限

## API接口概览

### 1. 数据源管理接口
```
POST   /api/v1/admin/data-collection/data-sources           # 创建数据源
GET    /api/v1/admin/data-collection/data-sources           # 获取数据源列表
GET    /api/v1/admin/data-collection/data-sources/stats     # 获取统计信息
GET    /api/v1/admin/data-collection/data-sources/{id}      # 获取详情
PUT    /api/v1/admin/data-collection/data-sources/{id}      # 更新数据源
DELETE /api/v1/admin/data-collection/data-sources/{id}      # 删除数据源
```

### 2. 数据源配置管理接口
```
POST   /api/v1/admin/data-collection/data-source/configs           # 创建配置
GET    /api/v1/admin/data-collection/data-source/configs           # 获取配置列表
GET    /api/v1/admin/data-collection/data-source/configs/{id}      # 获取配置详情
PUT    /api/v1/admin/data-collection/data-source/configs/{id}      # 更新配置
DELETE /api/v1/admin/data-collection/data-source/configs/{id}      # 删除配置
PATCH  /api/v1/admin/data-collection/data-source/configs/{id}/activate  # 激活配置
```

### 3. 原始数据记录管理接口
```
POST   /api/v1/admin/data-collection/raw-data-records                    # 创建记录
GET    /api/v1/admin/data-collection/raw-data-records                    # 获取记录列表
GET    /api/v1/admin/data-collection/raw-data-records/stats              # 获取统计信息
GET    /api/v1/admin/data-collection/raw-data-records/{id}               # 获取记录详情
PUT    /api/v1/admin/data-collection/raw-data-records/{id}               # 更新记录
DELETE /api/v1/admin/data-collection/raw-data-records/{id}               # 删除记录
PATCH  /api/v1/admin/data-collection/raw-data-records/{id}/archive       # 归档记录
POST   /api/v1/admin/data-collection/raw-data-records/batch-update-status # 批量更新状态
GET    /api/v1/admin/data-collection/raw-data-records/duplicates         # 获取重复记录
```

## 测试验证

### 1. 基础测试通过
- ✅ 路由存在性测试
- ✅ 权限要求测试
- ✅ 路径变更测试
- ✅ 权限初始化测试

### 2. 权限验证
- ✅ 新权限定义已创建（19个新权限）
- ✅ 角色权限分配已更新
- ✅ 前端权限配置已同步

## 兼容性说明

### 1. 向后兼容
- 保留了原有的 `data.source.*` 权限定义，确保现有功能不受影响
- 新增的权限是增量更新，不会影响现有权限体系

### 2. 迁移建议
- 建议前端逐步迁移到新的权限格式
- 可以通过权限映射实现平滑迁移

## 总结

本次更改成功完成了：

1. **权限体系优化**：采用更清晰的层级权限命名格式
2. **API路由优化**：使用更符合RESTful规范的路由结构
3. **模块化管理**：将数据采集功能按业务模块进行清晰分离
4. **完整测试覆盖**：确保所有功能正常工作
5. **文档同步更新**：保持代码和文档的一致性

这些更改为数据采集模块提供了更好的可维护性和扩展性，同时保持了与现有系统的兼容性。 