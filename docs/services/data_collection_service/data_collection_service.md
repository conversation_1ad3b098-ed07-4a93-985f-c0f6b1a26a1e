# 数据收集服务文档

## 1. 服务概述

数据收集服务负责从各种数据源收集、处理和存储金融市场数据，是FinSight系统的数据基础设施核心组件。

### 1.1 主要职责
- 多源数据采集（股票、债券、基金、期货等）
- 实时数据流处理
- 历史数据管理
- 数据质量监控
- API限流和错误重试
- 数据格式标准化

### 1.2 技术栈
- **框架**: FastAPI + Celery + Redis
- **数据存储**: PostgreSQL + InfluxDB
- **消息队列**: Redis/RabbitMQ
- **数据源**: 第三方金融数据API
- **缓存**: Redis (多级缓存)
- **监控**: Prometheus + Grafana

### 1.3 架构设计
- **微服务架构**: 独立的数据收集服务
- **异步处理**: Celery分布式任务队列
- **数据分层**: 实时数据 + 历史数据 + 缓存数据
- **容错机制**: 断点续传 + 失败重试 + 数据校验

## 2. 数据模型

### 2.1 股票基础信息表 (stocks)
```sql
CREATE TABLE stocks (
    id SERIAL PRIMARY KEY,
    symbol VARCHAR(20) UNIQUE NOT NULL,      -- 股票代码
    name VARCHAR(100) NOT NULL,              -- 股票名称
    exchange VARCHAR(20) NOT NULL,           -- 交易所
    sector VARCHAR(50),                      -- 行业
    market_cap BIGINT,                       -- 市值
    listing_date DATE,                       -- 上市日期
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 2.2 实时行情数据表 (realtime_quotes)
```sql
CREATE TABLE realtime_quotes (
    id SERIAL PRIMARY KEY,
    symbol VARCHAR(20) NOT NULL,
    price DECIMAL(10,4) NOT NULL,           -- 当前价格
    volume BIGINT NOT NULL,                 -- 成交量
    high DECIMAL(10,4),                     -- 最高价
    low DECIMAL(10,4),                      -- 最低价
    open DECIMAL(10,4),                     -- 开盘价
    prev_close DECIMAL(10,4),               -- 昨收价
    change_amount DECIMAL(10,4),            -- 涨跌额
    change_percent DECIMAL(5,2),            -- 涨跌幅
    timestamp TIMESTAMP NOT NULL,           -- 数据时间
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 2.3 历史K线数据表 (kline_data)
```sql
CREATE TABLE kline_data (
    id SERIAL PRIMARY KEY,
    symbol VARCHAR(20) NOT NULL,
    interval VARCHAR(10) NOT NULL,          -- 时间间隔(1m,5m,1h,1d)
    open_price DECIMAL(10,4) NOT NULL,
    high_price DECIMAL(10,4) NOT NULL,
    low_price DECIMAL(10,4) NOT NULL,
    close_price DECIMAL(10,4) NOT NULL,
    volume BIGINT NOT NULL,
    turnover DECIMAL(15,2),                 -- 成交额
    timestamp TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(symbol, interval, timestamp)
);
```

### 2.4 数据源配置表 (data_sources)
```sql
CREATE TABLE data_sources (
    id SERIAL PRIMARY KEY,
    name VARCHAR(50) UNIQUE NOT NULL,       -- 数据源名称
    type VARCHAR(20) NOT NULL,              -- 数据源类型(api/websocket/file)
    base_url VARCHAR(255),                  -- API基础URL
    api_key_required BOOLEAN DEFAULT FALSE, -- 是否需要API密钥
    rate_limit INTEGER DEFAULT 100,        -- 每分钟请求限制
    timeout INTEGER DEFAULT 30,            -- 超时时间(秒)
    is_active BOOLEAN DEFAULT TRUE,
    config JSONB,                           -- 其他配置信息
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 3. 底层协议逻辑

### 3.1 数据采集架构

#### 3.1.1 数据流架构图
```mermaid
graph TD
    A[外部数据源] -->|HTTP/WebSocket| B[数据采集器]
    B --> C[数据验证器]
    C --> D[数据标准化器]
    D --> E[消息队列]
    E --> F[数据处理器]
    F --> G[数据存储层]
    F --> H[实时缓存]
    
    G --> I[PostgreSQL]
    G --> J[InfluxDB]
    H --> K[Redis]
    
    L[API Gateway] --> K
    L --> I
```

#### 3.1.2 数据采集流程
```yaml
实时数据采集:
  - WebSocket连接管理
  - 心跳检测机制
  - 断线重连逻辑
  - 数据缓冲处理

历史数据采集:
  - 分批次拉取数据
  - 增量更新策略
  - 数据完整性检查
  - 进度状态管理

数据验证:
  - 格式校验
  - 业务规则验证
  - 异常数据处理
  - 数据质量评分
```

### 3.2 API限流和重试机制

#### 3.2.1 令牌桶算法
```python
class TokenBucket:
    def __init__(self, capacity: int, refill_rate: int):
        self.capacity = capacity      # 桶容量
        self.tokens = capacity        # 当前令牌数
        self.refill_rate = refill_rate  # 每秒补充令牌数
        self.last_refill = time.time()
    
    def consume(self, tokens: int = 1) -> bool:
        """消费令牌"""
        self._refill()
        if self.tokens >= tokens:
            self.tokens -= tokens
            return True
        return False
```

#### 3.2.2 指数退避重试
```yaml
重试策略:
  - 初始延迟: 1秒
  - 最大延迟: 60秒
  - 退避因子: 2
  - 最大重试次数: 5次
  - 抖动因子: 0.1

重试触发条件:
  - 网络超时
  - HTTP 5xx错误
  - 速率限制 (429)
  - 连接错误

不重试条件:
  - HTTP 4xx错误(除429)
  - 认证失败
  - 数据格式错误
```

### 3.3 数据存储策略

#### 3.3.1 时序数据存储
```yaml
InfluxDB存储:
  - 实时行情数据
  - K线数据
  - 技术指标数据
  - 自动数据压缩
  - 保留策略(RP)管理

PostgreSQL存储:
  - 基础信息数据
  - 配置数据
  - 元数据信息
  - 关系型数据

Redis缓存:
  - 热点数据缓存
  - 会话状态
  - 任务队列
  - 实时计算结果
```

## 4. API 接口

### 4.1 实时数据接口

#### 4.1.1 获取实时行情
- **GET** `/api/v1/data/realtime/{symbol}`
- **描述**: 获取指定股票的实时行情数据
- **认证**: 需要API Key
- **参数**: symbol (股票代码)
- **响应**: RealtimeQuoteResponse

**请求示例**:
```bash
GET /api/v1/data/realtime/000001.SZ
Authorization: Bearer API_KEY
```

**响应示例**:
```json
{
    "symbol": "000001.SZ",
    "name": "平安银行",
    "price": 12.45,
    "volume": 125467800,
    "high": 12.68,
    "low": 12.20,
    "open": 12.35,
    "prev_close": 12.40,
    "change_amount": 0.05,
    "change_percent": 0.40,
    "timestamp": "2024-01-01T15:30:00Z",
    "market_status": "open"
}
```

#### 4.1.2 批量获取实时行情
- **POST** `/api/v1/data/realtime/batch`
- **描述**: 批量获取多只股票的实时行情
- **认证**: 需要API Key
- **请求体**: BatchQuoteRequest
- **响应**: List[RealtimeQuoteResponse]

#### 4.1.3 实时数据流订阅
- **WebSocket** `/ws/realtime`
- **描述**: 通过WebSocket订阅实时数据流
- **认证**: 需要JWT Token
- **协议**: JSON格式消息

**订阅消息格式**:
```json
{
    "action": "subscribe",
    "symbols": ["000001.SZ", "000002.SZ"],
    "data_types": ["quote", "trade"]
}
```

**数据推送格式**:
```json
{
    "type": "quote",
    "symbol": "000001.SZ",
    "data": {
        "price": 12.45,
        "volume": 125467800,
        "timestamp": "2024-01-01T15:30:00Z"
    }
}
```

### 4.2 历史数据接口

#### 4.2.1 获取K线数据
- **GET** `/api/v1/data/kline/{symbol}`
- **描述**: 获取指定股票的K线数据
- **认证**: 需要API Key
- **参数**: 
  - symbol: 股票代码
  - interval: 时间间隔 (1m,5m,15m,30m,1h,4h,1d,1w,1M)
  - start_time: 开始时间
  - end_time: 结束时间
  - limit: 数据条数限制 (默认100，最大1000)

**请求示例**:
```bash
GET /api/v1/data/kline/000001.SZ?interval=1d&start_time=2024-01-01&end_time=2024-01-31&limit=100
```

**响应示例**:
```json
{
    "symbol": "000001.SZ",
    "interval": "1d",
    "data": [
        {
            "timestamp": "2024-01-01T00:00:00Z",
            "open": 12.30,
            "high": 12.68,
            "low": 12.20,
            "close": 12.45,
            "volume": 125467800,
            "turnover": 1554567890.50
        }
    ],
    "total": 31,
    "has_more": false
}
```

#### 4.2.2 获取历史成交数据
- **GET** `/api/v1/data/trades/{symbol}`
- **描述**: 获取历史成交明细数据
- **认证**: 需要API Key
- **响应**: TradeHistoryResponse

### 4.3 数据管理接口

#### 4.3.1 触发数据同步
- **POST** `/api/v1/data/sync`
- **描述**: 手动触发数据同步任务
- **认证**: 需要管理员权限
- **请求体**: DataSyncRequest
- **响应**: TaskResponse

#### 4.3.2 获取数据质量报告
- **GET** `/api/v1/data/quality`
- **描述**: 获取数据质量监控报告
- **认证**: 需要管理员权限
- **响应**: DataQualityResponse

#### 4.3.3 配置数据源
- **POST** `/api/v1/data/sources`
- **描述**: 添加或更新数据源配置
- **认证**: 需要管理员权限
- **请求体**: DataSourceConfig
- **响应**: DataSourceResponse

## 5. 数据采集任务

### 5.1 Celery任务定义

#### 5.1.1 实时数据采集任务
```python
@celery_app.task(bind=True, max_retries=3)
def collect_realtime_data(self, symbols: List[str]):
    """实时数据采集任务"""
    try:
        collector = RealtimeDataCollector()
        results = collector.collect_batch(symbols)
        return {"status": "success", "collected": len(results)}
    except Exception as exc:
        # 指数退避重试
        countdown = 2 ** self.request.retries
        raise self.retry(exc=exc, countdown=countdown)
```

#### 5.1.2 历史数据补齐任务
```python
@celery_app.task(bind=True)
def backfill_historical_data(self, symbol: str, start_date: str, end_date: str):
    """历史数据补齐任务"""
    collector = HistoricalDataCollector()
    return collector.backfill(symbol, start_date, end_date)
```

#### 5.1.3 数据质量检查任务
```python
@celery_app.task
def data_quality_check():
    """数据质量检查任务"""
    checker = DataQualityChecker()
    return checker.run_checks()
```

### 5.2 任务调度策略

#### 5.2.1 定时任务配置
```python
CELERYBEAT_SCHEDULE = {
    'collect-realtime-data': {
        'task': 'data_collection.tasks.collect_realtime_data',
        'schedule': timedelta(seconds=30),  # 每30秒执行一次
        'args': (['000001.SZ', '000002.SZ'],)
    },
    'daily-data-sync': {
        'task': 'data_collection.tasks.sync_daily_data',
        'schedule': crontab(hour=18, minute=0),  # 每天18:00执行
    },
    'data-quality-check': {
        'task': 'data_collection.tasks.data_quality_check',
        'schedule': crontab(hour=2, minute=0),   # 每天02:00执行
    }
}
```

## 6. 数据源适配器

### 6.1 通用数据源接口
```python
class DataSourceAdapter(ABC):
    """数据源适配器基类"""
    
    @abstractmethod
    async def get_realtime_quote(self, symbol: str) -> RealtimeQuote:
        """获取实时行情"""
        pass
    
    @abstractmethod
    async def get_kline_data(self, symbol: str, interval: str, 
                           start_time: datetime, end_time: datetime) -> List[KlineData]:
        """获取K线数据"""
        pass
    
    @abstractmethod
    async def get_trade_data(self, symbol: str, date: str) -> List[TradeData]:
        """获取成交数据"""
        pass
```

### 6.2 具体数据源实现

#### 6.2.1 东方财富适配器
```python
class EastMoneyAdapter(DataSourceAdapter):
    """东方财富数据源适配器"""
    
    def __init__(self):
        self.base_url = "http://push2.eastmoney.com"
        self.rate_limiter = TokenBucket(capacity=100, refill_rate=10)
    
    async def get_realtime_quote(self, symbol: str) -> RealtimeQuote:
        if not self.rate_limiter.consume():
            raise RateLimitExceeded("Rate limit exceeded")
        
        # 实现具体的数据获取逻辑
        return await self._fetch_realtime_data(symbol)
```

#### 6.2.2 同花顺适配器
```python
class ThsAdapter(DataSourceAdapter):
    """同花顺数据源适配器"""
    # 类似实现
```

## 7. 数据质量监控

### 7.1 质量检查规则

#### 7.1.1 数据完整性检查
```yaml
完整性检查:
  - 必填字段检查
  - 数据连续性检查
  - 时间序列完整性
  - 缺失数据统计

时效性检查:
  - 数据延迟监控
  - 实时数据新鲜度
  - 更新频率检查
  - 过期数据清理

准确性检查:
  - 异常值检测
  - 数据范围验证
  - 逻辑一致性检查
  - 多源数据对比
```

#### 7.1.2 异常数据处理
```python
class DataQualityChecker:
    """数据质量检查器"""
    
    def check_price_anomaly(self, quote: RealtimeQuote) -> bool:
        """检查价格异常"""
        # 价格涨跌幅检查
        if abs(quote.change_percent) > 20:
            self.logger.warning(f"Price anomaly detected: {quote.symbol}")
            return False
        
        # 价格范围检查
        if quote.price <= 0:
            self.logger.error(f"Invalid price: {quote.symbol}")
            return False
        
        return True
```

## 8. 监控和告警

### 8.1 系统监控指标

#### 8.1.1 业务指标
```yaml
数据采集指标:
  - 每秒处理请求数 (QPS)
  - 数据采集成功率
  - 平均响应时间
  - 数据覆盖率

数据质量指标:
  - 数据完整性百分比
  - 异常数据占比
  - 数据延迟时间
  - 源数据一致性

任务执行指标:
  - 任务成功率
  - 任务执行时间
  - 队列长度
  - 失败重试次数
```

#### 8.1.2 技术指标
```yaml
系统性能:
  - CPU使用率
  - 内存使用率
  - 网络I/O
  - 磁盘I/O

数据库性能:
  - 连接池使用率
  - 查询响应时间
  - 事务吞吐量
  - 锁等待时间

缓存性能:
  - 缓存命中率
  - 缓存响应时间
  - 内存使用情况
  - 键过期统计
```

### 8.2 告警配置
```yaml
告警规则:
  - 数据采集失败率 > 5%
  - API响应时间 > 5秒
  - 数据延迟 > 10分钟
  - 系统资源使用率 > 85%

告警级别:
  - Critical: 影响核心业务
  - Warning: 需要关注
  - Info: 信息通知

通知方式:
  - 钉钉/企业微信
  - 邮件通知
  - 短信通知
  - Webhook
```

## 9. 部署配置

### 9.1 环境变量
```bash
# 数据库配置
DATABASE_URL=postgresql://user:pass@host:port/db
INFLUXDB_URL=http://host:8086
INFLUXDB_TOKEN=your_token
INFLUXDB_ORG=your_org
INFLUXDB_BUCKET=market_data

# Redis配置
REDIS_URL=redis://host:port/db
CELERY_BROKER_URL=redis://host:port/0
CELERY_RESULT_BACKEND=redis://host:port/1

# 数据源API配置
EASTMONEY_API_KEY=your_api_key
THS_API_KEY=your_api_key
RATE_LIMIT_PER_MINUTE=1000

# 监控配置
PROMETHEUS_PORT=9090
GRAFANA_URL=http://host:3000
```

### 9.2 Docker Compose配置
```yaml
version: '3.8'

services:
  data-collector:
    build: .
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
    depends_on:
      - redis
      - postgres
      - influxdb
    
  celery-worker:
    build: .
    command: celery -A data_collection worker --loglevel=info
    environment:
      - CELERY_BROKER_URL=${REDIS_URL}
    depends_on:
      - redis
    
  celery-beat:
    build: .
    command: celery -A data_collection beat --loglevel=info
    environment:
      - CELERY_BROKER_URL=${REDIS_URL}
    depends_on:
      - redis
```

## 10. 扩展功能

### 10.1 数据源扩展
- 国际市场数据接入
- 加密货币数据
- 期货期权数据
- 宏观经济数据

### 10.2 高级功能
- 机器学习数据预处理
- 实时数据流分析
- 数据血缘追踪
- 智能数据修复 