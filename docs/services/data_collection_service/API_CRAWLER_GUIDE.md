# API爬虫使用指南

本指南详细介绍如何使用API JSON方式进行数据采集，以Twitter API v2采集马斯克推文为例。

## 目录

1. [概述](#概述)
2. [API方式 vs Web方式](#api方式-vs-web方式)
3. [Twitter API设置](#twitter-api设置)
4. [配置详解](#配置详解)
5. [运行示例](#运行示例)
6. [数据结构](#数据结构)
7. [错误处理](#错误处理)
8. [最佳实践](#最佳实践)

## 概述

API爬虫是一种通过调用目标平台的API接口来获取数据的方式。相比传统的网页爬虫，API方式具有以下优势：

- **数据质量高**：获取结构化的JSON数据
- **效率更高**：直接数据传输，无需页面渲染
- **稳定性好**：API版本相对稳定
- **功能完整**：可获取完整的数据字段和元信息

## API方式 vs Web方式

| 对比维度 | API方式 | Web方式 |
|----------|---------|---------|
| **数据获取方式** | HTTP API调用 | 浏览器页面抓取 |
| **数据格式** | JSON/XML等结构化数据 | HTML页面内容 |
| **性能表现** | 高效，直接数据传输 | 较慢，需要页面渲染 |
| **稳定性** | API版本稳定 | 易受页面变更影响 |
| **认证要求** | 需要API密钥/Token | 通常无需特殊认证 |
| **频率限制** | API官方限制 | 需要模拟人类行为 |
| **数据完整性** | 完整的官方数据 | 可能受页面限制 |
| **开发难度** | 配置相对简单 | 需要分析页面结构 |
| **维护成本** | API变更较少 | 需要适配页面变化 |

## Twitter API设置

### 1. 申请开发者账号

1. 访问 [Twitter开发者平台](https://developer.twitter.com/)
2. 使用Twitter账号登录
3. 填写开发者申请表单
4. 等待审核通过

### 2. 创建应用

1. 在开发者控制台创建新应用
2. 填写应用信息：
   - 应用名称
   - 应用描述
   - 网站URL
   - 使用场景说明

### 3. 获取认证凭据

Twitter API v2支持多种认证方式：

#### Bearer Token (推荐)
- 用于只读访问
- 无需用户授权
- 适合服务端应用

#### OAuth 2.0
- 用于代表用户操作
- 需要用户授权
- 支持更多操作权限

#### OAuth 1.0a
- 传统认证方式
- 需要API Key和Secret
- 兼容性好

### 4. 配置环境变量

```bash
# 方式1：命令行设置
export TWITTER_BEARER_TOKEN='your_bearer_token_here'

# 方式2：添加到.env.develop文件
echo "TWITTER_BEARER_TOKEN=your_bearer_token_here" >> .env.develop

# 可选：其他认证信息
export TWITTER_API_KEY='your_api_key'
export TWITTER_API_SECRET='your_api_secret'
export TWITTER_ACCESS_TOKEN='your_access_token'
export TWITTER_ACCESS_TOKEN_SECRET='your_access_token_secret'
```

## 配置详解

### 数据源配置

```python
datasource = DataSource(
    name="马斯克X账号-API",
    collection_method=CollectionMethod.API_JSON,  # 指定API JSON方式
    content_category=ContentCategory.SOCIAL_MEDIA,
    base_url="https://api.twitter.com/2/users/44196397/tweets",  # 马斯克用户ID
    description="通过Twitter API v2采集马斯克推文",
    crawl_mode=CrawlMode.INTERVAL,
    crawl_interval=1800,  # 30分钟采集一次
    priority=8,
    use_proxy=False,  # API通常不需要代理
    request_delay_min=1,   # API调用间隔
    request_delay_max=2,
    max_concurrent_tasks=1,
    tags=["马斯克", "特斯拉", "SpaceX", "Twitter API"]
)
```

### API认证配置

```python
headers_config = {
    "Authorization": f"Bearer {os.getenv('TWITTER_BEARER_TOKEN')}",
    "Content-Type": "application/json",
    "User-Agent": "FinSight-Crawler/1.0"
}
```

### JSON数据提取规则

```python
extraction_rules = {
    # 数据路径：Twitter API v2在response.data中返回推文数组
    "data_path": "$.data",
    
    # 字段映射：将API字段映射到系统标准字段
    "title_field": "text",          # 推文内容作为标题
    "content_field": "text",        # 推文内容
    "author_field": "author_id",    # 作者ID
    "time_field": "created_at",     # 创建时间
    "url_field": "id",              # 推文ID（后续转换为URL）
    
    # 额外字段：保存Twitter特有的数据
    "extra_fields": {
        "tweet_id": "id",
        "public_metrics": "public_metrics",      # 点赞、转发数据
        "context_annotations": "context_annotations",  # 内容标注
        "entities": "entities",                  # 实体信息（链接、标签等）
        "lang": "lang",                         # 语言
        "possibly_sensitive": "possibly_sensitive",
        "referenced_tweets": "referenced_tweets",  # 引用推文
        "reply_settings": "reply_settings"
    },
    
    # 过滤条件
    "filters": {
        "min_content_length": 1,
        "max_content_length": 5000,
        "exclude_retweets": False,
        "exclude_replies": False
    }
}
```

### API请求配置

```python
api_config = {
    "method": "GET",
    "params": {
        # 用户字段：获取用户相关信息
        "user.fields": "created_at,description,entities,id,location,name,pinned_tweet_id,profile_image_url,protected,public_metrics,url,username,verified,verified_type",
        
        # 推文字段：获取推文详细信息
        "tweet.fields": "attachments,author_id,context_annotations,conversation_id,created_at,entities,geo,id,in_reply_to_user_id,lang,possibly_sensitive,public_metrics,referenced_tweets,reply_settings,source,text,withheld",
        
        # 扩展字段：包含关联数据
        "expansions": "author_id,referenced_tweets.id,referenced_tweets.id.author_id",
        
        # 结果数量：每次最多获取的推文数
        "max_results": "10",
        
        # 排除条件：可选择排除转推和回复
        "exclude": "retweets,replies"
    },
    "timeout": 30,
    "retry_times": 3,
    "retry_delay": 5
}
```

### 数据验证规则

```python
validation_rules = {
    "required_fields": ["text", "id", "created_at"],
    "min_content_length": 1,
    "max_content_length": 5000,
    "content_filters": []  # 可添加内容过滤规则
}
```

## 运行示例

### 1. 环境准备

```bash
# 激活虚拟环境
source venv/finsight/bin/activate

# 配置Twitter API认证
export TWITTER_BEARER_TOKEN='your_bearer_token'
```

### 2. 运行示例脚本

```bash
python examples/musk_api_crawler_quickstart.py
```

### 3. 示例输出

```
🚀 Twitter API爬虫快速入门
==================================================

📋 Twitter API 设置指南
============================================================
1. 访问 https://developer.twitter.com/
2. 申请开发者账号并创建应用
3. 获取Bearer Token
4. 在环境变量中配置：
   export TWITTER_BEARER_TOKEN='your_bearer_token'
   或在 .env.develop 文件中添加：
   TWITTER_BEARER_TOKEN=your_bearer_token
============================================================

🔧 设置Twitter API数据源...
✅ 数据源创建成功，ID: 1

⚙️  设置Twitter API爬虫配置...
✅ API爬虫配置创建成功，版本: 1

📄 Twitter API v2 响应示例
============================================================
{
  "data": [
    {
      "id": "1234567890123456789",
      "text": "Exciting progress on sustainable energy! 🚀",
      "created_at": "2024-01-15T10:30:00.000Z",
      "author_id": "44196397",
      "public_metrics": {
        "retweet_count": 1234,
        "like_count": 5678,
        "reply_count": 234,
        "quote_count": 56
      }
    }
  ]
}
============================================================

📋 创建Twitter API测试任务...
✅ API测试任务创建成功，ID: 1

是否运行API爬虫测试？(y/N): y

🚀 开始运行Twitter API爬虫测试...
📡 正在连接到Twitter API...
⏳ 开始API数据采集...
✅ API测试完成: 成功采集 10/10 个数据项

📊 Twitter API爬虫配置总结
============================================================
数据源名称: 马斯克X账号-API
数据源ID: 1
采集方式: api_json
目标API: https://api.twitter.com/2/users/44196397/tweets
配置版本: 1
采集间隔: 1800秒

主要特点:
✅ 使用Twitter API v2进行数据采集
✅ 支持认证和频率限制管理
✅ 自动解析JSON响应结构
✅ 提取完整的推文元数据
✅ 支持用户信息和推文指标
✅ 可配置的数据过滤规则

⚠️  注意事项:
- 需要有效的Twitter API认证
- 遵守API频率限制（每15分钟300次请求）
- Academic Research产品轨道有更高限额
- 监控API使用量避免超限
============================================================

🎉 Twitter API爬虫配置完成！
💡 提示：配置好API认证后即可开始正式采集
```

## 数据结构

### Twitter API v2响应结构

```json
{
  "data": [
    {
      "id": "1234567890123456789",
      "text": "推文内容",
      "created_at": "2024-01-15T10:30:00.000Z",
      "author_id": "44196397",
      "public_metrics": {
        "retweet_count": 1234,
        "like_count": 5678,
        "reply_count": 234,
        "quote_count": 56
      },
      "context_annotations": [
        {
          "domain": {
            "id": "65",
            "name": "Interests and Hobbies Vertical"
          },
          "entity": {
            "id": "848920371311001600",
            "name": "Technology"
          }
        }
      ],
      "entities": {
        "urls": [
          {
            "start": 10,
            "end": 33,
            "url": "https://t.co/xyz",
            "expanded_url": "https://example.com",
            "display_url": "example.com"
          }
        ],
        "hashtags": [
          {
            "start": 34,
            "end": 44,
            "tag": "technology"
          }
        ]
      },
      "lang": "en"
    }
  ],
  "includes": {
    "users": [
      {
        "id": "44196397",
        "name": "Elon Musk",
        "username": "elonmusk",
        "verified": true,
        "public_metrics": {
          "followers_count": 150000000,
          "following_count": 200,
          "tweet_count": 25000,
          "listed_count": 100000
        }
      }
    ]
  },
  "meta": {
    "result_count": 1,
    "next_token": "b26v89c19zqg8o3fpz6r9wm6jqzajqxzm"
  }
}
```

### 系统存储结构

经过处理后，数据将按以下结构存储：

```json
{
  "title": "推文内容",
  "content": "推文内容",
  "author": "44196397",
  "publish_time": "2024-01-15T10:30:00.000Z",
  "url": "https://twitter.com/elonmusk/status/1234567890123456789",
  "extra_data": {
    "tweet_id": "1234567890123456789",
    "public_metrics": {
      "retweet_count": 1234,
      "like_count": 5678,
      "reply_count": 234,
      "quote_count": 56
    },
    "context_annotations": [...],
    "entities": {...},
    "lang": "en"
  },
  "hash_content": "abc123...",
  "created_at": "2024-01-15T10:30:00.000Z"
}
```

## 错误处理

### 常见错误及解决方案

#### 1. 认证错误
```
Error 401: Unauthorized
```
**解决方案**：
- 检查Bearer Token是否正确
- 确认Token是否已过期
- 验证应用权限设置

#### 2. 频率限制
```
Error 429: Too Many Requests
```
**解决方案**：
- 等待15分钟后重试
- 减少请求频率
- 考虑升级API计划

#### 3. 用户不存在
```
Error 404: Not Found
```
**解决方案**：
- 确认用户ID或用户名正确
- 检查用户是否已被暂停
- 验证用户隐私设置

#### 4. 网络超时
```
TimeoutError: Request timeout
```
**解决方案**：
- 检查网络连接
- 增加超时时间
- 实施重试机制

### 错误监控

系统提供以下监控机制：

- **请求日志**：记录所有API请求和响应
- **错误统计**：统计各类错误的发生频率
- **性能监控**：监控API响应时间
- **限制预警**：接近频率限制时发出预警

## 最佳实践

### 1. API使用优化

#### 频率限制管理
```python
# 在配置中设置合理的延迟
anti_crawler_config = {
    "random_delays": True,
    "delay_range": [1, 3],
    "respect_rate_limit": True,
    "rate_limit_buffer": 0.1  # 保留10%缓冲
}
```

#### 批量请求
```python
# 充分利用max_results参数
api_config = {
    "params": {
        "max_results": "100",  # 根据API限制调整
    }
}
```

### 2. 数据质量控制

#### 字段验证
```python
validation_rules = {
    "required_fields": ["text", "id", "created_at"],
    "min_content_length": 1,
    "content_filters": ["spam", "bot"]
}
```

#### 重复检测
系统自动基于内容哈希进行重复检测，无需额外配置。

### 3. 性能优化

#### 并发控制
```python
# 合理设置并发数
max_concurrent_tasks = 1  # API通常建议单线程
```

#### 缓存策略
- 缓存用户信息减少API调用
- 缓存已处理的推文ID避免重复处理

### 4. 监控告警

#### 关键指标
- API调用成功率
- 数据采集完成率
- 平均响应时间
- 错误类型分布

#### 告警规则
- API错误率超过5%
- 连续失败超过3次
- 接近频率限制阈值

### 5. 数据备份

#### 原始数据保留
```python
# 保存完整的API响应
"raw_response": api_response
```

#### 增量备份
- 定期导出新采集的数据
- 保持多个版本的数据副本

## 扩展开发

### 支持其他API平台

可以参考Twitter API的实现，扩展支持其他平台：

1. **LinkedIn API** - 职业社交网络数据
2. **YouTube API** - 视频和评论数据
3. **Reddit API** - 社区讨论数据
4. **GitHub API** - 代码仓库和开发者数据

### 自定义字段处理

```python
# 添加自定义字段处理逻辑
def custom_field_processor(item, config):
    # 自定义处理逻辑
    if "custom_field" in item:
        # 处理特殊字段
        pass
    return item
```

### 数据增强

```python
# 添加数据增强功能
def enhance_tweet_data(tweet_data):
    # 情感分析
    tweet_data["sentiment"] = analyze_sentiment(tweet_data["text"])
    
    # 主题分类
    tweet_data["topics"] = classify_topics(tweet_data["text"])
    
    # 实体识别
    tweet_data["entities"] = extract_entities(tweet_data["text"])
    
    return tweet_data
```

通过这些扩展，可以构建更加强大和智能的数据采集系统。 