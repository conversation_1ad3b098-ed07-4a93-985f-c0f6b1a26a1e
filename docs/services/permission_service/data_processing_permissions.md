# 数据处理模块权限管理文档

## 📖 概述

数据处理模块负责管理系统中的数据处理规则，实现从原始数据到业务数据的自动化转换。本文档详细说明了该模块的权限设计、角色分配和使用指南。

## 🔑 权限体系

### 权限分类

数据处理模块权限按照资源和操作进行分类：

#### 1. 规则管理权限 (Rule Management)

| 权限编码 | 权限名称 | 权限描述 | 操作范围 |
|----------|----------|----------|----------|
| `data_processing.rule.create` | 创建数据处理规则 | 创建新的数据处理规则配置 | 单个规则创建 |
| `data_processing.rule.read` | 查看数据处理规则 | 查看数据处理规则详细信息 | 单个规则查看 |
| `data_processing.rule.update` | 更新数据处理规则 | 修改数据处理规则配置 | 单个规则修改 |
| `data_processing.rule.delete` | 删除数据处理规则 | 删除指定的数据处理规则 | 单个规则删除 |
| `data_processing.rule.manage` | 管理数据处理规则 | 完整管理数据处理规则（包含所有操作） | 所有规则操作 |
| `data_processing.rule.test` | 测试数据处理规则 | 对数据处理规则进行测试验证 | 规则测试验证 |

#### 2. 列表和查询权限 (List & Query)

| 权限编码 | 权限名称 | 权限描述 | 操作范围 |
|----------|----------|----------|----------|
| `data_processing.list.read` | 查看处理规则列表 | 查看数据处理规则列表和搜索 | 规则列表查看 |
| `data_processing.stats.read` | 查看处理规则统计 | 查看数据处理规则执行统计数据 | 统计信息查看 |

#### 3. 批量操作权限 (Batch Operations)

| 权限编码 | 权限名称 | 权限描述 | 操作范围 |
|----------|----------|----------|----------|
| `data_processing.batch.update` | 批量更新处理规则 | 批量更新处理规则状态和优先级 | 批量操作 |

#### 4. 状态管理权限 (Status Management)

| 权限编码 | 权限名称 | 权限描述 | 操作范围 |
|----------|----------|----------|----------|
| `data_processing.status.read` | 查看处理状态 | 查看数据处理状态和进度 | 状态监控 |
| `data_processing.status.update` | 更新处理状态 | 更新数据处理状态和配置 | 状态管理 |

## 👥 角色权限分配

### 角色权限矩阵

| 角色 | 规则CRUD | 列表查看 | 统计查看 | 批量操作 | 状态管理 | 规则测试 |
|------|----------|----------|----------|----------|----------|----------|
| **Admin** | ✅ 全部 | ✅ | ✅ | ✅ | ✅ | ✅ |
| **DataAdmin** | ✅ 全部 | ✅ | ✅ | ✅ | ✅ | ✅ |
| **Analyst** | ❌ 仅查看 | ✅ | ✅ | ❌ | ✅ 仅查看 | ❌ |
| **Editor** | ❌ | ❌ | ❌ | ❌ | ❌ | ❌ |
| **User** | ❌ | ❌ | ❌ | ❌ | ❌ | ❌ |

### 详细权限分配

#### 系统管理员 (Admin)
```json
{
  "role": "Admin",
  "permissions": [
    "data_processing.rule.create",
    "data_processing.rule.read", 
    "data_processing.rule.update",
    "data_processing.rule.delete",
    "data_processing.rule.manage",
    "data_processing.rule.test",
    "data_processing.list.read",
    "data_processing.stats.read",
    "data_processing.batch.update",
    "data_processing.status.read",
    "data_processing.status.update"
  ]
}
```

#### 数据管理员 (DataAdmin)
```json
{
  "role": "DataAdmin", 
  "permissions": [
    "data_processing.rule.create",
    "data_processing.rule.read",
    "data_processing.rule.update", 
    "data_processing.rule.delete",
    "data_processing.rule.manage",
    "data_processing.rule.test",
    "data_processing.list.read",
    "data_processing.stats.read",
    "data_processing.batch.update",
    "data_processing.status.read",
    "data_processing.status.update"
  ]
}
```

#### 数据分析师 (Analyst)
```json
{
  "role": "Analyst",
  "permissions": [
    "data_processing.rule.read",
    "data_processing.list.read", 
    "data_processing.stats.read",
    "data_processing.status.read"
  ]
}
```

## 🛠️ API 接口权限

### B端管理接口权限要求

| HTTP方法 | API路径 | 权限要求 | 功能说明 |
|----------|---------|----------|----------|
| `GET` | `/api/v1/admin/data-processing/rules` | `data_processing.list.read` | 获取数据处理规则列表，支持分页和筛选 |
| `POST` | `/api/v1/admin/data-processing/rules` | `data_processing.rule.create` | 创建新的数据处理规则 |
| `GET` | `/api/v1/admin/data-processing/rules/{id}` | `data_processing.rule.read` | 获取指定ID的数据处理规则详情 |
| `PUT` | `/api/v1/admin/data-processing/rules/{id}` | `data_processing.rule.update` | 更新指定ID的数据处理规则 |
| `DELETE` | `/api/v1/admin/data-processing/rules/{id}` | `data_processing.rule.delete` | 删除指定ID的数据处理规则 |
| `GET` | `/api/v1/admin/data-processing/rules/stats` | `data_processing.stats.read` | 获取数据处理规则执行统计信息 |
| `GET` | `/api/v1/admin/data-processing/rules/business-type/{type}/active` | `data_processing.list.read` | 获取指定业务类型的活跃规则列表 |
| `PATCH` | `/api/v1/admin/data-processing/rules/batch/status` | `data_processing.batch.update` | 批量更新数据处理规则状态 |
| `PATCH` | `/api/v1/admin/data-processing/rules/batch/priority` | `data_processing.batch.update` | 批量更新数据处理规则优先级 |
| `POST` | `/api/v1/admin/data-processing/rules/{id}/test` | `data_processing.rule.test` | 测试指定ID的数据处理规则 |

### 权限验证示例

```python
# admin_router.py 中的权限验证示例

@router.post("/rules", response_model=DataProcessingRuleResponse)
async def create_processing_rule(
    rule_data: DataProcessingRuleCreate,
    current_user: User = Depends(require_permission("data_processing.rule.create")),
    service: DataProcessingRuleService = Depends(get_data_processing_rule_service),
    db: Session = Depends(get_db)
):
    """创建数据处理规则"""
    # 权限验证通过后的业务逻辑
    pass

@router.get("/rules", response_model=DataProcessingRuleListResponse)  
async def get_processing_rules(
    current_user: User = Depends(require_permission("data_processing.list.read")),
    # ... 其他参数
):
    """获取数据处理规则列表"""
    # 权限验证通过后的业务逻辑
    pass
```

## 🎯 前端权限集成

### 1. 权限常量定义

```javascript
// permissions/constants.js
export const DATA_PROCESSING_PERMISSIONS = {
  // 规则管理
  RULE_CREATE: 'data_processing.rule.create',
  RULE_READ: 'data_processing.rule.read', 
  RULE_UPDATE: 'data_processing.rule.update',
  RULE_DELETE: 'data_processing.rule.delete',
  RULE_MANAGE: 'data_processing.rule.manage',
  RULE_TEST: 'data_processing.rule.test',
  
  // 列表和统计
  LIST_READ: 'data_processing.list.read',
  STATS_READ: 'data_processing.stats.read',
  
  // 批量操作
  BATCH_UPDATE: 'data_processing.batch.update',
  
  // 状态管理
  STATUS_READ: 'data_processing.status.read',
  STATUS_UPDATE: 'data_processing.status.update'
};
```

### 2. Vue.js 组件权限控制

```vue
<template>
  <div class="data-processing-management">
    <!-- 创建规则按钮 -->
    <el-button 
      v-permission="DATA_PROCESSING_PERMISSIONS.RULE_CREATE"
      type="primary" 
      @click="handleCreateRule">
      创建处理规则
    </el-button>
    
    <!-- 规则列表 -->
    <el-table v-loading="loading" :data="ruleList">
      <el-table-column prop="rule_name" label="规则名称" />
      <el-table-column prop="business_data_type" label="业务类型" />
      <el-table-column prop="priority" label="优先级" />
      <el-table-column label="操作" width="300">
        <template #default="{ row }">
          <!-- 查看详情 -->
          <el-button 
            v-permission="DATA_PROCESSING_PERMISSIONS.RULE_READ"
            size="small" 
            @click="handleViewRule(row)">
            查看
          </el-button>
          
          <!-- 编辑规则 -->
          <el-button 
            v-permission="DATA_PROCESSING_PERMISSIONS.RULE_UPDATE"
            size="small" 
            type="primary"
            @click="handleEditRule(row)">
            编辑
          </el-button>
          
          <!-- 测试规则 -->
          <el-button 
            v-permission="DATA_PROCESSING_PERMISSIONS.RULE_TEST"
            size="small" 
            type="warning"
            @click="handleTestRule(row)">
            测试
          </el-button>
          
          <!-- 删除规则 -->
          <el-button 
            v-permission="DATA_PROCESSING_PERMISSIONS.RULE_DELETE"
            size="small" 
            type="danger"
            @click="handleDeleteRule(row)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 批量操作 -->
    <div v-permission="DATA_PROCESSING_PERMISSIONS.BATCH_UPDATE" class="batch-operations">
      <el-button @click="handleBatchUpdateStatus">批量启用/禁用</el-button>
      <el-button @click="handleBatchUpdatePriority">批量修改优先级</el-button>
    </div>
    
    <!-- 统计信息 -->
    <div v-permission="DATA_PROCESSING_PERMISSIONS.STATS_READ" class="statistics">
      <DataProcessingStats :stats="stats" />
    </div>
  </div>
</template>

<script setup>
import { DATA_PROCESSING_PERMISSIONS } from '@/permissions/constants';
// ... 其他逻辑
</script>
```

### 3. React 组件权限控制

```jsx
// DataProcessingManagement.jsx
import React from 'react';
import { Button, Table, Space } from 'antd';
import { PermissionWrapper } from '@/components/PermissionWrapper';
import { DATA_PROCESSING_PERMISSIONS } from '@/constants/permissions';

const DataProcessingManagement = () => {
  const columns = [
    {
      title: '规则名称',
      dataIndex: 'rule_name',
      key: 'rule_name',
    },
    {
      title: '业务类型', 
      dataIndex: 'business_data_type',
      key: 'business_data_type',
    },
    {
      title: '优先级',
      dataIndex: 'priority', 
      key: 'priority',
    },
    {
      title: '操作',
      key: 'action',
      render: (text, record) => (
        <Space size="middle">
          <PermissionWrapper permission={DATA_PROCESSING_PERMISSIONS.RULE_READ}>
            <Button size="small" onClick={() => handleViewRule(record)}>
              查看
            </Button>
          </PermissionWrapper>
          
          <PermissionWrapper permission={DATA_PROCESSING_PERMISSIONS.RULE_UPDATE}>
            <Button size="small" type="primary" onClick={() => handleEditRule(record)}>
              编辑
            </Button>
          </PermissionWrapper>
          
          <PermissionWrapper permission={DATA_PROCESSING_PERMISSIONS.RULE_TEST}>
            <Button size="small" onClick={() => handleTestRule(record)}>
              测试
            </Button>
          </PermissionWrapper>
          
          <PermissionWrapper permission={DATA_PROCESSING_PERMISSIONS.RULE_DELETE}>
            <Button size="small" danger onClick={() => handleDeleteRule(record)}>
              删除
            </Button>
          </PermissionWrapper>
        </Space>
      ),
    },
  ];

  return (
    <div className="data-processing-management">
      <div className="toolbar">
        <PermissionWrapper permission={DATA_PROCESSING_PERMISSIONS.RULE_CREATE}>
          <Button type="primary" onClick={handleCreateRule}>
            创建处理规则
          </Button>
        </PermissionWrapper>
        
        <PermissionWrapper permission={DATA_PROCESSING_PERMISSIONS.BATCH_UPDATE}>
          <Space>
            <Button onClick={handleBatchUpdateStatus}>批量启用/禁用</Button>
            <Button onClick={handleBatchUpdatePriority}>批量修改优先级</Button>
          </Space>
        </PermissionWrapper>
      </div>
      
      <Table 
        columns={columns} 
        dataSource={ruleList} 
        loading={loading}
        rowKey="id"
      />
      
      <PermissionWrapper permission={DATA_PROCESSING_PERMISSIONS.STATS_READ}>
        <DataProcessingStats stats={stats} />
      </PermissionWrapper>
    </div>
  );
};

export default DataProcessingManagement;
```

### 4. 路由权限配置

```javascript
// router/index.js
const dataProcessingRoutes = [
  {
    path: '/admin/data-processing',
    name: 'DataProcessing',
    component: () => import('@/views/admin/DataProcessing'),
    meta: {
      title: '数据处理管理',
      permission: DATA_PROCESSING_PERMISSIONS.LIST_READ,
      roles: ['Admin', 'DataAdmin', 'Analyst']
    },
    children: [
      {
        path: 'rules',
        name: 'DataProcessingRules', 
        component: () => import('@/views/admin/DataProcessing/Rules'),
        meta: {
          title: '处理规则管理',
          permission: DATA_PROCESSING_PERMISSIONS.LIST_READ
        }
      },
      {
        path: 'rules/create',
        name: 'CreateDataProcessingRule',
        component: () => import('@/views/admin/DataProcessing/CreateRule'),
        meta: {
          title: '创建处理规则',
          permission: DATA_PROCESSING_PERMISSIONS.RULE_CREATE
        }
      },
      {
        path: 'rules/:id/edit',
        name: 'EditDataProcessingRule', 
        component: () => import('@/views/admin/DataProcessing/EditRule'),
        meta: {
          title: '编辑处理规则',
          permission: DATA_PROCESSING_PERMISSIONS.RULE_UPDATE
        }
      },
      {
        path: 'stats',
        name: 'DataProcessingStats',
        component: () => import('@/views/admin/DataProcessing/Stats'),
        meta: {
          title: '处理统计',
          permission: DATA_PROCESSING_PERMISSIONS.STATS_READ
        }
      }
    ]
  }
];
```

## 🔒 安全注意事项

### 1. 权限验证原则

- **后端强制验证**：所有权限验证必须在后端进行，前端权限控制仅用于用户体验优化
- **最小权限原则**：用户只获得执行其职责所需的最小权限集合
- **权限分离**：读取和写入权限分离，查看和操作权限分离

### 2. 敏感操作保护

- **删除操作**：需要二次确认和管理员权限
- **批量操作**：限制单次操作的数量，记录操作日志
- **规则测试**：在隔离环境中进行，不影响生产数据

### 3. 审计和监控

- **操作日志**：记录所有权限验证和操作行为
- **异常监控**：监控权限验证失败和异常操作
- **定期审查**：定期审查用户权限分配的合理性

## 📋 权限检查清单

### 开发阶段检查

- [ ] 所有API接口都有正确的权限验证装饰器
- [ ] 前端组件使用权限指令或包装器
- [ ] 路由配置包含必要的权限元信息
- [ ] 权限常量定义完整且一致

### 测试阶段检查  

- [ ] 不同角色用户的权限验证正确
- [ ] 无权限用户无法访问受保护资源
- [ ] 权限变更后系统行为正确
- [ ] 异常情况下的权限处理正确

### 部署阶段检查

- [ ] 生产环境权限配置正确
- [ ] 默认管理员账户权限配置
- [ ] 权限数据库迁移脚本执行成功
- [ ] 审计日志功能正常运行

## 📚 相关文档

- [权限系统总览](./permissions_overview.md) - 完整权限系统说明
- [RBAC设计文档](./rbac_design.md) - 详细的权限系统设计
- [前端权限集成指南](./frontend_usage_guide.md) - 前端权限使用详细指南
- [API实现文档](./api_implementation.md) - API接口实现详情 