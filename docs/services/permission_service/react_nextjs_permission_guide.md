# FinSight React+NextJS 权限控制方案

基于 FinSight 后端 RBAC 系统的 React+TypeScript+NextJS+Ant Design 前端权限控制完整方案。

## 一、权限数据流转架构

### 1.1 FinSight 权限数据结构

根据后端系统，权限数据包含：

```typescript
// 权限数据结构
interface UserPermissionData {
  user: {
    id: number;
    username: string;
    email: string;
  };
  roles: Role[];
  permissions: string[];
  token: {
    access_token: string;
    refresh_token: string;
    expires_at: string;
  };
}

interface Role {
  id: number;
  name: 'Admin' | 'DataAdmin' | 'Analyst' | 'Editor' | 'AccountManager' | 'RiskOfficer' | 'User' | 'Guest';
  description: string;
  permissions: Permission[];
}

interface Permission {
  id: number;
  code: string; // 格式: {模块}.{资源}.{操作}
  name: string;
  module: string;
  resource: string;
  action: string;
}
```

### 1.2 权限获取与缓存策略

```typescript
// src/services/auth.service.ts
import { PermissionConfig } from '../config/permissions';

class AuthService {
  // 获取用户权限信息
  async getUserPermissions(): Promise<UserPermissionData> {
    const response = await fetch('/api/v1/permissions/me', {
      headers: { Authorization: `Bearer ${this.getToken()}` }
    });
    return response.json();
  }

  // 权限缓存策略
  cachePermissions(data: UserPermissionData) {
    // 1. Redux store 存储
    store.dispatch(setUserPermissions(data));
    
    // 2. IndexedDB 持久化
    const encryptedData = this.encrypt(JSON.stringify(data));
    localStorage.setItem('finsight_permissions', encryptedData);
    
    // 3. 设置权限版本号
    localStorage.setItem('permission_version', data.permissions.length.toString());
  }

  // 权限变更检测
  async checkPermissionUpdate(): Promise<boolean> {
    const localVersion = localStorage.getItem('permission_version');
    const serverData = await this.getUserPermissions();
    return localVersion !== serverData.permissions.length.toString();
  }
}
```

## 二、路由级权限控制方案

### 2.1 路由配置与权限映射

```typescript
// src/config/routes.config.ts
export interface RouteConfig {
  path: string;
  component: React.ComponentType;
  meta: {
    title: string;
    requiredPermissions?: string[]; // FinSight 权限编码
    requiredRoles?: string[];       // FinSight 角色名称
    requiresAuth: boolean;
    level?: 'public' | 'user' | 'admin';
  };
  children?: RouteConfig[];
}

export const routeConfig: RouteConfig[] = [
  {
    path: '/dashboard',
    component: Dashboard,
    meta: {
      title: '仪表板',
      requiredPermissions: ['data.report.read'],
      requiresAuth: true
    }
  },
  {
    path: '/users',
    component: UserManagement,
    meta: {
      title: '用户管理',
      requiredPermissions: ['user.list.read'],
      requiredRoles: ['Admin', 'AccountManager'],
      requiresAuth: true,
      level: 'admin'
    }
  },
  {
    path: '/data/sources',
    component: DataSourceManagement,
    meta: {
      title: '数据源管理',
      requiredPermissions: ['data.source.read'],
      requiredRoles: ['DataAdmin', 'Admin'],
      requiresAuth: true
    }
  }
];
```

### 2.2 NextJS 路由守卫实现

```typescript
// src/components/guards/RouteGuard.tsx
import { useRouter } from 'next/router';
import { useSelector } from 'react-redux';
import { useEffect } from 'react';
import { checkPermissions, checkRoles } from '../utils/permission.utils';

interface RouteGuardProps {
  children: React.ReactNode;
  requiredPermissions?: string[];
  requiredRoles?: string[];
  fallback?: React.ReactNode;
}

export const RouteGuard: React.FC<RouteGuardProps> = ({
  children,
  requiredPermissions = [],
  requiredRoles = [],
  fallback
}) => {
  const router = useRouter();
  const { user, permissions, roles, isAuthenticated } = useSelector(state => state.auth);

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/login');
      return;
    }

    const hasPermission = checkPermissions(permissions, requiredPermissions);
    const hasRole = checkRoles(roles, requiredRoles);

    if (!hasPermission || !hasRole) {
      router.push('/403');
      return;
    }
  }, [isAuthenticated, permissions, roles, router]);

  if (!isAuthenticated || 
      !checkPermissions(permissions, requiredPermissions) || 
      !checkRoles(roles, requiredRoles)) {
    return fallback || <div>Loading...</div>;
  }

  return <>{children}</>;
};
```

### 2.3 NextJS getServerSideProps 权限校验

```typescript
// src/utils/auth.server.ts
import { GetServerSidePropsContext } from 'next';
import jwt from 'jsonwebtoken';

export const withAuth = (
  handler: Function,
  requiredPermissions: string[] = [],
  requiredRoles: string[] = []
) => {
  return async (context: GetServerSidePropsContext) => {
    const { req } = context;
    const token = req.cookies.access_token;

    if (!token) {
      return {
        redirect: {
          destination: '/login',
          permanent: false,
        },
      };
    }

    try {
      // 验证 JWT token
      const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any;
      
      // 获取用户权限（从缓存或数据库）
      const userPermissions = await getUserPermissions(decoded.sub);
      
      // 权限校验
      const hasPermission = requiredPermissions.every(perm => 
        userPermissions.permissions.includes(perm)
      );
      
      const hasRole = requiredRoles.length === 0 || 
        requiredRoles.some(role => 
          userPermissions.roles.some(r => r.name === role)
        );

      if (!hasPermission || !hasRole) {
        return {
          redirect: {
            destination: '/403',
            permanent: false,
          },
        };
      }

      return handler(context, userPermissions);
    } catch (error) {
      return {
        redirect: {
          destination: '/login',
          permanent: false,
        },
      };
    }
  };
};

// 使用示例
export const getServerSideProps = withAuth(
  async (context, userPermissions) => {
    return {
      props: {
        userPermissions,
      },
    };
  },
  ['user.list.read'], // 必需权限
  ['Admin', 'AccountManager'] // 必需角色
);
```

## 三、组件级权限控制方案

### 3.1 权限控制组件

```typescript
// src/components/permission/PermissionGate.tsx
import { useSelector } from 'react-redux';
import { checkPermissions, checkRoles } from '../../utils/permission.utils';

interface PermissionGateProps {
  children: React.ReactNode;
  permissions?: string[];
  roles?: string[];
  fallback?: React.ReactNode;
  mode?: 'any' | 'all'; // any: 满足任一条件, all: 满足所有条件
}

export const PermissionGate: React.FC<PermissionGateProps> = ({
  children,
  permissions = [],
  roles = [],
  fallback = null,
  mode = 'all'
}) => {
  const { permissions: userPermissions, roles: userRoles } = useSelector(state => state.auth);

  const hasPermission = mode === 'any' 
    ? permissions.some(p => userPermissions.includes(p))
    : permissions.every(p => userPermissions.includes(p));

  const hasRole = mode === 'any'
    ? roles.some(r => userRoles.some(ur => ur.name === r))
    : roles.every(r => userRoles.some(ur => ur.name === r));

  // 如果没有指定权限和角色，直接显示
  if (permissions.length === 0 && roles.length === 0) {
    return <>{children}</>;
  }

  // 权限校验
  const canAccess = (permissions.length === 0 || hasPermission) && 
                   (roles.length === 0 || hasRole);

  return canAccess ? <>{children}</> : <>{fallback}</>;
};
```

### 3.2 Ant Design 组件适配

```typescript
// src/components/permission/PermissionButton.tsx
import { Button, ButtonProps } from 'antd';
import { PermissionGate } from './PermissionGate';

interface PermissionButtonProps extends ButtonProps {
  permissions?: string[];
  roles?: string[];
}

export const PermissionButton: React.FC<PermissionButtonProps> = ({
  permissions,
  roles,
  children,
  ...buttonProps
}) => {
  return (
    <PermissionGate permissions={permissions} roles={roles}>
      <Button {...buttonProps}>
        {children}
      </Button>
    </PermissionGate>
  );
};

// 使用示例
<PermissionButton 
  permissions={['user.create']} 
  roles={['Admin', 'AccountManager']}
  type="primary"
  onClick={handleCreateUser}
>
  创建用户
</PermissionButton>
```

### 3.3 表格权限控制

```typescript
// src/components/permission/PermissionTable.tsx
import { Table, TableProps } from 'antd';
import { usePermissionColumns } from '../../hooks/usePermissionColumns';

interface PermissionTableProps extends TableProps<any> {
  permissionConfig?: Record<string, string[]>; // 列权限配置
}

export const PermissionTable: React.FC<PermissionTableProps> = ({
  columns = [],
  permissionConfig = {},
  ...tableProps
}) => {
  const filteredColumns = usePermissionColumns(columns, permissionConfig);

  return <Table {...tableProps} columns={filteredColumns} />;
};

// hooks/usePermissionColumns.ts
export const usePermissionColumns = (columns: any[], permissionConfig: Record<string, string[]>) => {
  const { permissions } = useSelector(state => state.auth);

  return useMemo(() => {
    return columns.filter(column => {
      const requiredPermissions = permissionConfig[column.key];
      if (!requiredPermissions) return true;
      
      return requiredPermissions.every(perm => permissions.includes(perm));
    });
  }, [columns, permissionConfig, permissions]);
};
```

## 四、权限状态管理方案

### 4.1 Redux Toolkit 权限状态

```typescript
// src/store/slices/authSlice.ts
import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';

interface AuthState {
  user: User | null;
  roles: Role[];
  permissions: string[];
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  permissionVersion: string;
}

// 异步获取权限
export const fetchUserPermissions = createAsyncThunk(
  'auth/fetchPermissions',
  async (_, { rejectWithValue }) => {
    try {
      const response = await fetch('/api/v1/permissions/me');
      if (!response.ok) throw new Error('获取权限失败');
      return await response.json();
    } catch (error) {
      return rejectWithValue(error.message);
    }
  }
);

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    setUserPermissions: (state, action) => {
      const { user, roles, permissions } = action.payload;
      state.user = user;
      state.roles = roles;
      state.permissions = permissions;
      state.isAuthenticated = true;
      state.permissionVersion = Date.now().toString();
    },
    clearAuth: (state) => {
      state.user = null;
      state.roles = [];
      state.permissions = [];
      state.isAuthenticated = false;
    },
    updatePermissionVersion: (state, action) => {
      state.permissionVersion = action.payload;
    }
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchUserPermissions.fulfilled, (state, action) => {
        state.user = action.payload.user;
        state.roles = action.payload.roles;
        state.permissions = action.payload.permissions;
        state.isAuthenticated = true;
        state.isLoading = false;
      });
  }
});
```

### 4.2 权限工具函数

```typescript
// src/utils/permission.utils.ts
import { PERMISSIONS, ROLES } from '../config/permissions';

// 检查权限
export const hasPermission = (userPermissions: string[], requiredPermission: string): boolean => {
  return userPermissions.includes(requiredPermission);
};

// 检查多个权限（AND 逻辑）
export const hasAllPermissions = (userPermissions: string[], requiredPermissions: string[]): boolean => {
  return requiredPermissions.every(perm => userPermissions.includes(perm));
};

// 检查多个权限（OR 逻辑）
export const hasAnyPermission = (userPermissions: string[], requiredPermissions: string[]): boolean => {
  return requiredPermissions.some(perm => userPermissions.includes(perm));
};

// 检查角色
export const hasRole = (userRoles: Role[], requiredRole: string): boolean => {
  return userRoles.some(role => role.name === requiredRole);
};

// 检查多个角色
export const hasAnyRole = (userRoles: Role[], requiredRoles: string[]): boolean => {
  return requiredRoles.some(role => userRoles.some(userRole => userRole.name === role));
};

// 权限分组检查
export const checkModulePermissions = (userPermissions: string[], module: string): string[] => {
  return userPermissions.filter(perm => perm.startsWith(`${module}.`));
};

// 获取用户可访问的菜单权限
export const getAccessibleMenus = (userPermissions: string[], menuConfig: MenuConfig[]): MenuConfig[] => {
  return menuConfig.filter(menu => {
    if (!menu.requiredPermissions?.length) return true;
    return hasAnyPermission(userPermissions, menu.requiredPermissions);
  });
};
```

## 五、动态菜单生成方案

### 5.1 菜单配置与权限映射

```typescript
// src/config/menu.config.ts
import { PERMISSIONS } from './permissions';

export interface MenuConfig {
  key: string;
  label: string;
  icon?: React.ReactNode;
  path?: string;
  requiredPermissions?: string[];
  requiredRoles?: string[];
  children?: MenuConfig[];
  module?: string; // 对应 FinSight 模块
}

export const menuConfig: MenuConfig[] = [
  {
    key: 'dashboard',
    label: '仪表板',
    icon: <DashboardOutlined />,
    path: '/dashboard',
    requiredPermissions: ['data.report.read']
  },
  {
    key: 'users',
    label: '用户管理',
    icon: <UserOutlined />,
    module: 'user',
    requiredPermissions: ['user.list.read'],
    children: [
      {
        key: 'user-list',
        label: '用户列表',
        path: '/users',
        requiredPermissions: ['user.list.read']
      },
      {
        key: 'user-create',
        label: '创建用户',
        path: '/users/create',
        requiredPermissions: ['user.create']
      },
      {
        key: 'user-roles',
        label: '角色管理',
        path: '/users/roles',
        requiredPermissions: ['role.list.read']
      }
    ]
  },
  {
    key: 'data',
    label: '数据管理',
    icon: <DatabaseOutlined />,
    module: 'data',
    requiredRoles: ['DataAdmin', 'Admin'],
    children: [
      {
        key: 'data-sources',
        label: '数据源',
        path: '/data/sources',
        requiredPermissions: ['data.source.read']
      },
      {
        key: 'data-tasks',
        label: '采集任务',
        path: '/data/tasks',
        requiredPermissions: ['data.task.read']
      }
    ]
  },
  {
    key: 'content',
    label: '内容管理',
    icon: <FileTextOutlined />,
    module: 'content',
    requiredRoles: ['Editor', 'Admin'],
    children: [
      {
        key: 'articles',
        label: '文章管理',
        path: '/content/articles',
        requiredPermissions: ['content.article.read']
      }
    ]
  },
  {
    key: 'system',
    label: '系统设置',
    icon: <SettingOutlined />,
    module: 'system',
    requiredRoles: ['Admin'],
    children: [
      {
        key: 'system-config',
        label: '系统配置',
        path: '/system/config',
        requiredPermissions: ['system.config.read']
      },
      {
        key: 'system-logs',
        label: '系统日志',
        path: '/system/logs',
        requiredPermissions: ['system.log.read']
      }
    ]
  }
];
```

### 5.2 动态菜单组件

```typescript
// src/components/layout/DynamicMenu.tsx
import { Menu } from 'antd';
import { useSelector } from 'react-redux';
import { useMemo } from 'react';
import { useRouter } from 'next/router';
import { menuConfig } from '../../config/menu.config';
import { hasAnyPermission, hasAnyRole } from '../../utils/permission.utils';

export const DynamicMenu: React.FC = () => {
  const router = useRouter();
  const { permissions, roles } = useSelector(state => state.auth);

  const filteredMenus = useMemo(() => {
    const filterMenu = (menus: MenuConfig[]): MenuConfig[] => {
      return menus.filter(menu => {
        // 检查权限
        const hasPermission = !menu.requiredPermissions?.length ||
          hasAnyPermission(permissions, menu.requiredPermissions);
        
        // 检查角色
        const hasRole = !menu.requiredRoles?.length ||
          hasAnyRole(roles, menu.requiredRoles);

        if (!hasPermission || !hasRole) return false;

        // 递归过滤子菜单
        if (menu.children) {
          const filteredChildren = filterMenu(menu.children);
          menu.children = filteredChildren;
          // 如果没有可访问的子菜单且当前菜单没有路径，则隐藏
          return filteredChildren.length > 0 || !!menu.path;
        }

        return true;
      });
    };

    return filterMenu([...menuConfig]);
  }, [permissions, roles]);

  const handleMenuClick = ({ key }: { key: string }) => {
    const findMenuPath = (menus: MenuConfig[], targetKey: string): string | null => {
      for (const menu of menus) {
        if (menu.key === targetKey && menu.path) return menu.path;
        if (menu.children) {
          const childPath = findMenuPath(menu.children, targetKey);
          if (childPath) return childPath;
        }
      }
      return null;
    };

    const path = findMenuPath(filteredMenus, key);
    if (path) router.push(path);
  };

  const renderMenuItems = (menus: MenuConfig[]) => {
    return menus.map(menu => {
      if (menu.children?.length) {
        return (
          <Menu.SubMenu key={menu.key} icon={menu.icon} title={menu.label}>
            {renderMenuItems(menu.children)}
          </Menu.SubMenu>
        );
      }
      return (
        <Menu.Item key={menu.key} icon={menu.icon}>
          {menu.label}
        </Menu.Item>
      );
    });
  };

  return (
    <Menu
      mode="inline"
      selectedKeys={[router.pathname]}
      onClick={handleMenuClick}
    >
      {renderMenuItems(filteredMenus)}
    </Menu>
  );
};
```

## 六、安全增强策略

### 6.1 权限数据加密与验证

```typescript
// src/utils/security.utils.ts
import CryptoJS from 'crypto-js';
import jwt from 'jsonwebtoken';

class SecurityUtils {
  private readonly SECRET_KEY = process.env.NEXT_PUBLIC_ENCRYPT_KEY!;

  // 权限数据加密
  encryptPermissions(data: any): string {
    return CryptoJS.AES.encrypt(JSON.stringify(data), this.SECRET_KEY).toString();
  }

  // 权限数据解密
  decryptPermissions(encryptedData: string): any {
    try {
      const bytes = CryptoJS.AES.decrypt(encryptedData, this.SECRET_KEY);
      return JSON.parse(bytes.toString(CryptoJS.enc.Utf8));
    } catch {
      return null;
    }
  }

  // JWT Token 验证
  verifyToken(token: string): any {
    try {
      return jwt.verify(token, process.env.JWT_SECRET!);
    } catch {
      return null;
    }
  }

  // 权限版本校验
  async validatePermissionVersion(localVersion: string): Promise<boolean> {
    try {
      const response = await fetch('/api/v1/permissions/version');
      const { version } = await response.json();
      return localVersion === version;
    } catch {
      return false;
    }
  }
}

export const securityUtils = new SecurityUtils();
```

### 6.2 权限变更监听

```typescript
// src/hooks/usePermissionSync.ts
import { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { fetchUserPermissions } from '../store/slices/authSlice';

export const usePermissionSync = () => {
  const dispatch = useDispatch();

  useEffect(() => {
    // WebSocket 连接监听权限变更
    const ws = new WebSocket(process.env.NEXT_PUBLIC_WS_URL!);
    
    ws.onmessage = (event) => {
      const data = JSON.parse(event.data);
      
      if (data.type === 'PERMISSION_UPDATED') {
        // 权限变更时重新获取
        dispatch(fetchUserPermissions());
        
        // 清除本地缓存
        localStorage.removeItem('finsight_permissions');
        
        // 显示通知
        notification.info({
          message: '权限已更新',
          description: '您的权限已发生变更，页面将自动刷新'
        });
        
        // 延迟刷新页面
        setTimeout(() => window.location.reload(), 2000);
      }
    };

    return () => ws.close();
  }, [dispatch]);

  // 定期检查权限版本
  useEffect(() => {
    const checkInterval = setInterval(async () => {
      const localVersion = localStorage.getItem('permission_version');
      if (localVersion) {
        const isValid = await securityUtils.validatePermissionVersion(localVersion);
        if (!isValid) {
          dispatch(fetchUserPermissions());
        }
      }
    }, 5 * 60 * 1000); // 5分钟检查一次

    return () => clearInterval(checkInterval);
  }, [dispatch]);
};
```

## 七、权限 Hooks 封装

### 7.1 权限检查 Hooks

```typescript
// src/hooks/usePermissions.ts
import { useSelector } from 'react-redux';
import { useMemo } from 'react';
import { hasPermission, hasRole, hasAllPermissions } from '../utils/permission.utils';

export const usePermissions = () => {
  const { permissions, roles } = useSelector(state => state.auth);

  return useMemo(() => ({
    // 检查单个权限
    can: (permission: string) => hasPermission(permissions, permission),
    
    // 检查多个权限（AND）
    canAll: (perms: string[]) => hasAllPermissions(permissions, perms),
    
    // 检查多个权限（OR）
    canAny: (perms: string[]) => perms.some(p => hasPermission(permissions, p)),
    
    // 检查角色
    hasRole: (role: string) => hasRole(roles, role),
    
    // 检查是否为管理员
    isAdmin: () => hasRole(roles, 'Admin'),
    
    // 检查是否为数据管理员
    isDataAdmin: () => hasRole(roles, 'DataAdmin') || hasRole(roles, 'Admin'),
    
    // 获取模块权限
    getModulePermissions: (module: string) => 
      permissions.filter(p => p.startsWith(`${module}.`)),
    
    // 获取所有权限
    getAllPermissions: () => permissions,
    
    // 获取所有角色
    getAllRoles: () => roles.map(r => r.name)
  }), [permissions, roles]);
};

// 使用示例
const UserManagement = () => {
  const { can, isAdmin } = usePermissions();

  return (
    <div>
      {can('user.create') && (
        <Button onClick={handleCreate}>创建用户</Button>
      )}
      
      {isAdmin() && (
        <Button danger onClick={handleDelete}>删除用户</Button>
      )}
    </div>
  );
};
```

### 7.2 角色权限 Hooks

```typescript
// src/hooks/useRolePermissions.ts
export const useRolePermissions = (targetRole?: string) => {
  const { roles } = useSelector(state => state.auth);
  
  return useMemo(() => {
    const currentRole = targetRole ? 
      roles.find(r => r.name === targetRole) : 
      roles[0]; // 主要角色
    
    return {
      role: currentRole,
      permissions: currentRole?.permissions.map(p => p.code) || [],
      hasPermission: (permission: string) => 
        currentRole?.permissions.some(p => p.code === permission) || false,
      getPermissionsByModule: (module: string) =>
        currentRole?.permissions.filter(p => p.module === module) || []
    };
  }, [roles, targetRole]);
};
```

## 八、工程化实现建议

### 8.1 目录结构

```
src/
├── components/
│   ├── permission/           # 权限控制组件
│   │   ├── PermissionGate.tsx
│   │   ├── PermissionButton.tsx
│   │   ├── PermissionTable.tsx
│   │   └── index.ts
│   └── guards/              # 路由守卫
│       ├── RouteGuard.tsx
│       └── AuthGuard.tsx
├── hooks/
│   ├── usePermissions.ts    # 权限 hooks
│   ├── useRolePermissions.ts
│   └── usePermissionSync.ts
├── config/
│   ├── permissions.ts       # 权限常量配置
│   ├── menu.config.ts       # 菜单配置
│   └── routes.config.ts     # 路由配置
├── store/
│   ├── slices/
│   │   └── authSlice.ts     # 权限状态管理
│   └── index.ts
├── utils/
│   ├── permission.utils.ts  # 权限工具函数
│   ├── security.utils.ts    # 安全工具
│   └── auth.server.ts       # 服务端认证
└── types/
    ├── auth.types.ts        # 认证类型定义
    └── permission.types.ts  # 权限类型定义
```

### 8.2 类型定义规范

```typescript
// src/types/permission.types.ts
// 基于 FinSight 后端权限编码规范
export type PermissionModule = 
  | 'user' | 'permission' | 'data' | 'content' 
  | 'financial' | 'tag' | 'sms' | 'info' | 'push' | 'system';

export type PermissionAction = 
  | 'create' | 'read' | 'update' | 'delete' | 'list.read' 
  | 'execute' | 'assign' | 'revoke' | 'manage' | 'publish';

export type FinSightRole = 
  | 'Admin' | 'DataAdmin' | 'Analyst' | 'Editor' 
  | 'AccountManager' | 'RiskOfficer' | 'User' | 'Guest';

// 权限编码类型（基于后端实际权限）
export type PermissionCode = 
  // 用户管理
  | 'user.profile.read' | 'user.profile.update' | 'user.list.read' | 'user.create'
  | 'user.update' | 'user.delete' | 'user.role.assign' | 'user.role.revoke'
  // 数据管理  
  | 'data.source.read' | 'data.source.create' | 'data.source.update' | 'data.source.delete'
  | 'data.task.read' | 'data.task.create' | 'data.task.execute' | 'data.report.read'
  // 系统管理
  | 'system.config.read' | 'system.config.update' | 'system.log.read'
  // ... 其他权限
  ;
```

## 九、最佳实践总结

### 9.1 权限粒度控制

```typescript
// 1. 页面级权限 - 粗粒度
<RouteGuard requiredPermissions={['user.list.read']}>
  <UserListPage />
</RouteGuard>

// 2. 组件级权限 - 中粒度  
<PermissionGate permissions={['user.create']}>
  <CreateUserButton />
</PermissionGate>

// 3. 操作级权限 - 细粒度
const handleDelete = () => {
  if (!can('user.delete')) {
    message.error('无删除权限');
    return;
  }
  // 执行删除操作
};
```

### 9.2 权限降级策略

```typescript
// src/components/permission/GracefulPermissionGate.tsx
export const GracefulPermissionGate: React.FC<{
  permissions: string[];
  fallbackPermissions?: string[]; // 降级权限
  children: React.ReactNode;
  fallback?: React.ReactNode;
}> = ({ permissions, fallbackPermissions = [], children, fallback }) => {
  const { canAny } = usePermissions();
  
  // 优先检查主要权限
  if (canAny(permissions)) {
    return <>{children}</>;
  }
  
  // 降级检查备用权限
  if (fallbackPermissions.length && canAny(fallbackPermissions)) {
    return <>{children}</>;
  }
  
  return <>{fallback}</>;
};
```

### 9.3 性能优化

```typescript
// 权限计算缓存
const usePermissionMemo = (permissions: string[], deps: any[] = []) => {
  const { permissions: userPermissions } = useSelector(state => state.auth);
  
  return useMemo(() => {
    return permissions.every(p => userPermissions.includes(p));
  }, [userPermissions, ...deps]);
};

// 组件渲染优化
const PermissionOptimizedComponent = React.memo(({ permission, children }) => {
  const hasPermission = usePermissionMemo([permission]);
  return hasPermission ? children : null;
});
```

该方案基于 FinSight 后端 RBAC 系统，提供了完整的前端权限控制解决方案，确保权限的一致性和安全性。 