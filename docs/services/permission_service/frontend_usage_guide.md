# FinSight 前端权限使用指南

## 📖 快速开始

这是一个简化的前端权限使用指南，帮助前端开发者快速集成权限系统。

## 🚀 安装和初始化

### 1. 引入权限工具库

```javascript
// ES6 模块方式
import { 
  PermissionManager, 
  PERMISSIONS, 
  ROLES,
  permissionDirective,
  roleDirective,
  PermissionGate 
} from './frontend_permissions_utils.js';

// 或者使用动态导入
const permissionModule = await import('./frontend_permissions_utils.js');
```

### 2. 初始化权限管理器

```javascript
// 创建权限管理器实例
const permissionManager = new PermissionManager();

// 在用户登录后初始化权限
async function initializePermissions(userToken) {
  const success = await permissionManager.init(userToken);
  if (success) {
    console.log('权限初始化成功');
    // 将实例挂载到全局，便于在组件中使用
    window.permissionManager = permissionManager;
  } else {
    console.error('权限初始化失败');
  }
}
```

## 🔧 Vue.js 集成

### 1. 注册权限指令

```javascript
// main.js
import { createApp } from 'vue';
import { permissionDirective, roleDirective } from './utils/permissions.js';

const app = createApp(App);

// 注册权限指令
app.directive('permission', permissionDirective);
app.directive('role', roleDirective);

app.mount('#app');
```

### 2. 在组件中使用权限指令

```vue
<template>
  <div>
    <!-- 权限控制按钮 -->
    <button v-permission="'user.create'">创建用户</button>
    
    <!-- 多权限控制（任一权限） -->
    <button v-permission="['user.create', 'user.update']">用户操作</button>
    
    <!-- 多权限控制（所有权限） -->
    <button v-permission.all="['user.create', 'user.delete']">高级操作</button>
    
    <!-- 隐藏而不是移除元素 -->
    <div v-permission.hide="'admin.config'">管理配置</div>
    
    <!-- 角色控制 -->
    <div v-role="'Admin'">管理员专区</div>
    
    <!-- 多角色控制 -->
    <div v-role="['Admin', 'DataAdmin']">管理区域</div>
  </div>
</template>

<script>
import { PERMISSIONS, ROLES } from '@/utils/permissions.js';

export default {
  name: 'UserManagement',
  data() {
    return {
      PERMISSIONS,
      ROLES
    };
  },
  methods: {
    createUser() {
      // 在方法中也可以检查权限
      if (window.permissionManager.hasPermission(PERMISSIONS.USER.CREATE)) {
        // 执行创建用户逻辑
        console.log('执行创建用户');
      } else {
        this.$message.error('您没有权限执行此操作');
      }
    },
    
    checkUserPermissions() {
      const pm = window.permissionManager;
      
      // 检查单个权限
      const canCreateUser = pm.hasPermission('user.create');
      
      // 检查角色
      const isAdmin = pm.hasRole('Admin');
      
      // 检查模块权限
      const canAccessUserModule = pm.canAccessModule('user');
      
      // 获取所有权限编码
      const allPermissions = pm.getPermissionCodes();
      
      console.log({
        canCreateUser,
        isAdmin,
        canAccessUserModule,
        allPermissions
      });
    }
  }
};
</script>
```

### 3. 路由权限控制

```javascript
// router/index.js
import { createRouter, createWebHistory } from 'vue-router';

const routes = [
  {
    path: '/admin',
    component: AdminLayout,
    meta: { 
      requiresAuth: true,
      requiredPermission: 'admin.access' 
    },
    children: [
      {
        path: 'users',
        component: UserManagement,
        meta: { requiredPermission: 'user.list.read' }
      },
      {
        path: 'permissions',
        component: PermissionManagement,
        meta: { requiredPermission: 'permission.list.read' }
      }
    ]
  }
];

const router = createRouter({
  history: createWebHistory(),
  routes
});

// 权限守卫
router.beforeEach(async (to, from, next) => {
  // 检查是否需要登录
  if (to.meta.requiresAuth) {
    const token = localStorage.getItem('authToken');
    if (!token) {
      next('/login');
      return;
    }
    
    // 初始化权限（如果还没有初始化）
    if (!window.permissionManager?.isInitialized()) {
      await window.permissionManager.init(token);
    }
  }
  
  // 检查权限
  if (to.meta.requiredPermission) {
    const hasPermission = window.permissionManager.hasPermission(to.meta.requiredPermission);
    if (!hasPermission) {
      next('/unauthorized');
      return;
    }
  }
  
  next();
});
```

## ⚛️ React 集成

### 1. 使用权限组件

```jsx
import React from 'react';
import { PermissionGate, withPermission, PERMISSIONS, ROLES } from './utils/permissions.js';

// 使用权限门组件
function UserManagement() {
  return (
    <div>
      {/* 权限控制 */}
      <PermissionGate permission={PERMISSIONS.USER.CREATE}>
        <button onClick={createUser}>创建用户</button>
      </PermissionGate>
      
      {/* 多权限控制 */}
      <PermissionGate 
        permission={[PERMISSIONS.USER.CREATE, PERMISSIONS.USER.UPDATE]}
        requireAll={false}
      >
        <button>用户操作</button>
      </PermissionGate>
      
      {/* 角色控制 */}
      <PermissionGate role={ROLES.ADMIN}>
        <div>管理员专区</div>
      </PermissionGate>
      
      {/* 没有权限时显示替代内容 */}
      <PermissionGate 
        permission={PERMISSIONS.USER.DELETE}
        fallback={<span>您没有删除权限</span>}
      >
        <button onClick={deleteUser}>删除用户</button>
      </PermissionGate>
    </div>
  );
}

// 使用高阶组件
const ProtectedUserList = withPermission(UserList, PERMISSIONS.USER.LIST_READ);

function App() {
  return (
    <div>
      <ProtectedUserList />
    </div>
  );
}
```

### 2. 使用 Hooks

```jsx
import React, { useState, useEffect } from 'react';

// 自定义权限 Hook
function usePermission() {
  const [permissionManager, setPermissionManager] = useState(null);
  const [isLoaded, setIsLoaded] = useState(false);
  
  useEffect(() => {
    if (window.permissionManager?.isInitialized()) {
      setPermissionManager(window.permissionManager);
      setIsLoaded(true);
    }
  }, []);
  
  const hasPermission = (permission) => {
    return permissionManager?.hasPermission(permission) || false;
  };
  
  const hasRole = (role) => {
    return permissionManager?.hasRole(role) || false;
  };
  
  const hasAnyPermission = (permissions) => {
    return permissionManager?.hasAnyPermission(permissions) || false;
  };
  
  return {
    hasPermission,
    hasRole,
    hasAnyPermission,
    isLoaded,
    permissionManager
  };
}

// 使用权限 Hook 的组件
function UserActions() {
  const { hasPermission, hasRole } = usePermission();
  
  return (
    <div>
      {hasPermission(PERMISSIONS.USER.CREATE) && (
        <button onClick={createUser}>创建用户</button>
      )}
      
      {hasRole(ROLES.ADMIN) && (
        <div>管理员功能</div>
      )}
    </div>
  );
}
```

## 📡 API 调用示例

### 1. 获取用户权限

```javascript
// 获取当前用户权限
async function getCurrentUserPermissions() {
  try {
    const response = await fetch('/api/v1/permissions/me', {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('authToken')}`
      }
    });
    
    const data = await response.json();
    
    return {
      roles: data.roles,
      permissions: data.permissions,
      permissionCodes: data.permission_codes
    };
  } catch (error) {
    console.error('获取用户权限失败:', error);
    return null;
  }
}
```

### 2. 权限检查

```javascript
// 检查特定权限
async function checkPermission(permissionCode) {
  try {
    const response = await fetch('/api/v1/permissions/check', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        permission_code: permissionCode
      })
    });
    
    const data = await response.json();
    return data.has_permission;
  } catch (error) {
    console.error('权限检查失败:', error);
    return false;
  }
}
```

## 🎨 UI 框架集成

### Element Plus 集成

```vue
<template>
  <div>
    <!-- 在表格操作中使用权限控制 -->
    <el-table :data="users">
      <el-table-column prop="name" label="用户名" />
      <el-table-column label="操作">
        <template #default="{ row }">
          <el-button 
            v-permission="PERMISSIONS.USER.UPDATE"
            type="primary" 
            size="small"
            @click="editUser(row)"
          >
            编辑
          </el-button>
          
          <el-button 
            v-permission="PERMISSIONS.USER.DELETE"
            type="danger" 
            size="small"
            @click="deleteUser(row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 在菜单中使用权限控制 -->
    <el-menu>
      <el-menu-item v-permission="PERMISSIONS.USER.LIST_READ" index="users">
        用户管理
      </el-menu-item>
      <el-menu-item v-permission="PERMISSIONS.PERMISSION.LIST_READ" index="permissions">
        权限管理
      </el-menu-item>
      <el-menu-item v-permission="PERMISSIONS.DATA.SOURCE_READ" index="data">
        数据管理
      </el-menu-item>
    </el-menu>
  </div>
</template>
```

### Ant Design 集成

```jsx
import { Table, Button, Menu } from 'antd';
import { PermissionGate, PERMISSIONS } from './utils/permissions.js';

function UserTable() {
  const columns = [
    {
      title: '用户名',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <div>
          <PermissionGate permission={PERMISSIONS.USER.UPDATE}>
            <Button type="primary" size="small" onClick={() => editUser(record)}>
              编辑
            </Button>
          </PermissionGate>
          
          <PermissionGate permission={PERMISSIONS.USER.DELETE}>
            <Button type="primary" danger size="small" onClick={() => deleteUser(record)}>
              删除
            </Button>
          </PermissionGate>
        </div>
      ),
    },
  ];
  
  return <Table columns={columns} dataSource={users} />;
}

function AppMenu() {
  const { hasPermission } = usePermission();
  
  const menuItems = [
    hasPermission(PERMISSIONS.USER.LIST_READ) && {
      key: 'users',
      label: '用户管理',
    },
    hasPermission(PERMISSIONS.PERMISSION.LIST_READ) && {
      key: 'permissions',
      label: '权限管理',
    },
    hasPermission(PERMISSIONS.DATA.SOURCE_READ) && {
      key: 'data',
      label: '数据管理',
    },
  ].filter(Boolean);
  
  return <Menu items={menuItems} />;
}
```

## 🚦 常见使用场景

### 1. 按钮权限控制

```javascript
// Vue
<template>
  <div>
    <!-- 创建按钮 -->
    <el-button v-permission="'user.create'" type="primary">
      创建用户
    </el-button>
    
    <!-- 批量操作 -->
    <el-button v-permission="['user.update', 'user.delete']" type="warning">
      批量操作
    </el-button>
    
    <!-- 管理员功能 -->
    <el-button v-role="'Admin'" type="danger">
      系统设置
    </el-button>
  </div>
</template>

// React
function ActionButtons() {
  return (
    <div>
      <PermissionGate permission="user.create">
        <Button type="primary">创建用户</Button>
      </PermissionGate>
      
      <PermissionGate permission={["user.update", "user.delete"]}>
        <Button type="warning">批量操作</Button>
      </PermissionGate>
      
      <PermissionGate role="Admin">
        <Button type="danger">系统设置</Button>
      </PermissionGate>
    </div>
  );
}
```

### 2. 菜单权限控制

```javascript
// 动态生成菜单
function generateMenu() {
  const allMenus = [
    {
      title: '用户管理',
      path: '/users',
      permission: 'user.list.read',
      children: [
        { title: '用户列表', path: '/users/list', permission: 'user.list.read' },
        { title: '创建用户', path: '/users/create', permission: 'user.create' }
      ]
    },
    {
      title: '权限管理',
      path: '/permissions',
      permission: 'permission.list.read',
      children: [
        { title: '权限列表', path: '/permissions/list', permission: 'permission.list.read' },
        { title: '角色管理', path: '/permissions/roles', permission: 'role.list.read' }
      ]
    },
    {
      title: '数据管理',
      path: '/data',
      permission: 'data.source.read'
    }
  ];
  
  return filterMenusByPermission(allMenus);
}

function filterMenusByPermission(menus) {
  return menus.filter(menu => {
    // 检查菜单权限
    if (menu.permission && !window.permissionManager.hasPermission(menu.permission)) {
      return false;
    }
    
    // 递归过滤子菜单
    if (menu.children) {
      menu.children = filterMenusByPermission(menu.children);
      // 如果没有可访问的子菜单，则隐藏父菜单
      return menu.children.length > 0;
    }
    
    return true;
  });
}
```

### 3. 表格操作权限

```javascript
// 动态生成表格操作列
function getTableColumns() {
  const baseColumns = [
    { title: '用户名', dataIndex: 'name' },
    { title: '邮箱', dataIndex: 'email' },
    { title: '状态', dataIndex: 'status' }
  ];
  
  // 根据权限动态添加操作列
  const actions = [];
  
  if (window.permissionManager.hasPermission('user.update')) {
    actions.push({
      text: '编辑',
      onClick: (record) => editUser(record)
    });
  }
  
  if (window.permissionManager.hasPermission('user.delete')) {
    actions.push({
      text: '删除',
      onClick: (record) => deleteUser(record),
      confirm: true
    });
  }
  
  if (window.permissionManager.hasPermission('user.role.assign')) {
    actions.push({
      text: '分配角色',
      onClick: (record) => assignRole(record)
    });
  }
  
  if (actions.length > 0) {
    baseColumns.push({
      title: '操作',
      render: (record) => actions
    });
  }
  
  return baseColumns;
}
```

## 📝 最佳实践

### 1. 权限检查最佳实践

```javascript
// ❌ 不好的做法 - 硬编码权限字符串
if (window.permissionManager.hasPermission('user.create')) {
  // ...
}

// ✅ 好的做法 - 使用常量
import { PERMISSIONS } from '@/utils/permissions.js';

if (window.permissionManager.hasPermission(PERMISSIONS.USER.CREATE)) {
  // ...
}

// ✅ 更好的做法 - 封装权限检查逻辑
class UserPermissions {
  static canCreate() {
    return window.permissionManager.hasPermission(PERMISSIONS.USER.CREATE);
  }
  
  static canEdit() {
    return window.permissionManager.hasPermission(PERMISSIONS.USER.UPDATE);
  }
  
  static canDelete() {
    return window.permissionManager.hasPermission(PERMISSIONS.USER.DELETE);
  }
  
  static canManageRoles() {
    return window.permissionManager.hasAnyPermission([
      PERMISSIONS.USER.ROLE_ASSIGN,
      PERMISSIONS.USER.ROLE_REVOKE
    ]);
  }
}

// 使用
if (UserPermissions.canCreate()) {
  showCreateButton();
}
```

### 2. 错误处理

```javascript
// 统一的权限错误处理
function handlePermissionError(error, operation) {
  console.error(`权限检查失败 [${operation}]:`, error);
  
  // 显示用户友好的错误信息
  if (error.status === 403) {
    showMessage('您没有权限执行此操作', 'error');
  } else if (error.status === 401) {
    showMessage('登录已过期，请重新登录', 'warning');
    redirectToLogin();
  } else {
    showMessage('系统错误，请稍后重试', 'error');
  }
}

// API 调用时的权限处理
async function callProtectedAPI(url, options) {
  try {
    const response = await fetch(url, options);
    
    if (response.status === 403) {
      handlePermissionError({ status: 403 }, 'API调用');
      return null;
    }
    
    return await response.json();
  } catch (error) {
    handlePermissionError(error, 'API调用');
    return null;
  }
}
```

### 3. 性能优化

```javascript
// 权限缓存优化
class OptimizedPermissionManager extends PermissionManager {
  constructor() {
    super();
    this.permissionCache = new Map();
    this.cacheExpiry = 5 * 60 * 1000; // 5分钟
  }
  
  hasPermission(permissionCode) {
    // 检查缓存
    const cached = this.permissionCache.get(permissionCode);
    if (cached && Date.now() - cached.timestamp < this.cacheExpiry) {
      return cached.value;
    }
    
    // 执行权限检查
    const result = super.hasPermission(permissionCode);
    
    // 缓存结果
    this.permissionCache.set(permissionCode, {
      value: result,
      timestamp: Date.now()
    });
    
    return result;
  }
  
  clearCache() {
    this.permissionCache.clear();
  }
}
```

## 🔗 相关资源

- [完整权限列表](./permissions_overview.md) - 查看所有可用权限
- [权限配置文件](./frontend_permissions_config.json) - JSON格式的权限配置
- [工具类源码](./frontend_permissions_utils.js) - 完整的工具类实现
- [RBAC设计文档](./rbac_design.md) - 权限系统设计详情

---

**注意**: 请确保在生产环境中正确配置权限验证，前端权限控制主要用于用户体验优化，真正的安全验证应该在后端进行。 