# 角色与权限体系设计

## 1. 概述

本文档描述了金融洞察平台（FinSight）的角色与权限体系设计。系统采用基于角色的访问控制（Role-Based Access Control, RBAC）模型，实现灵活、安全的权限管理机制。

### 1.1 设计目标

- **安全性**：确保系统资源只能被授权用户访问
- **灵活性**：支持多种角色和权限组合，满足不同业务场景
- **可扩展性**：随业务发展，易于扩展新角色和权限
- **易管理性**：提供直观的权限管理界面和API
- **审计追踪**：记录权限变更和敏感操作日志

### 1.2 核心概念

- **用户（User）**：系统的使用者，可以是个人或服务账户
- **角色（Role）**：用户在系统中的职责集合，如管理员、分析师等
- **权限（Permission）**：执行特定操作的授权，如查看数据、创建报告等
- **资源（Resource）**：系统中可被访问的对象，如数据源、报告、用户信息等
- **操作（Action）**：对资源执行的行为，如读取、创建、更新、删除等

## 2. 角色设计

### 2.1 基础角色

系统定义以下基础角色：

| 角色ID | 角色名称 | 角色描述 |
|--------|----------|----------|
| 1 | 系统管理员（Admin） | 拥有系统最高权限，可管理所有资源和用户 |
| 2 | 数据分析师（Analyst） | 负责数据分析和报告生成，可访问大部分数据资源 |
| 3 | 内容编辑（Editor） | 负责内容创建和编辑，管理文章和资讯 |
| 4 | 普通用户（User） | 基础用户，可查看公开内容和个人数据 |
| 5 | 访客（Guest） | 未注册或未登录用户，只能访问公开内容 |

### 2.2 扩展角色

除基础角色外，系统支持以下扩展角色：

| 角色ID | 角色名称 | 角色描述 |
|--------|----------|----------|
| 101 | 数据管理员（DataAdmin） | 负责管理数据源和数据采集任务 |
| 102 | 风控专员（RiskOfficer） | 负责风险监控和合规检查 |
| 103 | 客户经理（AccountManager） | 负责用户服务和账户管理 |
| 104 | API用户（APIUser） | 第三方系统集成账户 |

### 2.3 角色层级

角色之间存在继承关系，上级角色自动拥有下级角色的所有权限：

```
系统管理员
├── 数据管理员
│   └── 数据分析师
├── 内容编辑
├── 客户经理
│   └── 普通用户
└── 风控专员
```

## 3. 权限设计

### 3.1 权限分类

权限按功能模块分为以下几类：

1. **用户管理权限**：用户创建、查询、修改、删除等
2. **数据采集权限**：数据源管理、采集任务配置等
3. **数据分析权限**：数据查询、分析、可视化等
4. **内容管理权限**：文章、资讯的创建、编辑、发布等
5. **系统配置权限**：系统参数设置、日志查看等

### 3.2 权限编码

权限采用分层编码方式，格式为：`{模块}.{资源}.{操作}`

示例：
- `user.profile.read` - 读取用户资料
- `data.source.create` - 创建数据源
- `content.article.publish` - 发布文章

### 3.3 权限列表

以下是系统核心权限列表：

#### 3.3.1 用户管理权限

| 权限编码 | 权限描述 | 默认角色 |
|----------|----------|----------|
| user.profile.read | 查看用户资料 | 所有角色 |
| user.profile.update | 更新用户资料 | User, Admin |
| user.list.read | 查看用户列表 | Admin, AccountManager |
| user.create | 创建用户 | Admin, AccountManager |
| user.delete | 删除用户 | Admin |
| user.role.assign | 分配用户角色 | Admin |

#### 3.3.2 数据采集权限

| 权限编码 | 权限描述 | 默认角色 |
|----------|----------|----------|
| data.source.read | 查看数据源 | Analyst, DataAdmin, Admin |
| data.source.create | 创建数据源 | DataAdmin, Admin |
| data.source.update | 更新数据源 | DataAdmin, Admin |
| data.source.delete | 删除数据源 | Admin |
| data.task.read | 查看采集任务 | Analyst, DataAdmin, Admin |
| data.task.create | 创建采集任务 | DataAdmin, Admin |
| data.task.update | 更新采集任务 | DataAdmin, Admin |
| data.task.execute | 执行采集任务 | DataAdmin, Admin |

#### 3.3.3 数据分析权限

| 权限编码 | 权限描述 | 默认角色 |
|----------|----------|----------|
| data.query.execute | 执行数据查询 | Analyst, DataAdmin, Admin |
| data.report.read | 查看分析报告 | User, Analyst, DataAdmin, Admin |
| data.report.create | 创建分析报告 | Analyst, DataAdmin, Admin |
| data.report.publish | 发布分析报告 | Analyst, DataAdmin, Admin |
| data.export | 导出数据 | Analyst, DataAdmin, Admin |

#### 3.3.4 内容管理权限

| 权限编码 | 权限描述 | 默认角色 |
|----------|----------|----------|
| content.article.read | 查看文章 | 所有角色 |
| content.article.create | 创建文章 | Editor, Admin |
| content.article.update | 更新文章 | Editor, Admin |
| content.article.publish | 发布文章 | Editor, Admin |
| content.article.delete | 删除文章 | Editor, Admin |
| content.comment.moderate | 管理评论 | Editor, Admin |

#### 3.3.5 系统配置权限

| 权限编码 | 权限描述 | 默认角色 |
|----------|----------|----------|
| system.config.read | 查看系统配置 | Admin |
| system.config.update | 更新系统配置 | Admin |
| system.log.read | 查看系统日志 | Admin, RiskOfficer |
| system.backup | 系统备份 | Admin |
| system.permission.manage | 管理权限设置 | Admin |

## 4. 数据模型设计

### 4.1 角色表 (roles)

```sql
CREATE TABLE roles (
    id SERIAL PRIMARY KEY,
    name VARCHAR(50) UNIQUE NOT NULL COMMENT '角色名称',
    description TEXT COMMENT '角色描述',
    is_system BOOLEAN DEFAULT FALSE COMMENT '是否系统内置角色',
    parent_id INTEGER NULL REFERENCES roles(id) COMMENT '父角色ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_parent_id (parent_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色表';
```

### 4.2 权限表 (permissions)

```sql
CREATE TABLE permissions (
    id SERIAL PRIMARY KEY,
    code VARCHAR(100) UNIQUE NOT NULL COMMENT '权限编码',
    name VARCHAR(100) NOT NULL COMMENT '权限名称',
    description TEXT COMMENT '权限描述',
    module VARCHAR(50) NOT NULL COMMENT '所属模块',
    resource VARCHAR(50) NOT NULL COMMENT '资源类型',
    action VARCHAR(50) NOT NULL COMMENT '操作类型',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_module (module),
    INDEX idx_resource (resource),
    INDEX idx_action (action)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='权限表';
```

### 4.3 角色权限关联表 (role_permissions)

```sql
CREATE TABLE role_permissions (
    id SERIAL PRIMARY KEY,
    role_id INTEGER NOT NULL COMMENT '角色ID',
    permission_id INTEGER NOT NULL COMMENT '权限ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY uk_role_permission (role_id, permission_id),
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE,
    INDEX idx_role_id (role_id),
    INDEX idx_permission_id (permission_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色权限关联表';
```

### 4.4 用户角色关联表 (user_roles)

```sql
CREATE TABLE user_roles (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL COMMENT '用户ID',
    role_id INTEGER NOT NULL COMMENT '角色ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER COMMENT '创建人ID',
    UNIQUE KEY uk_user_role (user_id, role_id),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_role_id (role_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户角色关联表';
```

### 4.5 权限操作日志表 (permission_operation_logs)

```sql
CREATE TABLE permission_operation_logs (
    id SERIAL PRIMARY KEY,
    operator_id INTEGER NOT NULL COMMENT '操作人ID',
    operation_type VARCHAR(20) NOT NULL COMMENT '操作类型：GRANT/REVOKE',
    target_type VARCHAR(20) NOT NULL COMMENT '目标类型：USER/ROLE',
    target_id INTEGER NOT NULL COMMENT '目标ID',
    role_id INTEGER COMMENT '角色ID',
    permission_id INTEGER COMMENT '权限ID',
    operation_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
    ip_address VARCHAR(45) COMMENT '操作IP',
    FOREIGN KEY (operator_id) REFERENCES users(id),
    INDEX idx_operator_id (operator_id),
    INDEX idx_target_id (target_id),
    INDEX idx_operation_time (operation_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='权限操作日志表';
```

## 5. 实现方案

### 5.1 系统架构

权限系统采用分层架构设计：

1. **数据层**：数据库存储角色、权限和关联关系
2. **服务层**：权限验证、角色管理、权限分配等核心服务
3. **API层**：提供权限管理和验证的REST接口
4. **应用层**：前端权限管理界面和权限控制组件

### 5.2 核心服务

#### 5.2.1 权限服务 (PermissionService)

负责权限的创建、查询、更新和删除：

```python
class PermissionService:
    """权限服务，负责权限的CRUD操作"""
    
    def __init__(self, db_session: Session):
        self.db = db_session
    
    def create_permission(self, permission_data: PermissionCreate) -> Permission:
        """创建新权限"""
        # 实现权限创建逻辑
        
    def get_permission(self, permission_id: int) -> Optional[Permission]:
        """获取权限详情"""
        # 实现权限查询逻辑
        
    def get_permissions_by_codes(self, codes: List[str]) -> List[Permission]:
        """根据权限编码获取权限列表"""
        # 实现权限查询逻辑
        
    def update_permission(self, permission_id: int, permission_data: PermissionUpdate) -> Optional[Permission]:
        """更新权限信息"""
        # 实现权限更新逻辑
        
    def delete_permission(self, permission_id: int) -> bool:
        """删除权限"""
        # 实现权限删除逻辑
        
    def get_all_permissions(self, module: Optional[str] = None) -> List[Permission]:
        """获取所有权限，可按模块过滤"""
        # 实现权限查询逻辑
```

#### 5.2.2 角色服务 (RoleService)

负责角色的管理和权限分配：

```python
class RoleService:
    """角色服务，负责角色管理和权限分配"""
    
    def __init__(self, db_session: Session):
        self.db = db_session
    
    def create_role(self, role_data: RoleCreate) -> Role:
        """创建新角色"""
        # 实现角色创建逻辑
        
    def get_role(self, role_id: int) -> Optional[Role]:
        """获取角色详情"""
        # 实现角色查询逻辑
        
    def update_role(self, role_id: int, role_data: RoleUpdate) -> Optional[Role]:
        """更新角色信息"""
        # 实现角色更新逻辑
        
    def delete_role(self, role_id: int) -> bool:
        """删除角色"""
        # 实现角色删除逻辑
        
    def get_all_roles(self) -> List[Role]:
        """获取所有角色"""
        # 实现角色查询逻辑
        
    def assign_permission_to_role(self, role_id: int, permission_id: int) -> bool:
        """为角色分配权限"""
        # 实现权限分配逻辑
        
    def revoke_permission_from_role(self, role_id: int, permission_id: int) -> bool:
        """从角色撤销权限"""
        # 实现权限撤销逻辑
        
    def get_role_permissions(self, role_id: int) -> List[Permission]:
        """获取角色的所有权限"""
        # 实现权限查询逻辑
        
    def get_role_hierarchy(self, role_id: int) -> Dict:
        """获取角色层级结构"""
        # 实现角色层级查询逻辑
```

#### 5.2.3 用户角色服务 (UserRoleService)

负责用户与角色的关联管理：

```python
class UserRoleService:
    """用户角色服务，负责用户与角色的关联管理"""
    
    def __init__(self, db_session: Session):
        self.db = db_session
    
    def assign_role_to_user(self, user_id: int, role_id: int, operator_id: int) -> bool:
        """为用户分配角色"""
        # 实现角色分配逻辑
        
    def revoke_role_from_user(self, user_id: int, role_id: int, operator_id: int) -> bool:
        """从用户撤销角色"""
        # 实现角色撤销逻辑
        
    def get_user_roles(self, user_id: int) -> List[Role]:
        """获取用户的所有角色"""
        # 实现角色查询逻辑
        
    def get_users_by_role(self, role_id: int) -> List[User]:
        """获取拥有指定角色的所有用户"""
        # 实现用户查询逻辑
        
    def has_role(self, user_id: int, role_id: int) -> bool:
        """检查用户是否拥有指定角色"""
        # 实现角色检查逻辑
```

#### 5.2.4 权限验证服务 (AuthorizationService)

负责权限验证和授权决策：

```python
class AuthorizationService:
    """权限验证服务，负责权限验证和授权决策"""
    
    def __init__(self, db_session: Session, cache_service: CacheService):
        self.db = db_session
        self.cache = cache_service
    
    def has_permission(self, user_id: int, permission_code: str) -> bool:
        """检查用户是否拥有指定权限"""
        # 实现权限检查逻辑
        
    def get_user_permissions(self, user_id: int) -> List[Permission]:
        """获取用户的所有权限（包括通过角色继承的权限）"""
        # 实现权限查询逻辑
        
    def filter_resources_by_permission(self, user_id: int, resources: List, permission_code: str) -> List:
        """根据权限过滤资源列表"""
        # 实现资源过滤逻辑
        
    def check_resource_permission(self, user_id: int, resource_id: int, resource_type: str, action: str) -> bool:
        """检查用户对特定资源的权限"""
        # 实现资源权限检查逻辑
```

### 5.3 权限验证中间件

在API层实现权限验证中间件，用于接口访问控制：

```python
class PermissionMiddleware:
    """权限验证中间件，用于API接口访问控制"""
    
    def __init__(self, auth_service: AuthorizationService):
        self.auth_service = auth_service
    
    async def __call__(self, request: Request, call_next):
        """验证请求权限"""
        # 获取当前用户
        user = request.state.user
        
        # 获取当前路由需要的权限
        required_permission = get_route_permission(request)
        
        # 如果路由不需要权限验证，直接放行
        if not required_permission:
            return await call_next(request)
        
        # 验证用户权限
        if not self.auth_service.has_permission(user.id, required_permission):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Not enough permissions"
            )
        
        # 权限验证通过，继续处理请求
        return await call_next(request)
```

### 5.4 权限验证装饰器

提供便捷的权限验证装饰器，用于控制器方法的权限控制：

```python
def require_permission(permission_code: str):
    """
    权限验证装饰器，用于控制器方法的权限控制
    
    Args:
        permission_code: 需要的权限编码
    """
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 获取当前用户
            current_user = None
            for arg in args:
                if isinstance(arg, User):
                    current_user = arg
                    break
            
            if not current_user:
                for _, arg in kwargs.items():
                    if isinstance(arg, User):
                        current_user = arg
                        break
            
            if not current_user:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Authentication required"
                )
            
            # 获取权限验证服务
            auth_service = get_authorization_service()
            
            # 验证用户权限
            if not auth_service.has_permission(current_user.id, permission_code):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Not enough permissions"
                )
            
            # 权限验证通过，执行原方法
            return await func(*args, **kwargs)
        
        return wrapper
    
    return decorator
```

### 5.5 前端权限控制

在前端实现基于角色和权限的UI控制：

```typescript
// 权限检查服务
class PermissionService {
  // 用户权限缓存
  private userPermissions: string[] = [];
  
  // 初始化用户权限
  async initUserPermissions() {
    try {
      const response = await api.get('/api/v1/users/permissions');
      this.userPermissions = response.data.permissions;
    } catch (error) {
      console.error('Failed to load user permissions:', error);
      this.userPermissions = [];
    }
  }
  
  // 检查用户是否有指定权限
  hasPermission(permissionCode: string): boolean {
    return this.userPermissions.includes(permissionCode);
  }
  
  // 检查用户是否有指定角色
  hasRole(roleName: string): boolean {
    // 角色通常转换为特定格式的权限，如 'role:admin'
    return this.hasPermission(`role:${roleName}`);
  }
  
  // 过滤用户有权限访问的路由
  filterAuthorizedRoutes(routes: Route[]): Route[] {
    return routes.filter(route => {
      // 如果路由不需要权限，则允许访问
      if (!route.meta?.requiredPermission) {
        return true;
      }
      
      // 检查用户是否有路由所需权限
      return this.hasPermission(route.meta.requiredPermission);
    });
  }
}

// 权限指令，用于控制UI元素的显示
const permissionDirective = {
  mounted(el, binding) {
    const permissionService = inject('permissionService');
    const permissionCode = binding.value;
    
    if (!permissionService.hasPermission(permissionCode)) {
      // 如果用户没有权限，则移除元素
      el.parentNode?.removeChild(el);
    }
  }
};
```

## 6. 缓存策略

为提高权限验证性能，实施以下缓存策略：

### 6.1 用户权限缓存

```python
# 缓存键设计
CACHE_KEYS = {
    'user_permissions': 'user:permissions:{user_id}',  # TTL: 30分钟
    'user_roles': 'user:roles:{user_id}',              # TTL: 30分钟
    'role_permissions': 'role:permissions:{role_id}',  # TTL: 1小时
}

class CacheService:
    """缓存服务"""
    
    def __init__(self, redis_client):
        self.redis = redis_client
    
    def cache_user_permissions(self, user_id: int, permissions: List[str], ttl: int = 1800):
        """缓存用户权限"""
        key = CACHE_KEYS['user_permissions'].format(user_id=user_id)
        self.redis.sadd(key, *permissions)
        self.redis.expire(key, ttl)
    
    def get_user_permissions(self, user_id: int) -> List[str]:
        """获取用户权限缓存"""
        key = CACHE_KEYS['user_permissions'].format(user_id=user_id)
        return self.redis.smembers(key)
    
    def invalidate_user_permissions(self, user_id: int):
        """使用户权限缓存失效"""
        key = CACHE_KEYS['user_permissions'].format(user_id=user_id)
        self.redis.delete(key)
    
    # 其他缓存方法...
```

### 6.2 缓存更新策略

- 用户角色变更时，使相关用户的权限缓存失效
- 角色权限变更时，使相关角色的权限缓存失效
- 定期刷新所有缓存，保证数据一致性

## 7. 权限审计

### 7.1 权限变更日志

记录所有权限相关操作，包括：

- 角色创建、修改、删除
- 权限分配、撤销
- 用户角色变更

### 7.2 敏感操作审计

对敏感操作进行额外的审计记录：

- 管理员权限使用记录
- 权限模型变更记录
- 批量权限操作记录

### 7.3 审计报告

提供权限审计报告功能：

- 用户权限变更历史
- 角色权限变更历史
- 异常权限操作检测

## 8. 部署与迁移

### 8.1 初始数据

系统初始化时，预设以下数据：

- 基础角色（Admin, User等）
- 核心权限集
- 角色权限映射关系
- 默认管理员账户

### 8.2 数据迁移

提供数据迁移脚本，用于：

- 初始化权限表
- 更新权限模型
- 角色权限关系迁移

### 8.3 版本兼容

确保权限系统的版本兼容性：

- 向后兼容的API设计
- 权限模型版本控制
- 平滑升级路径

## 9. 安全考量

### 9.1 权限提升防护

防止未授权的权限提升：

- 严格的角色分配验证
- 敏感操作的多重授权
- 权限变更通知机制

### 9.2 最小权限原则

遵循最小权限原则：

- 默认最小权限集
- 基于任务的临时权限
- 定期权限审查和清理

### 9.3 权限隔离

实施权限隔离机制：

- 多租户权限隔离
- 环境权限隔离（开发、测试、生产）
- 数据级权限隔离

## 10. 未来扩展

### 10.1 动态权限

支持动态权限定义：

- 自定义权限创建
- 权限组合和继承
- 基于条件的权限规则

### 10.2 跨服务授权

支持跨服务的权限管理：

- OAuth2.0 集成
- 联合身份验证
- 分布式权限验证

### 10.3 高级功能

计划中的高级功能：

- 基于时间的权限控制
- 基于位置的访问限制
- 权限使用分析和优化 