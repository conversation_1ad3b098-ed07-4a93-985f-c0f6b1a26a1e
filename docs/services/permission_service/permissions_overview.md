# FinSight 权限系统总览文档

## 📖 文档说明

本文档为前端开发者提供 FinSight 系统的完整权限信息，包括角色定义、权限列表、API路由和使用说明。

## 🎯 快速导航

- [角色体系](#角色体系)
- [完整权限列表](#完整权限列表)
- [模块权限分类](#模块权限分类)
- [API接口权限](#api接口权限)
- [前端集成指南](#前端集成指南)

## 🏗️ 角色体系

### 预定义角色

| 角色ID | 角色名称 | 英文名称 | 权限数量 | 角色描述 |
|--------|----------|----------|----------|----------|
| 1 | 系统管理员 | Admin | 57 | 拥有系统最高权限，可管理所有资源和用户 |
| 2 | 数据管理员 | DataAdmin | 27 | 负责数据源和采集任务管理，包含数据相关权限 |
| 3 | 数据分析师 | Analyst | 15 | 负责数据分析和报告生成，可访问分析功能 |
| 4 | 内容编辑 | Editor | 12 | 负责内容创建和编辑，管理文章和资讯 |
| 5 | 客户经理 | AccountManager | 7 | 负责用户服务和账户管理 |
| 6 | 风控专员 | RiskOfficer | 4 | 负责风险监控和合规检查 |
| 7 | 普通用户 | User | 5 | 基础用户，可查看公开内容和个人数据 |
| 8 | 访客 | Guest | 2 | 未注册或未登录用户，只能访问公开内容 |
| 9 | AI工程师 | AIEngineer | 8 | 负责AI模型训练和调优，拥有模型管理权限 |

### 角色层级关系

```
Admin (系统管理员)
├── DataAdmin (数据管理员)
│   └── Analyst (数据分析师)
├── Editor (内容编辑)
├── AccountManager (客户经理)
│   └── User (普通用户)
├── AIEngineer (AI工程师)
└── RiskOfficer (风控专员)
```

## 📝 完整权限列表

### 用户管理权限 (User Module)

| 权限编码 | 权限名称 | 权限描述 | 适用角色 |
|----------|----------|----------|----------|
| `user.profile.read` | 查看用户资料 | 查看用户基本信息 | 所有角色 |
| `user.profile.update` | 更新用户资料 | 修改用户基本信息 | User, AccountManager, Admin |
| `user.list.read` | 查看用户列表 | 查看系统用户列表 | AccountManager, RiskOfficer, Admin |
| `user.detail.read` | 查看用户详情 | 查看用户详细信息 | AccountManager, Admin |
| `user.create` | 创建用户 | 创建新用户账户 | AccountManager, Admin |
| `user.update` | 更新用户 | 修改用户信息 | AccountManager, Admin |
| `user.delete` | 删除用户 | 删除用户账户 | Admin |
| `user.status.update` | 更新用户状态 | 修改用户状态（启用/禁用） | Admin |
| `user.role.assign` | 分配用户角色 | 为用户分配或撤销角色 | Admin |
| `user.role.revoke` | 撤销用户角色 | 撤销用户角色 | Admin |
| `user.role.read` | 查看用户角色 | 查看用户的角色信息 | Admin |
| `user.permission.read` | 查看用户权限 | 查看用户的权限信息 | Admin |
| `user.role.batch.assign` | 批量分配角色 | 批量为用户分配角色 | Admin |
| `user.role.batch.revoke` | 批量撤销角色 | 批量撤销用户角色 | Admin |
| `user.preferences.read` | 读取用户偏好 | 查看用户偏好设置 | User, Admin |
| `user.preferences.update` | 更新用户偏好 | 修改用户偏好设置 | User, Admin |

### 权限管理权限 (Permission Module)

| 权限编码 | 权限名称 | 权限描述 | 适用角色 |
|----------|----------|----------|----------|
| `permission.create` | 创建权限 | 创建新的系统权限 | Admin |
| `permission.read` | 查看权限 | 查看权限详情 | Admin |
| `permission.update` | 更新权限 | 修改权限信息 | Admin |
| `permission.delete` | 删除权限 | 删除权限 | Admin |
| `permission.list.read` | 查看权限列表 | 查看系统权限列表 | Admin |
| `permission.analytics.read` | 查看权限统计 | 查看权限使用统计 | Admin |
| `role.create` | 创建角色 | 创建新角色 | Admin |
| `role.read` | 查看角色 | 查看角色详情 | Admin |
| `role.update` | 更新角色 | 修改角色信息 | Admin |
| `role.delete` | 删除角色 | 删除角色 | Admin |
| `role.list.read` | 查看角色列表 | 查看系统角色列表 | Admin |
| `role.permission.assign` | 分配角色权限 | 为角色分配权限 | Admin |
| `role.permission.revoke` | 撤销角色权限 | 从角色撤销权限 | Admin |
| `role.permission.read` | 查看角色权限 | 查看角色的权限列表 | Admin |
| `role.analytics.read` | 查看角色统计 | 查看角色使用统计 | Admin |

### 数据采集权限 (Data Module)

| 权限编码 | 权限名称 | 权限描述 | 适用角色 |
|----------|----------|----------|----------|
| `data.source.read` | 查看数据源 | 查看数据源配置 | Analyst, DataAdmin, Admin |
| `data.source.create` | 创建数据源 | 创建新的数据源 | DataAdmin, Admin |
| `data.source.update` | 更新数据源 | 修改数据源配置 | DataAdmin, Admin |
| `data.source.delete` | 删除数据源 | 删除数据源 | Admin |
| `data.source.query` | 查询数据源 | 执行数据源查询 | Analyst, DataAdmin, Admin |
| `data.source.list.read` | 查看数据源列表 | 查看数据源列表 | Analyst, DataAdmin, Admin |
| `data.task.read` | 查看采集任务 | 查看数据采集任务 | Analyst, DataAdmin, Admin |
| `data.task.create` | 创建采集任务 | 创建新的采集任务 | DataAdmin, Admin |
| `data.task.update` | 更新采集任务 | 修改采集任务配置 | DataAdmin, Admin |
| `data.task.execute` | 执行采集任务 | 执行数据采集任务 | DataAdmin, Admin |
| `data.task.delete` | 删除采集任务 | 删除采集任务 | Admin |
| `data.task.control` | 控制采集任务 | 启动/停止采集任务 | DataAdmin, Admin |
| `data.task.list.read` | 查看任务列表 | 查看采集任务列表 | Analyst, DataAdmin, Admin |
| `data.query.execute` | 执行数据查询 | 执行数据查询操作 | Analyst, DataAdmin, Admin |
| `data.report.read` | 查看分析报告 | 查看数据分析报告 | User, Analyst, DataAdmin, Admin |

### 数据源管理权限 (Data Source Module)

| 权限编码 | 权限名称 | 权限描述 | 适用角色 |
|----------|----------|----------|----------|
| `data_source.data_source.create` | 创建数据源 | 创建新的数据源 | DataAdmin, Admin |
| `data_source.data_source.read` | 查看数据源 | 查看数据源详情 | Analyst, DataAdmin, Admin |
| `data_source.data_source.update` | 更新数据源 | 修改数据源配置 | DataAdmin, Admin |
| `data_source.data_source.delete` | 删除数据源 | 删除数据源 | Admin |
| `data_source.list.read` | 查看数据源列表 | 查看数据源列表 | Analyst, DataAdmin, Admin |
| `data_source.stats.read` | 查看数据源统计 | 查看数据源统计信息 | Analyst, DataAdmin, Admin |
| `data_source.config.create` | 创建数据源配置 | 创建新的数据源配置 | DataAdmin, Admin |
| `data_source.config.read` | 查看数据源配置 | 查看数据源配置详情 | Analyst, DataAdmin, Admin |
| `data_source.config.update` | 更新数据源配置 | 修改数据源配置 | DataAdmin, Admin |
| `data_source.config.delete` | 删除数据源配置 | 删除数据源配置 | Admin |
| `data_source.config.manage` | 管理数据源配置 | 激活/停用数据源配置 | DataAdmin, Admin |

### 原始数据记录权限 (Raw Data Record Module)

| 权限编码 | 权限名称 | 权限描述 | 适用角色 |
|----------|----------|----------|----------|
| `raw_data_record.raw_data_record.create` | 创建原始数据记录 | 创建新的原始数据记录 | DataAdmin, Admin |
| `raw_data_record.raw_data_record.read` | 查看原始数据记录 | 查看原始数据记录详情 | Analyst, DataAdmin, Admin |
| `raw_data_record.raw_data_record.update` | 更新原始数据记录 | 修改原始数据记录 | DataAdmin, Admin |
| `raw_data_record.raw_data_record.delete` | 删除原始数据记录 | 删除原始数据记录 | Admin |
| `raw_data_record.raw_data_record.manage` | 管理原始数据记录 | 归档原始数据记录 | DataAdmin, Admin |
| `raw_data_record.raw_data_record.analyze` | 分析原始数据记录 | 获取重复记录分析 | Analyst, DataAdmin, Admin |
| `raw_data_record.list.read` | 查看原始数据记录列表 | 查看原始数据记录列表 | Analyst, DataAdmin, Admin |
| `raw_data_record.stats.read` | 查看原始数据记录统计 | 查看原始数据记录统计信息 | Analyst, DataAdmin, Admin |
| `data.report.create` | 创建分析报告 | 创建新的分析报告 | Analyst, DataAdmin, Admin |
| `data.report.update` | 更新分析报告 | 修改分析报告 | Analyst, DataAdmin, Admin |
| `data.report.publish` | 发布分析报告 | 发布分析报告 | Analyst, DataAdmin, Admin |
| `data.report.delete` | 删除分析报告 | 删除分析报告 | Admin |
| `data.export` | 导出数据 | 导出数据到文件 | Analyst, DataAdmin, Admin |
| `data.access.grant` | 授权数据访问 | 授权用户访问数据 | DataAdmin, Admin |
| `data.access.revoke` | 撤销数据访问 | 撤销用户数据访问权限 | DataAdmin, Admin |
| `data.monitoring.read` | 查看数据监控 | 查看数据采集监控信息 | DataAdmin, Admin |

### 内容管理权限 (Content Module)

| 权限编码 | 权限名称 | 权限描述 | 适用角色 |
|----------|----------|----------|----------|
| `content.article.read` | 查看文章 | 查看文章内容 | 所有角色 |
| `content.article.create` | 创建文章 | 创建新文章 | Editor, Admin |
| `content.article.update` | 更新文章 | 修改文章内容 | Editor, Admin |
| `content.article.publish` | 发布文章 | 发布文章到公开平台 | Editor, Admin |
| `content.article.delete` | 删除文章 | 删除文章 | Editor, Admin |
| `content.comment.moderate` | 管理评论 | 审核和管理文章评论 | Editor, Admin |

### 财经日历权限 (Financial Module)

| 权限编码 | 权限名称 | 权限描述 | 适用角色 |
|----------|----------|----------|----------|
| `financial.calendar.read` | 查看财经日历 | 查看财经日历事件 | 所有角色 |
| `financial.calendar.create` | 创建财经事件 | 创建新的财经事件 | Editor, Admin |
| `financial.calendar.update` | 更新财经事件 | 修改财经事件信息 | Editor, Admin |
| `financial.calendar.delete` | 删除财经事件 | 删除财经事件 | Editor, Admin |
| `calendar.event.detail.read` | 查看事件详情 | 查看财经事件详细信息 | User, Editor, Admin |
| `calendar.event.list.read` | 查看事件列表 | 查看财经事件列表 | User, Editor, Admin |
| `calendar.subscription.read` | 查看订阅 | 查看日历订阅信息 | User, Editor, Admin |
| `calendar.subscription.create` | 创建订阅 | 创建日历订阅 | User, Editor, Admin |
| `calendar.indicator.read` | 查看经济指标 | 查看经济指标数据 | User, Editor, Admin |
| `calendar.indicator.create` | 创建经济指标 | 创建经济指标 | Editor, Admin |
| `calendar.indicator.list.read` | 查看指标列表 | 查看经济指标列表 | User, Editor, Admin |
| `calendar.analytics.read` | 查看日历分析 | 查看日历分析报告 | Editor, Admin |

### 标签分类权限 (Tag Module)

| 权限编码 | 权限名称 | 权限描述 | 适用角色 |
|----------|----------|----------|----------|
| `tag.read` | 查看标签 | 查看标签信息 | User, Editor, Admin |
| `tag.create` | 创建标签 | 创建新标签 | Editor, Admin |
| `tag.update` | 更新标签 | 修改标签信息 | Editor, Admin |
| `tag.delete` | 删除标签 | 删除标签 | Editor, Admin |
| `tag.list.read` | 查看标签列表 | 查看标签列表 | User, Editor, Admin |
| `tag.type.create` | 创建标签类型 | 创建新的标签类型 | Editor, Admin |
| `tag.type.update` | 更新标签类型 | 修改标签类型 | Editor, Admin |
| `tag.type.delete` | 删除标签类型 | 删除标签类型 | Editor, Admin |
| `tag.category.create` | 创建标签分类 | 创建标签分类 | Editor, Admin |
| `tag.category.update` | 更新标签分类 | 修改标签分类 | Editor, Admin |
| `tag.category.delete` | 删除标签分类 | 删除标签分类 | Editor, Admin |
| `tag.preference.read` | 查看标签偏好 | 查看用户标签偏好 | User, Admin |
| `tag.preference.update` | 更新标签偏好 | 修改用户标签偏好 | User, Admin |
| `tag.recommendation.read` | 查看标签推荐 | 查看标签推荐 | User, Admin |
| `tag.analytics.read` | 查看标签统计 | 查看标签使用统计 | Editor, Admin |
| `classification.dimension.create` | 创建分类维度 | 创建分类维度 | Editor, Admin |
| `classification.dimension.update` | 更新分类维度 | 修改分类维度 | Editor, Admin |
| `classification.dimension.delete` | 删除分类维度 | 删除分类维度 | Editor, Admin |
| `classification.value.create` | 创建分类值 | 创建分类值 | Editor, Admin |
| `classification.value.update` | 更新分类值 | 修改分类值 | Editor, Admin |
| `classification.value.delete` | 删除分类值 | 删除分类值 | Editor, Admin |
| `classification.analytics.read` | 查看分类统计 | 查看分类使用统计 | Editor, Admin |
| `user.preference.manage` | 管理用户偏好 | 管理用户偏好设置 | Admin |
| `user.tag.analytics.read` | 查看用户标签统计 | 查看用户标签使用统计 | Admin |
| `category.create` | 创建分类 | 创建新分类 | Editor, Admin |
| `category.update` | 更新分类 | 修改分类信息 | Editor, Admin |
| `category.list.read` | 查看分类列表 | 查看分类列表 | User, Editor, Admin |

### 短信服务权限 (SMS Module)

| 权限编码 | 权限名称 | 权限描述 | 适用角色 |
|----------|----------|----------|----------|
| `sms.history.read` | 查看短信历史 | 查看短信发送历史 | Admin |
| `sms.business.create` | 创建短信业务 | 创建短信业务配置 | Admin |
| `sms.business.update` | 更新短信业务 | 修改短信业务配置 | Admin |
| `sms.business.list.read` | 查看业务列表 | 查看短信业务列表 | Admin |
| `sms.template.create` | 创建短信模板 | 创建短信模板 | Admin |
| `sms.template.list.read` | 查看模板列表 | 查看短信模板列表 | Admin |
| `sms.signature.create` | 创建短信签名 | 创建短信签名 | Admin |
| `sms.batch.send` | 批量发送短信 | 批量发送短信 | Admin |
| `sms.statistics.read` | 查看短信统计 | 查看短信发送统计 | Admin |
| `sms.logs.read` | 查看短信日志 | 查看短信操作日志 | Admin |

### 信息处理权限 (Information Module)

| 权限编码 | 权限名称 | 权限描述 | 适用角色 |
|----------|----------|----------|----------|
| `information.article.read` | 查看文章 | 查看信息处理的文章 | User, Editor, Admin |
| `information.article.create` | 创建文章 | 创建信息文章 | Editor, Admin |
| `information.article.update` | 更新文章 | 修改信息文章 | Editor, Admin |
| `information.article.delete` | 删除文章 | 删除信息文章 | Editor, Admin |
| `information.source.create` | 创建信息源 | 创建信息源配置 | Editor, Admin |
| `information.source.update` | 更新信息源 | 修改信息源配置 | Editor, Admin |
| `information.processing.read` | 查看处理任务 | 查看信息处理任务 | Editor, Admin |
| `information.processing.control` | 控制处理任务 | 控制信息处理任务 | Editor, Admin |
| `information.quality.manage` | 管理质量规则 | 管理信息质量规则 | Editor, Admin |
| `information.analytics.read` | 查看处理分析 | 查看信息处理分析 | Editor, Admin |

### 推送服务权限 (Push Module)

| 权限编码 | 权限名称 | 权限描述 | 适用角色 |
|----------|----------|----------|----------|
| `push.message.send` | 发送推送消息 | 发送推送消息 | Editor, Admin |
| `push.template.create` | 创建推送模板 | 创建推送消息模板 | Editor, Admin |
| `push.template.update` | 更新推送模板 | 修改推送消息模板 | Editor, Admin |
| `push.log.read` | 查看推送日志 | 查看推送操作日志 | Editor, Admin |

### AI 模型管理权限 (AI Model Module)

| 权限编码 | 权限名称 | 权限描述 | 适用角色 |
|----------|----------|----------|----------|
| `ai_model.model.create` | 创建AI模型 | 创建新的AI模型配置 | DataAdmin, AIEngineer, Admin |
| `ai_model.model.read` | 查看AI模型 | 查看AI模型详细信息 | Analyst, DataAdmin, AIEngineer, Admin |
| `ai_model.model.update` | 更新AI模型 | 修改AI模型配置和使用统计 | DataAdmin, AIEngineer, Admin |
| `ai_model.model.delete` | 删除AI模型 | 删除指定的AI模型 | DataAdmin, Admin |
| `ai_model.list.read` | 查看模型列表 | 查看AI模型列表和可用模型 | User, Analyst, DataAdmin, AIEngineer, Admin |
| `ai_model.status.update` | 更新模型状态 | 修改模型状态（激活/停用/测试/废弃） | DataAdmin, AIEngineer, Admin |
| `ai_model.default.update` | 设置默认模型 | 设置或取消模型为默认模型 | DataAdmin, Admin |
| `ai_model.metrics.create` | 创建模型指标 | 为指定模型创建指标记录 | DataAdmin, AIEngineer, Admin |
| `ai_model.metrics.read` | 查看模型指标 | 获取指定模型的指标数据 | Analyst, DataAdmin, AIEngineer, Admin |
| `ai_model.analytics.read` | 查看分析数据 | 获取系统信息、模型性能和对比分析 | User, Analyst, DataAdmin, AIEngineer, Admin |

### 数据处理规则管理权限 (Data Processing Module)

| 权限编码 | 权限名称 | 权限描述 | 适用角色 |
|----------|----------|----------|----------|
| `data_processing.rule.create` | 创建数据处理规则 | 创建新的数据处理规则配置 | DataAdmin, Admin |
| `data_processing.rule.read` | 查看数据处理规则 | 查看数据处理规则详细信息 | Analyst, DataAdmin, Admin |
| `data_processing.rule.update` | 更新数据处理规则 | 修改数据处理规则配置 | DataAdmin, Admin |
| `data_processing.rule.delete` | 删除数据处理规则 | 删除指定的数据处理规则 | DataAdmin, Admin |
| `data_processing.rule.manage` | 管理数据处理规则 | 完整管理数据处理规则（包含所有操作） | DataAdmin, Admin |
| `data_processing.rule.test` | 测试数据处理规则 | 对数据处理规则进行测试验证 | DataAdmin, Admin |
| `data_processing.list.read` | 查看处理规则列表 | 查看数据处理规则列表和搜索 | Analyst, DataAdmin, Admin |
| `data_processing.stats.read` | 查看处理规则统计 | 查看数据处理规则执行统计数据 | Analyst, DataAdmin, Admin |
| `data_processing.batch.update` | 批量更新处理规则 | 批量更新处理规则状态和优先级 | DataAdmin, Admin |
| `data_processing.status.read` | 查看处理状态 | 查看数据处理状态和进度 | Analyst, DataAdmin, Admin |
| `data_processing.status.update` | 更新处理状态 | 更新数据处理状态和配置 | DataAdmin, Admin |

### 系统管理权限 (System Module)

| 权限编码 | 权限名称 | 权限描述 | 适用角色 |
|----------|----------|----------|----------|
| `system.config.read` | 查看系统配置 | 查看系统配置参数 | Admin |
| `system.config.update` | 更新系统配置 | 修改系统配置参数 | Admin |
| `system.log.read` | 查看系统日志 | 查看系统操作日志 | RiskOfficer, Admin |
| `system.backup` | 系统备份 | 执行系统备份操作 | Admin |
| `system.permission.manage` | 管理权限设置 | 管理系统权限配置 | Admin |

## 🚀 API接口权限

### C端用户接口 (不需要特殊权限)

| 接口路径 | 方法 | 权限要求 | 说明 |
|----------|------|----------|------|
| `/api/v1/permissions/me` | GET | 登录用户 | 获取当前用户权限 |
| `/api/v1/permissions/check` | POST | 登录用户 | 检查当前用户权限 |

### B端管理接口 (需要管理权限)

| 接口路径 | 方法 | 权限要求 | 说明 |
|----------|------|----------|------|
| `/api/v1/admin/permissions/permissions` | GET | `permission.list.read` | 获取权限列表 |
| `/api/v1/admin/permissions/permissions` | POST | `permission.create` | 创建权限 |
| `/api/v1/admin/permissions/permissions/{id}` | GET | `permission.read` | 获取权限详情 |
| `/api/v1/admin/permissions/permissions/{id}` | PUT | `permission.update` | 更新权限 |
| `/api/v1/admin/permissions/permissions/{id}` | DELETE | `permission.delete` | 删除权限 |
| `/api/v1/admin/permissions/roles` | GET | `role.list.read` | 获取角色列表 |
| `/api/v1/admin/permissions/roles` | POST | `role.create` | 创建角色 |
| `/api/v1/admin/permissions/roles/{id}` | GET | `role.read` | 获取角色详情 |
| `/api/v1/admin/permissions/roles/{id}` | PUT | `role.update` | 更新角色 |
| `/api/v1/admin/permissions/roles/{id}` | DELETE | `role.delete` | 删除角色 |
| `/api/v1/admin/permissions/roles/{id}/permissions` | GET | `role.permission.read` | 获取角色权限 |
| `/api/v1/admin/permissions/roles/{id}/permissions` | POST | `role.permission.assign` | 分配角色权限 |
| `/api/v1/admin/permissions/users/{id}/roles` | GET | `user.role.read` | 获取用户角色 |
| `/api/v1/admin/permissions/users/{id}/roles` | POST | `user.role.assign` | 分配用户角色 |
| `/api/v1/admin/permissions/users/{id}/permissions` | GET | `user.permission.read` | 获取用户权限 |

### 数据处理管理接口

| 接口路径 | 方法 | 权限要求 | 说明 |
|----------|------|----------|------|
| `/api/v1/admin/data-processing/rules` | GET | `data_processing.list.read` | 获取数据处理规则列表 |
| `/api/v1/admin/data-processing/rules` | POST | `data_processing.rule.create` | 创建数据处理规则 |
| `/api/v1/admin/data-processing/rules/{id}` | GET | `data_processing.rule.read` | 获取数据处理规则详情 |
| `/api/v1/admin/data-processing/rules/{id}` | PUT | `data_processing.rule.update` | 更新数据处理规则 |
| `/api/v1/admin/data-processing/rules/{id}` | DELETE | `data_processing.rule.delete` | 删除数据处理规则 |
| `/api/v1/admin/data-processing/rules/stats` | GET | `data_processing.stats.read` | 获取处理规则统计 |
| `/api/v1/admin/data-processing/rules/business-type/{type}/active` | GET | `data_processing.list.read` | 获取指定类型的活跃规则 |
| `/api/v1/admin/data-processing/rules/batch/status` | PATCH | `data_processing.batch.update` | 批量更新规则状态 |
| `/api/v1/admin/data-processing/rules/batch/priority` | PATCH | `data_processing.batch.update` | 批量更新规则优先级 |
| `/api/v1/admin/data-processing/rules/{id}/test` | POST | `data_processing.rule.test` | 测试数据处理规则 |

## 🔧 前端集成指南

### 1. 获取用户权限

```javascript
// 获取当前用户权限
const getUserPermissions = async () => {
  try {
    const response = await fetch('/api/v1/permissions/me', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    const data = await response.json();
    return {
      roles: data.roles,           // 用户角色列表
      permissions: data.permissions, // 权限对象列表
      permissionCodes: data.permission_codes // 权限编码列表
    };
  } catch (error) {
    console.error('获取用户权限失败:', error);
    return null;
  }
};
```

### 2. 权限检查

```javascript
// 检查用户是否有指定权限
const hasPermission = (userPermissions, permissionCode) => {
  return userPermissions.permission_codes.includes(permissionCode);
};

// 检查用户是否有指定角色
const hasRole = (userPermissions, roleName) => {
  return userPermissions.roles.some(role => role.name === roleName);
};

// 检查用户是否有任一权限
const hasAnyPermission = (userPermissions, permissionCodes) => {
  return permissionCodes.some(code => 
    userPermissions.permission_codes.includes(code)
  );
};
```

### 3. Vue.js 权限指令

```javascript
// 权限指令
const permissionDirective = {
  mounted(el, binding) {
    const { value: permissionCode } = binding;
    const userPermissions = getCurrentUserPermissions(); // 获取当前用户权限
    
    if (!hasPermission(userPermissions, permissionCode)) {
      el.style.display = 'none';
      // 或者 el.parentNode?.removeChild(el);
    }
  },
  updated(el, binding) {
    const { value: permissionCode } = binding;
    const userPermissions = getCurrentUserPermissions();
    
    if (!hasPermission(userPermissions, permissionCode)) {
      el.style.display = 'none';
    } else {
      el.style.display = '';
    }
  }
};

// 注册指令
app.directive('permission', permissionDirective);
```

### 4. React 权限组件

```jsx
// 权限包装组件
const PermissionWrapper = ({ permission, children, fallback = null }) => {
  const userPermissions = useUserPermissions();
  
  if (!hasPermission(userPermissions, permission)) {
    return fallback;
  }
  
  return children;
};

// 使用示例
<PermissionWrapper permission="user.create">
  <button onClick={handleCreateUser}>创建用户</button>
</PermissionWrapper>
```

### 5. 路由权限控制

```javascript
// Vue Router 权限控制
router.beforeEach(async (to, from, next) => {
  const requiredPermission = to.meta.permission;
  
  if (!requiredPermission) {
    next();
    return;
  }
  
  const userPermissions = await getUserPermissions();
  
  if (hasPermission(userPermissions, requiredPermission)) {
    next();
  } else {
    next('/unauthorized');
  }
});

// 路由定义示例
const routes = [
  {
    path: '/admin/users',
    component: UserManagement,
    meta: { permission: 'user.list.read' }
  },
  {
    path: '/admin/permissions',
    component: PermissionManagement,
    meta: { permission: 'permission.list.read' }
  }
];
```

### 6. 权限常量定义

```javascript
// permissions.js
export const PERMISSIONS = {
  // 用户管理
  USER: {
    PROFILE_READ: 'user.profile.read',
    PROFILE_UPDATE: 'user.profile.update',
    LIST_READ: 'user.list.read',
    CREATE: 'user.create',
    UPDATE: 'user.update',
    DELETE: 'user.delete',
    ROLE_ASSIGN: 'user.role.assign'
  },
  
  // 数据管理
  DATA: {
    SOURCE_READ: 'data.source.read',
    SOURCE_CREATE: 'data.source.create',
    SOURCE_UPDATE: 'data.source.update',
    SOURCE_DELETE: 'data.source.delete',
    TASK_EXECUTE: 'data.task.execute',
    REPORT_CREATE: 'data.report.create'
  },
  
  // 系统管理
  SYSTEM: {
    CONFIG_READ: 'system.config.read',
    CONFIG_UPDATE: 'system.config.update',
    LOG_READ: 'system.log.read',
    PERMISSION_MANAGE: 'system.permission.manage'
  }
};

// 角色常量
export const ROLES = {
  ADMIN: 'Admin',
  DATA_ADMIN: 'DataAdmin',
  ANALYST: 'Analyst',
  EDITOR: 'Editor',
  ACCOUNT_MANAGER: 'AccountManager',
  RISK_OFFICER: 'RiskOfficer',
  USER: 'User',
  GUEST: 'Guest',
  AI_ENGINEER: 'AIEngineer'
};
```

## 📚 相关文档

- [RBAC设计文档](./rbac_design.md) - 详细的权限系统设计
- [API实现文档](./api_implementation.md) - API接口实现详情
- [部署指南](./deployment_guide.md) - 权限系统部署指南
- [缓存策略](./cache_and_migration.md) - 权限缓存和迁移
- [接口设计指南](./interface_design_guide.md) - 微服务架构设计

## 🔗 快速链接

- [权限管理界面] - `/admin/permissions` (需要 `permission.list.read` 权限)
- [角色管理界面] - `/admin/roles` (需要 `role.list.read` 权限)  
- [用户管理界面] - `/admin/users` (需要 `user.list.read` 权限)
- [系统配置界面] - `/admin/system` (需要 `system.config.read` 权限)

---

**注意**: 本文档会随着系统功能的增加而持续更新，请以最新版本为准。如有疑问或建议，请联系系统管理员。