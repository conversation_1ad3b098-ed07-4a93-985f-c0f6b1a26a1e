# 权限系统API实现

## 1. 依赖注入配置 (dependencies.py)

```python
"""
权限服务依赖注入
定义权限相关的依赖
"""

from typing import Annotated
from fastapi import Depends, HTTPException, status
from sqlalchemy.orm import Session

from ...core.database import get_db
from ...core.cache import get_cache_service
from ..user_service.dependencies import get_current_active_user
from ..user_service.models import User
from .service import PermissionService, RoleService, UserRoleService, AuthorizationService


def get_permission_service(
    db: Annotated[Session, Depends(get_db)],
    cache_service = Depends(get_cache_service)
) -> PermissionService:
    """获取权限服务"""
    return PermissionService(db, cache_service)


def get_role_service(
    db: Annotated[Session, Depends(get_db)],
    cache_service = Depends(get_cache_service)
) -> RoleService:
    """获取角色服务"""
    return RoleService(db, cache_service)


def get_user_role_service(
    db: Annotated[Session, Depends(get_db)],
    cache_service = Depends(get_cache_service)
) -> UserRoleService:
    """获取用户角色服务"""
    return UserRoleService(db, cache_service)


def get_authorization_service(
    db: Annotated[Session, Depends(get_db)],
    cache_service = Depends(get_cache_service)
) -> AuthorizationService:
    """获取权限验证服务"""
    return AuthorizationService(db, cache_service)


def require_permission(permission_code: str):
    """
    权限验证装饰器依赖
    
    Args:
        permission_code: 需要的权限编码
    """
    def permission_checker(
        current_user: Annotated[User, Depends(get_current_active_user)],
        auth_service: Annotated[AuthorizationService, Depends(get_authorization_service)]
    ):
        if not auth_service.has_permission(current_user.id, permission_code):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Permission required: {permission_code}"
            )
        return current_user
    
    return permission_checker


def require_admin(
    current_user: Annotated[User, Depends(get_current_active_user)],
    user_role_service: Annotated[UserRoleService, Depends(get_user_role_service)]
):
    """要求管理员权限"""
    if not user_role_service.has_role_by_name(current_user.id, "Admin"):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin permission required"
        )
    return current_user
```

## 2. API路由实现 (router.py)

```python
"""
权限服务API路由
定义权限管理相关的HTTP接口
"""

from typing import Annotated, List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session

from ...core.database import get_db
from ..user_service.dependencies import get_current_active_user
from ..user_service.models import User
from .dependencies import (
    get_permission_service, get_role_service, get_user_role_service,
    get_authorization_service, require_admin, require_permission
)
from .schemas import (
    PermissionCreate, PermissionUpdate, PermissionResponse,
    RoleCreate, RoleUpdate, RoleResponse,
    UserRoleAssign, UserRoleResponse,
    RolePermissionAssign, RolePermissionResponse,
    PermissionCheckRequest, PermissionCheckResponse,
    UserPermissionsResponse, PermissionOperationLogResponse
)
from .service import PermissionService, RoleService, UserRoleService, AuthorizationService

router = APIRouter(prefix="/api/v1/permissions", tags=["权限管理"])


# ==================== 权限管理接口 ====================

@router.post(
    "/permissions",
    response_model=PermissionResponse,
    status_code=status.HTTP_201_CREATED,
    summary="创建权限",
    description="创建新的系统权限"
)
async def create_permission(
    permission_data: PermissionCreate,
    current_user: Annotated[User, Depends(require_admin)],
    permission_service: Annotated[PermissionService, Depends(get_permission_service)]
):
    """创建权限"""
    try:
        permission = permission_service.create_permission(permission_data)
        return PermissionResponse.model_validate(permission)
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


@router.get(
    "/permissions",
    response_model=List[PermissionResponse],
    summary="获取权限列表",
    description="获取系统中所有权限"
)
async def get_permissions(
    current_user: Annotated[User, Depends(get_current_active_user)],
    permission_service: Annotated[PermissionService, Depends(get_permission_service)],
    module: Optional[str] = Query(None, description="按模块过滤")
):
    """获取权限列表"""
    permissions = permission_service.get_all_permissions(module=module)
    return [PermissionResponse.model_validate(perm) for perm in permissions]


@router.get(
    "/permissions/{permission_id}",
    response_model=PermissionResponse,
    summary="获取权限详情",
    description="根据ID获取权限详细信息"
)
async def get_permission(
    permission_id: int,
    current_user: Annotated[User, Depends(get_current_active_user)],
    permission_service: Annotated[PermissionService, Depends(get_permission_service)]
):
    """获取权限详情"""
    permission = permission_service.get_permission(permission_id)
    if not permission:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Permission not found"
        )
    return PermissionResponse.model_validate(permission)


@router.put(
    "/permissions/{permission_id}",
    response_model=PermissionResponse,
    summary="更新权限",
    description="更新权限信息"
)
async def update_permission(
    permission_id: int,
    permission_data: PermissionUpdate,
    current_user: Annotated[User, Depends(require_admin)],
    permission_service: Annotated[PermissionService, Depends(get_permission_service)]
):
    """更新权限"""
    permission = permission_service.update_permission(permission_id, permission_data)
    if not permission:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Permission not found"
        )
    return PermissionResponse.model_validate(permission)


@router.delete(
    "/permissions/{permission_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="删除权限",
    description="删除权限"
)
async def delete_permission(
    permission_id: int,
    current_user: Annotated[User, Depends(require_admin)],
    permission_service: Annotated[PermissionService, Depends(get_permission_service)]
):
    """删除权限"""
    success = permission_service.delete_permission(permission_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Permission not found"
        )


# ==================== 角色管理接口 ====================

@router.post(
    "/roles",
    response_model=RoleResponse,
    status_code=status.HTTP_201_CREATED,
    summary="创建角色",
    description="创建新的角色"
)
async def create_role(
    role_data: RoleCreate,
    current_user: Annotated[User, Depends(require_admin)],
    role_service: Annotated[RoleService, Depends(get_role_service)]
):
    """创建角色"""
    try:
        role = role_service.create_role(role_data)
        return RoleResponse.model_validate(role)
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


@router.get(
    "/roles",
    response_model=List[RoleResponse],
    summary="获取角色列表",
    description="获取系统中所有角色"
)
async def get_roles(
    current_user: Annotated[User, Depends(get_current_active_user)],
    role_service: Annotated[RoleService, Depends(get_role_service)]
):
    """获取角色列表"""
    roles = role_service.get_all_roles()
    return [RoleResponse.model_validate(role) for role in roles]


@router.get(
    "/roles/{role_id}",
    response_model=RoleResponse,
    summary="获取角色详情",
    description="根据ID获取角色详细信息"
)
async def get_role(
    role_id: int,
    current_user: Annotated[User, Depends(get_current_active_user)],
    role_service: Annotated[RoleService, Depends(get_role_service)]
):
    """获取角色详情"""
    role = role_service.get_role(role_id)
    if not role:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Role not found"
        )
    return RoleResponse.model_validate(role)


@router.put(
    "/roles/{role_id}",
    response_model=RoleResponse,
    summary="更新角色",
    description="更新角色信息"
)
async def update_role(
    role_id: int,
    role_data: RoleUpdate,
    current_user: Annotated[User, Depends(require_admin)],
    role_service: Annotated[RoleService, Depends(get_role_service)]
):
    """更新角色"""
    try:
        role = role_service.update_role(role_id, role_data)
        if not role:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Role not found"
            )
        return RoleResponse.model_validate(role)
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


@router.delete(
    "/roles/{role_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="删除角色",
    description="删除角色"
)
async def delete_role(
    role_id: int,
    current_user: Annotated[User, Depends(require_admin)],
    role_service: Annotated[RoleService, Depends(get_role_service)]
):
    """删除角色"""
    try:
        success = role_service.delete_role(role_id)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Role not found"
            )
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


# ==================== 角色权限管理接口 ====================

@router.post(
    "/roles/{role_id}/permissions/{permission_id}",
    response_model=RolePermissionResponse,
    status_code=status.HTTP_201_CREATED,
    summary="为角色分配权限",
    description="为指定角色分配权限"
)
async def assign_permission_to_role(
    role_id: int,
    permission_id: int,
    current_user: Annotated[User, Depends(require_admin)],
    role_service: Annotated[RoleService, Depends(get_role_service)]
):
    """为角色分配权限"""
    try:
        success = role_service.assign_permission_to_role(role_id, permission_id)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Permission already assigned to role"
            )
        # 返回成功信息
        return {"role_id": role_id, "permission_id": permission_id, "status": "assigned"}
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


@router.delete(
    "/roles/{role_id}/permissions/{permission_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="从角色撤销权限",
    description="从指定角色撤销权限"
)
async def revoke_permission_from_role(
    role_id: int,
    permission_id: int,
    current_user: Annotated[User, Depends(require_admin)],
    role_service: Annotated[RoleService, Depends(get_role_service)]
):
    """从角色撤销权限"""
    success = role_service.revoke_permission_from_role(role_id, permission_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Permission not found for this role"
        )


@router.get(
    "/roles/{role_id}/permissions",
    response_model=List[PermissionResponse],
    summary="获取角色权限",
    description="获取指定角色的所有权限"
)
async def get_role_permissions(
    role_id: int,
    current_user: Annotated[User, Depends(get_current_active_user)],
    role_service: Annotated[RoleService, Depends(get_role_service)]
):
    """获取角色权限"""
    permissions = role_service.get_role_permissions(role_id)
    return [PermissionResponse.model_validate(perm) for perm in permissions]


# ==================== 用户角色管理接口 ====================

@router.post(
    "/users/{user_id}/roles/{role_id}",
    response_model=UserRoleResponse,
    status_code=status.HTTP_201_CREATED,
    summary="为用户分配角色",
    description="为指定用户分配角色"
)
async def assign_role_to_user(
    user_id: int,
    role_id: int,
    current_user: Annotated[User, Depends(require_admin)],
    user_role_service: Annotated[UserRoleService, Depends(get_user_role_service)]
):
    """为用户分配角色"""
    try:
        success = user_role_service.assign_role_to_user(user_id, role_id, current_user.id)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Role already assigned to user"
            )
        return {"user_id": user_id, "role_id": role_id, "status": "assigned"}
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


@router.delete(
    "/users/{user_id}/roles/{role_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="从用户撤销角色",
    description="从指定用户撤销角色"
)
async def revoke_role_from_user(
    user_id: int,
    role_id: int,
    current_user: Annotated[User, Depends(require_admin)],
    user_role_service: Annotated[UserRoleService, Depends(get_user_role_service)]
):
    """从用户撤销角色"""
    success = user_role_service.revoke_role_from_user(user_id, role_id, current_user.id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Role not found for this user"
        )


@router.get(
    "/users/{user_id}/roles",
    response_model=List[RoleResponse],
    summary="获取用户角色",
    description="获取指定用户的所有角色"
)
async def get_user_roles(
    user_id: int,
    current_user: Annotated[User, Depends(get_current_active_user)],
    user_role_service: Annotated[UserRoleService, Depends(get_user_role_service)]
):
    """获取用户角色"""
    # 检查权限：用户只能查看自己的角色，管理员可以查看所有用户角色
    if current_user.id != user_id and not user_role_service.has_role_by_name(current_user.id, "Admin"):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    
    roles = user_role_service.get_user_roles(user_id)
    return [RoleResponse.model_validate(role) for role in roles]


# ==================== 权限验证接口 ====================

@router.post(
    "/check",
    response_model=PermissionCheckResponse,
    summary="检查用户权限",
    description="检查用户是否拥有指定权限"
)
async def check_permission(
    check_request: PermissionCheckRequest,
    current_user: Annotated[User, Depends(get_current_active_user)],
    auth_service: Annotated[AuthorizationService, Depends(get_authorization_service)]
):
    """检查用户权限"""
    # 只能检查自己的权限，或者管理员可以检查任何用户的权限
    if (check_request.user_id != current_user.id and 
        not auth_service.has_permission(current_user.id, "system.permission.manage")):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    
    has_permission = auth_service.has_permission(
        check_request.user_id, 
        check_request.permission_code
    )
    
    return PermissionCheckResponse(
        has_permission=has_permission,
        user_id=check_request.user_id,
        permission_code=check_request.permission_code
    )


@router.get(
    "/users/{user_id}/permissions",
    response_model=UserPermissionsResponse,
    summary="获取用户权限列表",
    description="获取用户的所有权限和角色"
)
async def get_user_permissions(
    user_id: int,
    current_user: Annotated[User, Depends(get_current_active_user)],
    auth_service: Annotated[AuthorizationService, Depends(get_authorization_service)]
):
    """获取用户权限列表"""
    # 检查权限：用户只能查看自己的权限，管理员可以查看所有用户权限
    if (user_id != current_user.id and 
        not auth_service.has_permission(current_user.id, "system.permission.manage")):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    
    permissions = auth_service.get_user_permission_codes(user_id)
    roles = auth_service.get_user_role_names(user_id)
    
    return UserPermissionsResponse(
        user_id=user_id,
        permissions=permissions,
        roles=roles
    )


@router.get(
    "/users/me/permissions",
    response_model=UserPermissionsResponse,
    summary="获取当前用户权限",
    description="获取当前登录用户的所有权限和角色"
)
async def get_current_user_permissions(
    current_user: Annotated[User, Depends(get_current_active_user)],
    auth_service: Annotated[AuthorizationService, Depends(get_authorization_service)]
):
    """获取当前用户权限列表"""
    permissions = auth_service.get_user_permission_codes(current_user.id)
    roles = auth_service.get_user_role_names(current_user.id)
    
    return UserPermissionsResponse(
        user_id=current_user.id,
        permissions=permissions,
        roles=roles
    )
```

## 3. 权限验证中间件

```python
"""
权限验证中间件
用于全局权限控制
"""

from typing import Dict, Optional
from fastapi import Request, HTTPException, status
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import Response

from .service import AuthorizationService


class PermissionMiddleware(BaseHTTPMiddleware):
    """权限验证中间件"""
    
    def __init__(self, app, auth_service: AuthorizationService):
        super().__init__(app)
        self.auth_service = auth_service
        
        # 定义路由权限映射
        self.route_permissions = {
            # 用户管理
            "GET:/api/v1/users": "user.list.read",
            "POST:/api/v1/users": "user.create",
            "DELETE:/api/v1/users": "user.delete",
            
            # 数据采集
            "GET:/api/v1/data/sources": "data.source.read",
            "POST:/api/v1/data/sources": "data.source.create",
            "PUT:/api/v1/data/sources": "data.source.update",
            "DELETE:/api/v1/data/sources": "data.source.delete",
            
            # 系统配置
            "GET:/api/v1/system/config": "system.config.read",
            "PUT:/api/v1/system/config": "system.config.update",
        }
    
    async def dispatch(self, request: Request, call_next):
        """处理请求权限验证"""
        # 构建路由键
        route_key = f"{request.method}:{request.url.path}"
        
        # 检查是否需要权限验证
        required_permission = self.route_permissions.get(route_key)
        
        if required_permission:
            # 获取当前用户
            user = getattr(request.state, 'user', None)
            
            if not user:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Authentication required"
                )
            
            # 验证权限
            if not self.auth_service.has_permission(user.id, required_permission):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"Permission required: {required_permission}"
                )
        
        # 继续处理请求
        response = await call_next(request)
        return response


def get_route_permission(request: Request) -> Optional[str]:
    """获取路由需要的权限"""
    # 可以从路由元数据或注解中获取权限要求
    # 这里简化处理，实际可以更复杂
    route = request.scope.get("route")
    if route:
        return getattr(route, "permission", None)
    return None
```

## 4. 使用示例

### 4.1 在控制器中使用权限验证

```python
from .dependencies import require_permission

@router.get("/sensitive-data")
async def get_sensitive_data(
    current_user: Annotated[User, Depends(require_permission("data.sensitive.read"))],
    # 其他依赖...
):
    """获取敏感数据 - 需要特定权限"""
    # 业务逻辑
    pass

@router.post("/admin-action")
async def admin_action(
    current_user: Annotated[User, Depends(require_admin)],
    # 其他依赖...
):
    """管理员操作 - 需要管理员权限"""
    # 业务逻辑
    pass
```

### 4.2 手动权限检查

```python
@router.get("/conditional-data")
async def get_conditional_data(
    current_user: Annotated[User, Depends(get_current_active_user)],
    auth_service: Annotated[AuthorizationService, Depends(get_authorization_service)]
):
    """根据权限返回不同数据"""
    base_data = get_base_data()
    
    # 根据权限添加额外数据
    if auth_service.has_permission(current_user.id, "data.detailed.read"):
        base_data["detailed_info"] = get_detailed_info()
    
    if auth_service.has_permission(current_user.id, "data.financial.read"):
        base_data["financial_data"] = get_financial_data()
    
    return base_data
``` 