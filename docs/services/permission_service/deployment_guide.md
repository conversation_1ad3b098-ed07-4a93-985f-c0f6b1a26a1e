# 权限系统部署和使用指南

## 1. 部署步骤

### 1.1 环境准备

确保以下组件已安装并配置：

- PostgreSQL 12+
- Redis 6+
- Python 3.8+
- 相关Python依赖包

### 1.2 数据库初始化

```bash
# 1. 执行数据库迁移脚本
psql -U postgres -d finsight -f migrations/001_create_permission_tables.sql
psql -U postgres -d finsight -f migrations/002_insert_initial_data.sql

# 2. 验证表创建
psql -U postgres -d finsight -c "\dt" | grep -E "(roles|permissions|user_roles|role_permissions)"
```

### 1.3 服务配置

在环境变量中添加权限相关配置：

```bash
# .env 文件
# Redis配置（用于权限缓存）
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=your_redis_password

# 权限缓存配置
PERMISSION_CACHE_TTL=1800  # 30分钟
ROLE_CACHE_TTL=3600        # 1小时

# 权限日志配置
PERMISSION_LOG_LEVEL=INFO
PERMISSION_AUDIT_ENABLED=true
```

### 1.4 服务启动

```python
# 在main.py中添加权限服务
from src.services.permission_service.router import router as permission_router

app = FastAPI(title="FinSight API")

# 注册权限服务路由
app.include_router(permission_router)

# 添加权限中间件
from src.services.permission_service.middleware import PermissionMiddleware
app.add_middleware(PermissionMiddleware)
```

### 1.5 初始管理员账户

```python
# scripts/create_admin_user.py
from src.core.database import get_db
from src.services.user_service.models import User
from src.services.permission_service.models import Role, UserRole
from src.services.user_service.service import UserService

def create_admin_user():
    db = next(get_db())
    
    try:
        # 创建管理员用户
        admin_user = User(
            phone="13800000000",
            username="admin",
            email="<EMAIL>",
            user_type=1,
            is_active=True,
            is_verified=True
        )
        db.add(admin_user)
        db.commit()
        db.refresh(admin_user)
        
        # 分配Admin角色
        admin_role = db.query(Role).filter(Role.name == "Admin").first()
        if admin_role:
            user_role = UserRole(
                user_id=admin_user.id,
                role_id=admin_role.id,
                created_by=admin_user.id
            )
            db.add(user_role)
            db.commit()
        
        print(f"Admin user created with ID: {admin_user.id}")
        
    except Exception as e:
        print(f"Failed to create admin user: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    create_admin_user()
```

## 2. API使用示例

### 2.1 权限管理API

```python
import httpx

# API基础URL
BASE_URL = "http://localhost:8000/api/v1"

# 管理员认证token
admin_token = "your_admin_jwt_token"
headers = {"Authorization": f"Bearer {admin_token}"}

async def permission_management_examples():
    async with httpx.AsyncClient() as client:
        
        # 1. 创建新权限
        permission_data = {
            "code": "finance.report.advanced",
            "name": "查看高级财务报告",
            "description": "查看包含敏感信息的高级财务报告",
            "module": "finance",
            "resource": "report",
            "action": "advanced_read"
        }
        
        response = await client.post(
            f"{BASE_URL}/permissions/permissions",
            json=permission_data,
            headers=headers
        )
        print(f"Created permission: {response.json()}")
        
        # 2. 创建新角色
        role_data = {
            "name": "FinanceManager",
            "description": "财务经理，可以查看高级财务报告",
            "parent_id": None
        }
        
        response = await client.post(
            f"{BASE_URL}/permissions/roles",
            json=role_data,
            headers=headers
        )
        finance_role = response.json()
        print(f"Created role: {finance_role}")
        
        # 3. 为角色分配权限
        permission_id = 1  # 假设权限ID
        role_id = finance_role["id"]
        
        response = await client.post(
            f"{BASE_URL}/permissions/roles/{role_id}/permissions/{permission_id}",
            headers=headers
        )
        print(f"Assigned permission to role: {response.status_code}")
        
        # 4. 为用户分配角色
        user_id = 123  # 目标用户ID
        
        response = await client.post(
            f"{BASE_URL}/permissions/users/{user_id}/roles/{role_id}",
            headers=headers
        )
        print(f"Assigned role to user: {response.status_code}")
        
        # 5. 检查用户权限
        response = await client.get(
            f"{BASE_URL}/permissions/users/{user_id}/permissions",
            headers=headers
        )
        user_permissions = response.json()
        print(f"User permissions: {user_permissions}")
```

### 2.2 权限验证API

```python
async def permission_check_examples():
    async with httpx.AsyncClient() as client:
        
        # 1. 检查特定权限
        check_data = {
            "user_id": 123,
            "permission_code": "finance.report.advanced"
        }
        
        response = await client.post(
            f"{BASE_URL}/permissions/check",
            json=check_data,
            headers=headers
        )
        result = response.json()
        print(f"Permission check result: {result['has_permission']}")
        
        # 2. 获取当前用户权限
        user_token = "user_jwt_token"
        user_headers = {"Authorization": f"Bearer {user_token}"}
        
        response = await client.get(
            f"{BASE_URL}/permissions/users/me/permissions",
            headers=user_headers
        )
        my_permissions = response.json()
        print(f"My permissions: {my_permissions}")
```

## 3. 前端集成

### 3.1 权限服务封装

```typescript
// services/permissionService.ts
import { api } from './api';

export interface Permission {
  id: number;
  code: string;
  name: string;
  description: string;
  module: string;
  resource: string;
  action: string;
}

export interface Role {
  id: number;
  name: string;
  description: string;
  permissions: Permission[];
}

export interface UserPermissions {
  user_id: number;
  permissions: string[];
  roles: string[];
}

class PermissionService {
  private userPermissions: string[] = [];
  private userRoles: string[] = [];

  // 初始化用户权限
  async initUserPermissions() {
    try {
      const response = await api.get<UserPermissions>('/permissions/users/me/permissions');
      this.userPermissions = response.data.permissions;
      this.userRoles = response.data.roles;
    } catch (error) {
      console.error('Failed to load user permissions:', error);
      this.userPermissions = [];
      this.userRoles = [];
    }
  }

  // 检查权限
  hasPermission(permissionCode: string): boolean {
    return this.userPermissions.includes(permissionCode);
  }

  // 检查角色
  hasRole(roleName: string): boolean {
    return this.userRoles.includes(roleName);
  }

  // 检查多个权限（任一满足）
  hasAnyPermission(permissionCodes: string[]): boolean {
    return permissionCodes.some(code => this.hasPermission(code));
  }

  // 检查多个权限（全部满足）
  hasAllPermissions(permissionCodes: string[]): boolean {
    return permissionCodes.every(code => this.hasPermission(code));
  }

  // 权限管理API
  async createRole(roleData: Partial<Role>) {
    return api.post<Role>('/permissions/roles', roleData);
  }

  async getRoles() {
    return api.get<Role[]>('/permissions/roles');
  }

  async assignRoleToUser(userId: number, roleId: number) {
    return api.post(`/permissions/users/${userId}/roles/${roleId}`);
  }

  async getUserRoles(userId: number) {
    return api.get<Role[]>(`/permissions/users/${userId}/roles`);
  }
}

export const permissionService = new PermissionService();
```

### 3.2 Vue.js集成示例

```vue
<!-- components/PermissionButton.vue -->
<template>
  <button 
    v-if="hasPermission" 
    :class="buttonClass"
    @click="$emit('click')"
  >
    <slot />
  </button>
</template>

<script setup lang="ts">
import { computed, inject } from 'vue';

interface Props {
  permission?: string;
  role?: string;
  anyPermissions?: string[];
  allPermissions?: string[];
  buttonClass?: string;
}

const props = withDefaults(defineProps<Props>(), {
  buttonClass: 'btn btn-primary'
});

const permissionService = inject('permissionService');

const hasPermission = computed(() => {
  if (!permissionService) return false;

  // 检查角色
  if (props.role) {
    return permissionService.hasRole(props.role);
  }

  // 检查单个权限
  if (props.permission) {
    return permissionService.hasPermission(props.permission);
  }

  // 检查任一权限
  if (props.anyPermissions?.length) {
    return permissionService.hasAnyPermission(props.anyPermissions);
  }

  // 检查全部权限
  if (props.allPermissions?.length) {
    return permissionService.hasAllPermissions(props.allPermissions);
  }

  return true;
});
</script>
```

```vue
<!-- 使用示例 -->
<template>
  <div class="admin-panel">
    <!-- 只有管理员才能看到的按钮 -->
    <PermissionButton role="Admin" @click="deleteUser">
      删除用户
    </PermissionButton>

    <!-- 需要特定权限的按钮 -->
    <PermissionButton permission="data.export" @click="exportData">
      导出数据
    </PermissionButton>

    <!-- 需要任一权限的按钮 -->
    <PermissionButton 
      :any-permissions="['content.article.create', 'content.article.update']"
      @click="editArticle"
    >
      编辑文章
    </PermissionButton>
  </div>
</template>
```

### 3.3 React集成示例

```tsx
// hooks/usePermission.ts
import { useContext, useEffect, useState } from 'react';
import { AuthContext } from '../contexts/AuthContext';
import { permissionService } from '../services/permissionService';

export const usePermission = () => {
  const { user } = useContext(AuthContext);
  const [permissions, setPermissions] = useState<string[]>([]);
  const [roles, setRoles] = useState<string[]>([]);

  useEffect(() => {
    if (user) {
      permissionService.initUserPermissions().then(() => {
        setPermissions(permissionService.userPermissions);
        setRoles(permissionService.userRoles);
      });
    }
  }, [user]);

  const hasPermission = (permissionCode: string) => {
    return permissions.includes(permissionCode);
  };

  const hasRole = (roleName: string) => {
    return roles.includes(roleName);
  };

  const hasAnyPermission = (permissionCodes: string[]) => {
    return permissionCodes.some(code => hasPermission(code));
  };

  return {
    permissions,
    roles,
    hasPermission,
    hasRole,
    hasAnyPermission
  };
};
```

```tsx
// components/PermissionGuard.tsx
import React from 'react';
import { usePermission } from '../hooks/usePermission';

interface PermissionGuardProps {
  permission?: string;
  role?: string;
  anyPermissions?: string[];
  fallback?: React.ReactNode;
  children: React.ReactNode;
}

export const PermissionGuard: React.FC<PermissionGuardProps> = ({
  permission,
  role,
  anyPermissions,
  fallback = null,
  children
}) => {
  const { hasPermission, hasRole, hasAnyPermission } = usePermission();

  let hasAccess = false;

  if (role) {
    hasAccess = hasRole(role);
  } else if (permission) {
    hasAccess = hasPermission(permission);
  } else if (anyPermissions) {
    hasAccess = hasAnyPermission(anyPermissions);
  } else {
    hasAccess = true;
  }

  return hasAccess ? <>{children}</> : <>{fallback}</>;
};
```

## 4. 监控和维护

### 4.1 权限使用监控

```python
# monitoring/permission_monitor.py
import logging
from datetime import datetime, timedelta
from collections import defaultdict
from typing import Dict, List

from src.core.database import get_db
from src.services.permission_service.models import PermissionOperationLog

logger = logging.getLogger(__name__)

class PermissionMonitor:
    """权限使用监控"""
    
    def __init__(self):
        self.db = next(get_db())
    
    def get_permission_usage_stats(self, days: int = 30) -> Dict:
        """获取权限使用统计"""
        start_date = datetime.now() - timedelta(days=days)
        
        # 查询权限操作日志
        logs = self.db.query(PermissionOperationLog).filter(
            PermissionOperationLog.operation_time >= start_date
        ).all()
        
        stats = {
            'total_operations': len(logs),
            'grant_operations': 0,
            'revoke_operations': 0,
            'top_operators': defaultdict(int),
            'operation_by_date': defaultdict(int),
            'target_types': defaultdict(int)
        }
        
        for log in logs:
            if log.operation_type == 'GRANT':
                stats['grant_operations'] += 1
            else:
                stats['revoke_operations'] += 1
            
            stats['top_operators'][log.operator_id] += 1
            stats['operation_by_date'][log.operation_time.date()] += 1
            stats['target_types'][log.target_type] += 1
        
        return stats
    
    def detect_suspicious_activities(self) -> List[Dict]:
        """检测可疑的权限活动"""
        suspicious_activities = []
        
        # 检测频繁的权限变更
        recent_logs = self.db.query(PermissionOperationLog).filter(
            PermissionOperationLog.operation_time >= datetime.now() - timedelta(hours=1)
        ).all()
        
        operator_counts = defaultdict(int)
        for log in recent_logs:
            operator_counts[log.operator_id] += 1
        
        # 标记1小时内操作超过10次的用户
        for operator_id, count in operator_counts.items():
            if count > 10:
                suspicious_activities.append({
                    'type': 'frequent_operations',
                    'operator_id': operator_id,
                    'operation_count': count,
                    'time_window': '1 hour'
                })
        
        return suspicious_activities
    
    def generate_audit_report(self, start_date: datetime, end_date: datetime) -> Dict:
        """生成权限审计报告"""
        logs = self.db.query(PermissionOperationLog).filter(
            PermissionOperationLog.operation_time.between(start_date, end_date)
        ).all()
        
        report = {
            'period': f"{start_date.date()} to {end_date.date()}",
            'total_operations': len(logs),
            'operations_by_type': defaultdict(int),
            'operations_by_target': defaultdict(int),
            'unique_operators': set(),
            'unique_targets': set(),
            'detailed_logs': []
        }
        
        for log in logs:
            report['operations_by_type'][log.operation_type] += 1
            report['operations_by_target'][log.target_type] += 1
            report['unique_operators'].add(log.operator_id)
            report['unique_targets'].add(log.target_id)
            
            report['detailed_logs'].append({
                'timestamp': log.operation_time,
                'operator_id': log.operator_id,
                'operation': log.operation_type,
                'target_type': log.target_type,
                'target_id': log.target_id,
                'role_id': log.role_id,
                'permission_id': log.permission_id
            })
        
        # 转换集合为计数
        report['unique_operators'] = len(report['unique_operators'])
        report['unique_targets'] = len(report['unique_targets'])
        
        return report
```

### 4.2 缓存健康检查

```python
# monitoring/cache_health.py
import logging
from typing import Dict
from src.services.permission_service.cache_service import PermissionCacheService

logger = logging.getLogger(__name__)

class CacheHealthChecker:
    """缓存健康检查"""
    
    def __init__(self, cache_service: PermissionCacheService):
        self.cache = cache_service
    
    def check_cache_health(self) -> Dict:
        """检查缓存健康状态"""
        health_status = {
            'redis_connection': False,
            'cache_stats': {},
            'memory_usage': {},
            'hit_rate': {},
            'recommendations': []
        }
        
        try:
            # 测试Redis连接
            self.cache.redis.ping()
            health_status['redis_connection'] = True
            
            # 获取缓存统计
            health_status['cache_stats'] = self.cache.get_cache_stats()
            
            # 获取内存使用情况
            memory_info = self.cache.redis.info('memory')
            health_status['memory_usage'] = {
                'used_memory': memory_info.get('used_memory'),
                'used_memory_human': memory_info.get('used_memory_human'),
                'maxmemory': memory_info.get('maxmemory'),
                'maxmemory_human': memory_info.get('maxmemory_human')
            }
            
            # 分析并提供建议
            health_status['recommendations'] = self._analyze_and_recommend(health_status)
            
        except Exception as e:
            logger.error(f"Cache health check failed: {e}")
            health_status['error'] = str(e)
        
        return health_status
    
    def _analyze_and_recommend(self, health_status: Dict) -> List[str]:
        """分析缓存状态并提供建议"""
        recommendations = []
        
        cache_stats = health_status.get('cache_stats', {})
        
        # 检查缓存项数量
        total_cache_items = sum(
            stat.get('count', 0) for stat in cache_stats.values()
        )
        
        if total_cache_items > 10000:
            recommendations.append("缓存项数量较多，考虑调整TTL或清理策略")
        
        # 检查内存使用
        memory_usage = health_status.get('memory_usage', {})
        used_memory = memory_usage.get('used_memory', 0)
        maxmemory = memory_usage.get('maxmemory', 0)
        
        if maxmemory > 0 and used_memory / maxmemory > 0.8:
            recommendations.append("内存使用率较高，建议增加内存或优化缓存策略")
        
        return recommendations
```

### 4.3 定期维护任务

```python
# tasks/permission_maintenance.py
import logging
from datetime import datetime, timedelta
from celery import Celery

from src.core.database import get_db
from src.services.permission_service.models import PermissionOperationLog
from src.services.permission_service.cache_service import PermissionCacheService

logger = logging.getLogger(__name__)
celery_app = Celery('permission_maintenance')

@celery_app.task
def cleanup_old_permission_logs():
    """清理旧的权限操作日志"""
    db = next(get_db())
    
    try:
        # 删除90天前的日志
        cutoff_date = datetime.now() - timedelta(days=90)
        
        deleted_count = db.query(PermissionOperationLog).filter(
            PermissionOperationLog.operation_time < cutoff_date
        ).delete()
        
        db.commit()
        logger.info(f"Cleaned up {deleted_count} old permission logs")
        
    except Exception as e:
        logger.error(f"Failed to cleanup permission logs: {e}")
        db.rollback()
    finally:
        db.close()

@celery_app.task
def refresh_permission_cache():
    """刷新权限缓存"""
    cache_service = PermissionCacheService(redis_client)
    
    try:
        # 清除所有缓存
        cache_service.invalidate_all_user_caches()
        cache_service.invalidate_all_role_caches()
        
        logger.info("Permission cache refreshed successfully")
        
    except Exception as e:
        logger.error(f"Failed to refresh permission cache: {e}")

@celery_app.task
def generate_daily_permission_report():
    """生成每日权限报告"""
    from monitoring.permission_monitor import PermissionMonitor
    
    monitor = PermissionMonitor()
    
    try:
        # 生成昨天的报告
        end_date = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        start_date = end_date - timedelta(days=1)
        
        report = monitor.generate_audit_report(start_date, end_date)
        
        # 保存报告到文件或发送邮件
        report_filename = f"permission_report_{start_date.date()}.json"
        with open(f"reports/{report_filename}", 'w') as f:
            import json
            json.dump(report, f, indent=2, default=str)
        
        logger.info(f"Daily permission report generated: {report_filename}")
        
    except Exception as e:
        logger.error(f"Failed to generate daily permission report: {e}")

# 配置定时任务
celery_app.conf.beat_schedule = {
    'cleanup-permission-logs': {
        'task': 'tasks.permission_maintenance.cleanup_old_permission_logs',
        'schedule': timedelta(days=1),  # 每天执行
    },
    'refresh-permission-cache': {
        'task': 'tasks.permission_maintenance.refresh_permission_cache',
        'schedule': timedelta(hours=6),  # 每6小时执行
    },
    'generate-daily-report': {
        'task': 'tasks.permission_maintenance.generate_daily_permission_report',
        'schedule': timedelta(days=1),  # 每天执行
    },
}
```

## 5. 故障排除

### 5.1 常见问题

1. **权限检查失败**
   - 检查用户是否已登录
   - 验证JWT token是否有效
   - 确认用户角色分配是否正确

2. **缓存不一致**
   - 检查Redis连接状态
   - 手动清除缓存后重试
   - 检查缓存更新逻辑

3. **权限更新不生效**
   - 清除相关用户的权限缓存
   - 检查角色权限关联关系
   - 验证权限继承逻辑

### 5.2 调试工具

```python
# utils/permission_debug.py
from src.core.database import get_db
from src.services.permission_service.service import AuthorizationService

def debug_user_permissions(user_id: int):
    """调试用户权限"""
    db = next(get_db())
    auth_service = AuthorizationService(db)
    
    print(f"=== User {user_id} Permission Debug ===")
    
    # 获取用户角色
    roles = auth_service.user_role_service.get_user_roles(user_id)
    print(f"Roles: {[role.name for role in roles]}")
    
    # 获取用户权限
    permissions = auth_service.get_user_permissions(user_id)
    print(f"Permissions: {[perm.code for perm in permissions]}")
    
    # 检查缓存状态
    if auth_service.cache:
        cached_perms = auth_service.cache.get_user_permissions(user_id)
        print(f"Cached permissions: {list(cached_perms)}")
    
    db.close()

def debug_role_hierarchy(role_id: int):
    """调试角色层级"""
    db = next(get_db())
    auth_service = AuthorizationService(db)
    
    print(f"=== Role {role_id} Hierarchy Debug ===")
    
    # 获取角色信息
    role = auth_service.role_service.get_role(role_id)
    if role:
        print(f"Role: {role.name}")
        print(f"Parent: {role.parent_id}")
        
        # 获取直接权限
        direct_perms = auth_service.role_service.get_role_permissions(role_id)
        print(f"Direct permissions: {[perm.code for perm in direct_perms]}")
        
        # 获取层级权限
        hierarchy_perms = auth_service.role_service.get_role_hierarchy_permissions(role_id)
        print(f"Hierarchy permissions: {list(hierarchy_perms)}")
    
    db.close()
```

通过以上文档和实现指南，您可以在FinSight系统中完整地实现一个功能强大、安全可靠的角色权限体系。 