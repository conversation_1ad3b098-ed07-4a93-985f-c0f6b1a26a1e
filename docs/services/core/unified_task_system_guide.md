# 统一任务系统使用指南

## 概述

本文档介绍了改进后的数据采集服务，采用统一的任务执行架构，支持同步和异步两种执行模式，确保API接口不阻塞并提供高可靠性。

## 架构设计

### 1. 统一任务框架

```
TaskManager -> TaskQueue (Celery) -> TaskExecutor -> Specific Implementation
```

- **TaskManager**: 统一任务管理器，负责任务调度和分发
- **TaskQueue**: 基于Celery的异步任务队列
- **TaskExecutor**: 抽象执行器接口，各服务实现具体逻辑
- **Implementation**: 具体的业务实现（爬虫、数据处理等）

### 2. 执行模式

#### Direct Mode (直接执行)
- 适用场景：快速响应的小型任务
- 特点：立即执行，非阻塞
- 示例：单页面爬取、实时数据查询

#### Async Queue Mode (异步队列)
- 适用场景：长时间运行的大型任务
- 特点：分布式执行，高可靠性
- 示例：全站爬取、批量数据处理

#### Auto Mode (自动选择)
- 系统根据任务特性自动选择最适合的执行模式
- 智能决策因素：
  - 任务预估执行时间
  - 数据量大小
  - 是否为批量操作
  - 数据源特性

## API接口使用

### 1. 提交单个爬取任务

```bash
POST /api/v1/admin/data-collection/tasks/crawl
```

**参数:**
- `data_source_id`: 数据源ID (必需)
- `execution_mode`: 执行模式 (可选: "direct", "async", "auto")
- `max_pages`: 最大爬取页数 (可选)
- `estimated_duration_minutes`: 预估执行时间 (可选)
- `priority`: 任务优先级 1-10 (可选，默认5)

**示例:**
```bash
curl -X POST "/api/v1/admin/data-collection/tasks/crawl" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d "data_source_id=1&execution_mode=auto&max_pages=5&priority=7"
```

**响应:**
```json
{
  "success": true,
  "message": "任务已提交",
  "data": {
    "crawl_task_id": 123,
    "execution_task_id": "crawler_20231201_143022_123456",
    "execution_mode": "direct",
    "status": "submitted"
  }
}
```

### 2. 批量提交爬取任务

```bash
POST /api/v1/admin/data-collection/tasks/crawl/batch
```

**参数:**
- `data_source_ids`: 数据源ID列表 (必需)
- `max_pages`: 最大爬取页数 (可选)
- `priority`: 任务优先级 (可选)

**示例:**
```bash
curl -X POST "/api/v1/admin/data-collection/tasks/crawl/batch" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"data_source_ids": [1, 2, 3], "max_pages": 10, "priority": 6}'
```

### 3. 查询任务状态

```bash
GET /api/v1/admin/data-collection/tasks/{task_id}/status
```

**响应:**
```json
{
  "success": true,
  "data": {
    "task_id": 123,
    "data_source_id": 1,
    "status": "running",
    "task_type": "manual",
    "created_at": "2023-12-01T14:30:22Z",
    "started_at": "2023-12-01T14:30:25Z",
    "completed_at": null,
    "error_message": null,
    "items_collected": 15,
    "items_failed": 2,
    "progress": 75.5
  }
}
```

### 4. 取消任务

```bash
POST /api/v1/admin/data-collection/tasks/{task_id}/cancel
```

### 5. 获取任务统计

```bash
GET /api/v1/admin/data-collection/tasks/stats
```

**响应:**
```json
{
  "success": true,
  "data": {
    "total_tasks": 156,
    "status_breakdown": {
      "pending": 5,
      "running": 3,
      "queued": 12,
      "completed": 128,
      "failed": 6,
      "cancelled": 2
    },
    "today_tasks": 23,
    "type_breakdown": {
      "manual": 89,
      "scheduled": 45,
      "event_driven": 22
    },
    "active_tasks": 20
  }
}
```

## 智能执行模式选择

系统根据以下规则自动选择执行模式：

### 使用异步队列的条件：
1. **批量模式**: `batch_mode=true`
2. **大型任务**: `max_pages > 10`
3. **长时间任务**: `estimated_duration_minutes > 5`
4. **实时采集**: 数据源支持实时采集
5. **明确指定**: `execution_mode="async"`

### 使用直接执行的条件：
1. **快速任务**: 预估5分钟内完成
2. **小型爬取**: 页面数量 ≤ 10
3. **立即响应**: 需要立即返回结果
4. **明确指定**: `execution_mode="direct"`

## 最佳实践

### 1. 任务类型选择

**适合直接执行的场景:**
```python
# 单页面新闻爬取
submit_crawl_task(
    data_source_id=1,
    execution_mode="direct",
    max_pages=1,
    priority=8
)

# 实时数据检查
submit_crawl_task(
    data_source_id=5,
    execution_mode="direct",
    estimated_duration_minutes=1
)
```

**适合异步队列的场景:**
```python
# 全站数据采集
submit_crawl_task(
    data_source_id=10,
    execution_mode="async",
    max_pages=100,
    priority=5
)

# 批量数据源处理
submit_batch_crawl_tasks(
    data_source_ids=[1, 2, 3, 4, 5],
    max_pages=50,
    priority=6
)
```

### 2. 优先级设置

- **高优先级 (8-10)**: 紧急任务、实时数据
- **中优先级 (5-7)**: 常规任务、定时采集
- **低优先级 (1-4)**: 批量处理、归档任务

### 3. 错误处理

系统提供自动重试机制：
- 网络错误：最多重试3次
- 临时故障：指数退避重试
- 数据错误：记录错误信息但不重试

### 4. 监控建议

定期检查任务统计：
```bash
# 每小时检查活跃任务数量
curl -s "/api/v1/admin/data-collection/tasks/stats" | jq '.data.active_tasks'

# 监控失败任务
curl -s "/api/v1/admin/data-collection/tasks/stats" | jq '.data.status_breakdown.failed'
```

## 性能优化

### 1. 并发控制

- 直接执行模式：系统自动控制并发数
- 异步队列模式：可配置worker数量

### 2. 资源管理

```python
# 配置建议
CELERY_WORKER_CONCURRENCY = 4  # 根据服务器性能调整
CELERY_WORKER_PREFETCH_MULTIPLIER = 1  # 避免内存占用过多
```

### 3. 队列优化

- 高优先级队列：实时任务专用
- 普通队列：常规任务处理
- 低优先级队列：批量后台任务

## 故障排除

### 1. 常见问题

**任务提交失败:**
- 检查数据源ID是否存在
- 验证用户权限
- 确认参数格式正确

**任务执行缓慢:**
- 检查Celery worker状态
- 监控Redis队列长度
- 查看系统资源使用情况

**任务频繁失败:**
- 检查网络连接
- 验证目标网站可访问性
- 查看爬虫配置是否正确

### 2. 调试工具

```bash
# 查看Celery worker状态
celery -A src.core.celery_app status

# 监控队列情况
celery -A src.core.celery_app inspect active_queues

# 查看任务详情
celery -A src.core.celery_app inspect active
```

## 迁移指南

### 从旧系统迁移

1. **更新API调用**: 使用新的任务提交接口
2. **调整监控**: 使用新的状态查询接口
3. **配置检查**: 确保Celery配置正确
4. **测试验证**: 在测试环境验证功能

### 兼容性

- 保持原有API的向后兼容
- 旧的同步接口仍然可用
- 逐步迁移到异步架构

## 总结

统一任务系统带来的改进：

✅ **非阻塞**: API接口立即响应，不会因长时间任务阻塞  
✅ **高可靠**: 基于Celery的分布式任务队列，支持故障恢复  
✅ **智能调度**: 根据任务特性自动选择最适合的执行模式  
✅ **易扩展**: 统一架构便于添加新的任务类型  
✅ **完善监控**: 提供详细的任务状态和统计信息  

这个架构为后续添加其他类型的任务（如数据清洗、AI分析等）提供了良好的基础。 