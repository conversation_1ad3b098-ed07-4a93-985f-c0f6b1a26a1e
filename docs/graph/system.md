```mermaid
graph TB
    subgraph "用户端"
        U1[Web用户]
        U2[移动用户]
        U3[API用户]
    end
    
    subgraph "接入层"
        AG[API Gateway<br/>Kong + Rate Limiting]
        LB[负载均衡器<br/>Nginx]
    end
    
    subgraph "微服务层"
        subgraph "数据采集服务"
            DC1[新闻爬虫<br/>Scrapy + Playwright]
            DC2[社交媒体采集<br/>API + Scraper]
            DC3[研报采集<br/>Third-party APIs]
            DC4[代理池管理<br/>Proxy Manager]
        end
        
        subgraph "信息处理服务"
            IP1[数据清洗<br/>Data Cleaning]
            IP2[去重检测<br/>SimHash + BERT]
            IP3[内容分类<br/>DeepSeek LLM]
            IP4[质量评估<br/>Quality Scorer]
            IP5[摘要生成<br/>Summarization]
        end
        
        subgraph "推送服务"
            PS1[推送策略引擎<br/>Rule Engine]
            PS2[短信推送<br/>SMS Gateway]
            PS3[邮件推送<br/>Email Service]
            PS4[微信推送<br/>WeChat API]
            PS5[APP推送<br/>Push Notifications]
        end
        
        subgraph "用户服务"
            US1[用户管理<br/>User Management]
            US2[认证授权<br/>JWT + OAuth]
            US3[用户画像<br/>User Profile]
            US4[订阅管理<br/>Subscription]
        end
    end
    
    subgraph "消息中间件"
        MQ1[Kafka - 原始数据]
        MQ2[Kafka - 处理数据]
        MQ3[Kafka - 推送队列]
        RD1[Redis - 缓存]
        RD2[Redis - 会话]
    end
    
    subgraph "数据层"
        subgraph "关系型数据库"
            PG1[(PostgreSQL<br/>用户数据)]
            PG2[(PostgreSQL<br/>配置数据)]
        end
        
        subgraph "文档数据库"
            MG1[(MongoDB<br/>原始数据)]
            MG2[(MongoDB<br/>日志数据)]
        end
        
        subgraph "搜索引擎"
            ES1[(Elasticsearch<br/>内容搜索)]
            ES2[(Elasticsearch<br/>用户行为)]
        end
        
        subgraph "对象存储"
            MIN[(MinIO<br/>文件存储)]
        end
    end
    
    subgraph "监控运维"
        PROM[Prometheus<br/>指标收集]
        GRAF[Grafana<br/>监控面板]
        LOG[ELK Stack<br/>日志分析]
        ALERT[AlertManager<br/>告警通知]
    end
    
    subgraph "外部服务"
        EXT1[新闻网站APIs]
        EXT2[社交媒体APIs]
        EXT3[研报平台APIs]
        EXT4[短信服务商]
        EXT5[邮件服务商]
    end
    
    %% 连接关系
    U1 --> LB
    U2 --> LB
    U3 --> LB
    LB --> AG
    
    AG --> US1
    AG --> DC1
    AG --> IP1
    AG --> PS1
    
    DC1 --> EXT1
    DC2 --> EXT2
    DC3 --> EXT3
    DC4 --> DC1
    DC4 --> DC2
    
    DC1 --> MQ1
    DC2 --> MQ1
    DC3 --> MQ1
    
    MQ1 --> IP1
    IP1 --> IP2
    IP2 --> IP3
    IP3 --> IP4
    IP4 --> IP5
    IP5 --> MQ2
    
    MQ2 --> PS1
    PS1 --> PS2
    PS1 --> PS3
    PS1 --> PS4
    PS1 --> PS5
    
    PS2 --> EXT4
    PS3 --> EXT5
    PS1 --> MQ3
    
    US1 --> PG1
    US2 --> RD2
    US3 --> PG1
    US4 --> PG2
    
    DC1 --> MG1
    IP1 --> MG1
    IP3 --> ES1
    PS1 --> ES2
    
    IP5 --> MIN
    
    DC1 --> RD1
    IP1 --> RD1
    PS1 --> RD1
    
    %% 监控连接
    DC1 -.-> PROM
    IP1 -.-> PROM
    PS1 -.-> PROM
    US1 -.-> PROM
    PROM --> GRAF
    PROM --> ALERT
    
    style AG fill:#e1f5fe
    style MQ1 fill:#f3e5f5
    style MQ2 fill:#f3e5f5
    style MQ3 fill:#f3e5f5
    style PG1 fill:#e8f5e8
    style PG2 fill:#e8f5e8
    style MG1 fill:#fff3e0
    style ES1 fill:#fce4ec
```