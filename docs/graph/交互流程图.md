```mermaid
sequenceDiagram
    participant Scheduler as Airflow调度器
    participant Collector as 数据采集服务
    participant RawDB as 原始数据存储
    participant Processor as 信息处理服务
    participant AI as AI处理引擎
    participant ProcessedDB as 处理数据存储
    participant Search as Elasticsearch
    participant PushEngine as 推送引擎
    participant UserService as 用户服务
    participant Queue as 消息队列
    participant Notification as 通知服务
    participant External as 外部渠道

    %% 数据采集流程
    Scheduler->>Collector: 触发采集任务
    Collector->>External: 爬取网站数据
    External->>Collector: 返回原始数据
    Collector->>RawDB: 存储原始数据
    Collector->>Queue: 发送采集事件

    %% 数据处理流程
    Queue->>Processor: 消费采集事件
    Processor->>RawDB: 获取原始数据
    Processor->>AI: 调用AI处理
    AI->>Processor: 返回处理结果
    
    alt 数据清洗
        Processor->>Processor: 清洗和标准化
    end
    
    alt 去重检测
        Processor->>Search: 检查相似内容
        Search->>Processor: 返回相似度结果
    end
    
    alt 内容分类
        Processor->>AI: 分类和标签提取
        AI->>Processor: 返回分类结果
    end
    
    Processor->>ProcessedDB: 存储处理后数据
    Processor->>Search: 更新搜索索引
    Processor->>Queue: 发送处理完成事件

    %% 推送决策流程
    Queue->>PushEngine: 消费处理完成事件
    PushEngine->>ProcessedDB: 获取内容信息
    PushEngine->>UserService: 获取用户画像
    UserService->>PushEngine: 返回用户偏好
    
    alt 内容匹配
        PushEngine->>Search: 搜索匹配用户
        Search->>PushEngine: 返回匹配结果
    end
    
    PushEngine->>PushEngine: 计算推送分数
    PushEngine->>Queue: 发送推送任务

    %% 消息分发流程
    Queue->>Notification: 消费推送任务
    
    par 多渠道并行推送
        Notification->>External: 发送短信
        and
        Notification->>External: 发送邮件
        and
        Notification->>External: 发送微信消息
        and
        Notification->>External: 发送APP推送
    end
    
    External->>Notification: 返回推送状态
    Notification->>ProcessedDB: 记录推送日志
    
    %% 反馈收集
    External->>Notification: 用户行为反馈
    Notification->>UserService: 更新用户画像
    UserService->>PushEngine: 优化推送策略
```