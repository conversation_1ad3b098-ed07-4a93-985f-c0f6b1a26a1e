/**
 * B端用户管理模块 TypeScript 类型定义
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

// ==================== 基础类型定义 ====================

/** 用户类型枚举 */
export enum UserType {
  INDIVIDUAL = 1,  // 个人用户
  INSTITUTION = 2, // 机构用户
}

/** 风险等级枚举 */
export enum RiskLevel {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
}

/** 短信验证码用途枚举 */
export enum SmsCodePurpose {
  LOGIN = 'login',
  REGISTER = 'register',
  RESET_PASSWORD = 'reset_password',
}

// ==================== 用户相关类型 ====================

/** 用户基础信息 */
export interface User {
  id: number;
  phone: string;
  username?: string;
  email?: string;
  userType: UserType;
  riskLevel: RiskLevel;
  knowledgeLevel: number;
  isActive: boolean;
  isVerified: boolean;
  isAdmin: boolean;
  firstLoginAt?: string;
  lastLoginAt?: string;
  createdAt: string;
  updatedAt: string;
}

/** 用户详情信息（包含角色和权限） */
export interface UserDetail extends User {
  roles: Role[];
  permissions: Permission[];
}

/** 用户创建请求 */
export interface CreateUserRequest {
  phone: string;
  username?: string;
  email?: string;
  userType?: UserType;
  riskLevel?: RiskLevel;
  knowledgeLevel?: number;
  isActive?: boolean;
  isVerified?: boolean;
  isAdmin?: boolean;
  roleIds?: number[];
}

/** 用户更新请求 */
export interface UpdateUserRequest {
  username?: string;
  email?: string;
  userType?: UserType;
  riskLevel?: RiskLevel;
  knowledgeLevel?: number;
}

/** 用户状态更新请求 */
export interface UserStatusUpdateRequest {
  isActive: boolean;
}

/** 用户资料更新请求 */
export interface UserProfileUpdateRequest {
  username?: string;
  email?: string;
  nickname?: string;
  avatar?: string;
}

/** 发送验证码请求 */
export interface SendVerificationCodeRequest {
  phone: string;
  purpose: SmsCodePurpose;
}

/** 管理员登录请求 */
export interface AdminLoginRequest {
  phone: string;
  verification_code: string;
}

/** 管理员登录响应 */
export interface AdminLoginResponse {
  access_token: string;
  token_type: string;
  expires_in: number; // token过期时间（秒）
  user: AdminUserDetail;
}

/** 管理员用户详情 */
export interface AdminUserDetail extends User {
  roles: Role[];
  permissions: Permission[];
}

// ==================== 角色权限相关类型 ====================

/** 角色信息 */
export interface Role {
  id: number;
  name: string;
  description?: string;
  createdAt: string;
  updatedAt: string;
}

/** 权限信息 */
export interface Permission {
  id: number;
  code: string;
  name: string;
  description?: string;
  module: string;
  resource: string;
  action: string;
}

/** 用户角色分配请求 */
export interface UserRoleAssignRequest {
  roleId: number;
}

/** 批量角色分配请求 */
export interface BatchRoleAssignRequest {
  userIds: number[];
  roleIds: number[];
}

/** 用户权限响应 */
export interface UserPermissionsResponse {
  userId: number;
  roles: Role[];
  permissions: Permission[];
  permissionCodes: string[];
}

// ==================== API响应类型 ====================

/** 基础API响应 */
export interface BaseResponse<T = any> {
  success: boolean;
  message: string;
  data: T;
  timestamp: string;
}

/** 分页响应 */
export interface PaginatedResponse<T> {
  users: T[];
  total: number;
  page: number;
  size: number;
  pages: number;
}

/** 用户列表响应 */
export interface UserListResponse extends PaginatedResponse<User> {}

/** 用户详情响应 */
export interface UserDetailResponse extends User {
  roles: Role[];
  permissions: Permission[];
}

// ==================== 表单相关类型 ====================

/** 用户筛选器参数 */
export interface UserFilterParams {
  search?: string;
  userType?: UserType;
  isActive?: boolean;
  isVerified?: boolean;
  page?: number;
  size?: number;
}

/** 用户表格列配置 */
export interface UserTableColumn {
  title: string;
  dataIndex: string;
  key: string;
  width?: number;
  sorter?: boolean;
  render?: (value: any, record: User) => any;
}

/** 用户操作按钮配置 */
export interface UserActionButton {
  key: string;
  label: string;
  permission: string;
  type?: 'primary' | 'default' | 'dashed' | 'text' | 'link';
  danger?: boolean;
  onClick: (user: User) => void;
}

// ==================== 权限控制相关类型 ====================

/** 权限上下文类型 */
export interface PermissionContextType {
  permissions: string[];
  roles: Role[];
  hasPermission: (permission: string) => boolean;
  hasRole: (roleName: string) => boolean;
  hasAnyPermission: (permissions: string[]) => boolean;
  loading: boolean;
}

/** 权限门控组件Props */
export interface PermissionGateProps {
  permission?: string;
  permissions?: string[];
  role?: string;
  roles?: string[];
  requireAll?: boolean;
  fallback?: any;
  children: any;
}

/** 保护路由组件Props */
export interface ProtectedRouteProps {
  permission?: string;
  permissions?: string[];
  fallback?: any;
  children: any;
}

// ==================== Redux状态类型 ====================

/** 认证模块状态 */
export interface AuthState {
  isAuthenticated: boolean;
  adminUser: AdminUserDetail | null;
  token: string | null;
  loading: boolean;
  error: string | null;
}

/** 用户模块状态 */
export interface UserState {
  users: User[];
  currentUser: UserDetail | null;
  loading: boolean;
  error: string | null;
  pagination: {
    current: number;
    pageSize: number;
    total: number;
  };
  filters: UserFilterParams;
}

/** 权限模块状态 */
export interface PermissionState {
  permissions: Permission[];
  roles: Role[];
  userPermissions: UserPermissionsResponse | null;
  loading: boolean;
  error: string | null;
}

// ==================== 组件Props类型 ====================

/** 用户列表组件Props */
export interface UserListProps {
  onUserSelect?: (user: User) => void;
  onUserCreate?: () => void;
  showActions?: boolean;
  compact?: boolean;
}

/** 用户表格组件Props */
export interface UserTableProps {
  users: User[];
  loading?: boolean;
  pagination?: {
    current: number;
    pageSize: number;
    total: number;
    onChange: (page: number, pageSize: number) => void;
  };
  onEdit?: (user: User) => void;
  onDelete?: (user: User) => void;
  onStatusChange?: (user: User) => void;
  onRoleManage?: (user: User) => void;
}

/** 用户筛选器组件Props */
export interface UserFiltersProps {
  initialValues?: UserFilterParams;
  onFilter: (filters: UserFilterParams) => void;
  onReset?: () => void;
  loading?: boolean;
}

/** 用户表单组件Props */
export interface UserFormProps {
  mode: 'create' | 'edit';
  initialValues?: Partial<User>;
  onSubmit: (values: CreateUserRequest | UpdateUserRequest) => void;
  onCancel: () => void;
  loading?: boolean;
}

/** 用户详情组件Props */
export interface UserDetailProps {
  user: UserDetail;
  onEdit?: () => void;
  onRoleManage?: () => void;
  onStatusChange?: () => void;
  onDelete?: () => void;
}

/** 管理员登录组件Props */
export interface AdminLoginProps {
  onLoginSuccess?: (user: AdminUserDetail) => void;
  redirectTo?: string;
}

/** 认证守卫组件Props */
export interface AuthGuardProps {
  children: any;
  fallback?: any;
}

// ==================== 工具类型 ====================

/** 用户权限检查工具类型 */
export interface UserPermissionUtils {
  canViewUserList: boolean;
  canViewUserDetail: boolean;
  canCreateUser: boolean;
  canUpdateUser: boolean;
  canDeleteUser: boolean;
  canUpdateUserStatus: boolean;
  canManageUserRoles: boolean;
  canViewUserPermissions: boolean;
  canBatchAssignRoles: boolean;
  canManageUsers: boolean;
}

/** 表格操作类型 */
export type TableAction = 'view' | 'edit' | 'delete' | 'toggle-status' | 'manage-roles';

/** 用户状态标签类型 */
export interface UserStatusTagProps {
  user: User;
  type?: 'status' | 'verification' | 'role';
}

// ==================== API Service类型 ====================

/** 用户API服务接口 */
export interface UserAPIService {
  // 用户管理
  getUsers(params: UserFilterParams): Promise<UserListResponse>;
  getUserDetail(id: number): Promise<UserDetailResponse>;
  createUser(userData: CreateUserRequest): Promise<User>;
  updateUser(id: number, userData: UpdateUserRequest): Promise<User>;
  updateUserStatus(id: number, isActive: boolean): Promise<User>;
  deleteUser(id: number): Promise<void>;

  // 权限管理
  getMyPermissions(): Promise<UserPermissionsResponse>;
  getUserRoles(userId: number): Promise<Role[]>;
  assignUserRole(userId: number, roleId: number): Promise<void>;
  revokeUserRole(userId: number, roleId: number): Promise<void>;
  batchAssignRoles(request: BatchRoleAssignRequest): Promise<void>;

  // 管理员认证
  sendAdminVerificationCode(phone: string): Promise<void>;
  adminLogin(phone: string, verificationCode: string): Promise<AdminLoginResponse>;
}

// ==================== 导出所有类型 ====================
export * from './types'; 