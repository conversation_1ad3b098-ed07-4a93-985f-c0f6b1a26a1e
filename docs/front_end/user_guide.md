# B端用户模块前端开发指南

## 🎯 概述

本文档为前端开发者提供FinSight系统B端用户管理模块的完整开发指南，包括路由设计、页面布局、按钮权限控制等。

**技术栈**: React + Redux + Next.js + Ant Design + TypeScript

## 📋 目录

- [1. 路由设计](#1-路由设计)
- [2. 页面结构与按钮划分](#2-页面结构与按钮划分)
- [3. 权限体系](#3-权限体系)
- [4. 权限控制实现](#4-权限控制实现)
- [5. API接口说明](#5-api接口说明)
- [6. 代码示例](#6-代码示例)

## 1. 路由设计

### 1.1 路由层级结构

```typescript
// B端管理系统路由配置
const adminRoutes = {
  // 管理员登录
  login: {
    path: '/admin/login',
    component: 'AdminLogin',
    public: true  // 无需认证
  },
  
  // 用户管理主模块
  users: {
    path: '/admin/users',
    component: 'UserManagement',
    requireAuth: true,  // 需要认证
    children: {
      // 用户列表页
      list: {
        path: '/admin/users',
        component: 'UserList',
        exact: true,
        permission: 'user.list.read'
      },
      // 用户详情页
      detail: {
        path: '/admin/users/:id',
        component: 'UserDetail',
        permission: 'user.detail.read'
      },
      // 创建用户页
      create: {
        path: '/admin/users/create',
        component: 'UserCreate',
        permission: 'user.create'
      },
      // 编辑用户页
      edit: {
        path: '/admin/users/:id/edit',
        component: 'UserEdit',
        permission: 'user.update'
      },
      // 用户角色管理页
      roles: {
        path: '/admin/users/:id/roles',
        component: 'UserRoleManagement',
        permission: 'user.role.assign'
      }
    }
  }
};
```

### 1.2 Next.js 文件结构

```
pages/admin/
├── login.tsx                    # 管理员登录页面 (/admin/login)
└── users/
    ├── index.tsx               # 用户列表页面 (/admin/users)
    ├── create.tsx              # 创建用户页面 (/admin/users/create)
    └── [id]/
        ├── index.tsx           # 用户详情页面 (/admin/users/[id])
        ├── edit.tsx            # 编辑用户页面 (/admin/users/[id]/edit)
        └── roles.tsx           # 用户角色管理 (/admin/users/[id]/roles)

components/
├── auth/
│   ├── AdminLogin/
│   │   └── index.tsx           # 管理员登录组件
│   ├── AuthGuard.tsx           # 认证路由守卫
│   └── LoginForm.tsx           # 登录表单组件
├── users/
│   ├── UserList/
│   │   ├── index.tsx           # 用户列表组件
│   │   ├── UserTable.tsx       # 用户表格组件
│   │   ├── UserFilters.tsx     # 过滤器组件
│   │   └── UserActions.tsx     # 操作按钮组件
│   ├── UserDetail/
│   │   ├── index.tsx           # 用户详情组件
│   │   ├── UserInfo.tsx        # 基本信息组件
│   │   ├── UserRoles.tsx       # 角色信息组件
│   │   └── UserPermissions.tsx # 权限信息组件
│   └── UserForm/
│       ├── index.tsx           # 用户表单组件
│       ├── BasicInfo.tsx       # 基本信息表单
│       └── RoleSelect.tsx      # 角色选择组件
└── shared/
    ├── PermissionGate.tsx      # 权限控制组件
    └── UserStatusTag.tsx       # 状态标签组件
```

## 2. 页面结构与按钮划分

### 2.1 用户列表页面 (`/admin/users`)

#### 页面布局
```tsx
// components/users/UserList/index.tsx
const UserList: React.FC = () => {
  return (
    <div className="user-list-container">
      {/* 页面头部 */}
      <div className="page-header">
        <h1>用户管理</h1>
        <PermissionGate permission="user.create">
          <Button type="primary" onClick={handleCreateUser}>
            新建用户
          </Button>
        </PermissionGate>
      </div>

      {/* 筛选器 */}
      <div className="filters-section">
        <UserFilters onFilter={handleFilter} />
      </div>

      {/* 用户表格 */}
      <div className="table-section">
        <UserTable 
          users={users} 
          loading={loading}
          onEdit={handleEdit}
          onDelete={handleDelete}
          onStatusChange={handleStatusChange}
        />
      </div>

      {/* 分页 */}
      <div className="pagination-section">
        <Pagination {...paginationProps} />
      </div>
    </div>
  );
};
```

#### 按钮权限划分

| 按钮名称 | 权限编码 | 适用角色 | 位置 | 说明 |
|----------|----------|----------|------|------|
| 新建用户 | `user.create` | Admin, AccountManager | 页面头部 | 创建新用户 |
| 查看详情 | `user.detail.read` | Admin, AccountManager | 表格行 | 查看用户详情 |
| 编辑用户 | `user.update` | Admin, AccountManager | 表格行 | 编辑用户信息 |
| 删除用户 | `user.delete` | Admin | 表格行 | 删除用户（危险操作） |
| 启用/禁用 | `user.status.update` | Admin | 表格行 | 切换用户状态 |
| 角色管理 | `user.role.assign` | Admin | 表格行 | 管理用户角色 |
| 批量操作 | `user.role.batch.assign` | Admin | 表格头部 | 批量分配角色 |

#### 筛选器组件
```tsx
// components/users/UserList/UserFilters.tsx
const UserFilters: React.FC<UserFiltersProps> = ({ onFilter }) => {
  return (
    <Form layout="inline" onFinish={onFilter}>
      <Form.Item name="search">
        <Input placeholder="搜索手机号/用户名" prefix={<SearchOutlined />} />
      </Form.Item>
      
      <Form.Item name="userType">
        <Select placeholder="用户类型" allowClear>
          <Option value={1}>个人用户</Option>
          <Option value={2}>机构用户</Option>
        </Select>
      </Form.Item>
      
      <Form.Item name="isActive">
        <Select placeholder="状态" allowClear>
          <Option value={true}>启用</Option>
          <Option value={false}>禁用</Option>
        </Select>
      </Form.Item>
      
      <Form.Item name="isVerified">
        <Select placeholder="验证状态" allowClear>
          <Option value={true}>已验证</Option>
          <Option value={false}>未验证</Option>
        </Select>
      </Form.Item>
      
      <Form.Item>
        <Button type="primary" htmlType="submit">
          搜索
        </Button>
        <Button onClick={handleReset} style={{ marginLeft: 8 }}>
          重置
        </Button>
      </Form.Item>
    </Form>
  );
};
```

### 2.2 用户详情页面 (`/admin/users/[id]`)

#### 页面布局
```tsx
// components/users/UserDetail/index.tsx
const UserDetail: React.FC = () => {
  return (
    <div className="user-detail-container">
      {/* 面包屑导航 */}
      <Breadcrumb>
        <Breadcrumb.Item>用户管理</Breadcrumb.Item>
        <Breadcrumb.Item>用户详情</Breadcrumb.Item>
      </Breadcrumb>

      {/* 操作按钮 */}
      <div className="action-buttons">
        <PermissionGate permission="user.update">
          <Button type="primary" onClick={handleEdit}>
            编辑用户
          </Button>
        </PermissionGate>
        
        <PermissionGate permission="user.role.assign">
          <Button onClick={handleManageRoles}>
            管理角色
          </Button>
        </PermissionGate>
        
        <PermissionGate permission="user.status.update">
          <Button 
            danger={user.isActive} 
            onClick={handleToggleStatus}
          >
            {user.isActive ? '禁用用户' : '启用用户'}
          </Button>
        </PermissionGate>
      </div>

      {/* 用户信息卡片 */}
      <div className="info-cards">
        <Card title="基本信息">
          <UserInfo user={user} />
        </Card>
        
        <Card title="角色信息">
          <UserRoles roles={user.roles} />
        </Card>
        
        <PermissionGate permission="user.permission.read">
          <Card title="权限信息">
            <UserPermissions permissions={user.permissions} />
          </Card>
        </PermissionGate>
      </div>
    </div>
  );
};
```

### 2.3 创建用户页面 (`/admin/users/create`)

#### 表单结构
```tsx
// components/users/UserForm/index.tsx
const UserForm: React.FC<UserFormProps> = ({ mode, initialValues }) => {
  return (
    <Form
      form={form}
      layout="vertical"
      initialValues={initialValues}
      onFinish={handleSubmit}
    >
      {/* 基本信息 */}
      <Card title="基本信息">
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item 
              name="phone" 
              label="手机号" 
              rules={[{ required: true, pattern: /^1[3-9]\d{9}$/ }]}
            >
              <Input placeholder="请输入手机号" />
            </Form.Item>
          </Col>
          
          <Col span={12}>
            <Form.Item name="username" label="用户名">
              <Input placeholder="请输入用户名" />
            </Form.Item>
          </Col>
        </Row>
        
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item name="email" label="邮箱">
              <Input type="email" placeholder="请输入邮箱" />
            </Form.Item>
          </Col>
          
          <Col span={12}>
            <Form.Item 
              name="userType" 
              label="用户类型" 
              rules={[{ required: true }]}
            >
              <Select placeholder="请选择用户类型">
                <Option value={1}>个人用户</Option>
                <Option value={2}>机构用户</Option>
              </Select>
            </Form.Item>
          </Col>
        </Row>
      </Card>

      {/* 角色分配 */}
      <PermissionGate permission="user.role.assign">
        <Card title="角色分配">
          <Form.Item name="roleIds" label="选择角色">
            <RoleSelect multiple />
          </Form.Item>
        </Card>
      </PermissionGate>

      {/* 提交按钮 */}
      <div className="form-actions">
        <Button type="primary" htmlType="submit" loading={loading}>
          {mode === 'create' ? '创建用户' : '更新用户'}
        </Button>
        <Button onClick={handleCancel} style={{ marginLeft: 8 }}>
          取消
        </Button>
      </div>
    </Form>
  );
};
```

## 3. 权限体系

### 3.1 用户管理模块权限列表

| 权限编码 | 权限名称 | 权限描述 | 适用角色 |
|----------|----------|----------|----------|
| `user.profile.read` | 查看用户资料 | 查看用户基本信息 | 所有角色 |
| `user.profile.update` | 更新用户资料 | 修改用户基本信息 | User, AccountManager, Admin |
| `user.list.read` | 查看用户列表 | 查看系统用户列表 | AccountManager, RiskOfficer, Admin |
| `user.detail.read` | 查看用户详情 | 查看用户详细信息 | AccountManager, Admin |
| `user.create` | 创建用户 | 创建新用户账户 | AccountManager, Admin |
| `user.update` | 更新用户 | 修改用户信息 | AccountManager, Admin |
| `user.delete` | 删除用户 | 删除用户账户 | Admin |
| `user.status.update` | 更新用户状态 | 修改用户状态（启用/禁用） | Admin |
| `user.role.assign` | 分配用户角色 | 为用户分配或撤销角色 | Admin |
| `user.role.revoke` | 撤销用户角色 | 撤销用户角色 | Admin |
| `user.role.read` | 查看用户角色 | 查看用户的角色信息 | Admin |
| `user.permission.read` | 查看用户权限 | 查看用户的权限信息 | Admin |
| `user.role.batch.assign` | 批量分配角色 | 批量为用户分配角色 | Admin |
| `user.role.batch.revoke` | 批量撤销角色 | 批量撤销用户角色 | Admin |
| `user.preferences.read` | 读取用户偏好 | 查看用户偏好设置 | User, Admin |
| `user.preferences.update` | 更新用户偏好 | 修改用户偏好设置 | User, Admin |

### 3.2 角色定义

| 角色名称 | 英文名称 | 权限描述 | 用户管理权限 |
|----------|----------|----------|--------------|
| 系统管理员 | Admin | 拥有系统最高权限 | 全部用户管理权限 |
| 客户经理 | AccountManager | 负责用户服务和账户管理 | 基础用户管理权限（不含删除和高级角色操作） |
| 风控专员 | RiskOfficer | 负责风险监控和合规检查 | 仅查看权限 |
| 普通用户 | User | 基础用户，管理个人信息 | 仅个人资料权限 |

## 4. 权限控制实现

### 4.1 权限上下文 (Context)

```tsx
// contexts/PermissionContext.tsx
interface PermissionContextType {
  permissions: string[];
  roles: Role[];
  hasPermission: (permission: string) => boolean;
  hasRole: (roleName: string) => boolean;
  hasAnyPermission: (permissions: string[]) => boolean;
  loading: boolean;
}

export const PermissionContext = createContext<PermissionContextType>({
  permissions: [],
  roles: [],
  hasPermission: () => false,
  hasRole: () => false,
  hasAnyPermission: () => false,
  loading: true,
});

export const PermissionProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [permissions, setPermissions] = useState<string[]>([]);
  const [roles, setRoles] = useState<Role[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchUserPermissions();
  }, []);

  const fetchUserPermissions = async () => {
    try {
      const response = await userAPI.getMyPermissions();
      setPermissions(response.permission_codes);
      setRoles(response.roles);
    } catch (error) {
      console.error('获取权限失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const hasPermission = (permission: string): boolean => {
    return permissions.includes(permission);
  };

  const hasRole = (roleName: string): boolean => {
    return roles.some(role => role.name === roleName);
  };

  const hasAnyPermission = (permissionList: string[]): boolean => {
    return permissionList.some(permission => permissions.includes(permission));
  };

  return (
    <PermissionContext.Provider value={{
      permissions,
      roles,
      hasPermission,
      hasRole,
      hasAnyPermission,
      loading,
    }}>
      {children}
    </PermissionContext.Provider>
  );
};
```

### 4.2 权限控制组件

```tsx
// components/shared/PermissionGate.tsx
interface PermissionGateProps {
  permission?: string;
  permissions?: string[];
  role?: string;
  roles?: string[];
  requireAll?: boolean;  // 是否需要满足所有权限
  fallback?: React.ReactNode;  // 无权限时显示的内容
  children: React.ReactNode;
}

export const PermissionGate: React.FC<PermissionGateProps> = ({
  permission,
  permissions = [],
  role,
  roles = [],
  requireAll = false,
  fallback = null,
  children,
}) => {
  const { hasPermission, hasRole, hasAnyPermission } = useContext(PermissionContext);

  // 检查单个权限
  if (permission && !hasPermission(permission)) {
    return <>{fallback}</>;
  }

  // 检查多个权限
  if (permissions.length > 0) {
    const hasRequiredPermissions = requireAll
      ? permissions.every(p => hasPermission(p))
      : hasAnyPermission(permissions);
    
    if (!hasRequiredPermissions) {
      return <>{fallback}</>;
    }
  }

  // 检查角色
  if (role && !hasRole(role)) {
    return <>{fallback}</>;
  }

  if (roles.length > 0) {
    const hasRequiredRoles = requireAll
      ? roles.every(r => hasRole(r))
      : roles.some(r => hasRole(r));
    
    if (!hasRequiredRoles) {
      return <>{fallback}</>;
    }
  }

  return <>{children}</>;
};
```

### 4.3 权限 Hook

```tsx
// hooks/usePermissions.ts
export const usePermissions = () => {
  const context = useContext(PermissionContext);
  
  if (!context) {
    throw new Error('usePermissions must be used within a PermissionProvider');
  }
  
  return context;
};

// 用户管理专用权限 Hook
export const useUserPermissions = () => {
  const { hasPermission, hasAnyPermission } = usePermissions();
  
  return {
    canViewUserList: hasPermission('user.list.read'),
    canViewUserDetail: hasPermission('user.detail.read'),
    canCreateUser: hasPermission('user.create'),
    canUpdateUser: hasPermission('user.update'),
    canDeleteUser: hasPermission('user.delete'),
    canUpdateUserStatus: hasPermission('user.status.update'),
    canManageUserRoles: hasPermission('user.role.assign'),
    canViewUserPermissions: hasPermission('user.permission.read'),
    canBatchAssignRoles: hasPermission('user.role.batch.assign'),
    canManageUsers: hasAnyPermission([
      'user.create',
      'user.update',
      'user.delete',
      'user.status.update'
    ]),
  };
};
```

### 4.4 路由权限控制

```tsx
// components/ProtectedRoute.tsx
interface ProtectedRouteProps {
  permission?: string;
  permissions?: string[];
  fallback?: React.ComponentType;
  children: React.ReactNode;
}

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  permission,
  permissions = [],
  fallback: Fallback = () => <div>无权限访问</div>,
  children,
}) => {
  const { hasPermission, hasAnyPermission, loading } = usePermissions();
  const router = useRouter();

  if (loading) {
    return <div>权限验证中...</div>;
  }

  // 检查权限
  let hasAccess = true;

  if (permission && !hasPermission(permission)) {
    hasAccess = false;
  }

  if (permissions.length > 0 && !hasAnyPermission(permissions)) {
    hasAccess = false;
  }

  if (!hasAccess) {
    return <Fallback />;
  }

  return <>{children}</>;
};

// 页面使用示例
const UserListPage = () => {
  return (
    <ProtectedRoute permission="user.list.read">
      <UserList />
    </ProtectedRoute>
  );
};
```

## 5. API接口说明

### 5.1 接口列表

| 接口路径 | 方法 | 权限要求 | 说明 |
|----------|------|----------|------|
| `/b/api/v1/admin/users/auth/send-code` | POST | 无 | 发送管理员登录验证码 |
| `/b/api/v1/admin/users/auth/login` | POST | 无 | B端管理员登录 |
| `/b/api/v1/admin/users/` | GET | `user.list.read` | 获取用户列表 |
| `/b/api/v1/admin/users/{id}` | GET | `user.detail.read` | 获取用户详情 |
| `/b/api/v1/admin/users/` | POST | `user.create` | 创建用户 |
| `/b/api/v1/admin/users/{id}` | PUT | `user.update` | 更新用户信息 |
| `/b/api/v1/admin/users/{id}/status` | PUT | `user.status.update` | 更新用户状态 |
| `/b/api/v1/admin/users/{id}` | DELETE | `user.delete` | 删除用户 |
| `/b/api/v1/admin/permissions/users/{id}/roles` | GET | `user.role.read` | 获取用户角色 |
| `/b/api/v1/admin/permissions/users/{id}/roles` | POST | `user.role.assign` | 分配用户角色 |
| `/b/api/v1/admin/permissions/users/{id}/permissions` | GET | `user.permission.read` | 获取用户权限 |

### 5.2 API Service 实现

```tsx
// services/userAPI.ts
class UserAPI {
  private baseURL = '/b/api/v1/admin/users';

  // 发送管理员登录验证码
  async sendAdminVerificationCode(phone: string): Promise<void> {
    const response = await fetch(`${this.baseURL}/auth/send-code`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        phone,
        purpose: 'login'
      }),
    });
    
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.detail || '发送验证码失败');
    }
  }

  // B端管理员登录
  async adminLogin(phone: string, verificationCode: string): Promise<AdminLoginResponse> {
    const response = await fetch(`${this.baseURL}/auth/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        phone,
        verification_code: verificationCode
      }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.detail || '登录失败');
    }

    const data = await response.json();
    
    // 存储访问令牌和过期时间
    if (data.access_token) {
      localStorage.setItem('admin_token', data.access_token);
      localStorage.setItem('admin_user', JSON.stringify(data.user));
      
      // 存储token过期时间（当前时间 + expires_in秒）
      if (data.expires_in) {
        const expiresAt = Date.now() + data.expires_in * 1000;
        localStorage.setItem('admin_token_expires_at', expiresAt.toString());
      }
    }

    return data;
  }

  // 获取用户列表
  async getUsers(params: GetUsersParams): Promise<UserListResponse> {
    // 转换前端参数名为后端参数名
    const backendParams = {};
    if (params.search !== undefined) backendParams.search = params.search;
    if (params.userType !== undefined) backendParams.user_type = params.userType;
    if (params.isActive !== undefined) backendParams.is_active = params.isActive;
    if (params.isVerified !== undefined) backendParams.is_verified = params.isVerified;
    if (params.page !== undefined) backendParams.page = params.page;
    if (params.size !== undefined) backendParams.size = params.size;
    
    const response = await fetch(`${this.baseURL}?${new URLSearchParams(backendParams)}`);
    
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.detail || '获取用户列表失败');
    }
    
    const data = await response.json();
    
    // 转换返回的用户列表数据
    return {
      ...data,
      users: data.users.map(user => fieldNameConverter.toFrontend(user))
    };
  }

  // 获取用户详情
  async getUserDetail(id: number): Promise<UserDetailResponse> {
    const response = await fetch(`${this.baseURL}/${id}`);
    
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.detail || '获取用户详情失败');
    }
    
    const data = await response.json();
    
    // 转换用户详情数据
    const userDetail = fieldNameConverter.toFrontend(data);
    
    // 处理角色和权限数据
    if (data.roles) {
      userDetail.roles = data.roles.map(role => ({
        id: role.id,
        name: role.name,
        description: role.description,
        createdAt: role.created_at,
        updatedAt: role.updated_at
      }));
    }
    
    if (data.permissions) {
      userDetail.permissions = data.permissions.map(perm => ({
        id: perm.id,
        code: perm.code,
        name: perm.name,
        description: perm.description,
        module: perm.module,
        resource: perm.resource,
        action: perm.action
      }));
    }
    
    return userDetail;
  }

  // 创建用户
  async createUser(userData: CreateUserRequest): Promise<UserResponse> {
    // 转换前端字段名为后端字段名
    const backendData = fieldNameConverter.toBackend(userData);
    
    const response = await fetch(this.baseURL, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(backendData),
    });
    
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.detail || '创建用户失败');
    }
    
    const data = await response.json();
    // 转换后端字段名为前端字段名
    return fieldNameConverter.toFrontend(data);
  }

  // 更新用户
  async updateUser(id: number, userData: UpdateUserRequest): Promise<UserResponse> {
    // 转换前端字段名为后端字段名
    const backendData = fieldNameConverter.toBackend(userData);
    
    const response = await fetch(`${this.baseURL}/${id}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(backendData),
    });
    
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.detail || '更新用户失败');
    }
    
    const data = await response.json();
    // 转换后端字段名为前端字段名
    return fieldNameConverter.toFrontend(data);
  }

  // 更新用户状态
  async updateUserStatus(id: number, isActive: boolean): Promise<UserResponse> {
    const response = await fetch(`${this.baseURL}/${id}/status`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ is_active: isActive }),
    });
    
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.detail || '更新用户状态失败');
    }
    
    const data = await response.json();
    // 转换后端字段名为前端字段名
    return fieldNameConverter.toFrontend(data);
  }
  
  // 删除用户
  async deleteUser(id: number): Promise<void> {
    const response = await fetch(`${this.baseURL}/${id}`, {
      method: 'DELETE',
      headers: { 'Content-Type': 'application/json' },
    });
    
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.detail || '删除用户失败');
    }
  }

  // 获取我的权限
  async getMyPermissions(): Promise<UserPermissionsResponse> {
    const response = await fetch('/c/api/v1/permissions/me');
    return response.json();
  }
}

export const userAPI = new UserAPI();

// 前后端字段名映射转换工具
export const fieldNameConverter = {
  // 前端字段名转后端字段名
  toBackend: (data) => {
    const result = {};
    
    // 基本字段映射
    if (data.phone !== undefined) result.phone = data.phone;
    if (data.username !== undefined) result.username = data.username;
    if (data.email !== undefined) result.email = data.email;
    if (data.isActive !== undefined) result.is_active = data.isActive;
    if (data.isVerified !== undefined) result.is_verified = data.isVerified;
    if (data.isAdmin !== undefined) result.is_admin = data.isAdmin;
    
    // 枚举类型映射
    if (data.userType !== undefined) result.user_type = data.userType;
    if (data.riskLevel !== undefined) result.risk_level = data.riskLevel;
    if (data.knowledgeLevel !== undefined) result.knowledge_level = data.knowledgeLevel;
    
    // 其他特殊字段映射
    if (data.roleIds !== undefined) result.role_ids = data.roleIds;
    
    return result;
  },
  
  // 后端字段名转前端字段名
  toFrontend: (data) => {
    const result = {};
    
    // 基本字段映射
    if (data.phone !== undefined) result.phone = data.phone;
    if (data.username !== undefined) result.username = data.username;
    if (data.email !== undefined) result.email = data.email;
    if (data.is_active !== undefined) result.isActive = data.is_active;
    if (data.is_verified !== undefined) result.isVerified = data.is_verified;
    if (data.is_admin !== undefined) result.isAdmin = data.is_admin;
    
    // 枚举类型映射
    if (data.user_type !== undefined) result.userType = data.user_type;
    if (data.risk_level !== undefined) result.riskLevel = data.risk_level;
    if (data.knowledge_level !== undefined) result.knowledgeLevel = data.knowledge_level;
    
    // 日期字段映射
    if (data.first_login_at !== undefined) result.firstLoginAt = data.first_login_at;
    if (data.last_login_at !== undefined) result.lastLoginAt = data.last_login_at;
    if (data.created_at !== undefined) result.createdAt = data.created_at;
    if (data.updated_at !== undefined) result.updatedAt = data.updated_at;
    
    return result;
  }
};
```
