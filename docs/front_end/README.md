# FinSight 前端开发文档

## 📋 文档目录

本目录包含 FinSight 系统前端开发的相关文档，基于 **React + Redux + Next.js + Ant Design + TypeScript** 技术栈。

## 📚 文档列表

### B端管理系统

- **[用户模块开发指南](./user_guide.md)** - B端用户管理模块的完整开发指南
  - 路由设计与页面结构
  - 权限控制与按钮划分
  - API接口与状态管理
  - 完整代码示例
- **[TypeScript类型定义](./types.ts)** - 用户模块相关的TypeScript类型定义
  - 用户、角色、权限相关接口
  - API请求响应类型
  - 组件Props类型
  - Redux状态类型

## 🎯 快速导航

### 用户管理模块
- [路由设计](./user_guide.md#1-路由设计) - Next.js 路由配置
- [页面布局](./user_guide.md#2-页面结构与按钮划分) - 页面组件结构
- [权限体系](./user_guide.md#3-权限体系) - RBAC权限模型
- [权限控制](./user_guide.md#4-权限控制实现) - 前端权限实现
- [API接口](./user_guide.md#5-api接口说明) - 后端接口调用（含登录认证）
- [代码示例](./user_guide.md#6-代码示例) - 登录组件和用户管理组件

## 🔗 相关文档

- [权限系统总览](../role_permission/permissions_overview.md) - 完整权限体系说明
- [API接口分离指南](../api_separation_guide.md) - B端/C端接口分离
- [项目运行指南](../RUN_GUIDE.md) - 项目启动和部署

## 📝 开发规范

在进行前端开发时，请遵循以下规范：

1. **代码风格**: 使用 TypeScript + ESLint + Prettier
2. **组件设计**: 遵循单一职责原则，组件功能清晰
3. **权限控制**: 使用 PermissionGate 组件包装需要权限的功能
4. **状态管理**: 使用 Redux Toolkit 进行状态管理
5. **API调用**: 统一使用 Service 层处理API请求
6. **错误处理**: 统一错误处理和用户提示

## 🚀 快速开始

1. 阅读 [用户模块开发指南](./user_guide.md) 了解完整开发流程
2. 参考代码示例快速搭建页面结构
3. 配置权限控制确保功能安全
4. 集成API接口实现数据交互
5. 测试权限和功能完整性

## 📞 技术支持

如有开发问题，请参考：
- 项目README文档
- 相关技术文档
- 代码注释和示例 