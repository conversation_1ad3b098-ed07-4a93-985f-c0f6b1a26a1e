# FinSight 数据采集模块详细文档

## 1. 模块概述

### 1.1 模块定位
数据采集模块是 FinSight 系统的核心基础模块，负责从多种金融数据源采集原始数据，为后续的数据处理、分析和推荐提供数据基础。

### 1.2 核心功能
- **多源数据采集**: 支持API接口、网页解析、动态网页、文件上传等多种采集方式
- **智能任务调度**: 基于时间间隔和事件驱动的混合调度机制
- **反爬虫机制**: 代理池、请求延迟、User-Agent轮换等完整反爬虫策略
- **配置版本管理**: 支持数据源配置的版本化管理和热更新
- **健康监控**: 实时监控数据源健康状态，自动故障恢复
- **安全认证**: 加密存储API密钥等敏感凭证信息

### 1.3 技术特点
- **双维度分类**: 技术维度(collection_method)和业务维度(content_category)独立管理
- **事件驱动**: 支持基于财经事件的智能采集触发机制
- **混合存储**: MongoDB存储原始内容，PostgreSQL存储元数据和配置
- **可扩展架构**: 模块化设计，易于扩展新的数据源和采集方式

## 2. 系统架构

### 2.1 架构概览

```mermaid
graph TB
    subgraph "调度层"
        TS[任务调度器]
        ES[事件调度器]
        TQ[任务队列(Redis)]
    end
    
    subgraph "采集层"
        CE[爬虫引擎]
        AP[API处理器]
        WS[网页解析器]
        AC[反爬虫组件]
    end
    
    subgraph "配置层"
        DS[数据源配置]
        PP[处理管道配置]
        CR[凭证管理]
        RM[规则管理]
    end
    
    subgraph "存储层"
        MG[(MongoDB<br/>原始内容)]
        PG[(PostgreSQL<br/>元数据)]
        RD[(Redis<br/>缓存&队列)]
    end
    
    subgraph "监控层"
        HM[健康监控]
        PM[性能监控]
        AM[告警管理]
    end
    
    TS --> TQ
    ES --> TQ
    TQ --> CE
    CE --> AP
    CE --> WS
    CE --> AC
    
    DS --> CE
    PP --> CE
    CR --> CE
    RM --> CE
    
    CE --> MG
    CE --> PG
    CE --> RD
    
    CE --> HM
    HM --> PM
    PM --> AM
```

### 2.2 核心组件

#### 2.2.1 任务调度器 (Task Scheduler)
- **功能**: 管理采集任务的创建、分发和执行
- **特点**: 支持定时调度和事件驱动调度
- **实现**: 基于Redis队列的分布式任务调度

#### 2.2.2 爬虫引擎 (Crawler Engine)
- **功能**: 执行具体的数据采集任务
- **特点**: 支持多种采集方式，具备反爬虫能力
- **实现**: 异步多线程架构，支持并发采集

#### 2.2.3 配置管理器 (Configuration Manager)
- **功能**: 管理数据源配置和处理规则
- **特点**: 支持版本化管理和热更新
- **实现**: 基于PostgreSQL的配置存储

#### 2.2.4 健康监控器 (Health Monitor)
- **功能**: 监控数据源健康状态和采集性能
- **特点**: 实时监控，自动故障恢复
- **实现**: 基于指标收集和阈值告警

### 2.3 存储架构详解

#### 2.3.1 MongoDB 原始内容存储
MongoDB 作为文档数据库，专门负责存储采集到的原始内容数据，包括完整的HTML、JSON响应和已清洗的文本内容。

**存储优势**：
- **灵活的文档结构**: 适应不同数据源的多样化内容格式
- **高性能写入**: 支持高并发的数据写入操作
- **内容完整性**: 保存完整的原始数据，便于后续重新处理
- **查询灵活**: 支持复杂的文档查询和聚合操作

**集合设计**：
```javascript
// 原始内容集合 (raw_content)
{
  "_id": ObjectId("65b3c4a5f1234567890abcde"),
  "task_id": 1001,
  "source_id": 1,
  "source_url": "https://jin10.com/flash/123456",
  "crawl_time": ISODate("2025-01-26T10:05:00Z"),
  "content_hash": "abc123def456789",
  "content_type": "financial_news",
  
  // 原始内容
  "raw_html": "<html>...</html>",
  "raw_json": {...},
  "raw_text": "原始文本内容",
  
  // 清洗后内容
  "title": "央行宣布降准0.5个百分点",
  "content": "中国人民银行今日宣布...",
  "author": "金十数据",
  "publish_time": ISODate("2025-01-26T09:30:00Z"),
  "summary": "AI生成的内容摘要",
  
  // 提取的元数据
  "metadata": {
    "word_count": 256,
    "language": "zh",
    "urgency_indicators": ["央行", "降准", "货币政策"],
    "extracted_entities": ["中国人民银行", "货币政策"],
    "sentiment_score": 0.75
  },
  
  // 媒体资源
  "media_resources": {
    "images": [
      {
        "url": "https://example.com/image1.jpg",
        "alt": "图片描述",
        "size": 1024576
      }
    ],
    "videos": [],
    "attachments": []
  },
  
  // 数据质量评分
  "quality_metrics": {
    "content_completeness": 0.95,
    "structure_validity": 0.90,
    "timeliness_score": 0.98,
    "duplicate_score": 0.02
  },
  
  // 处理状态
  "processing_status": "completed",
  "processed_at": ISODate("2025-01-26T10:10:00Z"),
  "processing_errors": [],
  
  // 版本信息
  "version": 1,
  "last_updated": ISODate("2025-01-26T10:10:00Z")
}
```

#### 2.3.2 Redis 缓存与队列系统
Redis 作为内存数据库，承担缓存、队列和实时数据存储的职责。

**使用场景**：
- **任务队列**: 采集任务的分发和状态管理
- **配置缓存**: 热点配置数据的快速访问
- **实时统计**: 采集性能和健康状态的实时统计
- **去重缓存**: URL和内容的快速去重检查
- **会话管理**: 爬虫会话和状态的临时存储

**数据结构设计**：

```redis
# 1. 任务队列系统
# 待处理任务队列 (List)
LPUSH crawl_tasks:pending '{"task_id": 1001, "source_id": 1, "url": "https://jin10.com/flash", "priority": 8}'
LPUSH crawl_tasks:pending '{"task_id": 1002, "source_id": 2, "url": "https://finance.sina.com.cn", "priority": 5}'

# 优先级任务队列 (Sorted Set)
ZADD crawl_tasks:priority 8 "task:1001"
ZADD crawl_tasks:priority 5 "task:1002"

# 正在处理的任务 (Hash)
HSET task:1001 status "running"
HSET task:1001 worker_id "crawler_worker_01"
HSET task:1001 started_at "2025-01-26T10:00:00Z"
HSET task:1001 progress "45"

# 任务结果临时存储 (Hash)
HSET task_result:1001 items_found "15"
HSET task_result:1001 items_processed "12"
HSET task_result:1001 items_success "10"
HSET task_result:1001 items_failed "2"

# 2. 配置缓存系统
# 数据源配置缓存 (Hash)
HSET source_config:1 collection_method "api_json"
HSET source_config:1 crawl_interval "300"
HSET source_config:1 max_concurrent_tasks "2"
HSET source_config:1 config_version "3"

# 数据源健康状态缓存 (Hash)
HSET data_source:1 health_score "0.95"
HSET data_source:1 last_success_time "2025-01-26T09:55:00Z"
HSET data_source:1 consecutive_errors "0"
HSET data_source:1 avg_response_time "1250"

# 处理管道配置缓存 (String - JSON)
SET pipeline_config:jin10_flash_news '{"parsing_config": {...}, "cleaning_config": {...}}'
EXPIRE pipeline_config:jin10_flash_news 3600

# 3. 去重缓存系统
# URL去重缓存 (Set)
SADD crawled_urls:20250126 "https://jin10.com/flash/123456"
SADD crawled_urls:20250126 "https://finance.sina.com.cn/news/article1.html"
EXPIRE crawled_urls:20250126 86400

# 内容哈希去重缓存 (Set)
SADD content_hashes:20250126 "abc123def456789"
SADD content_hashes:20250126 "def456ghi789abc"
EXPIRE content_hashes:20250126 86400

# URL哈希到内容哈希映射 (Hash)
HSET url_content_mapping "url_hash_1" "content_hash_1"
HSET url_content_mapping "url_hash_2" "content_hash_2"
EXPIRE url_content_mapping 86400

# 4. 实时统计系统
# 每日采集统计 (Hash)
HINCRBY daily_stats:20250126 total_tasks 1
HINCRBY daily_stats:20250126 success_tasks 1
HINCRBY daily_stats:20250126 failed_tasks 0
HINCRBY daily_stats:20250126 total_items 15
HSET daily_stats:20250126 avg_response_time "1250"

# 数据源实时统计 (Hash)
HINCRBY source_stats:1:20250126 crawl_count 1
HINCRBY source_stats:1:20250126 success_count 1
HINCRBY source_stats:1:20250126 item_count 15
HSET source_stats:1:20250126 last_crawl_time "2025-01-26T10:05:00Z"

# 小时级统计 (Hash)
HINCRBY hourly_stats:2025012610 total_tasks 1
HINCRBY hourly_stats:2025012610 total_items 15
HSET hourly_stats:2025012610 avg_processing_time "2.5"

# 5. 事件驱动系统
# 财经事件缓存 (Hash)
HSET financial_event:20250126_001 title "央行降准政策发布"
HSET financial_event:20250126_001 event_time "2025-01-26T09:30:00Z"
HSET financial_event:20250126_001 importance "9"
HSET financial_event:20250126_001 keywords "央行,降准,货币政策"
HSET financial_event:20250126_001 triggered_sources "1,2,3"

# 事件触发的任务队列 (List)
LPUSH event_triggered_tasks '{"event_id": "20250126_001", "source_ids": [1,2,3], "priority": 9}'

# 6. 会话管理系统
# 爬虫会话状态 (Hash)
HSET crawler_session:worker_01 current_task_id "1001"
HSET crawler_session:worker_01 session_cookies "session_data"
HSET crawler_session:worker_01 proxy_info "proxy_ip:port"
HSET crawler_session:worker_01 user_agent "Mozilla/5.0..."
EXPIRE crawler_session:worker_01 3600

# 代理池状态 (Hash)
HSET proxy_pool:pool_1 available_count "50"
HSET proxy_pool:pool_1 total_count "100"
HSET proxy_pool:pool_1 last_rotation "2025-01-26T10:00:00Z"

# 可用代理列表 (List)
LPUSH proxy_available:pool_1 "proxy1:8080"
LPUSH proxy_available:pool_1 "proxy2:8080"

# 7. 监控告警系统
# 告警规则状态 (Hash)
HSET alert_rule:source_health_critical last_triggered "2025-01-26T09:45:00Z"
HSET alert_rule:source_health_critical trigger_count "3"
HSET alert_rule:source_health_critical is_active "true"

# 告警历史 (List)
LPUSH alert_history '{"rule": "source_health_critical", "source_id": 1, "timestamp": "2025-01-26T09:45:00Z", "severity": "critical"}'
LTRIM alert_history 0 999  # 保留最近1000条

# 8. 数据处理队列
# 待处理数据队列 (List)
LPUSH data_processing:pending '{"raw_data_id": 12345, "target_type": "flash_news", "priority": 8}'
LPUSH data_processing:pending '{"raw_data_id": 12346, "target_type": "news_article", "priority": 5}'

# 处理进度缓存 (Hash)
HSET processing:12345 stage "classifying"
HSET processing:12345 progress "25"
HSET processing:12345 worker_id "processor_worker_02"
HSET processing:12345 started_at "2025-01-26T10:15:00Z"

# AI任务队列 (List)
LPUSH ai_tasks:summary '{"content_id": 12345, "content": "央行宣布降准...", "max_length": 100}'
LPUSH ai_tasks:tagging '{"content_id": 12345, "title": "央行宣布降准", "content": "..."}'
LPUSH ai_tasks:classification '{"content_id": 12345, "text": "...", "categories": ["monetary_policy", "financial_news"]}'

# AI处理结果缓存 (Hash)
HSET ai_result:summary:12345 result "央行宣布降准0.5个百分点，释放流动性约1.2万亿元"
HSET ai_result:summary:12345 confidence "0.92"
HSET ai_result:summary:12345 model_version "gpt-4-turbo"
EXPIRE ai_result:summary:12345 3600

HSET ai_result:tags:12345 tags '["央行", "降准", "货币政策", "流动性", "金融市场"]'
HSET ai_result:tags:12345 confidence_scores '[0.95, 0.90, 0.88, 0.85, 0.82]'
EXPIRE ai_result:tags:12345 3600

# 9. 性能优化缓存
# 热点数据源缓存 (Sorted Set)
ZADD hot_sources 95 "source:1"
ZADD hot_sources 87 "source:2"
ZADD hot_sources 76 "source:3"

# 频繁访问的配置缓存 (String)
SET frequent_config:source:1 '{"headers": {...}, "selectors": {...}}'
EXPIRE frequent_config:source:1 1800

# 预计算的统计数据 (Hash)
HSET precomputed_stats:daily success_rate "0.95"
HSET precomputed_stats:daily avg_items_per_task "12.5"
HSET precomputed_stats:daily top_performing_source "1"
EXPIRE precomputed_stats:daily 3600
```

#### 2.3.3 存储架构协作模式

**数据流向**：
```mermaid
graph LR
    A[爬虫引擎] --> B[Redis队列]
    A --> C[MongoDB原始内容]
    A --> D[PostgreSQL元数据]
    
    B --> E[任务分发]
    C --> F[内容检索]
    D --> G[关系查询]
    
    E --> H[数据处理]
    F --> H
    G --> H
    
    H --> I[业务数据表]
    H --> J[Redis缓存更新]
```

**存储职责分工**：

| 存储系统 | 主要职责 | 数据类型 | 访问模式 |
|----------|----------|----------|----------|
| **MongoDB** | 原始内容存储 | 完整HTML、JSON数据、清洗后文本 | 写入密集、偶尔读取 |
| **PostgreSQL** | 元数据和关系数据 | 任务信息、配置、统计、关系数据 | 事务性读写、复杂查询 |
| **Redis** | 缓存和队列 | 任务队列、配置缓存、实时统计 | 高频读写、临时存储 |

## 3. 数据库设计

### 3.1 核心表结构

#### 3.1.1 数据源表 (data_sources)

```sql
CREATE TABLE data_sources (
    id BIGSERIAL PRIMARY KEY COMMENT '数据源唯一标识符',
    name VARCHAR(100) NOT NULL UNIQUE COMMENT '数据源名称',
    
    -- 双维度分类
    collection_method VARCHAR(30) NOT NULL COMMENT '采集方式：api_json/web_scraping/api_xml/api_rss/web_dynamic/file_upload',
    content_category VARCHAR(30) NOT NULL COMMENT '内容分类：financial_news/official_data/research_report/social_media/regulatory_filing',
    business_data_type business_data_type NOT NULL DEFAULT 'news_article' COMMENT '业务数据类型',
    
    base_url VARCHAR(1000) COMMENT '基础URL',
    description TEXT COMMENT '数据源描述',
    
    -- 处理管道关联
    processing_pipeline_id BIGINT REFERENCES processing_pipelines(id) COMMENT '关联的处理管道ID',
    
    -- 采集模式配置
    crawl_mode VARCHAR(20) DEFAULT 'interval' COMMENT '采集模式：interval/event_driven/hybrid',
    crawl_interval INTEGER DEFAULT 3600 COMMENT '采集间隔（秒）',
    priority INTEGER DEFAULT 5 COMMENT '采集优先级',
    max_concurrent_tasks INTEGER DEFAULT 1 COMMENT '最大并发任务数',
    
    -- 事件驱动配置
    event_driven_config JSONB DEFAULT '{}' COMMENT '事件驱动配置',
    supports_realtime BOOLEAN DEFAULT FALSE COMMENT '是否支持实时采集',
    
    -- 反爬虫配置
    use_proxy BOOLEAN DEFAULT FALSE COMMENT '是否使用代理',
    proxy_pool VARCHAR(50) COMMENT '代理池标识符',
    request_delay_min INTEGER DEFAULT 2 COMMENT '请求最小延迟时间（秒）',
    request_delay_max INTEGER DEFAULT 10 COMMENT '请求最大延迟时间（秒）',
    
    -- 状态管理
    status VARCHAR(20) DEFAULT 'active' COMMENT '状态：active/inactive/disabled/maintenance',
    health_score DECIMAL(3,2) DEFAULT 1.00 COMMENT '健康评分',
    last_health_check TIMESTAMP COMMENT '最后健康检查时间',
    
    -- 时间信息
    last_crawl_time TIMESTAMP COMMENT '最后采集时间',
    last_success_time TIMESTAMP COMMENT '最后成功时间',
    next_crawl_time TIMESTAMP COMMENT '下次采集时间',
    
    -- 错误管理
    error_count INTEGER DEFAULT 0 COMMENT '总错误次数',
    consecutive_error_count INTEGER DEFAULT 0 COMMENT '连续错误次数',
    max_consecutive_errors INTEGER DEFAULT 10 COMMENT '最大连续错误次数',
    
    -- 统计信息
    total_crawled_count BIGINT DEFAULT 0 COMMENT '总采集次数',
    total_success_count BIGINT DEFAULT 0 COMMENT '总成功次数',
    avg_response_time_ms INTEGER COMMENT '平均响应时间（毫秒）',
    
    current_config_version INTEGER DEFAULT 1 COMMENT '当前配置版本号',
    
    created_at TIMESTAMP DEFAULT NOW() COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT NOW() COMMENT '更新时间',
    created_by VARCHAR(100) COMMENT '创建者',
    tags TEXT[] DEFAULT '{}' COMMENT '标签数组'
);
```

#### 3.1.2 数据源配置表 (data_source_configs)

```sql
CREATE TABLE data_source_configs (
    id BIGSERIAL PRIMARY KEY COMMENT '配置记录唯一标识符',
    source_id BIGINT NOT NULL REFERENCES data_sources(id) COMMENT '数据源ID',
    version INTEGER NOT NULL COMMENT '配置版本号',
    
    -- 数据获取技术配置
    selector_config JSONB DEFAULT '{}' COMMENT 'CSS选择器、XPath等数据定位配置',
    headers_config JSONB DEFAULT '{}' COMMENT '请求头配置',
    cookies_config JSONB DEFAULT '{}' COMMENT 'Cookie配置',
    request_params_config JSONB DEFAULT '{}' COMMENT '请求参数配置',
    
    -- 反爬虫和网络请求技术配置
    javascript_config JSONB DEFAULT '{}' COMMENT 'JavaScript执行配置',
    anti_crawler_config JSONB DEFAULT '{}' COMMENT '反爬虫配置',
    retry_config JSONB DEFAULT '{}' COMMENT '重试配置',
    proxy_config JSONB DEFAULT '{}' COMMENT '代理配置',
    
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用该配置版本',
    is_validated BOOLEAN DEFAULT FALSE COMMENT '是否已验证',
    validation_result JSONB COMMENT '验证结果',
    
    change_reason TEXT COMMENT '变更原因',
    changed_by VARCHAR(100) COMMENT '变更人',
    created_at TIMESTAMP DEFAULT NOW() COMMENT '创建时间',
    
    UNIQUE(source_id, version)
);
```

#### 3.1.3 采集任务表 (crawl_tasks)

```sql
CREATE TABLE crawl_tasks (
    id BIGSERIAL PRIMARY KEY COMMENT '采集任务唯一标识符',
    source_id BIGINT NOT NULL REFERENCES data_sources(id) COMMENT '数据源ID',
    task_type VARCHAR(50) NOT NULL COMMENT '任务类型：news_list/flash_news/research_report',
    
    -- 任务来源
    trigger_type VARCHAR(20) DEFAULT 'interval' COMMENT '触发类型：interval/event/manual',
    related_event_id BIGINT COMMENT '关联的财经事件ID',
    trigger_rule_id BIGINT REFERENCES event_driven_crawl_rules(id) COMMENT '触发规则ID',
    
    target_url VARCHAR(1000) COMMENT '目标URL',
    task_config JSONB DEFAULT '{}' COMMENT '任务配置',
    
    scheduled_time TIMESTAMP COMMENT '计划开始时间',
    started_time TIMESTAMP COMMENT '实际开始时间',
    completed_time TIMESTAMP COMMENT '完成时间',
    
    status VARCHAR(20) DEFAULT 'pending' COMMENT '任务状态：pending/running/completed/failed/cancelled',
    progress INTEGER DEFAULT 0 COMMENT '执行进度（0-100）',
    
    worker_id VARCHAR(100) COMMENT '工作线程ID',
    execution_node VARCHAR(100) COMMENT '执行节点',
    
    items_found INTEGER DEFAULT 0 COMMENT '发现项目数',
    items_processed INTEGER DEFAULT 0 COMMENT '已处理项目数',
    items_success INTEGER DEFAULT 0 COMMENT '成功项目数',
    items_failed INTEGER DEFAULT 0 COMMENT '失败项目数',
    
    error_message TEXT COMMENT '错误信息',
    retry_count INTEGER DEFAULT 0 COMMENT '重试次数',
    max_retry_count INTEGER DEFAULT 3 COMMENT '最大重试次数',
    next_retry_time TIMESTAMP COMMENT '下次重试时间',
    
    duration_seconds INTEGER COMMENT '执行耗时（秒）',
    memory_usage_mb INTEGER COMMENT '内存使用量（MB）',
    network_requests INTEGER DEFAULT 0 COMMENT '网络请求数',
    
    created_at TIMESTAMP DEFAULT NOW() COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT NOW() COMMENT '更新时间'
);
```

#### 3.1.4 原始数据记录表 (raw_data_records)

```sql
CREATE TABLE raw_data_records (
    id BIGSERIAL PRIMARY KEY COMMENT '原始数据记录唯一标识符',
    task_id BIGINT REFERENCES crawl_tasks(id) COMMENT '关联采集任务ID',
    source_id BIGINT NOT NULL REFERENCES data_sources(id) COMMENT '数据源ID',
    
    -- URL信息
    source_url VARCHAR(1000) NOT NULL COMMENT '数据来源URL',
    canonical_url VARCHAR(1000) COMMENT '规范化URL',
    url_hash VARCHAR(64) NOT NULL COMMENT 'URL哈希值',
    url_domain VARCHAR(200) COMMENT 'URL域名',
    
    -- 内容标识
    content_hash VARCHAR(64) COMMENT '内容哈希值',
    content_simhash BIGINT COMMENT '内容相似性哈希',
    content_length INTEGER COMMENT '内容长度',
    content_encoding VARCHAR(20) DEFAULT 'utf-8' COMMENT '内容编码格式',
    
    -- 基础元数据
    title VARCHAR(1000) COMMENT '标题',
    author VARCHAR(200) COMMENT '作者',
    publish_time TIMESTAMP COMMENT '发布时间',
    crawl_time TIMESTAMP DEFAULT NOW() COMMENT '采集时间',
    
    -- MongoDB引用
    mongodb_id VARCHAR(24) COMMENT 'MongoDB文档ID',
    mongodb_collection VARCHAR(50) DEFAULT 'raw_content' COMMENT 'MongoDB集合名',
    content_type VARCHAR(100) COMMENT '内容类型',
    
    -- 处理状态
    processing_status VARCHAR(20) DEFAULT 'pending' COMMENT '处理状态：pending/processing/processed/failed',
    processing_priority INTEGER DEFAULT 5 COMMENT '处理优先级',
    quality_score DECIMAL(3,2) COMMENT '质量评分',
    
    -- 数据生命周期管理
    retention_policy VARCHAR(20) DEFAULT 'standard' COMMENT '保留策略',
    archive_after_days INTEGER DEFAULT 365 COMMENT '归档天数',
    delete_after_days INTEGER DEFAULT 1095 COMMENT '删除天数',
    is_archived BOOLEAN DEFAULT FALSE COMMENT '是否已归档',
    archived_at TIMESTAMP COMMENT '归档时间',
    
    created_at TIMESTAMP DEFAULT NOW() COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT NOW() COMMENT '更新时间'
);
```

### 3.2 事件驱动采集

#### 3.2.1 事件驱动采集规则表 (event_driven_crawl_rules)

```sql
CREATE TABLE event_driven_crawl_rules (
    id BIGSERIAL PRIMARY KEY COMMENT '事件驱动采集规则唯一标识符',
    source_id BIGINT NOT NULL REFERENCES data_sources(id) COMMENT '关联的数据源ID',
    rule_name VARCHAR(100) NOT NULL COMMENT '规则名称',
    
    -- 触发条件
    trigger_type VARCHAR(50) NOT NULL COMMENT '触发类型：financial_event/time_based/external_signal',
    trigger_config JSONB NOT NULL COMMENT '触发配置',
    
    -- 时间配置
    advance_minutes INTEGER DEFAULT 0 COMMENT '提前采集分钟数',
    delay_minutes INTEGER DEFAULT 0 COMMENT '延后采集分钟数',
    repeat_interval_minutes INTEGER COMMENT '重复采集间隔（分钟）',
    max_repeat_count INTEGER DEFAULT 1 COMMENT '最大重复次数',
    
    -- 采集配置
    custom_task_config JSONB DEFAULT '{}' COMMENT '自定义任务配置',
    priority_boost INTEGER DEFAULT 0 COMMENT '优先级提升',
    
    -- 状态管理
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用该规则',
    last_triggered_at TIMESTAMP COMMENT '最后触发时间',
    trigger_count INTEGER DEFAULT 0 COMMENT '触发次数',
    success_count INTEGER DEFAULT 0 COMMENT '成功次数',
    
    created_at TIMESTAMP DEFAULT NOW() COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT NOW() COMMENT '更新时间',
    
    UNIQUE(source_id, rule_name)
);
```

### 3.3 安全认证

#### 3.3.1 数据源凭证表 (data_source_credentials)

```sql
CREATE TABLE data_source_credentials (
    id BIGSERIAL PRIMARY KEY COMMENT '凭证记录唯一标识符',
    source_id BIGINT NOT NULL REFERENCES data_sources(id) COMMENT '数据源ID',
    credential_type VARCHAR(50) NOT NULL COMMENT '凭证类型：api_key/username_password/oauth_token/certificate',
    
    encrypted_data BYTEA NOT NULL COMMENT '加密后的凭证数据',
    encryption_method VARCHAR(50) DEFAULT 'AES-256-GCM' COMMENT '加密方法',
    salt BYTEA COMMENT '加密盐值',
    
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用该凭证',
    expires_at TIMESTAMP COMMENT '凭证过期时间',
    last_validated TIMESTAMP COMMENT '最后验证时间',
    validation_status VARCHAR(20) DEFAULT 'unknown' COMMENT '验证状态：valid/invalid/expired/unknown',
    
    created_at TIMESTAMP DEFAULT NOW() COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT NOW() COMMENT '更新时间',
    
    UNIQUE(source_id, credential_type)
);
```

### 3.4 索引设计

```sql
-- 数据源管理索引
CREATE INDEX idx_data_sources_status_health ON data_sources(status, health_score DESC) WHERE status = 'active';
CREATE INDEX idx_data_sources_method_category ON data_sources(collection_method, content_category, status);
CREATE INDEX idx_data_sources_crawl_time ON data_sources(next_crawl_time) WHERE status = 'active';

-- 采集任务索引
CREATE INDEX idx_crawl_tasks_status_scheduled ON crawl_tasks(status, scheduled_time) WHERE status IN ('pending', 'running');
CREATE INDEX idx_crawl_tasks_source_status ON crawl_tasks(source_id, status, created_at DESC);
CREATE INDEX idx_crawl_tasks_trigger_type ON crawl_tasks(trigger_type, status);

-- 原始数据记录索引
CREATE INDEX idx_raw_data_records_processing_status ON raw_data_records(processing_status, processing_priority DESC);
CREATE INDEX idx_raw_data_records_source_crawl ON raw_data_records(source_id, crawl_time DESC);
CREATE INDEX idx_raw_data_records_url_hash ON raw_data_records(url_hash);
CREATE INDEX idx_raw_data_records_content_hash ON raw_data_records(content_hash) WHERE content_hash IS NOT NULL;
```

## 4. 采集流程

### 4.1 采集流程概览

```mermaid
sequenceDiagram
    participant Scheduler as 任务调度器
    participant Queue as 任务队列(Redis)
    participant Engine as 爬虫引擎
    participant Config as 配置管理
    participant Source as 数据源
    participant MongoDB as MongoDB
    participant PostgreSQL as PostgreSQL
    participant Monitor as 监控系统
    
    Scheduler->>Queue: 创建采集任务
    Engine->>Queue: 获取任务
    Engine->>Config: 获取数据源配置
    Config->>Engine: 返回配置信息
    Engine->>Source: 发起HTTP请求
    Source->>Engine: 返回原始数据
    Engine->>MongoDB: 保存原始内容
    Engine->>PostgreSQL: 保存元数据记录
    Engine->>Queue: 更新任务状态
    Engine->>Monitor: 上报监控数据
    Monitor->>Monitor: 更新健康状态
```

### 4.2 任务调度流程

#### 4.2.1 定时调度

```python
async def schedule_interval_tasks():
    """定时任务调度"""
    # 查询需要执行的数据源
    sources = db.query(DataSource).filter(
        DataSource.status == 'active',
        DataSource.crawl_mode.in_(['interval', 'hybrid']),
        DataSource.next_crawl_time <= datetime.now()
    ).all()
    
    for source in sources:
        # 创建采集任务
        task = CrawlTask(
            source_id=source.id,
            task_type=source.business_data_type,
            trigger_type='interval',
            target_url=source.base_url,
            scheduled_time=datetime.now(),
            status='pending'
        )
        db.add(task)
        
        # 更新下次采集时间
        source.next_crawl_time = datetime.now() + timedelta(seconds=source.crawl_interval)
        
        # 添加到Redis队列
        redis.lpush('crawl_tasks:pending', json.dumps({
            'task_id': task.id,
            'source_id': source.id,
            'priority': source.priority
        }))
    
    db.commit()
```

#### 4.2.2 事件驱动调度

```python
async def schedule_event_driven_tasks(event_data):
    """事件驱动任务调度"""
    # 查询匹配的事件规则
    rules = db.query(EventDrivenCrawlRule).filter(
        EventDrivenCrawlRule.is_active == True,
        EventDrivenCrawlRule.trigger_type == event_data['type']
    ).all()
    
    for rule in rules:
        # 检查触发条件
        if check_trigger_condition(rule.trigger_config, event_data):
            # 创建事件驱动任务
            task = CrawlTask(
                source_id=rule.source_id,
                task_type=rule.source.business_data_type,
                trigger_type='event',
                related_event_id=event_data['event_id'],
                trigger_rule_id=rule.id,
                scheduled_time=calculate_trigger_time(rule, event_data),
                status='pending'
            )
            db.add(task)
            
            # 添加到优先队列
            redis.zadd('crawl_tasks:priority', {
                json.dumps({'task_id': task.id}): 
                rule.source.priority + rule.priority_boost
            })
    
    db.commit()
```

### 4.3 数据采集执行

#### 4.3.1 爬虫引擎核心逻辑

```python
class CrawlerEngine:
    def __init__(self):
        self.session = aiohttp.ClientSession()
        self.proxy_pool = ProxyPool()
        self.anti_crawler = AntiCrawlerHandler()
    
    async def execute_task(self, task_id: int):
        """执行采集任务"""
        try:
            # 获取任务信息
            task = db.query(CrawlTask).filter(CrawlTask.id == task_id).first()
            if not task:
                return
            
            # 更新任务状态
            task.status = 'running'
            task.started_time = datetime.now()
            db.commit()
            
            # 获取数据源配置
            source = task.source
            config = self.get_source_config(source.id)
            
            # 执行采集
            result = await self.crawl_data(source, config, task)
            
            # 保存结果
            await self.save_crawl_result(task, result)
            
            # 更新任务状态
            task.status = 'completed'
            task.completed_time = datetime.now()
            task.duration_seconds = (task.completed_time - task.started_time).total_seconds()
            
        except Exception as e:
            # 错误处理
            await self.handle_task_error(task, e)
        
        finally:
            db.commit()
    
    async def crawl_data(self, source: DataSource, config: dict, task: CrawlTask):
        """执行数据采集"""
        # 根据采集方式选择处理器
        if source.collection_method == 'api_json':
            return await self.crawl_api_json(source, config, task)
        elif source.collection_method == 'web_scraping':
            return await self.crawl_web_scraping(source, config, task)
        elif source.collection_method == 'web_dynamic':
            return await self.crawl_web_dynamic(source, config, task)
        else:
            raise ValueError(f"Unsupported collection method: {source.collection_method}")
    
    async def crawl_api_json(self, source: DataSource, config: dict, task: CrawlTask):
        """API JSON采集"""
        # 构建请求
        headers = config.get('headers_config', {})
        params = config.get('request_params_config', {})
        
        # 添加认证信息
        auth_headers = await self.get_auth_headers(source.id)
        headers.update(auth_headers)
        
        # 发起请求
        async with self.session.get(
            source.base_url,
            headers=headers,
            params=params,
            timeout=30
        ) as response:
            data = await response.json()
            
            # 解析数据
            items = self.parse_api_response(data, config.get('selector_config', {}))
            
            return {
                'items': items,
                'total_count': len(items),
                'response_time': response.headers.get('X-Response-Time'),
                'status_code': response.status
            }
    
    async def crawl_web_scraping(self, source: DataSource, config: dict, task: CrawlTask):
        """网页解析采集"""
        # 反爬虫处理
        await self.anti_crawler.prepare_request(source, config)
        
        # 获取代理
        proxy = None
        if source.use_proxy:
            proxy = await self.proxy_pool.get_proxy(source.proxy_pool)
        
        # 构建请求
        headers = self.anti_crawler.get_headers(config.get('headers_config', {}))
        
        # 发起请求
        async with self.session.get(
            task.target_url or source.base_url,
            headers=headers,
            proxy=proxy,
            timeout=30
        ) as response:
            html = await response.text()
            
            # 解析HTML
            soup = BeautifulSoup(html, 'html.parser')
            items = self.parse_html_content(soup, config.get('selector_config', {}))
            
            return {
                'items': items,
                'total_count': len(items),
                'html_content': html,
                'status_code': response.status
            }
    
    async def save_crawl_result(self, task: CrawlTask, result: dict):
        """保存采集结果"""
        for item in result['items']:
            # 计算内容哈希
            content_hash = hashlib.md5(item['content'].encode()).hexdigest()
            url_hash = hashlib.md5(item['url'].encode()).hexdigest()
            
            # Redis去重检查
            is_duplicate = await self.check_duplicate_in_redis(url_hash, content_hash)
            if is_duplicate:
                continue
            
            # PostgreSQL去重检查
            existing = db.query(RawDataRecord).filter(
                RawDataRecord.url_hash == url_hash,
                RawDataRecord.content_hash == content_hash
            ).first()
            
            if existing:
                # 更新Redis缓存
                await self.update_duplicate_cache(url_hash, content_hash)
                continue
            
            # 保存到MongoDB
            mongodb_doc = await self.prepare_mongodb_document(task, item, content_hash)
            mongo_result = await mongo_db.raw_content.insert_one(mongodb_doc)
            
            # 保存到PostgreSQL
            raw_record = RawDataRecord(
                task_id=task.id,
                source_id=task.source_id,
                source_url=item['url'],
                canonical_url=self.get_canonical_url(item['url']),
                url_hash=url_hash,
                url_domain=self.extract_domain(item['url']),
                content_hash=content_hash,
                content_simhash=self.calculate_simhash(item['content']),
                content_length=len(item['content']),
                title=item.get('title'),
                author=item.get('author'),
                publish_time=item.get('publish_time'),
                mongodb_id=str(mongo_result.inserted_id),
                mongodb_collection='raw_content',
                content_type=item.get('content_type', 'text/html'),
                processing_status='pending',
                processing_priority=self.calculate_processing_priority(item),
                quality_score=self.calculate_quality_score(item)
            )
            
            db.add(raw_record)
            
            # 更新Redis缓存
            await self.update_redis_after_save(task, item, url_hash, content_hash, raw_record.id)
            
            # 更新任务统计
            task.items_success += 1
        
        # 更新任务统计
        task.items_found = result['total_count']
        task.items_processed = len(result['items'])
    
    async def check_duplicate_in_redis(self, url_hash: str, content_hash: str) -> bool:
        """在Redis中检查重复"""
        today = datetime.now().strftime('%Y%m%d')
        
        # 检查URL是否已爬取
        url_exists = await redis.sismember(f'crawled_urls:{today}', url_hash)
        if url_exists:
            return True
        
        # 检查内容是否重复
        content_exists = await redis.sismember(f'content_hashes:{today}', content_hash)
        if content_exists:
            return True
        
        return False
    
    async def update_duplicate_cache(self, url_hash: str, content_hash: str):
        """更新去重缓存"""
        today = datetime.now().strftime('%Y%m%d')
        
        # 添加到URL缓存
        await redis.sadd(f'crawled_urls:{today}', url_hash)
        await redis.expire(f'crawled_urls:{today}', 86400)
        
        # 添加到内容哈希缓存
        await redis.sadd(f'content_hashes:{today}', content_hash)
        await redis.expire(f'content_hashes:{today}', 86400)
        
        # 添加URL到内容的映射
        await redis.hset('url_content_mapping', url_hash, content_hash)
        await redis.expire('url_content_mapping', 86400)
    
    async def prepare_mongodb_document(self, task: CrawlTask, item: dict, content_hash: str) -> dict:
        """准备MongoDB文档"""
        # 基础文档结构
        mongodb_doc = {
            'task_id': task.id,
            'source_id': task.source_id,
            'source_url': item['url'],
            'crawl_time': datetime.now(),
            'content_hash': content_hash,
            'content_type': task.source.business_data_type,
            
            # 原始内容
            'raw_html': item.get('raw_html'),
            'raw_json': item.get('raw_json'),
            'raw_text': item.get('raw_text'),
            
            # 清洗后内容
            'title': item.get('title'),
            'content': item['content'],
            'author': item.get('author'),
            'publish_time': item.get('publish_time'),
            'summary': item.get('summary'),
            
            # 提取的元数据
            'metadata': {
                'word_count': len(item['content']),
                'language': item.get('language', 'zh'),
                'urgency_indicators': item.get('urgency_indicators', []),
                'extracted_entities': item.get('extracted_entities', []),
                'sentiment_score': item.get('sentiment_score')
            },
            
            # 媒体资源
            'media_resources': {
                'images': item.get('images', []),
                'videos': item.get('videos', []),
                'attachments': item.get('attachments', [])
            },
            
            # 数据质量评分
            'quality_metrics': {
                'content_completeness': item.get('content_completeness', 0.5),
                'structure_validity': item.get('structure_validity', 0.5),
                'timeliness_score': item.get('timeliness_score', 0.5),
                'duplicate_score': 0.0
            },
            
            # 处理状态
            'processing_status': 'pending',
            'processing_errors': [],
            
            # 版本信息
            'version': 1,
            'last_updated': datetime.now()
        }
        
        # 根据业务类型添加特定字段
        if task.source.business_data_type == 'flash_news':
            mongodb_doc.update({
                'urgency_level': item.get('urgency_level', 2),
                'importance_score': item.get('importance_score', 0.5),
                'impact_scope': item.get('impact_scope', 'domestic')
            })
        elif task.source.business_data_type == 'news_article':
            mongodb_doc.update({
                'subtitle': item.get('subtitle'),
                'abstract': item.get('abstract'),
                'source_media': item.get('source_media'),
                'word_count': len(item['content']),
                'reading_time_minutes': len(item['content']) // 200
            })
        elif task.source.business_data_type == 'research_report':
            mongodb_doc.update({
                'report_type': item.get('report_type'),
                'institution_name': item.get('institution_name'),
                'analyst_name': item.get('analyst_name'),
                'investment_rating': item.get('investment_rating'),
                'target_price': item.get('target_price')
            })
        
        return mongodb_doc
    
    async def update_redis_after_save(self, task: CrawlTask, item: dict, 
                                    url_hash: str, content_hash: str, record_id: int):
        """保存后更新Redis缓存"""
        today = datetime.now().strftime('%Y%m%d')
        hour = datetime.now().strftime('%Y%m%d%H')
        
        # 更新去重缓存
        await self.update_duplicate_cache(url_hash, content_hash)
        
        # 更新实时统计
        await redis.hincrby(f'daily_stats:{today}', 'total_items', 1)
        await redis.hincrby(f'source_stats:{task.source_id}:{today}', 'item_count', 1)
        await redis.hincrby(f'hourly_stats:{hour}', 'total_items', 1)
        
        # 更新任务进度
        await redis.hset(f'task:{task.id}', 'items_processed', task.items_processed + 1)
        await redis.hset(f'task:{task.id}', 'items_success', task.items_success + 1)
        
        # 添加到处理队列
        processing_task = {
            'raw_data_id': record_id,
            'target_type': task.source.business_data_type,
            'priority': self.calculate_processing_priority(item),
            'source_id': task.source_id,
            'created_at': datetime.now().isoformat()
        }
        
        await redis.lpush('data_processing:pending', json.dumps(processing_task))
        
        # 如果是高优先级内容，添加到优先队列
        if processing_task['priority'] >= 8:
            await redis.zadd('data_processing:priority', {
                json.dumps(processing_task): processing_task['priority']
            })
    
    def calculate_processing_priority(self, item: dict) -> int:
        """计算处理优先级"""
        priority = 5  # 默认优先级
        
        # 根据紧急程度调整
        if item.get('urgency_level') == 3:
            priority += 3
        elif item.get('urgency_level') == 2:
            priority += 1
        
        # 根据重要性评分调整
        importance = item.get('importance_score', 0.5)
        if importance > 0.8:
            priority += 2
        elif importance > 0.6:
            priority += 1
        
        # 根据时效性调整
        publish_time = item.get('publish_time')
        if publish_time:
            time_diff = datetime.now() - publish_time
            if time_diff.total_seconds() < 3600:  # 1小时内
                priority += 2
            elif time_diff.total_seconds() < 21600:  # 6小时内
                priority += 1
        
        return min(priority, 10)  # 最大优先级为10
    
    def calculate_simhash(self, content: str) -> int:
        """计算内容的SimHash值"""
        # 简化的SimHash实现
        import hashlib
        
        # 分词并计算哈希
        words = content.split()
        hash_bits = []
        
        for word in words:
            word_hash = int(hashlib.md5(word.encode()).hexdigest(), 16)
            hash_bits.append(word_hash)
        
        # 计算SimHash
        simhash = 0
        for i in range(64):
            bit_count = sum(1 for h in hash_bits if h & (1 << i))
            if bit_count > len(hash_bits) // 2:
                simhash |= (1 << i)
        
        return simhash
            
            # 保存到PostgreSQL
            raw_record = RawDataRecord(
                task_id=task.id,
                source_id=task.source_id,
                source_url=item['url'],
                url_hash=url_hash,
                content_hash=content_hash,
                title=item.get('title'),
                author=item.get('author'),
                publish_time=item.get('publish_time'),
                mongodb_id=str(mongo_result.inserted_id),
                content_length=len(item['content']),
                processing_status='pending',
                quality_score=self.calculate_quality_score(item)
            )
            
            db.add(raw_record)
            
            # 更新任务统计
            task.items_success += 1
        
        # 更新任务统计
        task.items_found = result['total_count']
        task.items_processed = len(result['items'])
```

### 4.4 MongoDB 数据管理

#### 4.4.1 MongoDB 连接和集合管理

```python
from motor.motor_asyncio import AsyncIOMotorClient
from pymongo import IndexModel, ASCENDING, DESCENDING, TEXT
import asyncio
import logging

logger = logging.getLogger(__name__)

class MongoDBManager:
    def __init__(self, connection_string: str, database_name: str = 'finsight'):
        self.client = AsyncIOMotorClient(connection_string)
        self.db = self.client[database_name]
        self.raw_content = self.db.raw_content
        self.processed_content = self.db.processed_content
        self.content_archive = self.db.content_archive
    
    async def initialize_collections(self):
        """初始化集合和索引"""
        # 原始内容集合索引
        await self.raw_content.create_indexes([
            IndexModel([("task_id", ASCENDING)]),
            IndexModel([("source_id", ASCENDING)]),
            IndexModel([("content_hash", ASCENDING)], unique=True),
            IndexModel([("source_url", ASCENDING)]),
            IndexModel([("crawl_time", DESCENDING)]),
            IndexModel([("publish_time", DESCENDING)]),
            IndexModel([("processing_status", ASCENDING)]),
            IndexModel([("content_type", ASCENDING)]),
            IndexModel([("quality_metrics.content_completeness", DESCENDING)]),
            IndexModel([("metadata.word_count", DESCENDING)]),
            
            # 复合索引
            IndexModel([("source_id", ASCENDING), ("crawl_time", DESCENDING)]),
            IndexModel([("content_type", ASCENDING), ("processing_status", ASCENDING)]),
            IndexModel([("processing_status", ASCENDING), ("crawl_time", DESCENDING)]),
            
            # 文本搜索索引
            IndexModel([("title", TEXT), ("content", TEXT), ("summary", TEXT)]),
            
            # TTL索引（自动删除30天前的数据）
            IndexModel([("crawl_time", ASCENDING)], expireAfterSeconds=2592000)
        ])
        
        # 处理后内容集合索引
        await self.processed_content.create_indexes([
            IndexModel([("raw_data_id", ASCENDING)]),
            IndexModel([("business_type", ASCENDING)]),
            IndexModel([("processed_at", DESCENDING)]),
            IndexModel([("quality_score", DESCENDING)]),
            IndexModel([("tags", ASCENDING)]),
            
            # 复合索引
            IndexModel([("business_type", ASCENDING), ("processed_at", DESCENDING)]),
            IndexModel([("business_type", ASCENDING), ("quality_score", DESCENDING)])
        ])
    
    async def save_raw_content(self, document: dict) -> str:
        """保存原始内容"""
        try:
            result = await self.raw_content.insert_one(document)
            return str(result.inserted_id)
        except Exception as e:
            logger.error(f"Failed to save raw content: {e}")
            raise
    
    async def get_raw_content(self, content_id: str) -> dict:
        """获取原始内容"""
        from bson import ObjectId
        try:
            document = await self.raw_content.find_one({"_id": ObjectId(content_id)})
            return document
        except Exception as e:
            logger.error(f"Failed to get raw content: {e}")
            return None
    
    async def update_processing_status(self, content_id: str, status: str, 
                                     errors: list = None):
        """更新处理状态"""
        from bson import ObjectId
        update_data = {
            "processing_status": status,
            "last_updated": datetime.now()
        }
        
        if errors:
            update_data["processing_errors"] = errors
        
        await self.raw_content.update_one(
            {"_id": ObjectId(content_id)},
            {"$set": update_data}
        )
    
    async def find_content_by_hash(self, content_hash: str) -> dict:
        """根据内容哈希查找内容"""
        return await self.raw_content.find_one({"content_hash": content_hash})
    
    async def find_similar_content(self, simhash: int, threshold: int = 3) -> list:
        """查找相似内容"""
        # 查找SimHash相似的内容
        pipeline = [
            {
                "$match": {
                    "metadata.simhash": {"$exists": True}
                }
            },
            {
                "$project": {
                    "content_hash": 1,
                    "title": 1,
                    "source_url": 1,
                    "simhash": "$metadata.simhash",
                    "hamming_distance": {
                        "$reduce": {
                            "input": {"$range": [0, 64]},
                            "initialValue": 0,
                            "in": {
                                "$add": [
                                    "$$value",
                                    {
                                        "$cond": [
                                            {
                                                "$ne": [
                                                    {"$mod": [{"$floor": {"$divide": [simhash, {"$pow": [2, "$$this"]}]}}, 2]},
                                                    {"$mod": [{"$floor": {"$divide": ["$metadata.simhash", {"$pow": [2, "$$this"]}]}}, 2]}
                                                ]
                                            },
                                            1,
                                            0
                                        ]
                                    }
                                ]
                            }
                        }
                    }
                }
            },
            {
                "$match": {
                    "hamming_distance": {"$lte": threshold}
                }
            },
            {
                "$sort": {"hamming_distance": 1}
            }
        ]
        
        cursor = self.raw_content.aggregate(pipeline)
        return await cursor.to_list(length=10)
    
    async def get_content_statistics(self, source_id: int = None, 
                                   start_date: datetime = None, 
                                   end_date: datetime = None) -> dict:
        """获取内容统计信息"""
        match_conditions = {}
        
        if source_id:
            match_conditions["source_id"] = source_id
        
        if start_date or end_date:
            date_filter = {}
            if start_date:
                date_filter["$gte"] = start_date
            if end_date:
                date_filter["$lte"] = end_date
            match_conditions["crawl_time"] = date_filter
        
        pipeline = [
            {"$match": match_conditions},
            {
                "$group": {
                    "_id": None,
                    "total_count": {"$sum": 1},
                    "avg_word_count": {"$avg": "$metadata.word_count"},
                    "avg_quality_score": {"$avg": "$quality_metrics.content_completeness"},
                    "content_types": {"$addToSet": "$content_type"},
                    "processing_status_counts": {
                        "$push": "$processing_status"
                    }
                }
            },
            {
                "$project": {
                    "total_count": 1,
                    "avg_word_count": {"$round": ["$avg_word_count", 2]},
                    "avg_quality_score": {"$round": ["$avg_quality_score", 2]},
                    "content_types": 1,
                    "processing_status_counts": {
                        "$reduce": {
                            "input": "$processing_status_counts",
                            "initialValue": {},
                            "in": {
                                "$mergeObjects": [
                                    "$$value",
                                    {
                                        "$arrayToObject": [
                                            [{"k": "$$this", "v": {"$add": [{"$ifNull": [{"$getField": {"field": "$$this", "input": "$$value"}}, 0]}, 1]}}]
                                        ]
                                    }
                                ]
                            }
                        }
                    }
                }
            }
        ]
        
        cursor = self.raw_content.aggregate(pipeline)
        result = await cursor.to_list(length=1)
        return result[0] if result else {}
    
    async def archive_old_content(self, days_old: int = 30):
        """归档旧内容"""
        cutoff_date = datetime.now() - timedelta(days=days_old)
        
        # 查找需要归档的内容
        cursor = self.raw_content.find({
            "crawl_time": {"$lt": cutoff_date},
            "processing_status": {"$in": ["completed", "failed"]}
        })
        
        archived_count = 0
        async for document in cursor:
            # 移动到归档集合
            await self.content_archive.insert_one(document)
            
            # 从原集合删除
            await self.raw_content.delete_one({"_id": document["_id"]})
            
            archived_count += 1
        
        return archived_count
    
    async def cleanup_failed_content(self, days_old: int = 7):
        """清理失败的内容"""
        cutoff_date = datetime.now() - timedelta(days=days_old)
        
        result = await self.raw_content.delete_many({
            "crawl_time": {"$lt": cutoff_date},
            "processing_status": "failed",
            "processing_errors": {"$exists": True, "$ne": []}
        })
        
        return result.deleted_count

# MongoDB管理工具类
class MongoDBContentManager:
    def __init__(self, mongodb_manager: MongoDBManager):
        self.mongodb = mongodb_manager
    
    async def get_pending_content(self, limit: int = 100) -> list:
        """获取待处理的内容"""
        cursor = self.mongodb.raw_content.find(
            {"processing_status": "pending"},
            sort=[("crawl_time", DESCENDING)]
        ).limit(limit)
        
        return await cursor.to_list(length=limit)
    
    async def mark_content_processed(self, content_id: str, 
                                   business_record_id: int,
                                   processing_result: dict):
        """标记内容已处理"""
        from bson import ObjectId
        
        await self.mongodb.raw_content.update_one(
            {"_id": ObjectId(content_id)},
            {
                "$set": {
                    "processing_status": "completed",
                    "processed_at": datetime.now(),
                    "business_record_id": business_record_id,
                    "processing_result": processing_result
                }
            }
        )
    
    async def get_content_by_source(self, source_id: int, 
                                  start_date: datetime = None,
                                  end_date: datetime = None,
                                  limit: int = 100) -> list:
        """根据数据源获取内容"""
        query = {"source_id": source_id}
        
        if start_date or end_date:
            date_filter = {}
            if start_date:
                date_filter["$gte"] = start_date
            if end_date:
                date_filter["$lte"] = end_date
            query["crawl_time"] = date_filter
        
        cursor = self.mongodb.raw_content.find(
            query,
            sort=[("crawl_time", DESCENDING)]
        ).limit(limit)
        
        return await cursor.to_list(length=limit)
```

### 4.5 Redis 缓存管理

#### 4.5.1 Redis 连接和数据管理

```python
import redis.asyncio as redis
import json
from typing import List, Dict, Optional, Any
from datetime import datetime, timedelta

class RedisManager:
    def __init__(self, connection_string: str):
        self.redis = redis.from_url(connection_string)
        self.key_prefix = "finsight:"
    
    def get_key(self, key: str) -> str:
        """获取带前缀的键名"""
        return f"{self.key_prefix}{key}"
    
    async def close(self):
        """关闭连接"""
        await self.redis.close()
    
    # 任务队列管理
    async def push_task(self, queue_name: str, task_data: dict, priority: int = 5):
        """推送任务到队列"""
        task_json = json.dumps(task_data)
        
        # 添加到普通队列
        await self.redis.lpush(self.get_key(f"queue:{queue_name}"), task_json)
        
        # 如果有优先级，添加到优先级队列
        if priority > 5:
            await self.redis.zadd(
                self.get_key(f"priority:{queue_name}"), 
                {task_json: priority}
            )
    
    async def pop_task(self, queue_name: str, timeout: int = 10) -> Optional[dict]:
        """从队列弹出任务"""
        # 先检查优先级队列
        priority_task = await self.redis.zpopmax(self.get_key(f"priority:{queue_name}"))
        if priority_task:
            return json.loads(priority_task[0][0])
        
        # 再检查普通队列
        task_data = await self.redis.brpop(
            self.get_key(f"queue:{queue_name}"), 
            timeout=timeout
        )
        
        if task_data:
            return json.loads(task_data[1])
        
        return None
    
    async def get_queue_length(self, queue_name: str) -> int:
        """获取队列长度"""
        normal_length = await self.redis.llen(self.get_key(f"queue:{queue_name}"))
        priority_length = await self.redis.zcard(self.get_key(f"priority:{queue_name}"))
        return normal_length + priority_length
    
    # 任务状态管理
    async def set_task_status(self, task_id: int, status: str, 
                            worker_id: str = None, progress: int = 0):
        """设置任务状态"""
        key = self.get_key(f"task:{task_id}")
        
        data = {
            "status": status,
            "updated_at": datetime.now().isoformat(),
            "progress": progress
        }
        
        if worker_id:
            data["worker_id"] = worker_id
        
        await self.redis.hmset(key, data)
        await self.redis.expire(key, 86400)  # 24小时过期
    
    async def get_task_status(self, task_id: int) -> Optional[dict]:
        """获取任务状态"""
        key = self.get_key(f"task:{task_id}")
        data = await self.redis.hgetall(key)
        
        if data:
            return {k.decode(): v.decode() for k, v in data.items()}
        return None
    
    # 去重缓存管理
    async def check_url_duplicate(self, url_hash: str) -> bool:
        """检查URL是否重复"""
        today = datetime.now().strftime('%Y%m%d')
        key = self.get_key(f"crawled_urls:{today}")
        return await self.redis.sismember(key, url_hash)
    
    async def add_crawled_url(self, url_hash: str):
        """添加已爬取URL"""
        today = datetime.now().strftime('%Y%m%d')
        key = self.get_key(f"crawled_urls:{today}")
        await self.redis.sadd(key, url_hash)
        await self.redis.expire(key, 86400)
    
    async def check_content_duplicate(self, content_hash: str) -> bool:
        """检查内容是否重复"""
        today = datetime.now().strftime('%Y%m%d')
        key = self.get_key(f"content_hashes:{today}")
        return await self.redis.sismember(key, content_hash)
    
    async def add_content_hash(self, content_hash: str):
        """添加内容哈希"""
        today = datetime.now().strftime('%Y%m%d')
        key = self.get_key(f"content_hashes:{today}")
        await self.redis.sadd(key, content_hash)
        await self.redis.expire(key, 86400)
    
    # 配置缓存管理
    async def cache_source_config(self, source_id: int, config: dict, ttl: int = 3600):
        """缓存数据源配置"""
        key = self.get_key(f"source_config:{source_id}")
        await self.redis.setex(key, ttl, json.dumps(config))
    
    async def get_source_config(self, source_id: int) -> Optional[dict]:
        """获取数据源配置"""
        key = self.get_key(f"source_config:{source_id}")
        data = await self.redis.get(key)
        
        if data:
            return json.loads(data)
        return None
    
    async def cache_pipeline_config(self, pipeline_code: str, config: dict, ttl: int = 3600):
        """缓存处理管道配置"""
        key = self.get_key(f"pipeline_config:{pipeline_code}")
        await self.redis.setex(key, ttl, json.dumps(config))
    
    async def get_pipeline_config(self, pipeline_code: str) -> Optional[dict]:
        """获取处理管道配置"""
        key = self.get_key(f"pipeline_config:{pipeline_code}")
        data = await self.redis.get(key)
        
        if data:
            return json.loads(data)
        return None
    
    # 统计数据管理
    async def increment_daily_stat(self, stat_name: str, value: int = 1):
        """增加每日统计"""
        today = datetime.now().strftime('%Y%m%d')
        key = self.get_key(f"daily_stats:{today}")
        await self.redis.hincrby(key, stat_name, value)
        await self.redis.expire(key, 86400)
    
    async def increment_source_stat(self, source_id: int, stat_name: str, value: int = 1):
        """增加数据源统计"""
        today = datetime.now().strftime('%Y%m%d')
        key = self.get_key(f"source_stats:{source_id}:{today}")
        await self.redis.hincrby(key, stat_name, value)
        await self.redis.expire(key, 86400)
    
    async def get_daily_stats(self, date: str = None) -> dict:
        """获取每日统计"""
        if not date:
            date = datetime.now().strftime('%Y%m%d')
        
        key = self.get_key(f"daily_stats:{date}")
        data = await self.redis.hgetall(key)
        
        if data:
            return {k.decode(): int(v.decode()) for k, v in data.items()}
        return {}
    
    async def get_source_stats(self, source_id: int, date: str = None) -> dict:
        """获取数据源统计"""
        if not date:
            date = datetime.now().strftime('%Y%m%d')
        
        key = self.get_key(f"source_stats:{source_id}:{date}")
        data = await self.redis.hgetall(key)
        
        if data:
            return {k.decode(): int(v.decode()) for k, v in data.items()}
        return {}
    
    # 健康状态管理
    async def update_source_health(self, source_id: int, health_data: dict):
        """更新数据源健康状态"""
        key = self.get_key(f"source_health:{source_id}")
        
        # 转换数据类型
        redis_data = {}
        for k, v in health_data.items():
            if isinstance(v, (int, float)):
                redis_data[k] = str(v)
            elif isinstance(v, datetime):
                redis_data[k] = v.isoformat()
            else:
                redis_data[k] = str(v)
        
        await self.redis.hmset(key, redis_data)
        await self.redis.expire(key, 3600)
    
    async def get_source_health(self, source_id: int) -> Optional[dict]:
        """获取数据源健康状态"""
        key = self.get_key(f"source_health:{source_id}")
        data = await self.redis.hgetall(key)
        
        if data:
            result = {}
            for k, v in data.items():
                key_str = k.decode()
                value_str = v.decode()
                
                # 尝试转换数据类型
                if key_str in ['health_score', 'avg_response_time']:
                    result[key_str] = float(value_str)
                elif key_str in ['consecutive_errors', 'error_count']:
                    result[key_str] = int(value_str)
                elif key_str.endswith('_time'):
                    try:
                        result[key_str] = datetime.fromisoformat(value_str)
                    except:
                        result[key_str] = value_str
                else:
                    result[key_str] = value_str
            
            return result
        return None
    
    # 会话管理
    async def save_crawler_session(self, worker_id: str, session_data: dict, ttl: int = 3600):
        """保存爬虫会话"""
        key = self.get_key(f"crawler_session:{worker_id}")
        await self.redis.hmset(key, session_data)
        await self.redis.expire(key, ttl)
    
    async def get_crawler_session(self, worker_id: str) -> Optional[dict]:
        """获取爬虫会话"""
        key = self.get_key(f"crawler_session:{worker_id}")
        data = await self.redis.hgetall(key)
        
        if data:
            return {k.decode(): v.decode() for k, v in data.items()}
        return None
    
    # 代理池管理
    async def add_proxy(self, pool_name: str, proxy: str):
        """添加代理到代理池"""
        key = self.get_key(f"proxy_pool:{pool_name}")
        await self.redis.sadd(key, proxy)
    
    async def get_proxy(self, pool_name: str) -> Optional[str]:
        """从代理池获取代理"""
        key = self.get_key(f"proxy_pool:{pool_name}")
        proxy = await self.redis.spop(key)
        
        if proxy:
            # 使用后重新添加到池中（轮换使用）
            await self.redis.sadd(key, proxy)
            return proxy.decode()
        return None
    
    async def remove_proxy(self, pool_name: str, proxy: str):
        """从代理池移除代理"""
        key = self.get_key(f"proxy_pool:{pool_name}")
        await self.redis.srem(key, proxy)
    
    # 告警管理
    async def add_alert(self, alert_data: dict):
        """添加告警"""
        key = self.get_key("alert_history")
        await self.redis.lpush(key, json.dumps(alert_data))
        await self.redis.ltrim(key, 0, 999)  # 保留最近1000条
    
    async def get_recent_alerts(self, limit: int = 10) -> List[dict]:
        """获取最近的告警"""
        key = self.get_key("alert_history")
        alerts = await self.redis.lrange(key, 0, limit - 1)
        
        return [json.loads(alert) for alert in alerts]
    
    # 批量操作
    async def batch_update_stats(self, stats_updates: List[dict]):
        """批量更新统计数据"""
        pipe = self.redis.pipeline()
        
        for update in stats_updates:
            if update['type'] == 'daily':
                key = self.get_key(f"daily_stats:{update['date']}")
                pipe.hincrby(key, update['stat_name'], update['value'])
                pipe.expire(key, 86400)
            elif update['type'] == 'source':
                key = self.get_key(f"source_stats:{update['source_id']}:{update['date']}")
                pipe.hincrby(key, update['stat_name'], update['value'])
                pipe.expire(key, 86400)
        
        await pipe.execute()
    
    async def cleanup_expired_keys(self):
        """清理过期的键"""
        # 清理过期的URL缓存
        yesterday = (datetime.now() - timedelta(days=1)).strftime('%Y%m%d')
        url_key = self.get_key(f"crawled_urls:{yesterday}")
        content_key = self.get_key(f"content_hashes:{yesterday}")
        
        await self.redis.delete(url_key, content_key)
        
        # 清理过期的统计数据（保留30天）
        old_date = (datetime.now() - timedelta(days=30)).strftime('%Y%m%d')
        pattern = self.get_key(f"*_stats:*:{old_date}")
        
        keys = await self.redis.keys(pattern)
        if keys:
            await self.redis.delete(*keys)
```

### 4.6 配置管理

#### 4.4.1 配置版本管理

```python
class ConfigurationManager:
    def __init__(self):
        self.config_cache = {}
    
    def get_source_config(self, source_id: int, version: int = None) -> dict:
        """获取数据源配置"""
        cache_key = f"source_config:{source_id}:{version or 'latest'}"
        
        if cache_key in self.config_cache:
            return self.config_cache[cache_key]
        
        # 获取配置
        if version:
            config = db.query(DataSourceConfig).filter(
                DataSourceConfig.source_id == source_id,
                DataSourceConfig.version == version,
                DataSourceConfig.is_active == True
            ).first()
        else:
            config = db.query(DataSourceConfig).filter(
                DataSourceConfig.source_id == source_id,
                DataSourceConfig.is_active == True
            ).order_by(desc(DataSourceConfig.version)).first()
        
        if not config:
            raise ValueError(f"No active config found for source {source_id}")
        
        # 构建配置字典
        config_dict = {
            'version': config.version,
            'selector_config': config.selector_config,
            'headers_config': config.headers_config,
            'cookies_config': config.cookies_config,
            'request_params_config': config.request_params_config,
            'javascript_config': config.javascript_config,
            'anti_crawler_config': config.anti_crawler_config,
            'retry_config': config.retry_config,
            'proxy_config': config.proxy_config
        }
        
        # 缓存配置
        self.config_cache[cache_key] = config_dict
        
        return config_dict
    
    def update_source_config(self, source_id: int, config_updates: dict, 
                           change_reason: str, changed_by: str) -> int:
        """更新数据源配置"""
        # 获取当前版本
        current_config = db.query(DataSourceConfig).filter(
            DataSourceConfig.source_id == source_id,
            DataSourceConfig.is_active == True
        ).order_by(desc(DataSourceConfig.version)).first()
        
        new_version = (current_config.version + 1) if current_config else 1
        
        # 创建新版本配置
        new_config = DataSourceConfig(
            source_id=source_id,
            version=new_version,
            selector_config=config_updates.get('selector_config', {}),
            headers_config=config_updates.get('headers_config', {}),
            cookies_config=config_updates.get('cookies_config', {}),
            request_params_config=config_updates.get('request_params_config', {}),
            javascript_config=config_updates.get('javascript_config', {}),
            anti_crawler_config=config_updates.get('anti_crawler_config', {}),
            retry_config=config_updates.get('retry_config', {}),
            proxy_config=config_updates.get('proxy_config', {}),
            change_reason=change_reason,
            changed_by=changed_by,
            is_active=True
        )
        
        db.add(new_config)
        
        # 停用旧版本
        if current_config:
            current_config.is_active = False
        
        # 更新数据源的配置版本
        source = db.query(DataSource).filter(DataSource.id == source_id).first()
        source.current_config_version = new_version
        
        db.commit()
        
        # 清除缓存
        self.clear_config_cache(source_id)
        
        return new_version
    
    def validate_config(self, source_id: int, config: dict) -> dict:
        """验证配置有效性"""
        validation_result = {
            'is_valid': True,
            'errors': [],
            'warnings': []
        }
        
        # 基础验证
        if not config.get('selector_config'):
            validation_result['errors'].append('selector_config is required')
        
        # 请求头验证
        headers = config.get('headers_config', {})
        if not headers.get('User-Agent'):
            validation_result['warnings'].append('User-Agent not specified')
        
        # 代理配置验证
        if config.get('proxy_config', {}).get('enabled'):
            if not config['proxy_config'].get('proxy_list'):
                validation_result['errors'].append('proxy_list is required when proxy is enabled')
        
        validation_result['is_valid'] = len(validation_result['errors']) == 0
        
        return validation_result
```

### 4.5 反爬虫机制

#### 4.5.1 反爬虫处理器

```python
class AntiCrawlerHandler:
    def __init__(self):
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36'
        ]
        self.request_counts = {}
    
    async def prepare_request(self, source: DataSource, config: dict):
        """准备请求的反爬虫处理"""
        # 请求频率控制
        await self.rate_limit(source)
        
        # 随机延迟
        delay = random.uniform(source.request_delay_min, source.request_delay_max)
        await asyncio.sleep(delay)
    
    def get_headers(self, base_headers: dict) -> dict:
        """获取请求头"""
        headers = base_headers.copy()
        
        # 随机User-Agent
        if 'User-Agent' not in headers:
            headers['User-Agent'] = random.choice(self.user_agents)
        
        # 添加常见头部
        headers.update({
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        })
        
        return headers
    
    async def rate_limit(self, source: DataSource):
        """请求频率限制"""
        now = time.time()
        source_key = f"rate_limit:{source.id}"
        
        # 获取当前请求计数
        current_count = self.request_counts.get(source_key, {'count': 0, 'window_start': now})
        
        # 检查是否需要重置窗口
        if now - current_count['window_start'] > 60:  # 1分钟窗口
            current_count = {'count': 0, 'window_start': now}
        
        # 检查是否超过限制
        max_requests_per_minute = 60 // source.request_delay_min
        if current_count['count'] >= max_requests_per_minute:
            sleep_time = 60 - (now - current_count['window_start'])
            await asyncio.sleep(sleep_time)
            current_count = {'count': 0, 'window_start': time.time()}
        
        # 更新计数
        current_count['count'] += 1
        self.request_counts[source_key] = current_count
```

## 5. 监控与运维

### 5.1 健康监控

#### 5.1.1 数据源健康检查

```python
class HealthMonitor:
    def __init__(self):
        self.health_metrics = {}
    
    async def check_source_health(self, source_id: int) -> dict:
        """检查数据源健康状态"""
        source = db.query(DataSource).filter(DataSource.id == source_id).first()
        if not source:
            return {'status': 'not_found'}
        
        health_result = {
            'source_id': source_id,
            'status': 'healthy',
            'score': 1.0,
            'issues': [],
            'metrics': {}
        }
        
        # 检查连接性
        connectivity_score = await self.check_connectivity(source)
        health_result['metrics']['connectivity'] = connectivity_score
        
        # 检查响应时间
        response_time = await self.check_response_time(source)
        health_result['metrics']['response_time'] = response_time
        
        # 检查错误率
        error_rate = await self.check_error_rate(source)
        health_result['metrics']['error_rate'] = error_rate
        
        # 检查数据质量
        data_quality = await self.check_data_quality(source)
        health_result['metrics']['data_quality'] = data_quality
        
        # 计算综合健康分数
        health_result['score'] = (
            connectivity_score * 0.3 +
            (1 - response_time / 10000) * 0.2 +  # 响应时间权重
            (1 - error_rate) * 0.3 +
            data_quality * 0.2
        )
        
        # 判断健康状态
        if health_result['score'] < 0.3:
            health_result['status'] = 'critical'
        elif health_result['score'] < 0.6:
            health_result['status'] = 'warning'
        
        # 更新数据源健康状态
        source.health_score = health_result['score']
        source.last_health_check = datetime.now()
        db.commit()
        
        return health_result
    
    async def check_connectivity(self, source: DataSource) -> float:
        """检查连接性"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(source.base_url, timeout=10) as response:
                    return 1.0 if response.status == 200 else 0.5
        except:
            return 0.0
    
    async def check_response_time(self, source: DataSource) -> float:
        """检查响应时间"""
        try:
            start_time = time.time()
            async with aiohttp.ClientSession() as session:
                async with session.get(source.base_url, timeout=10) as response:
                    end_time = time.time()
                    return (end_time - start_time) * 1000  # 毫秒
        except:
            return 10000  # 超时返回10秒
    
    async def check_error_rate(self, source: DataSource) -> float:
        """检查错误率"""
        # 查询最近24小时的任务
        since_time = datetime.now() - timedelta(hours=24)
        tasks = db.query(CrawlTask).filter(
            CrawlTask.source_id == source.id,
            CrawlTask.created_at >= since_time
        ).all()
        
        if not tasks:
            return 0.0
        
        failed_tasks = [t for t in tasks if t.status == 'failed']
        return len(failed_tasks) / len(tasks)
    
    async def check_data_quality(self, source: DataSource) -> float:
        """检查数据质量"""
        # 查询最近采集的数据
        since_time = datetime.now() - timedelta(hours=24)
        records = db.query(RawDataRecord).filter(
            RawDataRecord.source_id == source.id,
            RawDataRecord.created_at >= since_time
        ).all()
        
        if not records:
            return 0.5
        
        # 计算平均质量分数
        quality_scores = [r.quality_score for r in records if r.quality_score is not None]
        if not quality_scores:
            return 0.5
        
        return sum(quality_scores) / len(quality_scores)
```

### 5.2 性能监控

#### 5.2.1 性能指标收集

```python
class PerformanceMonitor:
    def __init__(self):
        self.metrics_buffer = []
    
    async def collect_task_metrics(self, task: CrawlTask):
        """收集任务性能指标"""
        metrics = {
            'task_id': task.id,
            'source_id': task.source_id,
            'start_time': task.started_time,
            'end_time': task.completed_time,
            'duration': task.duration_seconds,
            'items_found': task.items_found,
            'items_processed': task.items_processed,
            'items_success': task.items_success,
            'success_rate': task.items_success / task.items_processed if task.items_processed > 0 else 0,
            'memory_usage': task.memory_usage_mb,
            'network_requests': task.network_requests,
            'status': task.status
        }
        
        # 保存到Redis
        redis.lpush('performance_metrics', json.dumps(metrics))
        
        # 更新实时统计
        await self.update_realtime_stats(task.source_id, metrics)
    
    async def update_realtime_stats(self, source_id: int, metrics: dict):
        """更新实时统计"""
        today = datetime.now().strftime('%Y-%m-%d')
        
        # 更新每日统计
        redis.hincrby(f'daily_stats:{source_id}:{today}', 'total_tasks', 1)
        redis.hincrby(f'daily_stats:{source_id}:{today}', 'total_items', metrics['items_processed'])
        
        if metrics['status'] == 'completed':
            redis.hincrby(f'daily_stats:{source_id}:{today}', 'success_tasks', 1)
        
        # 更新平均响应时间
        current_avg = redis.hget(f'daily_stats:{source_id}:{today}', 'avg_duration')
        if current_avg:
            current_avg = float(current_avg)
            task_count = redis.hget(f'daily_stats:{source_id}:{today}', 'total_tasks')
            new_avg = (current_avg * (int(task_count) - 1) + metrics['duration']) / int(task_count)
            redis.hset(f'daily_stats:{source_id}:{today}', 'avg_duration', new_avg)
        else:
            redis.hset(f'daily_stats:{source_id}:{today}', 'avg_duration', metrics['duration'])
```

### 5.3 告警管理

#### 5.3.1 告警规则

```python
class AlertManager:
    def __init__(self):
        self.alert_rules = [
            {
                'name': 'source_health_critical',
                'condition': lambda metrics: metrics.get('health_score', 1.0) < 0.3,
                'severity': 'critical',
                'message': 'Data source health score is critically low'
            },
            {
                'name': 'high_error_rate',
                'condition': lambda metrics: metrics.get('error_rate', 0.0) > 0.5,
                'severity': 'warning',
                'message': 'High error rate detected'
            },
            {
                'name': 'slow_response_time',
                'condition': lambda metrics: metrics.get('avg_response_time', 0) > 5000,
                'severity': 'warning',
                'message': 'Slow response time detected'
            }
        ]
    
    async def check_alerts(self, source_id: int, metrics: dict):
        """检查告警条件"""
        for rule in self.alert_rules:
            if rule['condition'](metrics):
                await self.trigger_alert(source_id, rule, metrics)
    
    async def trigger_alert(self, source_id: int, rule: dict, metrics: dict):
        """触发告警"""
        alert = {
            'source_id': source_id,
            'rule_name': rule['name'],
            'severity': rule['severity'],
            'message': rule['message'],
            'metrics': metrics,
            'timestamp': datetime.now().isoformat()
        }
        
        # 发送告警
        await self.send_alert(alert)
        
        # 记录告警历史
        redis.lpush('alert_history', json.dumps(alert))
    
    async def send_alert(self, alert: dict):
        """发送告警通知"""
        # 这里可以集成邮件、短信、钉钉等通知方式
        print(f"ALERT: {alert['message']} - Source: {alert['source_id']}")
```

## 6. 配置示例

### 6.1 数据源配置示例

#### 6.1.1 金十数据快讯配置

```sql
-- 数据源基础配置
INSERT INTO data_sources (
    name, collection_method, content_category, business_data_type,
    base_url, crawl_mode, crawl_interval, priority,
    use_proxy, request_delay_min, request_delay_max,
    max_concurrent_tasks, supports_realtime
) VALUES (
    '金十数据快讯',
    'api_json',
    'financial_news',
    'flash_news',
    'https://flash-api.jin10.com/get_flash_list',
    'hybrid',
    300,  -- 5分钟
    8,
    FALSE,
    2,
    5,
    2,
    TRUE
);

-- 数据源技术配置
INSERT INTO data_source_configs (
    source_id, version,
    selector_config, headers_config, request_params_config,
    anti_crawler_config, retry_config
) VALUES (
    (SELECT id FROM data_sources WHERE name = '金十数据快讯'),
    1,
    '{
        "data_path": "$.data",
        "title_field": "content",
        "time_field": "time",
        "id_field": "id",
        "importance_field": "importance"
    }',
    '{
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
        "Accept": "application/json, text/javascript, */*; q=0.01",
        "Referer": "https://www.jin10.com/"
    }',
    '{
        "max_id": 0,
        "limit": 20,
        "channel": "flash"
    }',
    '{
        "rotate_user_agent": true,
        "random_delay": true,
        "session_rotation": 100
    }',
    '{
        "max_retries": 3,
        "retry_delay": 5,
        "backoff_factor": 2
    }'
);
```

#### 6.1.2 新浪财经新闻配置

```sql
-- 数据源基础配置
INSERT INTO data_sources (
    name, collection_method, content_category, business_data_type,
    base_url, crawl_mode, crawl_interval, priority,
    use_proxy, request_delay_min, request_delay_max
) VALUES (
    '新浪财经新闻',
    'web_scraping',
    'financial_news',
    'news_article',
    'https://finance.sina.com.cn/roll/',
    'interval',
    1800,  -- 30分钟
    6,
    TRUE,
    5,
    15
);

-- 数据源技术配置
INSERT INTO data_source_configs (
    source_id, version,
    selector_config, headers_config, anti_crawler_config
) VALUES (
    (SELECT id FROM data_sources WHERE name = '新浪财经新闻'),
    1,
    '{
        "article_list": ".feed-card-item",
        "title": "h2 a",
        "url": "h2 a@href",
        "time": ".feed-card-time",
        "summary": ".feed-card-summary"
    }',
    '{
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8"
    }',
    '{
        "use_proxy": true,
        "proxy_rotation": true,
        "session_cookies": true,
        "javascript_execution": false
    }'
);
```

### 6.2 事件驱动配置示例

```sql
-- 央行政策发布监控
INSERT INTO event_driven_crawl_rules (
    source_id, rule_name, trigger_type, trigger_config,
    advance_minutes, delay_minutes, repeat_interval_minutes, max_repeat_count,
    priority_boost
) VALUES (
    (SELECT id FROM data_sources WHERE name = '金十数据快讯'),
    '央行政策发布监控',
    'financial_event',
    '{
        "keywords": ["央行", "货币政策", "利率", "准备金"],
        "importance_threshold": 8,
        "event_types": ["monetary_policy", "central_bank_announcement"]
    }',
    30,   -- 提前30分钟开始采集
    10,   -- 延后10分钟结束采集
    5,    -- 每5分钟重复采集
    8,    -- 最多重复8次
    3     -- 优先级提升3级
);

-- 重要经济数据发布监控
INSERT INTO event_driven_crawl_rules (
    source_id, rule_name, trigger_type, trigger_config,
    advance_minutes, delay_minutes, repeat_interval_minutes, max_repeat_count
) VALUES (
    (SELECT id FROM data_sources WHERE name = '东方财富经济数据'),
    '重要经济数据发布监控',
    'economic_data_release',
    '{
        "indicators": ["CPI", "PPI", "GDP", "PMI"],
        "countries": ["中国", "美国"],
        "importance_star": 4
    }',
    15,   -- 提前15分钟
    5,    -- 延后5分钟
    2,    -- 每2分钟
    10    -- 最多10次
);
```

## 7. 部署与运维

### 7.1 部署架构

```yaml
# docker-compose.yml
version: '3.8'

services:
  crawler-scheduler:
    build: ./crawler
    command: python -m crawler.scheduler
    environment:
      - REDIS_URL=redis://redis:6379
      - DATABASE_URL=************************************/finsight
    depends_on:
      - redis
      - postgres
    
  crawler-worker:
    build: ./crawler
    command: python -m crawler.worker
    environment:
      - REDIS_URL=redis://redis:6379
      - DATABASE_URL=************************************/finsight
      - MONGODB_URL=mongodb://mongo:27017/finsight
    depends_on:
      - redis
      - postgres
      - mongo
    deploy:
      replicas: 3
    
  crawler-monitor:
    build: ./crawler
    command: python -m crawler.monitor
    environment:
      - REDIS_URL=redis://redis:6379
      - DATABASE_URL=************************************/finsight
    depends_on:
      - redis
      - postgres
    
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    
  postgres:
    image: postgres:15
    environment:
      - POSTGRES_DB=finsight
      - POSTGRES_USER=user
      - POSTGRES_PASSWORD=pass
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    
  mongo:
    image: mongo:6
    ports:
      - "27017:27017"
    volumes:
      - mongo_data:/data/db

volumes:
  postgres_data:
  mongo_data:
```

### 7.2 监控配置

```yaml
# monitoring/prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'crawler'
    static_configs:
      - targets: ['crawler-worker:8080']
    metrics_path: '/metrics'
    scrape_interval: 5s

  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']

  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres:5432']
```

### 7.3 日志配置

```python
# logging_config.py
import logging
from logging.handlers import RotatingFileHandler
import json

class JSONFormatter(logging.Formatter):
    def format(self, record):
        log_entry = {
            'timestamp': self.formatTime(record, self.datefmt),
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'funcName': record.funcName,
            'lineno': record.lineno
        }
        
        if hasattr(record, 'source_id'):
            log_entry['source_id'] = record.source_id
        
        if hasattr(record, 'task_id'):
            log_entry['task_id'] = record.task_id
        
        if record.exc_info:
            log_entry['exception'] = self.formatException(record.exc_info)
        
        return json.dumps(log_entry, ensure_ascii=False)

def setup_logging():
    """设置日志配置"""
    logger = logging.getLogger('crawler')
    logger.setLevel(logging.INFO)
    
    # 文件处理器
    file_handler = RotatingFileHandler(
        'logs/crawler.log',
        maxBytes=10*1024*1024,  # 10MB
        backupCount=5
    )
    file_handler.setFormatter(JSONFormatter())
    
    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(JSONFormatter())
    
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
    
    return logger
```

## 8. 总结

### 8.1 模块特点

1. **高可扩展性**: 支持多种数据源类型和采集方式
2. **智能调度**: 定时调度和事件驱动的混合模式
3. **配置灵活**: 版本化配置管理，支持热更新
4. **监控完善**: 全方位的健康监控和性能监控
5. **反爬虫能力**: 完整的反爬虫机制和代理支持

### 8.2 技术优势

1. **双维度分类**: 技术维度和业务维度的独立管理
2. **混合存储**: MongoDB + PostgreSQL + Redis的混合存储架构
3. **异步处理**: 基于异步编程的高性能采集引擎
4. **故障恢复**: 自动重试和故障恢复机制
5. **安全保障**: 凭证加密存储和访问控制

### 8.3 应用场景

1. **金融快讯采集**: 实时采集各大财经网站的快讯信息
2. **新闻文章采集**: 定时采集财经新闻和分析文章
3. **研究报告采集**: 采集券商研报和分析报告
4. **经济数据采集**: 采集官方经济数据和统计信息
5. **社交媒体监控**: 监控财经相关的社交媒体内容

数据采集模块作为 FinSight 系统的基础模块，为整个系统提供了稳定、高效、智能的数据采集能力，是系统数据驱动能力的重要保障。 