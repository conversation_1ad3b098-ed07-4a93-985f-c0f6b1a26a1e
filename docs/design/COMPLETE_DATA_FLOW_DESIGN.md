# FinSight 完整数据流程设计方案

## 1. 概述

### 1.1 文档目的
本文档详细描述了 FinSight 系统从数据爬取到用户推送的完整数据流程，包括数据采集、处理、标签关联、用户画像构建和个性化推送的全链路设计。

### 1.2 系统架构概览

```mermaid
graph TB
    subgraph "数据源层"
        DS1[金十数据]
        DS2[新浪财经]
        DS3[东方财富]
        DS4[财经头条]
    end
    
    subgraph "数据采集层"
        CE[爬虫引擎]
        ES[事件调度器]
        TQ[任务队列]
    end
    
    subgraph "数据存储层"
        MG[(MongoDB<br/>原始内容)]
        PG[(PostgreSQL<br/>结构化数据)]
        RD[(Redis<br/>缓存&队列)]
    end
    
    subgraph "数据处理层"
        DP[数据处理引擎]
        AI[AI分析服务]
        TE[标签提取器]
    end
    
    subgraph "业务数据层"
        FN[快讯表]
        NA[新闻表]
        RR[研报表]
        ED[经济数据表]
    end
    
    subgraph "标签系统"
        TS[标签库]
        TC[标签分类]
        TR[标签关系]
        AM[AI标签匹配]
    end
    
    subgraph "用户系统"
        UP[用户画像]
        UI[用户兴趣]
        UB[用户行为]
        UR[推荐引擎]
    end
    
    subgraph "推送系统"
        PS[推送服务]
        PN[推送通知]
        PF[推送反馈]
    end
    
    DS1 --> CE
    DS2 --> CE
    DS3 --> CE
    DS4 --> CE
    
    CE --> MG
    CE --> PG
    CE --> RD
    
    MG --> DP
    PG --> DP
    RD --> DP
    
    DP --> AI
    DP --> TE
    
    DP --> FN
    DP --> NA
    DP --> RR
    DP --> ED
    
    TE --> TS
    TE --> AM
    
    FN --> UR
    NA --> UR
    RR --> UR
    ED --> UR
    
    TS --> UR
    UP --> UR
    UI --> UR
    UB --> UR
    
    UR --> PS
    PS --> PN
    PN --> PF
    PF --> UB
```

### 1.3 数据库使用分工

| 数据库 | 主要用途 | 存储内容 |
|--------|----------|----------|
| **MongoDB** | 原始内容存储 | 完整的HTML、JSON原始数据，已清洗的文本内容 |
| **PostgreSQL** | 结构化数据存储 | 元数据、业务数据、用户数据、标签系统、关系数据 |
| **Redis** | 缓存和队列 | 任务队列、用户会话、热点数据缓存、推荐结果缓存 |

## 2. 数据采集流程

### 2.1 数据采集架构

#### 2.1.1 采集任务调度
```mermaid
sequenceDiagram
    participant Scheduler as 任务调度器
    participant TaskQueue as 任务队列(Redis)
    participant CrawlerEngine as 爬虫引擎
    participant DataSource as 数据源
    participant MongoDB as MongoDB
    participant PostgreSQL as PostgreSQL
    
    Scheduler->>TaskQueue: 创建采集任务
    CrawlerEngine->>TaskQueue: 获取任务
    CrawlerEngine->>DataSource: 发起HTTP请求
    DataSource->>CrawlerEngine: 返回原始数据
    CrawlerEngine->>MongoDB: 保存原始内容
    CrawlerEngine->>PostgreSQL: 保存元数据记录
    CrawlerEngine->>TaskQueue: 更新任务状态
```

#### 2.1.2 Redis 任务队列设计
```redis
# 任务队列结构
LPUSH crawl_tasks:pending '{"task_id": 1001, "source_id": 1, "url": "https://jin10.com/flash", "priority": 8}'
LPUSH crawl_tasks:pending '{"task_id": 1002, "source_id": 2, "url": "https://finance.sina.com.cn", "priority": 5}'

# 任务状态跟踪
HSET task:1001 status "running"
HSET task:1001 worker_id "crawler_worker_01"
HSET task:1001 started_at "2025-01-26T10:00:00Z"

# 任务优先级队列
ZADD crawl_tasks:priority 8 "task:1001"
ZADD crawl_tasks:priority 5 "task:1002"

# 数据源健康状态缓存
HSET data_source:1 health_score "0.95"
HSET data_source:1 last_success_time "2025-01-26T09:55:00Z"
HSET data_source:1 consecutive_errors "0"
```

#### 2.1.3 MongoDB 原始数据存储
```javascript
// MongoDB 文档结构
{
  "_id": ObjectId("65b3c4a5f1234567890abcde"),
  "task_id": 1001,
  "source_id": 1,
  "source_url": "https://jin10.com/flash/123456",
  "crawl_time": ISODate("2025-01-26T10:05:00Z"),
  "content_hash": "abc123def456789",
  "content_type": "financial_news",
  
  // 原始内容
  "raw_html": "<html>...</html>",
  "raw_json": {...},
  
  // 清洗后内容
  "title": "央行宣布降准0.5个百分点",
  "content": "中国人民银行今日宣布...",
  "author": "金十数据",
  "publish_time": ISODate("2025-01-26T09:30:00Z"),
  
  // 提取的元数据
  "metadata": {
    "word_count": 256,
    "language": "zh",
    "urgency_indicators": ["央行", "降准", "货币政策"]
  },
  
  // 数据质量评分
  "quality_metrics": {
    "content_completeness": 0.95,
    "structure_validity": 0.90,
    "timeliness_score": 0.98
  }
}
```

#### 2.1.4 PostgreSQL 元数据记录
```sql
-- 插入原始数据记录
INSERT INTO raw_data_records (
    task_id, source_id, source_url, url_hash, url_domain,
    content_hash, content_length, title, author, publish_time,
    mongodb_id, content_type, processing_status, quality_score,
    crawl_time, created_at
) VALUES (
    1001, 1, 'https://jin10.com/flash/123456', 'hash_abc123', 'jin10.com',
    'abc123def456789', 256, '央行宣布降准0.5个百分点', '金十数据', '2025-01-26 09:30:00',
    '65b3c4a5f1234567890abcde', 'flash_news', 'pending', 0.95,
    NOW(), NOW()
);
```

### 2.2 事件驱动采集

#### 2.2.1 财经事件监控
```sql
-- 事件驱动采集规则
INSERT INTO event_driven_crawl_rules (
    source_id, rule_name, trigger_type, trigger_config,
    advance_minutes, delay_minutes, is_active
) VALUES (
    1, '央行政策发布监控', 'financial_event', 
    '{"keywords": ["央行", "货币政策", "利率", "准备金"], "importance_threshold": 8}',
    30, 10, TRUE
);
```

#### 2.2.2 Redis 事件缓存
```redis
# 财经事件缓存
HSET financial_event:20250126_001 title "央行降准政策发布"
HSET financial_event:20250126_001 event_time "2025-01-26T09:30:00Z"
HSET financial_event:20250126_001 importance 9
HSET financial_event:20250126_001 keywords "央行,降准,货币政策"

# 事件触发的任务队列
LPUSH event_triggered_tasks '{"event_id": "20250126_001", "source_ids": [1,2,3], "priority": 9}'
```

## 3. 数据处理流程

### 3.1 数据处理管道

#### 3.1.1 处理流程概览
```mermaid
graph LR
    A[原始数据] --> B[解析阶段]
    B --> C[清洗阶段]
    C --> D[转换阶段]
    D --> E[验证阶段]
    E --> F[增强阶段]
    F --> G[业务表存储]
    
    subgraph "AI处理"
        H[摘要生成]
        I[标签提取]
        J[分类识别]
        K[情感分析]
        L[实体提取]
    end
    
    F --> H
    F --> I
    F --> J
    F --> K
    F --> L
    
    H --> G
    I --> G
    J --> G
    K --> G
    L --> G
```

#### 3.1.2 处理状态跟踪
```sql
-- 创建处理状态记录
INSERT INTO data_processing_status (
    raw_data_id, target_business_type, processing_stage,
    progress_percentage, started_at
) VALUES (
    12345, 'flash_news', 'parsing', 0, NOW()
);

-- 更新处理进度
UPDATE data_processing_status 
SET processing_stage = 'classifying',
    progress_percentage = 25,
    current_step = '正在进行AI分类识别',
    stage_details = '{"parsed_fields": ["title", "content", "publish_time"], "validation_passed": true}'::jsonb
WHERE raw_data_id = 12345;
```

#### 3.1.3 Redis 处理队列
```redis
# 数据处理任务队列
LPUSH data_processing:pending '{"raw_data_id": 12345, "target_type": "flash_news", "priority": 8}'
LPUSH data_processing:pending '{"raw_data_id": 12346, "target_type": "news_article", "priority": 5}'

# 处理进度缓存
HSET processing:12345 stage "classifying"
HSET processing:12345 progress "25"
HSET processing:12345 worker_id "processor_worker_02"

# AI任务队列
LPUSH ai_tasks:summary '{"content_id": 12345, "content": "央行宣布降准...", "max_length": 100}'
LPUSH ai_tasks:tagging '{"content_id": 12345, "title": "央行宣布降准", "content": "..."}'
```

### 3.2 AI处理服务

#### 3.2.1 AI任务分发
```python
# AI任务处理示例
async def process_ai_tasks():
    # 从Redis获取AI任务
    summary_task = redis.lpop('ai_tasks:summary')
    tagging_task = redis.lpop('ai_tasks:tagging')
    
    if summary_task:
        result = await ai_client.generate_summary(summary_task)
        # 将结果存储到Redis缓存
        redis.hset(f"ai_result:summary:{task['content_id']}", 
                  "result", result["summary"])
    
    if tagging_task:
        tags = await ai_client.extract_tags(tagging_task)
        redis.hset(f"ai_result:tags:{task['content_id']}", 
                  "tags", json.dumps(tags))
```

#### 3.2.2 AI结果缓存
```redis
# AI处理结果缓存
HSET ai_result:summary:12345 result "央行宣布降准0.5个百分点，释放流动性约1.2万亿元"
HSET ai_result:summary:12345 confidence 0.92
HSET ai_result:summary:12345 model_version "gpt-4-turbo"

HSET ai_result:tags:12345 tags '["央行", "降准", "货币政策", "流动性", "金融市场"]'
HSET ai_result:tags:12345 confidence_scores '[0.95, 0.90, 0.88, 0.85, 0.82]'

HSET ai_result:classification:12345 primary_category "monetary_policy"
HSET ai_result:classification:12345 secondary_categories '["central_bank", "financial_regulation"]'
```

### 3.3 业务数据存储

#### 3.3.1 快讯数据处理
```sql
-- 插入快讯数据
INSERT INTO flash_news (
    raw_data_id, title, content, summary, publish_time,
    urgency_level, importance_score, news_category,
    process_time, status
) VALUES (
    12345,
    '央行宣布降准0.5个百分点',
    '中国人民银行今日宣布，决定于2025年2月1日降准0.5个百分点...',
    '央行宣布降准0.5个百分点，释放流动性约1.2万亿元',
    '2025-01-26 09:30:00',
    3, -- 紧急
    0.95,
    'monetary_policy',
    NOW(),
    'published'
);

-- 相关标签现在通过统一标签系统管理
-- 插入标签关联（示例）
INSERT INTO unified_content_tags (content_type, content_id, tag_id, relevance_score, confidence_score, source)
SELECT 'flash_news', 12345, t.id, 0.9, 0.95, 'ai'
FROM tags t
WHERE t.tag_name IN ('央行', '降准', '货币政策', '流动性', '000001.SZ', '600036.SH', '银行', '证券');
```

#### 3.3.2 新闻文章处理
```sql
-- 插入新闻文章数据
INSERT INTO news_articles (
    raw_data_id, title, subtitle, abstract, content,
    author, source_media, publish_time, word_count,
    content_quality_score, primary_category, secondary_categories,
    mentioned_companies, mentioned_people, related_stocks,
    process_time, status
) VALUES (
    12346,
    '央行降准对股市影响分析',
    '专家解读货币政策调整对资本市场的深远影响',
    '央行此次降准将释放大量流动性，预计将对股市产生积极影响...',
    '详细分析内容...',
    '张经济',
    '新浪财经',
    '2025-01-26 10:15:00',
    1580,
    0.88,
    'market_analysis',
    ARRAY['monetary_policy', 'stock_market'],
    ARRAY['中国人民银行', '上海证券交易所'],
    ARRAY['易纲', '周小川'],
    ARRAY['000001.SZ', '600036.SH', '000002.SZ'],
    NOW(),
    'published'
);
```

## 4. 标签系统与关联

### 4.1 标签提取与匹配

#### 4.1.1 AI标签提取流程
```mermaid
sequenceDiagram
    participant DP as 数据处理器
    participant AI as AI服务
    participant Redis as Redis缓存
    participant PG as PostgreSQL
    
    DP->>AI: 请求标签提取
    AI->>AI: 分析内容生成原始标签
    AI->>Redis: 缓存AI标签结果
    AI->>DP: 返回原始标签列表
    DP->>PG: 查询标准标签库匹配
    DP->>PG: 保存AI标签匹配记录
    DP->>PG: 创建内容-标签关联
```

#### 4.1.2 AI标签匹配处理
```sql
-- AI标签匹配记录
INSERT INTO ai_tag_matches (
    ai_tag_text, standard_tag_id, confidence_score,
    match_method, similarity_score, is_verified
) VALUES 
('央行政策', 1001, 0.95, 'exact', 1.0, TRUE),
('货币宽松', 1002, 0.88, 'synonym', 0.92, FALSE),
('银行股利好', NULL, 0.75, 'new', NULL, FALSE); -- 新标签，需要人工审核

-- 统一内容标签关联
INSERT INTO unified_content_tags (
    content_type, content_id, tag_id,
    relevance_score, confidence_score, importance_score,
    source, ai_tag_match_id, position_start, position_end
) VALUES 
('flash_news', 12345, 1001, 0.95, 0.95, 0.90, 'ai', 1, 0, 10),
('flash_news', 12345, 1002, 0.88, 0.88, 0.85, 'ai', 2, 15, 25),
('flash_news', 12345, 1003, 0.92, 0.90, 0.88, 'ai', NULL, 30, 40);
```

#### 4.1.3 Redis 标签缓存
```redis
# 热门标签缓存
ZADD hot_tags 1250 "央行"
ZADD hot_tags 890 "降准"
ZADD hot_tags 756 "货币政策"
ZADD hot_tags 623 "流动性"

# 标签关系缓存
SADD tag_relations:央行 "货币政策" "金融监管" "利率政策"
SADD tag_relations:降准 "货币政策" "流动性" "银行股"

# 内容标签快速查询缓存
SADD content_tags:flash_news:12345 "央行" "降准" "货币政策" "流动性"
SADD content_tags:news_article:12346 "央行" "股市" "分析" "影响"
```

### 4.2 标签权重计算

#### 4.2.1 动态权重更新
```sql
-- 更新标签权重
UPDATE tags 
SET usage_count = usage_count + 1,
    daily_usage_count = daily_usage_count + 1,
    last_used_at = NOW(),
    popularity_weight = CASE 
        WHEN daily_usage_count > 100 THEN 1.0
        WHEN daily_usage_count > 50 THEN 0.8
        WHEN daily_usage_count > 10 THEN 0.6
        ELSE 0.4
    END
WHERE id IN (1001, 1002, 1003);

-- 计算综合权重（通过触发器或定时任务）
UPDATE tags 
SET computed_weight = (base_weight * 0.4 + popularity_weight * 0.3 + quality_weight * 0.2 + temporal_weight * 0.1)
WHERE id IN (1001, 1002, 1003);
```

## 5. 用户画像构建

### 5.1 用户行为收集

#### 5.1.1 行为数据记录
```sql
-- 用户行为记录
INSERT INTO user_behavior_logs (
    user_id, content_type, content_id, action_type,
    duration_seconds, scroll_percentage, channel, device_type,
    from_recommendation, recommendation_algorithm, created_at
) VALUES 
(10001, 'flash_news', 12345, 'view', 45, 0.85, 'app', 'mobile', TRUE, 'collaborative_filtering', NOW()),
(10001, 'flash_news', 12345, 'share', NULL, NULL, 'app', 'mobile', TRUE, 'collaborative_filtering', NOW()),
(10002, 'news_article', 12346, 'view', 120, 0.95, 'web', 'desktop', FALSE, NULL, NOW());
```

#### 5.1.2 Redis 行为实时统计
```redis
# 用户实时行为统计
HINCRBY user_stats:10001 daily_views 1
HINCRBY user_stats:10001 daily_shares 1
HINCRBY user_stats:10001 total_view_time 45

# 内容实时统计
HINCRBY content_stats:flash_news:12345 view_count 1
HINCRBY content_stats:flash_news:12345 share_count 1
HINCRBY content_stats:flash_news:12345 total_view_time 45

# 用户最近浏览记录
LPUSH user_recent_views:10001 '{"content_type": "flash_news", "content_id": 12345, "timestamp": "2025-01-26T10:30:00Z"}'
LTRIM user_recent_views:10001 0 99  # 保留最近100条
```

### 5.2 兴趣标签计算

#### 5.2.1 用户兴趣权重更新
```sql
-- 更新用户兴趣标签
INSERT INTO user_interest_tags (
    user_id, tag_id, explicit_interest, implicit_interest,
    last_reinforced_at, reinforcement_count, click_count, view_time_seconds
) VALUES (
    10001, 1001, 0.0, 0.8, NOW(), 1, 1, 45
) ON CONFLICT (user_id, tag_id) 
DO UPDATE SET 
    implicit_interest = LEAST(1.0, user_interest_tags.implicit_interest + 0.1),
    last_reinforced_at = NOW(),
    reinforcement_count = user_interest_tags.reinforcement_count + 1,
    click_count = user_interest_tags.click_count + 1,
    view_time_seconds = user_interest_tags.view_time_seconds + 45;
```

#### 5.2.2 Redis 用户兴趣缓存
```redis
# 用户兴趣标签权重缓存
ZADD user_interests:10001 0.85 "央行"
ZADD user_interests:10001 0.78 "货币政策"
ZADD user_interests:10001 0.72 "银行股"
ZADD user_interests:10001 0.68 "金融监管"

# 用户分类偏好缓存
HSET user_preferences:10001 monetary_policy 0.90
HSET user_preferences:10001 stock_market 0.75
HSET user_preferences:10001 financial_regulation 0.65
```

### 5.3 用户画像快照

#### 5.3.1 画像数据生成
```sql
-- 生成用户画像快照
INSERT INTO user_profile_snapshots (
    user_id, snapshot_date, top_interests, interest_categories,
    behavioral_patterns, total_interactions, active_days_count,
    preferred_content_types
) VALUES (
    10001,
    CURRENT_DATE,
    '{"央行": 0.85, "货币政策": 0.78, "银行股": 0.72, "金融监管": 0.68}'::jsonb,
    '{"monetary_policy": 0.90, "stock_market": 0.75, "financial_regulation": 0.65}'::jsonb,
    '{"peak_hours": ["09:00-10:00", "21:00-22:00"], "avg_session_duration": 180, "preferred_content_length": "medium"}'::jsonb,
    156,
    28,
    ARRAY['flash_news', 'news_article', 'research_report']
);
```

## 6. 推荐系统

### 6.1 推荐算法

#### 6.1.1 统一推荐评分计算
```sql
-- 推荐评分计算函数调用
SELECT calculate_unified_recommendation_score('flash_news', 12345, 10001) as recommendation_result;

-- 返回结果示例
{
  "final_score": 8.75,
  "component_scores": {
    "tag_score": 7.8,
    "classification_score": 6.5,
    "quality_score": 0.95,
    "freshness_score": 1.0
  },
  "content_type": "flash_news",
  "content_time": "2025-01-26T09:30:00Z"
}
```

#### 6.1.2 Redis 推荐结果缓存
```redis
# 用户推荐结果缓存
ZADD user_recommendations:10001 8.75 "flash_news:12345"
ZADD user_recommendations:10001 7.92 "news_article:12346"
ZADD user_recommendations:10001 7.58 "research_report:12347"
EXPIRE user_recommendations:10001 3600  # 1小时过期

# 热门内容缓存
ZADD trending_content:flash_news 950 "12345"
ZADD trending_content:news_article 875 "12346"
ZADD trending_content:research_report 768 "12347"

# 推荐多样性控制
SADD user_recommended_today:10001 "flash_news:12345"
SADD user_recommended_today:10001 "news_article:12346"
EXPIRE user_recommended_today:10001 86400  # 24小时过期
```

### 6.2 推荐内容生成

#### 6.2.1 个性化推荐查询
```sql
-- 获取用户个性化推荐
WITH user_top_interests AS (
    SELECT tag_id, computed_interest 
    FROM user_interest_tags 
    WHERE user_id = 10001 AND computed_interest > 0.5
    ORDER BY computed_interest DESC 
    LIMIT 10
),
recommended_content AS (
    SELECT 
        ucr.content_type,
        ucr.content_id,
        ucr.title,
        ucr.content_time,
        ucr.quality_score,
        SUM(uit.computed_interest * uct.final_score * t.computed_weight) as recommendation_score
    FROM unified_content_recommendations ucr
    JOIN unified_content_tags uct ON ucr.content_type = uct.content_type 
                                  AND ucr.content_id = uct.content_id
    JOIN user_top_interests uit ON uct.tag_id = uit.tag_id
    JOIN tags t ON uct.tag_id = t.id
    WHERE ucr.content_time >= NOW() - INTERVAL '24 hours'
      AND ucr.status = 'published'
    GROUP BY ucr.content_type, ucr.content_id, ucr.title, ucr.content_time, ucr.quality_score
    HAVING SUM(uit.computed_interest * uct.final_score * t.computed_weight) > 5.0
)
SELECT * FROM recommended_content 
ORDER BY recommendation_score DESC 
LIMIT 20;
```

## 7. 推送系统

### 7.1 推送策略

#### 7.1.1 推送决策流程
```mermaid
graph TD
    A[内容发布] --> B{是否突发新闻}
    B -->|是| C[立即推送]
    B -->|否| D{用户兴趣匹配度}
    D -->|高| E{推送频率限制}
    D -->|低| F[不推送]
    E -->|未超限| G[加入推送队列]
    E -->|已超限| H[延迟推送]
    C --> I[记录推送]
    G --> I
    H --> I
    I --> J[用户接收]
    J --> K[行为反馈]
    K --> L[更新用户画像]
```

#### 7.1.2 推送队列管理
```redis
# 实时推送队列（高优先级）
LPUSH push_queue:realtime '{"user_id": 10001, "content_type": "flash_news", "content_id": 12345, "urgency": 3, "score": 8.75}'

# 定时推送队列（普通优先级）
ZADD push_queue:scheduled 1706265000 '{"user_id": 10002, "content_type": "news_article", "content_id": 12346, "scheduled_time": "2025-01-26T11:00:00Z"}'

# 用户推送频率控制
HINCRBY user_push_limits:10001 daily_count 1
HSET user_push_limits:10001 last_push_time "2025-01-26T10:30:00Z"
EXPIRE user_push_limits:10001 86400

# 推送去重控制
SADD user_pushed_content:10001 "flash_news:12345"
EXPIRE user_pushed_content:10001 86400
```

### 7.2 推送执行

#### 7.2.1 推送记录
```sql
-- 记录推送
INSERT INTO push_records (
    user_id, content_type, content_id, push_channel, push_type,
    recommendation_score, algorithm_used, push_status, sent_at
) VALUES (
    10001, 'flash_news', 12345, 'app', 'real_time',
    8.75, 'collaborative_filtering', 'sent', NOW()
);
```

#### 7.2.2 推送效果跟踪
```redis
# 推送效果实时统计
HINCRBY push_stats:daily:20250126 sent_count 1
HINCRBY push_stats:daily:20250126 delivered_count 1

# 用户推送效果
HINCRBY user_push_stats:10001 total_sent 1
HINCRBY user_push_stats:10001 total_opened 0  # 待用户打开后更新

# 内容推送效果
HINCRBY content_push_stats:flash_news:12345 sent_count 1
HINCRBY content_push_stats:flash_news:12345 click_count 0  # 待用户点击后更新
```

### 7.3 推送反馈处理

#### 7.3.1 用户反馈收集
```sql
-- 更新推送记录（用户打开）
UPDATE push_records 
SET push_status = 'opened', 
    opened_at = NOW() 
WHERE user_id = 10001 AND content_type = 'flash_news' AND content_id = 12345;

-- 记录用户反馈
UPDATE push_records 
SET user_feedback = 'like',
    feedback_at = NOW(),
    feedback_score = 5
WHERE user_id = 10001 AND content_type = 'flash_news' AND content_id = 12345;
```

#### 7.3.2 反馈数据更新用户画像
```sql
-- 根据正面反馈增强用户兴趣
UPDATE user_interest_tags 
SET implicit_interest = LEAST(1.0, implicit_interest + 0.05),
    positive_feedback_count = positive_feedback_count + 1,
    last_reinforced_at = NOW()
WHERE user_id = 10001 AND tag_id IN (
    SELECT tag_id FROM unified_content_tags 
    WHERE content_type = 'flash_news' AND content_id = 12345
);
```

## 8. 系统监控与优化

### 8.1 性能监控

#### 8.1.1 Redis 监控指标
```redis
# 系统性能指标
HSET system_metrics:20250126 crawl_tasks_completed 1250
HSET system_metrics:20250126 processing_tasks_completed 1180
HSET system_metrics:20250126 recommendations_generated 25600
HSET system_metrics:20250126 pushes_sent 8750

# 数据库连接池监控
HSET db_pool_stats:postgresql active_connections 15
HSET db_pool_stats:postgresql max_connections 50
HSET db_pool_stats:mongodb active_connections 8
HSET db_pool_stats:mongodb max_connections 20
```

#### 8.1.2 数据质量监控
```sql
-- 数据质量统计
SELECT 
    processing_stage,
    COUNT(*) as total_count,
    COUNT(CASE WHEN error_count = 0 THEN 1 END) as success_count,
    AVG(data_quality_score) as avg_quality_score,
    AVG(processing_duration_seconds) as avg_processing_time
FROM data_processing_status 
WHERE created_at >= CURRENT_DATE
GROUP BY processing_stage;
```

### 8.2 故障恢复

#### 8.2.1 失败任务重试
```python
# 自动重试失败的处理任务
async def retry_failed_tasks():
    # 查找失败的处理任务
    failed_tasks = db.query(DataProcessingStatus).filter(
        DataProcessingStatus.processing_stage == 'failed',
        DataProcessingStatus.retry_count < 3,
        DataProcessingStatus.updated_at < datetime.now() - timedelta(minutes=30)
    ).all()
    
    for task in failed_tasks:
        # 重新加入处理队列
        redis.lpush('data_processing:retry', json.dumps({
            'raw_data_id': task.raw_data_id,
            'target_type': task.target_business_type,
            'retry_count': task.retry_count + 1
        }))
        
        # 更新重试计数
        task.retry_count += 1
        task.updated_at = datetime.now()
    
    db.commit()
```

## 9. 总结

### 9.1 数据流程优势

1. **高可靠性**：MongoDB保证原始数据不丢失，PostgreSQL确保结构化数据一致性
2. **高性能**：Redis缓存热点数据，减少数据库压力
3. **可扩展性**：微服务架构，各组件可独立扩展
4. **实时性**：事件驱动采集，实时推荐推送
5. **智能化**：AI驱动的标签提取和个性化推荐

### 9.2 关键技术点

- **数据存储分层**：原始数据(MongoDB) + 结构化数据(PostgreSQL) + 缓存(Redis)
- **处理流程可恢复**：详细的状态跟踪和断点续传机制
- **标签系统统一**：跨业务类型的统一标签关联
- **用户画像动态**：基于行为的实时兴趣计算
- **推荐系统精准**：多因子评分的个性化推荐
- **推送策略智能**：基于用户画像的智能推送决策

### 9.3 运维保障

- **监控完善**：全链路性能和质量监控
- **故障自愈**：自动重试和故障恢复机制
- **数据备份**：多层次数据备份和恢复策略
- **扩容便捷**：水平扩展和负载均衡支持

这套完整的数据流程设计确保了从数据采集到用户推送的全链路高效、可靠、智能运行。 