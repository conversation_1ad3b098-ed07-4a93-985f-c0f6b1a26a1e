# 数据处理服务详细流程文档

## 概述

数据处理服务（data_processing_service）是FinSight后端系统的核心组件，负责将原始数据转换为结构化的业务数据。该服务采用多阶段处理流程，集成AI分析、数据清洗、质量控制等功能，确保数据的准确性和完整性。

## 系统架构

### 核心组件

```
数据处理服务架构
├── DataProcessingEngine (引擎)          # 核心处理引擎
├── DataProcessingTaskManager (任务管理)  # 异步任务管理
├── DataProcessingScheduler (调度器)     # 定时任务调度
├── AIContentAnalyzer (AI分析)          # AI内容分析
├── DataCleaner (数据清洗)              # 数据清洗器
├── ContentFilter (内容过滤)            # 垃圾内容过滤
├── PerformanceMonitor (性能监控)       # 性能监控
└── DataProcessingPipelineService (管道服务) # 处理管道管理
```

### 数据流向

```
原始数据(MongoDB) → 数据处理引擎 → 业务数据表(PostgreSQL)
                      ↓
                  处理状态跟踪
                      ↓
                  AI分析增强
                      ↓
                  质量控制
```
```mermaid
graph TD
    A[原始数据记录<br/>RawDataRecord] --> B{处理状态检查}
    B -->|pending| C[数据处理引擎<br/>DataProcessingEngine]
    B -->|processing| D[跳过处理]
    B -->|completed| D
    
    C --> E[管道匹配<br/>find_processing_pipeline]
    E --> F[创建处理状态<br/>DataProcessingStatus]
    F --> G[获取MongoDB原始内容]
    
    G --> H[阶段1: 数据提取<br/>EXTRACTING 10%]
    H --> I[阶段2: 数据清洗<br/>CLEANING 30%]
    I --> J[阶段3: 垃圾检测<br/>SPAM_DETECTION 40%]
    
    J -->|是垃圾内容| K[删除垃圾数据]
    J -->|正常内容| L[阶段4: 数据转换<br/>TRANSFORMING 50%]
    
    K --> M[更新状态为已删除<br/>DELETED]
    
    L --> N[阶段5: 数据验证<br/>VALIDATING 60%]
    N --> O[阶段6: AI分析<br/>AI_ANALYZING 80%]
    O --> P[阶段7: 数据保存<br/>SAVING 90%]
    
    P --> Q[保存到业务表]
    Q --> R[建立标签关联]
    R --> S[建立分类关联]
    S --> T[更新处理状态<br/>COMPLETED 100%]
    
    T --> U[处理完成]
    M --> U
    
    style A fill:#e1f5fe
    style C fill:#f3e5f5
    style H fill:#fff3e0
    style I fill:#fff3e0
    style J fill:#fff3e0
    style L fill:#fff3e0
    style N fill:#fff3e0
    style O fill:#fff3e0
    style P fill:#fff3e0
    style K fill:#ffebee
    style U fill:#e8f5e8

```
```mermaid
graph TB
    subgraph "数据输入层"
        A[原始数据记录<br/>PostgreSQL]
        B[原始内容<br/>MongoDB]
    end
    
    subgraph "调度管理层"
        C[数据处理调度器<br/>DataProcessingScheduler]
        D[任务管理器<br/>DataProcessingTaskManager]
        E[Celery任务队列]
    end
    
    subgraph "核心处理层"
        F[数据处理引擎<br/>DataProcessingEngine]
        G[管道服务<br/>DataProcessingPipelineService]
    end
    
    subgraph "数据处理组件"
        H[数据清洗器<br/>DataCleaner]
        I[内容过滤器<br/>ContentFilter]
        J[AI内容分析器<br/>AIContentAnalyzer]
    end
    
    subgraph "AI服务层"
        K[DeepSeek API]
        L[OpenAI API]
        M[AI客户端工厂<br/>AIClientFactory]
    end
    
    subgraph "数据输出层"
        N[快讯表<br/>FlashNews]
        O[新闻文章表<br/>NewsArticle]
        P[研究报告表<br/>ResearchReport]
        Q[经济指标表<br/>EconomicIndicatorData]
    end
    
    subgraph "关联数据层"
        R[统一标签关联<br/>UnifiedContentTags]
        S[统一分类关联<br/>UnifiedContentClassifications]
        T[AI标签匹配<br/>AITagMatches]
    end
    
    subgraph "监控管理层"
        U[性能监控器<br/>PerformanceMonitor]
        V[处理状态管理<br/>DataProcessingStatus]
        W[日志系统<br/>Logging]
    end
    
    A --> C
    B --> F
    C --> D
    D --> E
    E --> F
    F --> G
    G --> H
    G --> I
    G --> J
    
    J --> M
    M --> K
    M --> L
    
    F --> N
    F --> O
    F --> P
    F --> Q
    
    F --> R
    F --> S
    F --> T
    
    F --> U
    F --> V
    F --> W
    
    style A fill:#e3f2fd
    style B fill:#e3f2fd
    style C fill:#f3e5f5
    style D fill:#f3e5f5
    style E fill:#f3e5f5
    style F fill:#fff3e0
    style G fill:#fff3e0
    style H fill:#e8f5e8
    style I fill:#e8f5e8
    style J fill:#e8f5e8
    style K fill:#fce4ec
    style L fill:#fce4ec
    style M fill:#fce4ec
    style N fill:#f1f8e9
    style O fill:#f1f8e9
    style P fill:#f1f8e9
    style Q fill:#f1f8e9
    style R fill:#fff8e1
    style S fill:#fff8e1
    style T fill:#fff8e1
    style U fill:#f3e5f5
    style V fill:#f3e5f5
    style W fill:#f3e5f5
```
```mermaid

```


```

## 详细处理流程

### 1. 数据输入阶段

#### 1.1 数据源识别
- **输入**: RawDataRecord (原始数据记录)
- **位置**: PostgreSQL `raw_data_records` 表
- **状态**: `processing_status = 'pending'`

#### 1.2 处理管道匹配
**文件**: `service.py` - `find_processing_pipeline()`

匹配优先级：
1. **精确数据源匹配** - 根据 `source_id` 精确匹配
2. **URL模式匹配** - 使用正则表达式匹配URL
3. **域名匹配** - 根据域名模式匹配
4. **业务类型匹配** - 根据业务数据类型匹配
5. **通用管道** - 使用默认通用处理管道

```python
# 匹配逻辑示例
def find_processing_pipeline(self, db: Session, raw_data_record: RawDataRecord):
    # 1. 精确数据源匹配
    pipeline = db.query(DataProcessingPipeline).filter(
        DataProcessingPipeline.source_id == raw_data_record.source_id,
        DataProcessingPipeline.is_active == True
    ).order_by(desc(DataProcessingPipeline.priority)).first()
    
    # 2. URL模式匹配
    if not pipeline and raw_data_record.source_url:
        # URL正则匹配逻辑
        
    # 3. 域名匹配
    # 4. 业务类型匹配
    # 5. 通用管道
```

### 2. 数据处理引擎执行

#### 2.1 处理状态初始化
**文件**: `engine.py` - `_create_processing_status()`

创建处理状态记录：
```python
processing_status = DataProcessingStatus(
    record_id=record.id,
    processing_stage=ProcessingStage.PENDING,
    progress_percentage=0,
    current_step="初始化处理",
    start_time=datetime.now(timezone.utc)
)
```

#### 2.2 MongoDB原始内容获取
**文件**: `engine.py` - `_get_raw_content()`

从MongoDB获取完整的原始内容数据：
- 根据 `record.mongodb_id` 查询
- 验证数据完整性
- 处理JSON格式数据

### 3. 七阶段数据处理流程

#### 阶段1: 数据提取 (EXTRACTING)
**文件**: `data_cleaners.py` - `apply_extraction_rules()`

**进度**: 10%
**功能**:
- 应用字段映射规则
- 提取结构化数据
- 时间字段解析
- 元数据提取

```python
# 配置示例
extraction_config = {
    "content_cleaning": {
        "remove_html": True,
        "normalize_whitespace": True,
        "max_length": 10000
    },
    "time_processing": {
        "publish_time": {
            "required": True,
            "formats": ["%Y-%m-%d %H:%M:%S", "%Y/%m/%d %H:%M:%S"]
        }
    },
    "metadata_extraction": {
        "calculate_word_count": True,
        "extract_domain": True
    }
}
```

#### 阶段2: 数据清洗 (CLEANING)
**文件**: `data_cleaners.py` - `clean_content()`

**进度**: 30%
**功能**:
- HTML标签去除
- 空格规范化
- 广告内容清理
- 标题清洗

```python
# 标题清洗规则
title_clean_patterns = [
    (r'^【.*?】\s*', ''),      # 去除开头的【】标记
    (r'^\[.*?\]\s*', ''),      # 去除开头的[]标记
    (r'^快讯[:：]\s*', ''),     # 去除"快讯:"前缀
    (r'^财经[:：]\s*', ''),     # 去除"财经:"前缀
    (r'\s+', ' '),            # 多个空格合并为一个
]
```

#### 阶段3: 垃圾内容检测 (SPAM_DETECTION)
**文件**: `data_cleaners.py` - `ContentFilter.is_spam()`

**进度**: 40%
**检测规则**:
- 垃圾关键词检测（广告、推广、营销等）
- 低质量内容模式匹配
- 重复内容比例分析
- 无意义内容识别

```python
spam_keywords = [
    "广告", "推广", "营销", "代理", "加盟", "招商",
    "微信", "QQ", "电话", "联系方式", "咨询",
    "免费", "优惠", "折扣", "特价", "促销"
]
```

**垃圾内容处理**:
- 删除MongoDB中的原始数据
- 删除PostgreSQL中的记录
- 更新处理状态为已完成
- 记录删除原因

#### 阶段4: 数据转换 (TRANSFORMING)
**文件**: `data_cleaners.py` - `transform_data()`

**进度**: 50%
**功能**:
- 重要性检测和评级
- 分类映射应用
- 数据格式标准化
- 业务规则应用

```python
# 紧急程度检测
urgent_keywords = ["突发", "紧急", "重大", "立即", "马上"]
important_keywords = ["央行", "降准", "加息", "IPO", "重组", "监管"]
```

#### 阶段5: 数据验证 (VALIDATING)
**文件**: `data_cleaners.py` - `validate_data()`

**进度**: 60%
**验证项目**:
- 必填字段检查
- 字段长度验证
- 数据格式验证
- 业务规则验证

```python
validation_config = {
    "required_fields": ["title", "content", "publish_time"],
    "field_lengths": {"title": 500, "content": 50000},
    "format_checks": {"url": r"^https?://.*"}
}
```

#### 阶段6: AI内容分析 (AI_ANALYZING)
**文件**: `ai_services.py` - `AIContentAnalyzer`

**进度**: 80%
**AI分析功能**:

1. **摘要生成**
   - 提取关键信息
   - 生成简洁摘要
   - 保持原文语义

2. **标签提取**
   - 实体识别（公司、人物、地点）
   - 股票代码识别
   - 行业标签提取
   - 主题标签生成

3. **内容分类**
   - 新闻类型分类
   - 重要性评级
   - 情感分析
   - 影响范围评估

4. **标签匹配**
   ```python
   # 标签匹配策略
   def match_tags(self, ai_tags: List[str], existing_tags: List[Tag]):
       # 1. 精确匹配
       # 2. 同义词匹配  
       # 3. 模糊匹配
       # 4. 新标签识别
   ```

#### 阶段7: 数据保存 (SAVING)
**文件**: `engine.py` - `_save_business_data()`

**进度**: 90-100%
**保存流程**:
1. 根据业务类型选择目标表
2. 创建业务数据记录
3. 保存AI分析结果
4. 建立标签关联
5. 建立分类关联
6. 更新原始记录状态

```python
# 业务表映射
business_table_mapping = {
    BusinessDataType.FLASH_NEWS: FlashNews,
    BusinessDataType.NEWS_ARTICLE: NewsArticle,
    BusinessDataType.RESEARCH_REPORT: ResearchReport,
    BusinessDataType.ECONOMIC_DATA: EconomicIndicatorData,
}
```

### 4. 异步任务管理

#### 4.1 任务提交
**文件**: `task_manager.py` - `DataProcessingTaskManager`

**任务类型**:
- 单个记录处理任务
- 批量处理任务
- 重试任务
- 清理任务

```python
# 批量处理任务提交
async def submit_batch_processing_task(self, batch_size: int = 20):
    task = submit_batch_processing_task.apply_async(
        args=[batch_size],
        priority=TaskPriority.HIGH.value
    )
    return task.id
```

#### 4.2 Celery任务执行
**文件**: `tasks.py`

**任务函数**:
- `process_single_record` - 处理单个记录
- `process_batch_records` - 批量处理记录
- `retry_failed_processing` - 重试失败任务
- `cleanup_old_processing_status` - 清理旧状态

### 5. 定时调度系统

#### 5.1 调度器配置
**文件**: `processing_scheduler.py` - `DataProcessingScheduler`

**默认调度任务**:
1. **批量处理** - 每60秒执行
2. **状态清理** - 每小时执行  
3. **失败重试** - 每5分钟执行
4. **健康检查** - 每5分钟执行

```python
# 调度任务配置
await self.add_processing_schedule(
    job_id="batch_processing",
    job_name="批量处理待处理数据",
    schedule_type="interval",
    schedule_config={"seconds": 60},
    func=self._execute_batch_processing
)
```

#### 5.2 调度执行逻辑
- 查询待处理数据
- 提交批量处理任务
- 监控任务执行状态
- 记录调度统计信息

### 6. 性能监控系统

#### 6.1 性能指标收集
**文件**: `performance_monitor.py` - `PerformanceMonitor`

**监控指标**:
- 处理耗时
- 内存使用率
- CPU使用率
- 成功率统计
- 错误统计

```python
# 性能计时器使用
with create_timer("engine", "process_record", record_id=record.id):
    result = await self.process_record(record)
```

#### 6.2 系统资源监控
- CPU使用率监控
- 内存使用率监控
- 磁盘使用率监控
- 性能告警机制

### 7. 错误处理和恢复

#### 7.1 错误分类
- **临时错误** - 网络超时、资源不足等
- **数据错误** - 格式错误、缺失字段等
- **系统错误** - 数据库连接失败等
- **业务错误** - 规则验证失败等

#### 7.2 重试机制
```python
# 重试配置
retry_config = {
    "max_retries": 3,
    "retry_delay": [60, 300, 900],  # 1分钟、5分钟、15分钟
    "retry_conditions": ["network_error", "timeout", "resource_unavailable"]
}
```

#### 7.3 失败恢复
- 保存中间处理状态
- 支持断点续传
- 失败任务自动重试
- 人工干预接口

## 配置管理

### 处理管道配置
**文件**: `pipeline_configs.py`

每个处理管道包含：
- 字段映射规则
- 数据提取配置
- 数据转换配置
- 数据验证配置
- 数据增强配置

```python
# 快讯处理管道配置示例
"flash_news_basic": {
    "pipeline_code": "flash_news_basic",
    "pipeline_name": "快讯基础处理",
    "target_business_type": BusinessDataType.FLASH_NEWS,
    "field_mapping": {
        "title": "title",
        "content": "content", 
        "publish_time": "publish_time"
    },
    "data_extraction_config": {
        "content_cleaning": {
            "remove_html": True,
            "normalize_whitespace": True,
            "max_length": 10000
        }
    }
}
```

## 数据模型

### 业务数据表
1. **FlashNews** - 财经快讯
2. **NewsArticle** - 新闻文章
3. **ResearchReport** - 研究报告
4. **EconomicIndicatorData** - 经济指标数据

### 关联表
1. **UnifiedContentTags** - 统一标签关联
2. **UnifiedContentClassifications** - 统一分类关联
3. **AITagMatches** - AI标签匹配

### 状态管理表
1. **DataProcessingStatus** - 处理状态跟踪
2. **DataProcessingPipeline** - 处理管道配置

## 部署和运维

### 启动服务
```bash
# 启动数据处理管理器
python -m src.services.data_processing_service.data_processing_manager

# 启动Celery工作进程
celery worker -A src.services.data_processing_service.tasks

# 启动调度器
python -m src.services.data_processing_service.processing_scheduler
```

### 监控命令
```bash
# 查看处理状态
python scripts/check_processing_status.py

# 查看性能指标
python scripts/show_performance_metrics.py

# 重启失败任务
python scripts/retry_failed_tasks.py
```

## API接口

### 管理接口
**文件**: `admin_router.py`

#### 处理管道管理
```python
# 创建处理管道
POST /admin/data-processing/pipelines
{
    "pipeline_code": "custom_news_pipeline",
    "pipeline_name": "自定义新闻处理管道",
    "target_business_type": "NEWS_ARTICLE",
    "field_mapping": {...},
    "data_extraction_config": {...}
}

# 查询处理管道
GET /admin/data-processing/pipelines?page=1&size=20

# 更新处理管道
PUT /admin/data-processing/pipelines/{pipeline_id}

# 删除处理管道
DELETE /admin/data-processing/pipelines/{pipeline_id}
```

#### 处理状态查询
```python
# 查询处理状态
GET /admin/data-processing/status?record_id=123

# 批量查询处理状态
GET /admin/data-processing/status/batch?stage=failed&limit=50

# 重启失败任务
POST /admin/data-processing/retry/{record_id}
```

### 服务管理接口
```python
# 启动数据处理服务
POST /admin/data-processing/service/start

# 停止数据处理服务
POST /admin/data-processing/service/stop

# 查询服务状态
GET /admin/data-processing/service/status

# 查询性能指标
GET /admin/data-processing/metrics
```

## 日志和监控

### 日志配置
**文件**: `src/core/logging_config.py`

**日志级别**:
- DEBUG: 详细调试信息
- INFO: 一般信息记录
- WARNING: 警告信息
- ERROR: 错误信息
- CRITICAL: 严重错误

**日志格式**:
```
{timestamp} | {level} | {module} | {component} | {record_id} | {task_id} | {message}
```

**示例日志**:
```
2025-07-18 21:25:43 | INFO | data_processing_engine | engine | 12345 | task-001 | 开始处理记录
2025-07-18 21:25:44 | INFO | data_processing_engine | ai_analyzer | 12345 | task-001 | AI分析完成，提取标签: 5个
2025-07-18 21:25:45 | INFO | data_processing_engine | engine | 12345 | task-001 | 处理完成，保存到FlashNews表
```

### 性能监控指标

#### 处理性能指标
- **处理耗时**: 每个阶段的处理时间
- **吞吐量**: 每分钟处理的记录数
- **成功率**: 处理成功的记录比例
- **错误率**: 处理失败的记录比例

#### 系统资源指标
- **CPU使用率**: 实时CPU使用情况
- **内存使用率**: 实时内存使用情况
- **磁盘使用率**: 磁盘空间使用情况
- **网络IO**: 网络传输情况

#### AI服务指标
- **AI调用次数**: AI服务调用统计
- **AI响应时间**: AI服务响应时间
- **AI成功率**: AI服务调用成功率
- **AI成本**: AI服务使用成本

### 告警机制

#### 性能告警
```python
# 处理耗时告警
if duration_ms > 30000:  # 30秒
    logger.warning(f"操作耗时过长: {duration_ms}ms")

# 内存使用告警
if memory_usage > 80:  # 80%
    logger.warning(f"内存使用率过高: {memory_usage}%")

# 错误率告警
if error_rate > 0.1:  # 10%
    logger.error(f"错误率过高: {error_rate*100}%")
```

#### 业务告警
- 垃圾内容检测率异常
- AI服务调用失败
- 数据库连接异常
- 处理队列积压

## 故障排查指南

### 常见问题

#### 1. 处理任务卡住
**症状**: 处理状态长时间停留在某个阶段
**排查步骤**:
```bash
# 1. 查看处理状态
SELECT * FROM data_processing_status WHERE processing_stage = 'processing' AND start_time < NOW() - INTERVAL '1 hour';

# 2. 查看Celery任务状态
celery inspect active

# 3. 查看系统资源
top
free -h
df -h

# 4. 重启卡住的任务
python scripts/restart_stuck_tasks.py
```

#### 2. AI服务调用失败
**症状**: AI分析阶段频繁失败
**排查步骤**:
```bash
# 1. 检查AI服务配置
python scripts/test_ai_service.py

# 2. 查看AI服务日志
grep "ai_services" logs/data_processing.log

# 3. 检查API密钥和配额
curl -H "Authorization: Bearer $API_KEY" https://api.deepseek.com/v1/models

# 4. 切换备用AI服务
export AI_PROVIDER=openai
```

#### 3. 数据库连接问题
**症状**: 数据保存阶段失败
**排查步骤**:
```bash
# 1. 检查数据库连接
python scripts/test_db_connection.py

# 2. 查看数据库日志
tail -f /var/log/postgresql/postgresql.log

# 3. 检查连接池状态
SELECT * FROM pg_stat_activity WHERE application_name = 'finsight';

# 4. 重启数据库连接池
python scripts/restart_db_pool.py
```

#### 4. MongoDB连接问题
**症状**: 无法获取原始内容
**排查步骤**:
```bash
# 1. 检查MongoDB连接
python scripts/test_mongodb_connection.py

# 2. 查看MongoDB日志
tail -f /var/log/mongodb/mongod.log

# 3. 检查数据完整性
python scripts/verify_mongodb_data.py

# 4. 修复损坏的数据
python scripts/repair_mongodb_data.py
```

### 性能优化建议

#### 1. 数据库优化
```sql
-- 创建必要的索引
CREATE INDEX idx_raw_data_processing_status ON raw_data_records(processing_status);
CREATE INDEX idx_processing_status_stage ON data_processing_status(processing_stage);
CREATE INDEX idx_processing_status_record_id ON data_processing_status(record_id);

-- 定期清理旧数据
DELETE FROM data_processing_status WHERE created_at < NOW() - INTERVAL '30 days';
```

#### 2. 缓存优化
```python
# Redis缓存配置
REDIS_CONFIG = {
    "host": "localhost",
    "port": 6379,
    "db": 0,
    "max_connections": 100,
    "socket_timeout": 30
}

# 缓存AI分析结果
@cache.memoize(timeout=3600)  # 1小时缓存
def analyze_content(content_hash):
    return ai_analyzer.analyze(content)
```

#### 3. 并发优化
```python
# Celery并发配置
CELERY_CONFIG = {
    "worker_concurrency": 4,  # 工作进程数
    "worker_prefetch_multiplier": 1,  # 预取任务数
    "task_acks_late": True,  # 延迟确认
    "worker_max_tasks_per_child": 1000  # 子进程最大任务数
}
```

#### 4. 内存优化
```python
# 批量处理优化
BATCH_CONFIG = {
    "batch_size": 20,  # 批量大小
    "memory_limit": "1GB",  # 内存限制
    "gc_threshold": 100  # 垃圾回收阈值
}
```

## 扩展开发指南

### 添加新的业务数据类型

#### 1. 定义数据模型
```python
# 在 models.py 中添加新表
class CustomBusinessData(Base):
    __tablename__ = "custom_business_data"

    id = Column(BigInteger, primary_key=True)
    title = Column(String(500), nullable=False)
    content = Column(Text)
    # ... 其他字段
```

#### 2. 更新业务类型枚举
```python
# 在 schemas.py 中添加新类型
class BusinessDataType(str, Enum):
    FLASH_NEWS = "FLASH_NEWS"
    NEWS_ARTICLE = "NEWS_ARTICLE"
    RESEARCH_REPORT = "RESEARCH_REPORT"
    ECONOMIC_DATA = "ECONOMIC_DATA"
    CUSTOM_DATA = "CUSTOM_DATA"  # 新增
```

#### 3. 更新引擎映射
```python
# 在 engine.py 中添加映射
self.business_table_mapping = {
    BusinessDataType.FLASH_NEWS: FlashNews,
    BusinessDataType.NEWS_ARTICLE: NewsArticle,
    BusinessDataType.RESEARCH_REPORT: ResearchReport,
    BusinessDataType.ECONOMIC_DATA: EconomicIndicatorData,
    BusinessDataType.CUSTOM_DATA: CustomBusinessData,  # 新增
}
```

#### 4. 创建处理管道配置
```python
# 在 pipeline_configs.py 中添加配置
"custom_data_pipeline": {
    "pipeline_code": "custom_data_pipeline",
    "pipeline_name": "自定义数据处理",
    "target_business_type": BusinessDataType.CUSTOM_DATA,
    "field_mapping": {
        "title": "title",
        "content": "content"
    },
    # ... 其他配置
}
```

### 添加新的AI分析功能

#### 1. 扩展AI分析器
```python
# 在 ai_services.py 中添加新方法
class AIContentAnalyzer:
    async def custom_analysis(self, content: str) -> Dict[str, Any]:
        """自定义AI分析功能"""
        # 实现自定义分析逻辑
        pass
```

#### 2. 更新处理流程
```python
# 在 engine.py 中调用新功能
if pipeline.data_enrichment_config.get("custom_analysis", False):
    custom_result = await self.ai_analyzer.custom_analysis(content)
    ai_analysis_result["custom_analysis"] = custom_result
```

### 添加新的数据清洗规则

#### 1. 扩展数据清洗器
```python
# 在 data_cleaners.py 中添加新方法
class DataCleaner:
    def custom_cleaning_rule(self, text: str, config: Dict[str, Any]) -> str:
        """自定义清洗规则"""
        # 实现自定义清洗逻辑
        return cleaned_text
```

#### 2. 更新清洗配置
```python
# 在管道配置中添加新规则
"data_transformation_config": {
    "custom_cleaning": {
        "enabled": True,
        "rules": ["rule1", "rule2"]
    }
}
```

## 总结

数据处理服务通过多阶段流水线处理、AI智能分析、质量控制等机制，实现了从原始数据到高质量业务数据的自动化转换。系统具有以下特点：

### 核心优势
1. **完整的处理流程** - 七阶段处理确保数据质量
2. **智能AI分析** - 自动提取标签、分类和增强内容
3. **强大的错误处理** - 多层次的错误检测和恢复机制
4. **高性能处理** - 异步任务和批量处理提升效率
5. **灵活的配置** - 可配置的处理管道适应不同数据源
6. **全面的监控** - 实时性能监控和告警机制

### 技术特色
- **微服务架构** - 模块化设计，易于维护和扩展
- **异步处理** - Celery任务队列支持高并发处理
- **智能调度** - 自动化的任务调度和资源管理
- **质量保证** - 多层次的数据验证和质量控制
- **可观测性** - 详细的日志记录和性能监控

该系统能够满足大规模财经数据处理的需求，为FinSight平台提供高质量的结构化数据支撑。