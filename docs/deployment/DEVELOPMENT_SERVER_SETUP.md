# 开发服务器设置指南

本文档详细说明如何设置 FinSight 后端项目的开发服务器环境。

## 1. 系统要求

- Ubuntu 20.04 LTS 或更高版本
- 至少 2GB RAM
- 至少 20GB 存储空间
- 内网 IP 地址

## 2. 系统更新

```bash
# 更新系统包
sudo apt update
sudo apt upgrade -y

# 安装基本工具
sudo apt install -y git curl wget vim htop
```

## 3. 安装 Python 环境

```bash
# 安装 Python 3.12
sudo apt install -y software-properties-common
sudo add-apt-repository ppa:deadsnakes/ppa
sudo apt update
sudo apt install -y python3.12 python3.12-venv python3.12-dev

# 安装 pip
curl -sS https://bootstrap.pypa.io/get-pip.py | sudo python3.12
```

## 3.1 安装 Node.js 和 Playwright 依赖

如果项目使用 Playwright 进行网页抓取，需要安装 Node.js 和相关依赖：

```bash
# 安装 Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt install -y nodejs

# 验证安装
node -v
npm -v

# 安装系统依赖（Playwright需要）
sudo apt install -y libwebkit2gtk-4.0-dev \
    build-essential \
    libssl-dev \
    libgtk-3-dev \
    libgbm-dev \
    libnss3-dev \
    libxss-dev \
    libasound2-dev \
    libxshmfence-dev \
    libgles2-mesa-dev \
    libgl1-mesa-dev \
    libatk1.0-dev \
    libatk-bridge2.0-dev \
    xvfb

# 安装 Playwright 依赖
cd /home/<USER>/workspace/app_finsight_backend
source venv/finsight/bin/activate
pip install playwright
playwright install
```

**注意**：确保 Node.js 安装正确，这对于 Playwright 的正常运行至关重要。如果在 systemd 服务中运行时出现"Connection closed while reading from the driver"错误，通常是因为 Node.js 路径或环境变量配置不正确。

## 4. 安装 PostgreSQL

```bash
# 安装 PostgreSQL
sudo apt install -y postgresql postgresql-contrib

# 启动 PostgreSQL 服务
sudo systemctl start postgresql
sudo systemctl enable postgresql

# 创建数据库和用户
sudo -u postgres psql -c "CREATE DATABASE finsight_dev;"
sudo -u postgres psql -c "CREATE USER finsight_dev WITH PASSWORD 'dev_password';"
sudo -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE finsight_dev TO finsight_dev;"

# 配置 PostgreSQL 允许本地连接
sudo nano /etc/postgresql/12/main/pg_hba.conf
```

添加以下配置：
```
# 允许本地连接
local   all             postgres                                peer
local   all             all                                     md5
host    all             all             127.0.0.1/32            md5
host    all             all             ::1/128                 md5
```

```bash
# 重启 PostgreSQL 服务
sudo systemctl restart postgresql
```

## 5. 安装 Redis

```bash
# 安装 Redis
sudo apt install -y redis-server

# 配置 Redis 只允许本地连接
sudo nano /etc/redis/redis.conf
```

修改以下配置：
```
bind 127.0.0.1
protected-mode yes
```

```bash
# 启动 Redis 服务
sudo systemctl start redis-server
sudo systemctl enable redis-server
```

## 6. 创建项目目录

```bash
# 创建项目目录
sudo mkdir -p /home/<USER>/workspace/app_finsight_backend
sudo chown -R ubuntu:ubuntu /home/<USER>/workspace/app_finsight_backend

# 创建备份目录
mkdir -p /home/<USER>/workspace/app_finsight_backend/backups
```

## 7. 配置系统服务

```bash
# 创建服务文件
sudo nano /etc/systemd/system/finsight.service
```

添加以下内容：
```ini
[Unit]
Description=FinSight Backend Development Service
After=network.target postgresql.service redis.service
Requires=postgresql.service redis.service

[Service]
User=ubuntu
Group=ubuntu
WorkingDirectory=/home/<USER>/workspace/app_finsight_backend

# 环境变量设置 - 保持与终端环境一致
Environment="PATH=/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin"
Environment="PYTHONPATH=/home/<USER>/workspace/app_finsight_backend"
Environment="ENVIRONMENT=development"
Environment="ENV_FILE=.env.develop"
Environment="PYTHONUNBUFFERED=1"
Environment="LC_ALL=C.UTF-8"
Environment="LANG=C.UTF-8"
Environment="HOME=/home/<USER>"
Environment="SHELL=/bin/bash"
Environment="TERM=xterm-256color"
Environment="XDG_RUNTIME_DIR=/run/user/1000"

# 数据库连接配置
Environment="PGCONNECT_TIMEOUT=10"
Environment="PGPOOL_CONNECTION_LIMIT=20"
Environment="PGPOOL_IDLE_TIMEOUT=300"
Environment="PGCLIENTENCODING=utf8"
Environment="PGSSLMODE=disable"

# 进程资源限制
LimitNOFILE=65535
LimitNPROC=65535
LimitAS=infinity
LimitFSIZE=infinity
TimeoutStartSec=300
TimeoutStopSec=300
RestartSec=30
Restart=always
StartLimitBurst=5
StartLimitIntervalSec=300

# OOM处理
OOMScoreAdjust=-500

# 启动命令 - 使用与终端相同的激活方式
ExecStartPre=/bin/sleep 5
ExecStart=/bin/bash -c 'source /home/<USER>/workspace/app_finsight_backend/venv/finsight/bin/activate && python -m src.main'

[Install]
WantedBy=multi-user.target
```

启动开发环境服务
```bash
sudo systemctl daemon-reload
sudo systemctl enable finsight.service
sudo systemctl start finsight.service
```

### 7.1 服务配置说明

上述服务配置特别注意以下几点：

1. **环境变量设置**：确保服务运行环境与终端环境尽可能一致，这对于依赖特定环境变量的工具（如Playwright）尤为重要。

2. **启动命令**：使用`/bin/bash -c 'source ... && python ...'`的方式启动程序，确保与终端相同的激活方式，这样虚拟环境中的所有依赖都能正确加载。

3. **资源限制**：设置了合理的进程资源限制，避免服务因资源不足而崩溃。

4. **依赖关系**：通过`Requires`指令确保PostgreSQL和Redis服务在启动前已经就绪。

5. **OOM处理**：通过`OOMScoreAdjust`降低服务被OOM Killer终止的可能性。

如果服务启动后出现"Connection closed while reading from the driver"等错误，通常是因为环境变量或路径配置不正确，请检查以上配置是否正确应用。

## 8. 配置 Nginx（可选）

```bash
# 安装 Nginx
sudo apt install -y nginx

# 创建 Nginx 配置
sudo nano /etc/nginx/sites-available/finsight-dev
```

添加以下内容：
```nginx
server {
    listen 80;
    server_name hellodev.lightrain.com;

    location / {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

```bash
# 启用配置
sudo ln -s /etc/nginx/sites-available/finsight-dev /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

## 9. 配置防火墙

```bash
# 安装 UFW
sudo apt install -y ufw

# 配置防火墙规则
sudo ufw allow ssh
sudo ufw allow http
sudo ufw allow https
sudo ufw enable
```

## 10. 开发工具配置

```bash
# 安装开发工具
sudo apt install -y postgresql-client redis-tools

# 安装 Python 开发工具
pip install ipython ipdb pytest pytest-cov flake8 black
```

## 11. 日志配置

```bash
# 创建日志目录
sudo mkdir -p /var/log/finsight-dev
sudo chown -R ubuntu:ubuntu /var/log/finsight-dev

# 配置日志轮转
sudo nano /etc/logrotate.d/finsight-dev
```

添加以下内容：
```
/var/log/finsight-dev/*.log {
    daily
    missingok
    rotate 7
    compress
    delaycompress
    notifempty
    create 0640 ubuntu ubuntu
    sharedscripts
    postrotate
        systemctl reload finsight.service
    endscript
}
```

## 12. 开发环境配置

```bash
# 创建开发环境配置文件
cd /home/<USER>/workspace/app_finsight_backend
cp env_example.txt .env.develop
```

编辑 `.env.develop` 文件，确保包含以下配置：
```ini
# 本地数据库配置
DATABASE_URL=postgresql://finsight_dev:dev_password@localhost:5432/finsight_dev
REDIS_URL=redis://localhost:6379/1

# 其他配置
JWT_SECRET_KEY=dev_secret_key
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30
ENVIRONMENT=development
DEBUG=true
```

## 13. 安全配置
1.在本地生成新密钥对(mac版本)，并配置：
```bash
# 生成ed25519版本密钥对
ssh-keygen -t ed25519 -C "github_actions@app_finsight_backend" -f ~/.ssh/github_actions_ed25519_dev
# 复制公钥
cat ~/.ssh/github_actions_ed25519_dev.pub
# 将上述公钥粘贴到服务器的.ssh/authorized_keys下

# 复制私钥
cat ~/.ssh/github_actions_ed25519_dev
# 将上述私钥粘贴到GitHub 的DEV_SERVER_SSH_KEY(与ci_cd保持一致)
```
2. 配置 SSH
```bash
sudo nano /etc/ssh/sshd_config
```

修改以下设置：
```
PubkeyAuthentication yes
```

```bash
# 重启 SSH 服务
sudo systemctl restart sshd
```


## 14. 验证安装

```bash
# 检查服务状态
sudo systemctl status finsight.service
sudo systemctl status postgresql
sudo systemctl status redis-server
sudo systemctl status nginx

# 检查日志
sudo journalctl -u finsight.service -f
```

## 15. 开发工作流

1. 克隆代码：
```bash
cd /home/<USER>/workspace/app_finsight_backend
git clone <repository-url> .
```

2. 创建虚拟环境：
```bash
python3 -m venv venv/finsight
source venv/finsight/bin/activate
```

3. 安装依赖：
```bash
pip install -r requirements.txt
```

4. 运行测试：
```bash
pytest
```

5. 启动开发服务器：
```bash
python -m src.main
```

## 16. 故障排查

如果遇到问题，请检查：

1. 服务状态：
```bash
sudo systemctl status finsight.service
```

2. 日志文件：
```bash
sudo journalctl -u finsight.service -f
```

3. 数据库连接：
```bash
sudo -u postgres psql -d finsight_dev
```

4. Redis 连接：
```bash
redis-cli ping
```

5. 网络连接：
```bash
curl localhost:8000/health
```

### 16.1 常见问题解决方案

#### Playwright 相关问题

1. **"Connection closed while reading from the driver" 错误**

   这通常是因为 Playwright 在 systemd 服务环境中无法找到正确的 Node.js 路径或依赖。

   解决方案：
   ```bash
   # 检查 Node.js 是否正确安装
   node -v
   
   # 确保 Playwright 浏览器已安装
   source venv/finsight/bin/activate
   playwright install
   
   # 修改 systemd 服务配置，使用与终端相同的环境
   sudo systemctl edit finsight.service
   # 添加以下内容
   # [Service]
   # ExecStart=/bin/bash -c 'source /home/<USER>/workspace/app_finsight_backend/venv/finsight/bin/activate && python -m src.main'
   # Environment="HOME=/home/<USER>"
   
   # 重新加载并重启服务
   sudo systemctl daemon-reload
   sudo systemctl restart finsight.service
   ```

2. **浏览器启动失败**

   可能是缺少系统依赖或权限问题。

   解决方案：
   ```bash
   # 安装缺失的依赖
   sudo apt install -y libx11-xcb1 libxcomposite1 libxdamage1 libxext6 libxfixes3 libxi6 libxrandr2 libxrender1 libcairo2 libcups2 libdbus-1-3 libexpat1 libfontconfig1 libgcc1 libglib2.0-0 libnspr4 libpango-1.0-0 libpangocairo-1.0-0 libstdc++6 libx11-6 libxcb1 libxcursor1 libxdamage1 libxext6 libxfixes3 libxi6 libxrandr2 libxrender1 libxss1 libxtst6
   
   # 确保用户有权限
   sudo chown -R ubuntu:ubuntu /home/<USER>/.cache/ms-playwright
   ```

#### systemd 服务问题

1. **服务无法启动**

   检查日志以获取详细错误信息：
   ```bash
   sudo journalctl -u finsight.service -e
   ```

2. **环境变量问题**

   确保服务配置中包含所有必要的环境变量：
   ```bash
   # 查看当前终端的环境变量
   env | grep -E 'PATH|PYTHON|HOME'
   
   # 确保这些环境变量在服务配置中正确设置
   sudo systemctl edit finsight.service
   ```

3. **权限问题**

   确保服务用户有权访问所有必要的文件和目录：
   ```bash
   # 检查目录权限
   ls -la /home/<USER>/workspace/app_finsight_backend
   
   # 如果需要，调整权限
   sudo chown -R ubuntu:ubuntu /home/<USER>/workspace/app_finsight_backend
   ```

#### 数据库连接问题

1. **连接超时**

   可能是数据库服务未运行或网络问题：
   ```bash
   # 检查 PostgreSQL 服务状态
   sudo systemctl status postgresql
   
   # 检查 Redis 服务状态
   sudo systemctl status redis-server
   ```

2. **认证失败**

   检查凭据是否正确：
   ```bash
   # 验证 PostgreSQL 凭据
   sudo -u postgres psql -c "SELECT usename, passwd FROM pg_shadow WHERE usename='finsight_dev';"
   
   # 重置密码（如需要）
   sudo -u postgres psql -c "ALTER USER finsight_dev WITH PASSWORD 'new_password';"
   ```

## 17. 开发建议

1. 使用虚拟环境：
```bash
source venv/finsight/bin/activate
```

2. 运行代码检查：
```bash
flake8 .
black .
```

3. 运行测试：
```bash
pytest --cov=./ --cov-report=term-missing
```

4. 检查日志：
```bash
tail -f /var/log/finsight-dev/app.log
```

## 18. 联系支持

如果遇到无法解决的问题，请联系：
- 开发团队：[<EMAIL>]
- 技术支持：[<EMAIL>]

## 19. GitHub 配置

### 19.1 配置 SSH 密钥

```bash
# 生成 SSH 密钥对
ssh-keygen -t rsa -b 4096 -C "<EMAIL>"

# 查看公钥
cat ~/.ssh/id_rsa.pub
```

将公钥添加到 GitHub：
1. 登录 GitHub
2. 进入 Settings -> SSH and GPG keys
3. 点击 "New SSH key"
4. 标题填写 "Development Server"
5. 粘贴公钥内容
6. 点击 "Add SSH key"

### 19.2 配置 Git

```bash
# 配置 Git 用户信息
git config --global user.name "Development Server"
git config --global user.email "<EMAIL>"

# 配置 Git 凭证存储
git config --global credential.helper store
```

### 19.3 配置 GitHub Actions Secrets

在 GitHub 仓库中配置以下 Secrets：

1. 开发环境相关：
   - `DEV_SERVER_HOST`: 开发服务器 IP 地址
   - `DEV_SERVER_USERNAME`: 开发服务器用户名
   - `DEV_SERVER_SSH_KEY`: 开发服务器 SSH 私钥
   - `DEV_APP_URL`: 开发环境访问 URL

2. 通用配置：
   - `SLACK_WEBHOOK_URL`: Slack 通知 Webhook URL（可选）

### 19.4 验证 GitHub 配置

```bash
# 测试 SSH 连接
ssh -T **************

# 克隆仓库
cd /home/<USER>/workspace
<NAME_EMAIL>:your-org/app_finsight_backend.git
cd app_finsight_backend

# 配置开发分支
git checkout develop
git pull origin develop
```

### 19.5 GitHub Actions 工作流配置

1. 确保 `.github/workflows/ci_cd.yml` 文件存在并包含正确的配置
2. 在 GitHub 仓库的 Settings -> Actions -> General 中：
   - 启用 Actions
   - 允许本地 Actions
   - 配置工作流权限

### 19.6 自动部署配置

1. 在开发服务器上创建部署脚本：
```bash
nano /home/<USER>/workspace/app_finsight_backend/scripts/deploy.sh
```

添加以下内容：
```bash
#!/bin/bash

# 切换到项目目录
cd /home/<USER>/workspace/app_finsight_backend

# 拉取最新代码
git fetch --all
git reset --hard origin/develop

# 更新虚拟环境
source venv/finsight/bin/activate
pip install -r requirements.txt

# 重启服务
sudo systemctl restart finsight.service

# 检查服务状态
sudo systemctl status finsight.service
```

```bash
# 设置执行权限
chmod +x /home/<USER>/workspace/app_finsight_backend/scripts/deploy.sh
```

2. 配置 GitHub Webhook（可选）：
   - 在 GitHub 仓库的 Settings -> Webhooks 中
   - 添加新的 Webhook
   - 配置开发服务器的 Webhook 接收地址
   - 选择触发事件（push, pull_request 等） 