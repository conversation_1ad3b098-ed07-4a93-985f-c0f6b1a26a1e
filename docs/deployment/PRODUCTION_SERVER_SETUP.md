# 生产服务器设置指南

本文档详细说明如何设置 FinSight 后端项目的生产服务器环境。

## 1. 系统要求

- Ubuntu 20.04 LTS 或更高版本
- 至少 4GB RAM
- 至少 50GB 存储空间
- 公网 IP 地址

## 2. 系统更新

```bash
# 更新系统包
sudo apt update
sudo apt upgrade -y

# 安装基本工具
sudo apt install -y git curl wget vim htop
```

## 3. 安装 Python 环境

```bash
# 安装 Python 3.12
sudo apt install -y software-properties-common
sudo add-apt-repository ppa:deadsnakes/ppa
sudo apt update
sudo apt install -y python3.12 python3.12-venv python3.12-dev

# 安装 pip
curl -sS https://bootstrap.pypa.io/get-pip.py | sudo python3.12
```

## 3.1 安装 Node.js 和 Playwright 依赖

如果项目使用 Playwright 进行网页抓取，需要安装 Node.js 和相关依赖：

```bash
# 安装 Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt install -y nodejs

# 验证安装
node -v
npm -v

# 安装系统依赖（Playwright需要）
sudo apt install -y libwebkit2gtk-4.0-dev \
    build-essential \
    libssl-dev \
    libgtk-3-dev \
    libgbm-dev \
    libnss3-dev \
    libxss-dev \
    libasound2-dev \
    libxshmfence-dev \
    libgles2-mesa-dev \
    libgl1-mesa-dev \
    libatk1.0-dev \
    libatk-bridge2.0-dev \
    xvfb

# 安装 Playwright 依赖
cd /home/<USER>/workspace/app_finsight_backend
source venv/finsight/bin/activate
pip install playwright
playwright install --with-deps chromium
```

**注意**：在生产环境中，建议只安装必要的浏览器（如chromium）以节省资源。确保 Node.js 安装正确，这对于 Playwright 的正常运行至关重要。

## 4. 安装 PostgreSQL

```bash
# 安装 PostgreSQL
sudo apt install -y postgresql postgresql-contrib

# 启动 PostgreSQL 服务
sudo systemctl start postgresql
sudo systemctl enable postgresql

# 创建数据库和用户
sudo -u postgres psql -c "CREATE DATABASE finsight;"
sudo -u postgres psql -c "CREATE USER finsight WITH PASSWORD 'your_secure_password';"
sudo -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE finsight TO finsight;"
```

## 5. 安装 Redis

```bash
# 安装 Redis
sudo apt install -y redis-server

# 启动 Redis 服务
sudo systemctl start redis-server
sudo systemctl enable redis-server
```

## 6. 配置腾讯云数据库连接

```bash
# 安装 PostgreSQL 客户端（仅用于管理）
sudo apt install -y postgresql-client

# 安装 Redis 客户端（仅用于管理）
sudo apt install -y redis-tools
```

### 4.1 配置数据库连接

1. 获取腾讯云数据库连接信息：
   - PostgreSQL 内网地址和端口
   - Redis 内网地址和端口
   - 数据库用户名和密码
   - 数据库名称

2. 测试数据库连接：
```bash
# 测试 PostgreSQL 连接
psql -h <postgres-internal-ip> -U <username> -d <database-name>

# 测试 Redis 连接
redis-cli -h <redis-internal-ip> -p <port> ping
```

### 4.2 配置安全组

1. 在腾讯云控制台中配置安全组：
   - 允许生产服务器内网 IP 访问数据库
   - 配置 PostgreSQL 端口（默认 5432）
   - 配置 Redis 端口（默认 6379）

2. 验证网络连接：
```bash
# 测试 PostgreSQL 端口
telnet <postgres-internal-ip> 5432

# 测试 Redis 端口
telnet <redis-internal-ip> 6379
```

## 7. 创建项目目录

```bash
# 创建项目目录
sudo mkdir -p /home/<USER>/workspace/app_finsight_backend
sudo chown -R ubuntu:ubuntu /home/<USER>/workspace/app_finsight_backend

# 创建备份目录
mkdir -p /home/<USER>/workspace/app_finsight_backend/backups
```

## 8. 配置系统服务

```bash
# 创建服务文件
sudo nano /etc/systemd/system/finsight.service
```

添加以下内容：
```ini
[Unit]
Description=FinSight Backend Service
After=network.target postgresql.service redis.service
Requires=postgresql.service redis.service

[Service]
User=ubuntu
Group=ubuntu
WorkingDirectory=/home/<USER>/workspace/app_finsight_backend

# 环境变量设置 - 保持与终端环境一致
Environment="PATH=/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin"
Environment="PYTHONPATH=/home/<USER>/workspace/app_finsight_backend"
Environment="ENVIRONMENT=production"
Environment="ENV_FILE=.env.production"
Environment="PYTHONUNBUFFERED=1"
Environment="LC_ALL=C.UTF-8"
Environment="LANG=C.UTF-8"
Environment="HOME=/home/<USER>"
Environment="SHELL=/bin/bash"
Environment="TERM=xterm-256color"
Environment="XDG_RUNTIME_DIR=/run/user/1000"

# 数据库连接配置
Environment="PGCONNECT_TIMEOUT=10"
Environment="PGPOOL_CONNECTION_LIMIT=20"
Environment="PGPOOL_IDLE_TIMEOUT=300"
Environment="PGCLIENTENCODING=utf8"
Environment="PGSSLMODE=disable"

# 进程资源限制
LimitNOFILE=65535
LimitNPROC=65535
LimitAS=infinity
LimitFSIZE=infinity
TimeoutStartSec=300
TimeoutStopSec=300
RestartSec=30
Restart=always
StartLimitBurst=5
StartLimitIntervalSec=300

# OOM处理
OOMScoreAdjust=-500

# 启动命令 - 使用与终端相同的激活方式
ExecStartPre=/bin/sleep 5
ExecStart=/bin/bash -c 'source /home/<USER>/workspace/app_finsight_backend/venv/finsight/bin/activate && python -m src.main'

[Install]
WantedBy=multi-user.target
```

启动生产环境服务
```bash
sudo systemctl daemon-reload
sudo systemctl enable finsight.service
sudo systemctl start finsight.service
```

### 8.1 服务配置说明

上述服务配置特别注意以下几点：

1. **环境变量设置**：确保服务运行环境与终端环境尽可能一致，这对于依赖特定环境变量的工具（如Playwright）尤为重要。

2. **启动命令**：使用`/bin/bash -c 'source ... && python ...'`的方式启动程序，确保与终端相同的激活方式，这样虚拟环境中的所有依赖都能正确加载。

3. **资源限制**：设置了合理的进程资源限制，避免服务因资源不足而崩溃。

4. **依赖关系**：通过`Requires`指令确保PostgreSQL和Redis服务在启动前已经就绪。

5. **OOM处理**：通过`OOMScoreAdjust`降低服务被OOM Killer终止的可能性。

### 8.2 常见问题排查

如果服务启动后出现以下问题：

1. **"Connection closed while reading from the driver"错误**：
   - 检查环境变量配置是否正确
   - 确认Node.js和Playwright依赖是否已安装
   - 验证虚拟环境是否正确激活

2. **数据库连接错误**：
   - 验证数据库服务是否运行
   - 检查连接字符串是否正确
   - 确认网络连接和防火墙设置

3. **服务启动失败**：
   - 检查日志：`sudo journalctl -u finsight.service -f`
   - 验证文件路径和权限
   - 确认依赖服务（PostgreSQL、Redis）是否正常运行

## 9. 配置 Nginx（可选）

```bash
# 安装 Nginx
sudo apt install -y nginx

# 创建 Nginx 配置
sudo nano /etc/nginx/sites-available/finsight
```

添加以下内容：
```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

```bash
# 启用配置
sudo ln -s /etc/nginx/sites-available/finsight /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

## 10. 配置防火墙

```bash
# 安装 UFW
sudo apt install -y ufw

# 配置防火墙规则
sudo ufw allow ssh
sudo ufw allow http
sudo ufw allow https
sudo ufw enable
```

## 11. 配置 SSL（可选）

```bash
# 安装 Certbot
sudo apt install -y certbot python3-certbot-nginx

# 获取 SSL 证书
sudo certbot --nginx -d your-domain.com
```

## 12. 监控设置

```bash
# 安装监控工具
sudo apt install -y prometheus-node-exporter

# 启动监控服务
sudo systemctl start prometheus-node-exporter
sudo systemctl enable prometheus-node-exporter
```

## 13. 日志配置

```bash
# 创建日志目录
sudo mkdir -p /var/log/finsight
sudo chown -R ubuntu:ubuntu /var/log/finsight

# 配置日志轮转
sudo nano /etc/logrotate.d/finsight
```

添加以下内容：
```
/var/log/finsight/*.log {
    daily
    missingok
    rotate 14
    compress
    delaycompress
    notifempty
    create 0640 ubuntu ubuntu
    sharedscripts
    postrotate
        systemctl reload finsight.service
    endscript
}
```

## 14. 备份配置

```bash
# 创建备份脚本
nano /home/<USER>/workspace/app_finsight_backend/backup.sh
```

添加以下内容：
```bash
#!/bin/bash
BACKUP_DIR="/home/<USER>/workspace/app_finsight_backend/backups"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
tar -czf "$BACKUP_DIR/backup-$TIMESTAMP.tar.gz" /home/<USER>/workspace/app_finsight_backend/current
find "$BACKUP_DIR" -type f -mtime +7 -delete
```

```bash
# 设置执行权限
chmod +x /home/<USER>/workspace/app_finsight_backend/backup.sh

# 添加到 crontab
(crontab -l 2>/dev/null; echo "0 2 * * * /home/<USER>/workspace/app_finsight_backend/backup.sh") | crontab -
```

## 15. 安全配置

```bash
# 配置 SSH
sudo nano /etc/ssh/sshd_config
```

修改以下设置：
```
PermitRootLogin no
PasswordAuthentication no
```

```bash
# 重启 SSH 服务
sudo systemctl restart sshd
```

## 16. 验证安装

```bash
# 检查服务状态
sudo systemctl status finsight.service
sudo systemctl status postgresql
sudo systemctl status redis-server
sudo systemctl status nginx

# 检查日志
sudo journalctl -u finsight.service -f
```

## 17. 维护建议

1. 定期更新系统：
```bash
sudo apt update && sudo apt upgrade -y
```

2. 监控磁盘空间：
```bash
df -h
```

3. 检查日志文件大小：
```bash
du -sh /var/log/finsight/
```

4. 检查备份状态：
```bash
ls -lh /home/<USER>/workspace/app_finsight_backend/backups/
```

## 18. 联系支持

如果遇到无法解决的问题，请联系：
- 系统管理员：[<EMAIL>]
- 技术支持：[<EMAIL>]

## 19. 生产环境配置

```bash
# 创建生产环境配置文件
cd /home/<USER>/workspace/app_finsight_backend
cp env_example.txt .env
```

编辑 `.env` 文件，确保包含以下配置：
```ini
# 腾讯云数据库配置
DATABASE_URL=postgresql://<username>:<password>@<postgres-internal-ip>:5432/<database-name>
REDIS_URL=redis://<redis-internal-ip>:6379/0

# 其他配置
JWT_SECRET_KEY=<production-secret-key>
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30
ENV=production
DEBUG=false
```

### 19.1 数据库连接测试脚本

创建数据库连接测试脚本：
```bash
nano /home/<USER>/workspace/app_finsight_backend/scripts/test_db_connection.sh
```

添加以下内容：
```bash
#!/bin/bash

# 测试 PostgreSQL 连接
echo "Testing PostgreSQL connection..."
PGPASSWORD=$POSTGRES_PASSWORD psql -h $POSTGRES_HOST -U $POSTGRES_USER -d $POSTGRES_DB -c "SELECT 1;" || exit 1

# 测试 Redis 连接
echo "Testing Redis connection..."
redis-cli -h $REDIS_HOST -p $REDIS_PORT ping || exit 1

echo "All database connections successful!"
```

```bash
# 设置执行权限
chmod +x /home/<USER>/workspace/app_finsight_backend/scripts/test_db_connection.sh
```

### 19.2 数据库监控

1. 配置数据库监控：
```bash
# 安装监控工具
sudo apt install -y prometheus-node-exporter

# 创建数据库监控脚本
nano /home/<USER>/workspace/app_finsight_backend/scripts/monitor_db.sh
```

添加以下内容：
```bash
#!/bin/bash

# 检查 PostgreSQL 连接
pg_isready -h $POSTGRES_HOST -p 5432 || echo "PostgreSQL connection failed"

# 检查 Redis 连接
redis-cli -h $REDIS_HOST -p $REDIS_PORT ping || echo "Redis connection failed"
```

```bash
# 设置执行权限
chmod +x /home/<USER>/workspace/app_finsight_backend/scripts/monitor_db.sh

# 添加到 crontab
(crontab -l 2>/dev/null; echo "*/5 * * * * /home/<USER>/workspace/app_finsight_backend/scripts/monitor_db.sh >> /var/log/finsight/db_monitor.log 2>&1") | crontab -
```

## 20. 故障排查

如果遇到问题，请检查：

1. 数据库连接：
```bash
# 检查 PostgreSQL 连接
pg_isready -h <postgres-internal-ip> -p 5432

# 检查 Redis 连接
redis-cli -h <redis-internal-ip> -p 6379 ping
```

2. 网络连接：
```bash
# 检查数据库端口
telnet <postgres-internal-ip> 5432
telnet <redis-internal-ip> 6379
```

3. 安全组配置：
- 检查腾讯云控制台中的安全组规则
- 确认生产服务器 IP 是否在允许列表中

4. 数据库日志：
- 在腾讯云控制台查看数据库日志
- 检查数据库监控指标

5. 服务状态：
```bash
sudo systemctl status finsight.service
```

6. 日志文件：
```bash
sudo journalctl -u finsight.service -f
```

7. 网络连接：
```bash
curl localhost:8000/health
```

### 20.1 常见问题解决方案

#### Playwright 相关问题

1. **"Connection closed while reading from the driver" 错误**

   这通常是因为 Playwright 在 systemd 服务环境中无法找到正确的 Node.js 路径或依赖。

   解决方案：
   ```bash
   # 检查 Node.js 是否正确安装
   node -v
   
   # 确保 Playwright 浏览器已安装
   source venv/finsight/bin/activate
   playwright install --with-deps chromium
   
   # 修改 systemd 服务配置，使用与终端相同的环境
   sudo systemctl edit finsight.service
   # 添加以下内容
   # [Service]
   # ExecStart=/bin/bash -c 'source /home/<USER>/workspace/app_finsight_backend/venv/finsight/bin/activate && python -m src.main'
   # Environment="HOME=/home/<USER>"
   
   # 重新加载并重启服务
   sudo systemctl daemon-reload
   sudo systemctl restart finsight.service
   ```

2. **浏览器启动失败**

   可能是缺少系统依赖或权限问题。

   解决方案：
   ```bash
   # 安装缺失的依赖
   sudo apt install -y libx11-xcb1 libxcomposite1 libxdamage1 libxext6 libxfixes3 libxi6 libxrandr2 libxrender1 libcairo2 libcups2 libdbus-1-3 libexpat1 libfontconfig1 libgcc1 libglib2.0-0 libnspr4 libpango-1.0-0 libpangocairo-1.0-0 libstdc++6 libx11-6 libxcb1 libxcursor1 libxdamage1 libxext6 libxfixes3 libxi6 libxrandr2 libxrender1 libxss1 libxtst6
   
   # 确保用户有权限
   sudo chown -R ubuntu:ubuntu /home/<USER>/.cache/ms-playwright
   ```

3. **生产环境中的资源问题**

   Playwright在生产环境可能消耗过多资源。

   解决方案：
   ```bash
   # 在服务配置中添加资源限制
   sudo systemctl edit finsight.service
   # 添加以下内容
   # [Service]
   # CPUQuota=80%
   # MemoryLimit=2G
   
   # 使用无头模式并禁用不必要的功能
   # 在代码中确保使用以下配置
   # browser_config = {
   #     "headless": True,
   #     "args": ["--disable-gpu", "--no-sandbox", "--disable-dev-shm-usage"]
   # }
   ```

#### systemd 服务问题

1. **服务无法启动**

   检查日志以获取详细错误信息：
   ```bash
   sudo journalctl -u finsight.service -e
   ```

2. **环境变量问题**

   确保服务配置中包含所有必要的环境变量：
   ```bash
   # 查看当前终端的环境变量
   env | grep -E 'PATH|PYTHON|HOME'
   
   # 确保这些环境变量在服务配置中正确设置
   sudo systemctl edit finsight.service
   ```

3. **权限问题**

   确保服务用户有权访问所有必要的文件和目录：
   ```bash
   # 检查目录权限
   ls -la /home/<USER>/workspace/app_finsight_backend
   
   # 如果需要，调整权限
   sudo chown -R ubuntu:ubuntu /home/<USER>/workspace/app_finsight_backend
   ```

4. **服务依赖问题**

   确保所有依赖服务都已启动：
   ```bash
   # 检查依赖服务状态
   sudo systemctl status postgresql redis-server
   
   # 修改服务配置，添加明确的依赖关系
   sudo systemctl edit finsight.service
   # 添加以下内容
   # [Unit]
   # Requires=postgresql.service redis.service
   # After=postgresql.service redis.service
   ```

#### 数据库连接问题

1. **连接池耗尽**

   可能是连接池配置不当导致连接泄漏。

   解决方案：
   ```bash
   # 检查活动连接数
   sudo -u postgres psql -c "SELECT count(*) FROM pg_stat_activity;"
   
   # 在环境变量中调整连接池配置
   sudo systemctl edit finsight.service
   # 添加以下内容
   # [Service]
   # Environment="PGPOOL_CONNECTION_LIMIT=20"
   # Environment="PGPOOL_IDLE_TIMEOUT=300"
   ```

2. **连接超时**

   可能是网络延迟或数据库负载过高。

   解决方案：
   ```bash
   # 增加连接超时时间
   sudo systemctl edit finsight.service
   # 添加以下内容
   # [Service]
   # Environment="PGCONNECT_TIMEOUT=30"
   
   # 检查数据库负载
   sudo -u postgres psql -c "SELECT * FROM pg_stat_activity WHERE state = 'active';"
   ```

3. **安全组或防火墙问题**

   确保网络配置允许连接：
   ```bash
   # 测试网络连接
   telnet <postgres-internal-ip> 5432
   
   # 检查本地防火墙规则
   sudo ufw status
   ```

## 21. GitHub 配置

### 21.1 配置 SSH 密钥

```bash
# 生成 SSH 密钥对
ssh-keygen -t rsa -b 4096 -C "<EMAIL>"

# 查看公钥
cat ~/.ssh/id_rsa.pub
```

将公钥添加到 GitHub：
1. 登录 GitHub
2. 进入 Settings -> SSH and GPG keys
3. 点击 "New SSH key"
4. 标题填写 "Production Server"
5. 粘贴公钥内容
6. 点击 "Add SSH key"

### 21.2 配置 Git

```bash
# 配置 Git 用户信息
git config --global user.name "Production Server"
git config --global user.email "<EMAIL>"

# 配置 Git 凭证存储
git config --global credential.helper store
```

### 21.3 配置 GitHub Actions Secrets

在 GitHub 仓库中配置以下 Secrets：

1. 生产环境相关：
   - `SERVER_HOST`: 生产服务器 IP 地址
   - `SERVER_USERNAME`: 生产服务器用户名
   - `SERVER_SSH_KEY`: 生产服务器 SSH 私钥
   - `APP_URL`: 生产环境访问 URL

2. 通用配置：
   - `SLACK_WEBHOOK_URL`: Slack 通知 Webhook URL（可选）

### 21.4 验证 GitHub 配置

```bash
# 测试 SSH 连接
ssh -T **************

# 克隆仓库
cd /home/<USER>/workspace
<NAME_EMAIL>:your-org/app_finsight_backend.git
cd app_finsight_backend

# 配置主分支
git checkout main
git pull origin main
```

### 21.5 GitHub Actions 工作流配置

1. 确保 `.github/workflows/ci_cd.yml` 文件存在并包含正确的配置
2. 在 GitHub 仓库的 Settings -> Actions -> General 中：
   - 启用 Actions
   - 允许本地 Actions
   - 配置工作流权限
   - 设置环境保护规则（可选）

### 21.6 自动部署配置

1. 在生产服务器上创建部署脚本：
```bash
nano /home/<USER>/workspace/app_finsight_backend/scripts/deploy.sh
```

添加以下内容：
```bash
#!/bin/bash

# 切换到项目目录
cd /home/<USER>/workspace/app_finsight_backend

# 备份当前版本
if [ -d "current" ]; then
    tar -czf backups/backup-$(date +%Y%m%d_%H%M%S).tar.gz current/
fi

# 拉取最新代码
git fetch --all
git reset --hard origin/main

# 更新虚拟环境
source venv/bin/activate
pip install -r requirements.txt

# 重启服务
sudo systemctl restart finsight.service

# 检查服务状态
sudo systemctl status finsight.service

# 运行健康检查
curl -f http://localhost:8000/health || exit 1
```

```bash
# 设置执行权限
chmod +x /home/<USER>/workspace/app_finsight_backend/scripts/deploy.sh
```

2. 配置部署保护：
```bash
# 创建部署保护脚本
nano /home/<USER>/workspace/app_finsight_backend/scripts/protect_deploy.sh
```

添加以下内容：
```bash
#!/bin/bash

# 检查系统资源
check_resources() {
    # 检查内存使用
    memory_usage=$(free | grep Mem | awk '{print $3/$2 * 100.0}')
    if (( $(echo "$memory_usage > 90" | bc -l) )); then
        echo "Memory usage too high: $memory_usage%"
        exit 1
    fi

    # 检查磁盘空间
    disk_usage=$(df / | tail -1 | awk '{print $5}' | sed 's/%//')
    if [ "$disk_usage" -gt 90 ]; then
        echo "Disk usage too high: $disk_usage%"
        exit 1
    fi

    # 检查数据库连接
    pg_isready -h $POSTGRES_HOST -p 5432 || exit 1
    redis-cli -h $REDIS_HOST -p $REDIS_PORT ping || exit 1
}

# 执行检查
check_resources
```

```bash
# 设置执行权限
chmod +x /home/<USER>/workspace/app_finsight_backend/scripts/protect_deploy.sh
```

3. 配置部署监控：
```bash
# 创建部署监控脚本
nano /home/<USER>/workspace/app_finsight_backend/scripts/monitor_deploy.sh
```

添加以下内容：
```bash
#!/bin/bash

# 监控部署后的服务状态
monitor_deployment() {
    # 等待服务启动
    sleep 30

    # 检查服务状态
    if ! systemctl is-active --quiet finsight.service; then
        echo "Service failed to start"
        exit 1
    fi

    # 检查健康状态
    for i in {1..5}; do
        if curl -f http://localhost:8000/health; then
            echo "Deployment successful"
            exit 0
        fi
        sleep 10
    done

    echo "Health check failed"
    exit 1
}

# 执行监控
monitor_deployment
```

```bash
# 设置执行权限
chmod +x /home/<USER>/workspace/app_finsight_backend/scripts/monitor_deploy.sh
```

4. 配置自动回滚：
```bash
# 创建回滚脚本
nano /home/<USER>/workspace/app_finsight_backend/scripts/rollback.sh
```

添加以下内容：
```bash
#!/bin/bash

# 获取最新的备份
LATEST_BACKUP=$(ls -t backups/backup-*.tar.gz | head -1)

if [ -n "$LATEST_BACKUP" ]; then
    # 停止服务
    sudo systemctl stop finsight.service

    # 恢复备份
    rm -rf current
    tar -xzf $LATEST_BACKUP

    # 重启服务
    sudo systemctl restart finsight.service

    # 检查服务状态
    sudo systemctl status finsight.service

    echo "Rolled back to: $LATEST_BACKUP"
else
    echo "No backup found for rollback"
    exit 1
fi
```

```bash
# 设置执行权限
chmod +x /home/<USER>/workspace/app_finsight_backend/scripts/rollback.sh
```

### 21.7 部署流程

1. 手动部署：
```bash
# 执行部署保护检查
./scripts/protect_deploy.sh

# 执行部署
./scripts/deploy.sh

# 监控部署
./scripts/monitor_deploy.sh
```

2. 自动部署（通过 GitHub Actions）：
   - 推送代码到 main 分支
   - GitHub Actions 自动触发部署流程
   - 部署脚本自动执行保护、部署和监控步骤
   - 如果失败，自动执行回滚

### 21.8 部署检查清单

在每次部署前，确保：

1. 数据库备份已完成
2. 系统资源充足
3. 所有服务正常运行
4. 监控系统正常
5. 回滚脚本可用
6. 通知系统配置正确 