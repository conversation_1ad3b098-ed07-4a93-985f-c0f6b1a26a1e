# FinSight Backend CI/CD 配置说明

## 概述

FinSight Backend 项目采用分支环境隔离的 CI/CD 策略，将开发环境和生产环境的部署流程完全分离，确保部署的稳定性和可控性。

## 分支策略

### 分支结构
- **develop 分支**: 开发环境分支，用于日常开发和测试
- **main 分支**: 生产环境分支，用于生产环境部署

### 工作流程
1. 开发人员在 `develop` 分支进行功能开发
2. `develop` 分支的代码经过充分测试后合并到 `main` 分支
3. `main` 分支的代码自动部署到生产环境

## CI/CD 工作流配置

### 1. 开发环境工作流 (develop.yml)

**触发条件:**
- `develop` 分支的 push 操作
- 向 `develop` 分支的 pull request

**工作流程:**
1. **测试阶段 (test)**
   - 代码检出
   - Python 3.12.3 环境配置
   - 依赖安装 (包含开发依赖)
   - 代码质量检查 (flake8)
   - 单元测试 (pytest with coverage)
   - 数据库和缓存服务 (PostgreSQL + Redis)

2. **部署阶段 (deploy_development)**
   - 仅在测试通过后执行
   - 部署到开发服务器
   - 使用 `finsight-dev.service` 服务
   - 备份文件命名: `backup-dev-{timestamp}.tar.gz`

3. **健康检查 (health_check_development)**
   - 验证应用启动状态
   - 健康检查端点: `http://49.233.29.134:8080/health`

4. **回滚机制 (rollback_development)**
   - 部署失败时自动回滚到最近备份
   - Slack 通知失败信息

### 2. 生产环境工作流 (production.yml)

**触发条件:**
- `main` 分支的 push 操作
- 向 `main` 分支的 pull request

**工作流程:**
1. **测试阶段 (test)**
   - 与开发环境相同的测试流程
   - 确保代码质量和功能完整性

2. **部署阶段 (deploy_production)**
   - 仅在测试通过后执行
   - 部署到生产服务器
   - 使用 `finsight.service` 服务
   - 备份文件命名: `backup-prod-{timestamp}.tar.gz`

3. **健康检查 (health_check_production)**
   - 验证生产环境应用启动状态
   - 健康检查端点: 通过 `secrets.APP_URL` 配置

4. **回滚机制 (rollback_production)**
   - 部署失败时自动回滚到最近备份
   - Slack 通知失败信息

5. **成功通知 (notify_success)**
   - 部署成功后发送 Slack 通知

## 环境变量配置

### GitHub Secrets

**开发环境:**
- `DEV_SERVER_HOST`: 开发服务器地址
- `DEV_SERVER_USERNAME`: 开发服务器用户名
- `DEV_SERVER_SSH_KEY`: 开发服务器SSH私钥

**生产环境:**
- `SERVER_HOST`: 生产服务器地址
- `SERVER_USERNAME`: 生产服务器用户名
- `SERVER_SSH_KEY`: 生产服务器SSH私钥
- `APP_URL`: 生产环境应用访问地址

**通用配置:**
- `SLACK_WEBHOOK_URL`: Slack 通知 Webhook 地址

### 服务器环境变量文件
- 开发环境: `.env.develop`
- 生产环境: `.env.production`

## 关键改进

### 1. 分支隔离
- 开发和生产环境完全独立的工作流
- 避免交叉影响和意外部署

### 2. 分支触发策略
- 开发和生产环境分别响应对应分支的更新
- 支持所有类型的提交，包括合并提交

### 3. 服务区分
- 开发环境: `finsight-dev.service`
- 生产环境: `finsight.service`

### 4. 备份策略
- 按环境区分备份文件前缀
- 开发环境: `backup-dev-{timestamp}`
- 生产环境: `backup-prod-{timestamp}`

## 部署流程

### 开发环境部署
1. 开发人员推送代码到 `develop` 分支
2. 自动触发 `develop.yml` 工作流
3. 执行测试 → 部署 → 健康检查
4. 部署完成或失败回滚

### 生产环境部署
1. 将 `develop` 分支合并到 `main` 分支
2. 自动触发 `production.yml` 工作流
3. 执行测试 → 部署 → 健康检查 → 成功通知
4. 部署完成或失败回滚

## 监控和通知

### Slack 集成
- 部署失败时自动发送通知
- 生产环境部署成功时发送确认通知
- 包含详细的部署信息和错误日志

### 健康检查
- 部署后自动验证应用状态
- 多次重试机制确保服务稳定性
- 检查失败时触发回滚机制

## 最佳实践

### 代码质量
- 强制代码格式检查 (flake8)
- 完整的单元测试覆盖
- 依赖安全性检查

### 部署安全
- 渐进式部署策略
- 自动备份和回滚机制
- 环境隔离和权限控制

### 监控告警
- 实时健康检查
- 部署状态通知
- 异常情况自动处理

## 故障排查

### 常见问题
1. **测试失败**: 检查代码质量和单元测试
2. **部署失败**: 检查服务器连接和权限
3. **健康检查失败**: 检查应用启动状态和端口配置
4. **回滚失败**: 检查备份文件完整性

### 日志查看
- GitHub Actions 工作流日志
- 服务器部署日志: `sudo journalctl -u finsight.service`
- 应用运行日志: 应用配置的日志目录

## 维护建议

### 定期维护
- 定期清理旧的备份文件
- 更新依赖版本和安全补丁
- 检查和更新 CI/CD 配置

### 性能优化
- 监控部署时间和成功率
- 优化测试执行速度
- 减少部署停机时间 