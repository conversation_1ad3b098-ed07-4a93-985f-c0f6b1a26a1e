# FinSight 部署文档

## 1. 环境要求

### 1.1 系统要求
- **操作系统**: Ubuntu 20.04+ / CentOS 8+ / macOS 10.15+
- **内存**: 最低 8GB，推荐 16GB+
- **存储**: 最低 50GB 可用空间
- **网络**: 稳定的互联网连接

### 1.2 软件依赖
- **Python**: 3.11+
- **Docker**: 20.10+
- **Docker Compose**: 2.0+
- **Git**: 2.30+

## 2. 环境配置

### 2.1 开发环境

#### 2.1.1 虚拟环境设置
```bash
# 创建虚拟环境
python -m venv venv/finsight

# 激活虚拟环境
source venv/finsight/bin/activate  # Linux/macOS
# 或
venv\finsight\Scripts\activate     # Windows

# 安装依赖
pip install -r requirements.txt
```

#### 2.1.2 环境变量配置
```bash
# 复制环境变量模板
cp env_example.txt .env.develop

# 编辑环境变量文件
vim .env.develop
```

#### 2.1.3 数据库初始化
```bash
# 启动数据库容器
docker-compose up -d db redis

# 执行数据库迁移
alembic upgrade head

# 创建初始数据
python scripts/init_data.py
```

### 2.2 测试环境

#### 2.2.1 环境变量
```bash
# 创建测试环境配置
cp env_example.txt .env.test

# 修改数据库名为测试库
DB_NAME=finsight_test
```

#### 2.2.2 运行测试
```bash
# 设置测试环境
export ENVIRONMENT=testing

# 运行单元测试
pytest tests/

# 运行集成测试
pytest tests/integration/

# 生成测试报告
pytest --cov=src --cov-report=html
```

### 2.3 生产环境

#### 2.3.1 环境变量
```bash
# 创建生产环境配置
cp env_example.txt .env.production

# 设置生产环境变量
export ENVIRONMENT=production
export DEBUG=false
```

#### 2.3.2 安全配置
- 使用强随机密钥
- 启用HTTPS
- 配置防火墙规则
- 设置日志监控

## 3. Docker 部署

### 3.1 单机部署
```bash
# 构建镜像
docker build -t finsight-backend .

# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f api
```

### 3.2 生产环境部署
```bash
# 使用生产配置
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d

# 健康检查
curl http://localhost:8000/health
```

## 4. Kubernetes 部署

### 4.1 配置文件
```yaml
# k8s/namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: finsight

---
# k8s/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: finsight-api
  namespace: finsight
spec:
  replicas: 3
  selector:
    matchLabels:
      app: finsight-api
  template:
    metadata:
      labels:
        app: finsight-api
    spec:
      containers:
      - name: api
        image: finsight/backend:latest
        ports:
        - containerPort: 8000
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: db-secret
              key: url
```

### 4.2 部署命令
```bash
# 创建命名空间
kubectl apply -f k8s/namespace.yaml

# 创建配置和密钥
kubectl apply -f k8s/secrets.yaml
kubectl apply -f k8s/configmap.yaml

# 部署应用
kubectl apply -f k8s/deployment.yaml
kubectl apply -f k8s/service.yaml

# 查看部署状态
kubectl get pods -n finsight
```

## 5. 监控和日志

### 5.1 Prometheus 监控
```yaml
# monitoring/prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'finsight-api'
    static_configs:
      - targets: ['api:8000']
    metrics_path: '/metrics'
```

### 5.2 日志配置
```bash
# 查看应用日志
docker-compose logs -f api

# 查看特定服务日志
kubectl logs -f deployment/finsight-api -n finsight

# 日志轮转配置
logrotate /etc/logrotate.d/finsight
```

## 6. 备份和恢复

### 6.1 数据库备份
```bash
# 备份数据库
docker exec -t postgres pg_dump -U postgres finsight > backup_$(date +%Y%m%d_%H%M%S).sql

# 恢复数据库
docker exec -i postgres psql -U postgres finsight < backup_20231201_120000.sql
```

### 6.2 Redis 备份
```bash
# 备份 Redis
docker exec redis redis-cli BGSAVE

# 复制备份文件
docker cp redis:/data/dump.rdb ./redis_backup_$(date +%Y%m%d_%H%M%S).rdb
```

## 7. 性能优化

### 7.1 数据库优化
- 创建合适的索引
- 定期更新统计信息
- 配置连接池
- 监控慢查询

### 7.2 缓存优化
- 合理设置TTL
- 监控缓存命中率
- 预热关键数据
- 实现缓存降级

### 7.3 应用优化
- 异步处理长任务
- 合理设置连接数
- 启用Gzip压缩
- 配置CDN加速

## 8. 故障排查

### 8.1 常见问题
1. **应用启动失败**
   - 检查环境变量配置
   - 验证数据库连接
   - 查看详细日志

2. **数据库连接失败**
   - 检查数据库服务状态
   - 验证连接参数
   - 检查网络连通性

3. **Redis连接失败**
   - 检查Redis服务状态
   - 验证连接配置
   - 检查内存使用情况

### 8.2 调试命令
```bash
# 进入容器调试
docker exec -it finsight-api bash

# 查看系统资源
docker stats

# 检查端口占用
netstat -tulpn | grep 8000

# 测试数据库连接
psql -h localhost -U postgres -d finsight
```

## 9. 安全检查清单

### 9.1 部署前检查
- [ ] 所有默认密码已更改
- [ ] SSL证书已配置
- [ ] 防火墙规则已设置
- [ ] 敏感信息已加密存储
- [ ] 依赖包已更新到最新版本

### 9.2 运行时检查
- [ ] 监控和告警已配置
- [ ] 日志审计已启用
- [ ] 备份策略已实施
- [ ] 访问控制已验证
- [ ] 性能指标正常

## 10. 版本升级

### 10.1 滚动升级
```bash
# 更新镜像版本
docker-compose pull

# 滚动更新
docker-compose up -d --no-deps api

# 验证新版本
curl http://localhost:8000/health
```

### 10.2 回滚策略
```bash
# 查看部署历史
kubectl rollout history deployment/finsight-api -n finsight

# 回滚到上一版本
kubectl rollout undo deployment/finsight-api -n finsight

# 回滚到指定版本
kubectl rollout undo deployment/finsight-api --to-revision=2 -n finsight
``` 