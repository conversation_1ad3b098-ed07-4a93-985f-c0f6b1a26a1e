"""
统一任务系统测试
验证数据采集服务的异步任务架构改进
"""

import asyncio
import pytest
from unittest.mock import AsyncMock, MagicMock, patch

from src.core.task_framework import (
    TaskType, TaskExecutionMode, TaskContext, TaskStatus, 
    UnifiedTaskManager, BaseTaskExecutor
)
from src.services.data_collection_service.task_manager import (
    CrawlerTaskExecutor, DataCollectionTaskManager
)


class TestUnifiedTaskFramework:
    """测试统一任务框架"""
    
    def test_task_context_creation(self):
        """测试任务上下文创建"""
        context = TaskContext(
            task_id="test_001",
            task_type=TaskType.CRAWLER,
            execution_mode=TaskExecutionMode.DIRECT,
            parameters={"data_source_id": 1},
            priority=5
        )
        
        assert context.task_id == "test_001"
        assert context.task_type == TaskType.CRAWLER
        assert context.execution_mode == TaskExecutionMode.DIRECT
        assert context.parameters["data_source_id"] == 1
        assert context.status == TaskStatus.PENDING
    
    def test_task_manager_executor_registration(self):
        """测试任务管理器执行器注册"""
        manager = UnifiedTaskManager()
        executor = CrawlerTaskExecutor()
        
        manager.register_executor(TaskType.CRAWLER, executor)
        
        registered_executor = manager.get_executor(TaskType.CRAWLER)
        assert registered_executor is executor


class TestCrawlerTaskExecutor:
    """测试爬虫任务执行器"""
    
    def test_parameter_validation(self):
        """测试参数验证"""
        executor = CrawlerTaskExecutor()
        
        # 有效参数
        valid_params = {"data_source_id": 1}
        assert executor.validate_parameters(valid_params) is True
        
        # 无效参数 - 缺少必需字段
        invalid_params = {}
        assert executor.validate_parameters(invalid_params) is False
        
        # 无效参数 - 数据源ID无效
        invalid_params = {"data_source_id": "invalid"}
        assert executor.validate_parameters(invalid_params) is False
    
    @patch('src.services.data_collection_service.task_manager.SessionLocal')
    def test_execution_mode_selection(self, mock_session):
        """测试执行模式选择逻辑"""
        executor = CrawlerTaskExecutor()
        
        # Mock数据源
        mock_data_source = MagicMock()
        mock_data_source.supports_realtime = True
        
        mock_db = MagicMock()
        mock_session.return_value = mock_db
        executor.data_source_service.get_data_source_by_id = MagicMock(return_value=mock_data_source)
        
        # 实时采集应使用异步队列
        params = {"data_source_id": 1}
        mode = executor.get_execution_mode(params)
        assert mode == TaskExecutionMode.ASYNC_QUEUE
        
        # 批量模式应使用异步队列
        params = {"data_source_id": 1, "batch_mode": True}
        mode = executor.get_execution_mode(params)
        assert mode == TaskExecutionMode.ASYNC_QUEUE
        
        # 大型任务应使用异步队列
        mock_data_source.supports_realtime = False
        params = {"data_source_id": 1, "config": {"max_pages": 50}}
        mode = executor.get_execution_mode(params)
        assert mode == TaskExecutionMode.ASYNC_QUEUE


class TestDataCollectionTaskManager:
    """测试数据采集任务管理器"""
    
    @pytest.mark.asyncio
    @patch('src.services.data_collection_service.task_manager.SessionLocal')
    @patch('src.core.task_queue.submit_crawler_task')
    async def test_submit_crawl_task_async(self, mock_submit_task, mock_session):
        """测试异步提交爬取任务"""
        manager = DataCollectionTaskManager()
        
        # Mock依赖
        mock_db = MagicMock()
        mock_session.return_value = mock_db
        
        mock_data_source = MagicMock()
        manager.data_source_service.get_data_source_by_id = MagicMock(return_value=mock_data_source)
        
        mock_task = MagicMock()
        mock_task.id = 123
        manager.crawler_task_service.create_task = MagicMock(return_value=mock_task)
        manager.crawler_task_service.update_task_status = MagicMock(return_value=True)
        
        mock_submit_task.return_value = "celery_task_123"
        
        # 测试异步提交
        result = await manager.submit_crawl_task(
            data_source_id=1,
            execution_mode="async",
            priority=5
        )
        
        assert result["crawl_task_id"] == 123
        assert result["celery_task_id"] == "celery_task_123"
        assert result["execution_mode"] == "async"
        assert result["status"] == "queued"
    
    @pytest.mark.asyncio
    @patch('src.services.data_collection_service.task_manager.SessionLocal')
    async def test_get_task_status(self, mock_session):
        """测试获取任务状态"""
        manager = DataCollectionTaskManager()
        
        # Mock依赖
        mock_db = MagicMock()
        mock_session.return_value = mock_db
        
        mock_task = MagicMock()
        mock_task.id = 123
        mock_task.status = "running"
        mock_task.data_source_id = 1
        mock_task.created_at.isoformat.return_value = "2023-01-01T00:00:00"
        manager.crawler_task_service.get_task_by_id = MagicMock(return_value=mock_task)
        
        # 测试获取状态
        status_info = await manager.get_task_status(123)
        
        assert status_info is not None
        assert status_info["task_id"] == 123
        assert status_info["status"] == "running"
        assert status_info["data_source_id"] == 1


class TestTaskExecution:
    """测试任务执行流程"""
    
    @pytest.mark.asyncio
    @patch('src.services.data_collection_service.task_manager.SessionLocal')
    @patch('src.services.data_collection_service.crawlers.crawler_factory.CrawlerFactory')
    async def test_crawler_task_execution(self, mock_factory, mock_session):
        """测试爬虫任务执行"""
        executor = CrawlerTaskExecutor()
        
        # Mock依赖
        mock_db = MagicMock()
        mock_session.return_value = mock_db
        
        mock_data_source = MagicMock()
        mock_data_source.id = 1
        mock_data_source.collection_method = "web"
        executor.data_source_service.get_data_source_by_id = MagicMock(return_value=mock_data_source)
        
        mock_task = MagicMock()
        mock_task.id = 456
        executor.crawler_task_service.get_task_by_id = MagicMock(return_value=mock_task)
        executor.crawler_task_service.update_task_status = MagicMock(return_value=True)
        
        # Mock爬虫
        mock_crawler_class = AsyncMock()
        mock_crawler = AsyncMock()
        mock_crawler_class.return_value = mock_crawler
        mock_factory.return_value.get_crawler_class = MagicMock(return_value=mock_crawler_class)
        
        # Mock爬取结果
        mock_crawl_result = MagicMock()
        mock_crawl_result.items_collected = 10
        mock_crawl_result.items_failed = 1
        mock_crawl_result.start_time.isoformat.return_value = "2023-01-01T00:00:00"
        mock_crawl_result.end_time.isoformat.return_value = "2023-01-01T00:05:00"
        mock_crawl_result.errors = []
        mock_crawl_result.metadata = {}
        mock_crawler.crawl = AsyncMock(return_value=mock_crawl_result)
        
        # 创建任务上下文
        context = TaskContext(
            task_id="test_crawler_001",
            task_type=TaskType.CRAWLER,
            execution_mode=TaskExecutionMode.DIRECT,
            parameters={
                "data_source_id": 1,
                "task_id": 456,
                "config": {}
            }
        )
        
        # 执行任务
        result = await executor.execute_task(context)
        
        # 验证结果
        assert result["status"] == "success"
        assert result["items_collected"] == 10
        assert result["items_failed"] == 1
        assert result["task_id"] == 456
        assert result["data_source_id"] == 1


def test_integration_scenario():
    """集成测试场景"""
    
    # 测试场景：
    # 1. 系统启动时注册爬虫任务执行器
    # 2. 用户通过API提交不同类型的爬取任务
    # 3. 系统根据任务特性选择合适的执行模式
    # 4. 任务执行完成后更新状态
    
    print("🚀 开始集成测试...")
    
    # 1. 注册执行器
    from src.core.task_framework import task_manager
    crawler_executor = CrawlerTaskExecutor()
    task_manager.register_executor(TaskType.CRAWLER, crawler_executor)
    print("✅ 爬虫任务执行器已注册")
    
    # 2. 验证执行器注册成功
    registered_executor = task_manager.get_executor(TaskType.CRAWLER)
    assert registered_executor is not None
    print("✅ 执行器注册验证通过")
    
    # 3. 测试参数验证
    valid_params = {"data_source_id": 1}
    assert crawler_executor.validate_parameters(valid_params) is True
    print("✅ 参数验证测试通过")
    
    print("🎉 集成测试完成！")


if __name__ == "__main__":
    # 运行集成测试
    test_integration_scenario()
    
    # 运行异步测试示例
    async def run_async_tests():
        print("\n🔄 运行异步测试...")
        
        # 创建任务管理器
        manager = DataCollectionTaskManager()
        
        # 模拟任务状态检查
        print("📊 测试任务状态管理...")
        
        print("✅ 异步测试完成！")
    
    # 运行异步测试
    asyncio.run(run_async_tests()) 