"""
标签分类服务测试配置
"""

from datetime import date, datetime
from decimal import Decimal

import pytest
from fastapi import FastAPI
from fastapi.testclient import TestClient
from jose import jwt
from sqlalchemy import (JSON, BigInteger, Boolean, Column, Date, DateTime,
                        ForeignKey, Integer, Numeric, String, Text,
                        create_engine)
from sqlalchemy.orm import declarative_base, relationship, sessionmaker
from sqlalchemy.pool import StaticPool
from sqlalchemy.sql import func

from src.services.tag_classification_service.router import router
from src.services.tag_classification_service.service import (
    ClassificationService, TagClassificationService, TagService,
    UserInterestService)

# 为测试创建独立的Base，避免与原有模型冲突
TestBase = declarative_base()


# 测试专用的模型定义，兼容SQLite
class TagClassification(TestBase):
    __tablename__ = "tag_classifications"

    id = Column(Integer, primary_key=True, autoincrement=True)
    classification_code = Column(String(50), nullable=False, unique=True)
    classification_name = Column(String(100), nullable=False)
    parent_id = Column(Integer, ForeignKey("tag_classifications.id"))
    level = Column(Integer, default=1)
    path = Column(String(500))
    description = Column(Text)
    icon = Column(String(50))
    color = Column(String(7))
    sort_order = Column(Integer, default=0)
    is_active = Column(Boolean, default=True)
    is_system = Column(Boolean, default=False)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    # 关系定义
    parent = relationship("TagClassification", remote_side=[id], back_populates="children")
    children = relationship("TagClassification", back_populates="parent")
    tags = relationship("Tag", back_populates="classification")


# TagType和TagCategory已被TagClassification替代，这里保留空定义以避免导入错误
# 实际测试中使用TagClassification


class Tag(TestBase):
    __tablename__ = "tags"

    id = Column(Integer, primary_key=True, autoincrement=True)
    tag_name = Column(String(100), nullable=False)
    tag_code = Column(String(50), nullable=False, unique=True)
    tag_slug = Column(String(50), nullable=False, unique=True)
    parent_id = Column(Integer, ForeignKey("tags.id"))
    level = Column(Integer, default=1)
    path = Column(String(500))  # SQLite使用String替代LTREE
    classification_id = Column(Integer, ForeignKey("tag_classifications.id"), nullable=False)
    color = Column(String(7))
    icon = Column(String(50))
    base_weight = Column(Numeric(3, 2), default=1.00)
    popularity_weight = Column(Numeric(3, 2), default=0.00)
    quality_weight = Column(Numeric(3, 2), default=0.00)
    temporal_weight = Column(Numeric(3, 2), default=0.00)
    computed_weight = Column(
        Numeric(3, 2), default=1.00
    )  # SQLite不支持GENERATED ALWAYS
    usage_count = Column(Integer, default=0)
    daily_usage_count = Column(Integer, default=0)
    last_used_at = Column(DateTime)
    positive_feedback_count = Column(Integer, default=0)
    negative_feedback_count = Column(Integer, default=0)
    feedback_score = Column(Numeric(3, 2), default=0.5)  # SQLite不支持GENERATED ALWAYS
    lifecycle_stage = Column(String(20), default="active")
    auto_retirement_date = Column(Date)
    description = Column(Text)
    synonyms = Column(Text)  # SQLite使用Text替代ARRAY，存储JSON字符串
    is_active = Column(Boolean, default=True)
    is_system = Column(Boolean, default=False)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    # 关系定义
    classification = relationship("TagClassification", back_populates="tags")
    parent = relationship("Tag", remote_side=[id], back_populates="children")
    children = relationship("Tag", back_populates="parent")
    # 标签关系
    source_relationships = relationship(
        "TagRelationship",
        foreign_keys="TagRelationship.tag_id",
        back_populates="source_tag",
    )
    target_relationships = relationship(
        "TagRelationship",
        foreign_keys="TagRelationship.related_tag_id",
        back_populates="related_tag",
    )


class TagRelationship(TestBase):
    __tablename__ = "tag_relationships"

    id = Column(Integer, primary_key=True, autoincrement=True)
    tag_id = Column(Integer, ForeignKey("tags.id"), nullable=False)
    related_tag_id = Column(Integer, ForeignKey("tags.id"), nullable=False)
    relationship_type = Column(String(50), nullable=False)
    strength = Column(Numeric(3, 2), default=1.0)
    confidence = Column(Numeric(3, 2), default=1.0)
    source = Column(String(50), default="manual")
    created_by = Column(String(100))
    usage_count = Column(Integer, default=0)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    # 关系定义
    source_tag = relationship(
        "Tag",
        foreign_keys=[tag_id],
        back_populates="source_relationships",
        overlaps="target_relationships",
    )
    related_tag = relationship(
        "Tag",
        foreign_keys=[related_tag_id],
        back_populates="target_relationships",
        overlaps="source_relationships",
    )


class ClassificationDimension(TestBase):
    __tablename__ = "classification_dimensions"

    id = Column(Integer, primary_key=True, autoincrement=True)
    dimension_name = Column(String(50), nullable=False, unique=True)
    display_name = Column(String(100), nullable=False)
    description = Column(Text)
    is_active = Column(Boolean, default=True)
    sort_order = Column(Integer, default=0)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    # 关系定义
    values = relationship("ClassificationValue", back_populates="dimension")
    user_preferences = relationship(
        "UserClassificationPreference", back_populates="dimension"
    )


class ClassificationValue(TestBase):
    __tablename__ = "classification_values"

    id = Column(Integer, primary_key=True, autoincrement=True)
    dimension_id = Column(
        Integer, ForeignKey("classification_dimensions.id"), nullable=False
    )
    value_code = Column(String(50), nullable=False)
    display_name = Column(String(100), nullable=False)
    description = Column(Text)
    parent_id = Column(Integer, ForeignKey("classification_values.id"))
    level = Column(Integer, default=1)
    sort_order = Column(Integer, default=0)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    # 关系定义
    dimension = relationship("ClassificationDimension", back_populates="values")
    parent = relationship("ClassificationValue", remote_side=[id], backref="children")
    user_preferences = relationship(
        "UserClassificationPreference", back_populates="value"
    )


class UserInterestTag(TestBase):
    __tablename__ = "user_interest_tags"

    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, nullable=False)
    tag_id = Column(Integer, nullable=False)
    explicit_interest = Column(Numeric(3, 2), default=0.0)
    implicit_interest = Column(Numeric(3, 2), default=0.0)
    computed_interest = Column(
        Numeric(3, 2), default=0.0
    )  # SQLite不支持GENERATED ALWAYS
    last_reinforced_at = Column(DateTime, default=func.now())
    reinforcement_count = Column(Integer, default=1)
    daily_decay_rate = Column(Numeric(5, 4), default=0.9990)
    click_count = Column(Integer, default=0)
    view_time_seconds = Column(Integer, default=0)
    share_count = Column(Integer, default=0)
    source = Column(String(50), default="behavior")
    confidence = Column(Numeric(3, 2), default=0.5)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())


class UserClassificationPreference(TestBase):
    __tablename__ = "user_classification_preferences"

    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, nullable=False)
    dimension_id = Column(
        Integer, ForeignKey("classification_dimensions.id"), nullable=False
    )
    value_id = Column(Integer, ForeignKey("classification_values.id"), nullable=False)
    preference_score = Column(Numeric(3, 2), default=0.5)
    source = Column(String(50), default="behavior")
    last_updated = Column(DateTime, default=func.now(), onupdate=func.now())

    # 关系定义
    dimension = relationship(
        "ClassificationDimension", back_populates="user_preferences"
    )
    value = relationship("ClassificationValue", back_populates="user_preferences")


class UserProfileSnapshot(TestBase):
    __tablename__ = "user_profile_snapshots"

    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, nullable=False)
    snapshot_date = Column(Date, nullable=False)
    top_interests = Column(
        Text, nullable=False
    )  # SQLite使用Text替代JSONB，存储JSON字符串
    interest_categories = Column(Text, nullable=False)  # SQLite使用Text替代JSONB
    behavioral_patterns = Column(Text)  # SQLite使用Text替代JSONB
    recommendation_weights = Column(Text, default="{}")  # SQLite使用Text替代JSONB
    content_filters = Column(Text, default="{}")  # SQLite使用Text替代JSONB
    created_at = Column(DateTime, default=func.now())


# 测试数据库设置
SQLITE_DATABASE_URL = "sqlite:///:memory:"

engine = create_engine(
    SQLITE_DATABASE_URL,
    connect_args={"check_same_thread": False},
    poolclass=StaticPool,
)
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


# 为测试重写服务层的模型引用
def patch_service_models():
    """为测试重写服务层的模型引用"""
    # 将测试模型设置到服务层中
    from src.services.tag_classification_service import service

    # 替换服务层中的模型引用
    service.TagClassification = TagClassification
    service.Tag = Tag
    service.TagRelationship = TagRelationship
    service.ClassificationDimension = ClassificationDimension
    service.ClassificationValue = ClassificationValue
    service.UserInterestTag = UserInterestTag
    service.UserClassificationPreference = UserClassificationPreference
    service.UserProfileSnapshot = UserProfileSnapshot


@pytest.fixture
def db_session():
    """创建测试数据库会话"""
    # 重写服务层的模型引用
    patch_service_models()

    TestBase.metadata.create_all(bind=engine)

    session = TestingSessionLocal()
    try:
        yield session
    finally:
        session.close()
        TestBase.metadata.drop_all(bind=engine)


@pytest.fixture
def tag_classification_service(db_session):
    """创建标签分类服务实例"""
    return TagClassificationService(db_session)


@pytest.fixture
def tag_service(db_session):
    """创建标签服务实例"""
    return TagService(db_session)


@pytest.fixture
def user_interest_service(db_session):
    """创建用户兴趣服务实例"""
    return UserInterestService(db_session)


@pytest.fixture
def classification_service(db_session):
    """创建分类服务实例"""
    return ClassificationService(db_session)


@pytest.fixture
def sample_tag_classification(db_session):
    """创建示例标签分类"""
    classification = TagClassification(
        classification_code="general.industry",
        classification_name="通用行业",
        description="通用行业分类",
        icon="industry",
        color="#2196F3",
        sort_order=10,
        level=1,
        path="general.industry",
    )
    db_session.add(classification)
    db_session.commit()
    db_session.refresh(classification)
    return classification


@pytest.fixture
def sample_tag(db_session, sample_tag_classification):
    """创建示例标签"""
    tag = Tag(
        tag_name="科技",
        tag_code="technology",
        tag_slug="technology",
        classification_id=sample_tag_classification.id,
        level=1,
        path="technology",
        color="#FF9800",
        icon="tech",
        base_weight=Decimal("0.8"),
        description="科技相关标签",
        computed_weight=Decimal("0.8"),  # SQLite需要手动设置
    )
    db_session.add(tag)
    db_session.commit()
    db_session.refresh(tag)
    return tag


@pytest.fixture
def sample_classification_dimension(db_session):
    """创建示例分类维度"""
    dimension = ClassificationDimension(
        dimension_name="sentiment",
        display_name="情感倾向",
        description="情感分类维度",
        sort_order=10,
    )
    db_session.add(dimension)
    db_session.commit()
    db_session.refresh(dimension)
    return dimension


@pytest.fixture
def sample_classification_value(db_session, sample_classification_dimension):
    """创建示例分类值"""
    value = ClassificationValue(
        dimension_id=sample_classification_dimension.id,
        value_code="positive",
        display_name="积极",
        description="积极情感",
        sort_order=10,
    )
    db_session.add(value)
    db_session.commit()
    db_session.refresh(value)
    return value


@pytest.fixture
def sample_user_interest(db_session, sample_tag):
    """创建示例用户兴趣"""
    interest = UserInterestTag(
        user_id=1,
        tag_id=sample_tag.id,
        explicit_interest=Decimal("0.8"),
        implicit_interest=Decimal("0.6"),
        computed_interest=Decimal("0.7"),  # SQLite需要手动设置
        source="behavior",
    )
    db_session.add(interest)
    db_session.commit()
    db_session.refresh(interest)
    return interest


@pytest.fixture
def sample_user_classification_preference(
    db_session, sample_classification_dimension, sample_classification_value
):
    """创建示例用户分类偏好"""
    preference = UserClassificationPreference(
        user_id=1,
        dimension_id=sample_classification_dimension.id,
        value_id=sample_classification_value.id,
        preference_score=Decimal("0.7"),
        source="behavior",
    )
    db_session.add(preference)
    db_session.commit()
    db_session.refresh(preference)
    return preference


@pytest.fixture
def app(db_session):
    """创建 FastAPI 应用实例"""
    from fastapi import FastAPI

    app = FastAPI()
    app.include_router(router)  # 公共路由器已经定义了prefix="/api/v1"
    
    # 导入并注册管理员路由
    from src.services.tag_classification_service.admin_router import router as admin_router
    app.include_router(admin_router, prefix="/api/v1/admin")

    # 用于测试的依赖项覆盖
    from src.core.database import get_db
    from src.services.user_service.dependencies import get_current_active_user

    app.dependency_overrides[get_db] = lambda: db_session

    # 模拟认证用户
    def mock_get_current_active_user():
        """模拟当前活跃用户"""

        class MockUser:
            def __init__(self):
                self.id = 1
                self.username = "test_admin"
                self.phone = "18810541701"
                self.user_type = 1  # 管理员类型
                self.is_active = True

        return MockUser()

    app.dependency_overrides[get_current_active_user] = mock_get_current_active_user

    return app


@pytest.fixture
def client(app):
    """创建测试客户端"""
    return TestClient(app)


@pytest.fixture
def admin_token():
    """生成管理员 JWT 令牌"""
    payload = {
        "sub": "1",
        "phone": "18810541701",
        "user_type": 1,  # 管理员
        "exp": 9999999999,  # 很远的过期时间
        "type": "access",
        "iat": 1750579815,
    }
    # 使用测试用的简单密钥
    return jwt.encode(payload, "test_secret", algorithm="HS256")


@pytest.fixture
def user_token():
    """生成普通用户 JWT 令牌"""
    payload = {
        "sub": "1001",
        "phone": "18800000001",
        "user_type": 2,  # 普通用户
        "exp": 9999999999,  # 很远的过期时间
        "type": "access",
        "iat": 1750579815,
    }
    # 使用测试用的简单密钥
    return jwt.encode(payload, "test_secret", algorithm="HS256")
