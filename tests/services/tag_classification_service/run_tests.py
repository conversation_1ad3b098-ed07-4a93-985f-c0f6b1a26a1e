#!/usr/bin/env python3
"""
标签分类服务测试运行脚本
"""

import os
import subprocess
import sys
from pathlib import Path


def main():
    """运行所有测试"""
    # 确保在正确的目录
    project_root = Path(__file__).parent.parent.parent.parent
    os.chdir(project_root)

    # 激活虚拟环境
    print("🔄 激活虚拟环境...")
    activate_command = "source venv/finsight/bin/activate"

    # 运行测试
    test_commands = [
        # 运行标签类型服务测试
        "python -m pytest tests/services/tag_classification_service/test_tag_type_service.py -v",
        # 运行标签服务测试
        "python -m pytest tests/services/tag_classification_service/test_tag_service.py -v",
        # 运行API路由测试
        "python -m pytest tests/services/tag_classification_service/test_router.py -v",
        # 运行所有标签分类服务测试
        "python -m pytest tests/services/tag_classification_service/ -v --tb=short",
        # 生成测试覆盖率报告
        "python -m pytest tests/services/tag_classification_service/ --cov=src/services/tag_classification_service --cov-report=html --cov-report=term",
    ]

    print("🧪 开始运行标签分类服务测试...")

    for i, command in enumerate(test_commands, 1):
        print(f"\n📋 运行测试 {i}/{len(test_commands)}: {command}")

        # 组合完整命令
        full_command = f"{activate_command} && {command}"

        try:
            result = subprocess.run(
                full_command, shell=True, check=True, capture_output=True, text=True
            )

            print("✅ 测试通过")
            if result.stdout:
                print(result.stdout)

        except subprocess.CalledProcessError as e:
            print(f"❌ 测试失败: {e}")
            if e.stdout:
                print("STDOUT:", e.stdout)
            if e.stderr:
                print("STDERR:", e.stderr)

            # 如果是基础测试失败，停止后续测试
            if i <= 3:
                print("⚠️  基础测试失败，停止后续测试")
                return False

    print("\n🎉 所有测试完成!")
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
