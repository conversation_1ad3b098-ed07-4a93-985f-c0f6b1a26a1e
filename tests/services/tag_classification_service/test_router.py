"""
标签分类服务API路由测试
"""

from unittest.mock import Mock, patch

import pytest
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from src.core.database import get_db
from src.main import app
from src.services.tag_classification_service.models import TagType
from src.services.tag_classification_service.schemas import (TagTypeCreate,
                                                             TagTypeUpdate)
from src.services.user_service.dependencies import get_current_active_user


@pytest.fixture
def client(db_session):
    """创建测试客户端，重写数据库依赖"""

    def override_get_db():
        try:
            yield db_session
        finally:
            pass

    app.dependency_overrides[get_db] = override_get_db
    client = TestClient(app)
    yield client
    app.dependency_overrides.clear()


@pytest.fixture
def mock_current_user():
    """模拟当前用户"""
    user = Mock()
    user.id = 1
    user.username = "testuser"
    user.is_active = True
    user.is_verified = True
    return user


@pytest.fixture
def authenticated_client(client, mock_current_user):
    """创建已认证的测试客户端"""

    def override_get_current_active_user():
        return mock_current_user

    app.dependency_overrides[get_current_active_user] = override_get_current_active_user
    yield client
    app.dependency_overrides.clear()


class TestTagTypeRouter:
    """标签类型路由测试"""

    def test_create_tag_type_success(self, authenticated_client, mock_current_user):
        """测试创建标签类型API成功"""
        tag_type_data = {
            "type_code": "test_entity",
            "type_name": "测试实体标签",
            "description": "测试用实体标签类型",
            "icon": "test-entity",
            "color": "#9C27B0",
            "sort_order": 5,
        }

        response = authenticated_client.post("/api/v1/tags/types", json=tag_type_data)

        assert response.status_code == 201
        response_data = response.json()
        assert response_data["type_code"] == "test_entity"
        assert response_data["type_name"] == "测试实体标签"
        assert response_data["description"] == "测试用实体标签类型"

    def test_create_tag_type_unauthorized(self, client):
        """测试未认证创建标签类型"""
        tag_type_data = {"type_code": "unauthorized", "type_name": "未授权标签"}

        response = client.post("/api/v1/tags/types", json=tag_type_data)
        assert response.status_code == 403

    def test_create_tag_type_invalid_data(
        self, authenticated_client, mock_current_user
    ):
        """测试无效数据创建标签类型"""
        # 缺少必需字段
        tag_type_data = {
            "type_code": "invalid"
            # 缺少 type_name
        }

        response = authenticated_client.post("/api/v1/tags/types", json=tag_type_data)
        assert response.status_code == 422

    def test_get_tag_types_success(self, client):
        """测试获取标签类型列表成功"""
        response = client.get("/api/v1/tags/types")

        assert response.status_code == 200
        response_data = response.json()
        assert "items" in response_data
        assert "total" in response_data
        assert "page" in response_data
        assert "size" in response_data
        assert "pages" in response_data

    def test_get_tag_types_with_pagination(self, client):
        """测试带分页参数获取标签类型"""
        response = client.get("/api/v1/tags/types?page=1&size=10")

        assert response.status_code == 200
        response_data = response.json()
        assert response_data["page"] == 1
        assert response_data["size"] == 10

    def test_get_tag_types_active_only(self, client):
        """测试只获取活跃标签类型"""
        response = client.get("/api/v1/tags/types?is_active=true")

        assert response.status_code == 200
        response_data = response.json()
        # 验证返回的所有标签类型都是活跃的
        for item in response_data["items"]:
            assert item["is_active"] is True

    def test_get_tag_type_by_id_success(self, client, sample_tag_type):
        """测试根据ID获取标签类型成功"""
        response = client.get(f"/api/v1/tags/types/{sample_tag_type.id}")

        assert response.status_code == 200
        response_data = response.json()
        assert response_data["id"] == sample_tag_type.id
        assert response_data["type_code"] == sample_tag_type.type_code

    def test_get_tag_type_by_id_not_found(self, client):
        """测试获取不存在的标签类型"""
        response = client.get("/api/v1/tags/types/9999")
        assert response.status_code == 404

    def test_update_tag_type_success(
        self, authenticated_client, mock_current_user, sample_tag_type
    ):
        """测试更新标签类型成功"""
        update_data = {"type_name": "更新的标签类型", "description": "更新的描述"}

        response = authenticated_client.put(
            f"/api/v1/tags/types/{sample_tag_type.id}", json=update_data
        )

        assert response.status_code == 200
        response_data = response.json()
        assert response_data["type_name"] == "更新的标签类型"
        assert response_data["description"] == "更新的描述"

    def test_update_tag_type_not_found(self, authenticated_client, mock_current_user):
        """测试更新不存在的标签类型"""
        update_data = {"type_name": "不存在的标签"}
        response = authenticated_client.put("/api/v1/tags/types/9999", json=update_data)
        assert response.status_code == 404

    def test_delete_tag_type_success(
        self, authenticated_client, mock_current_user, db_session, sample_tag_type
    ):
        """测试删除标签类型成功"""
        # 创建一个新的标签类型用于删除测试
        new_tag_type = TagType(type_code="to_delete_api", type_name="API删除测试")
        db_session.add(new_tag_type)
        db_session.commit()

        response = authenticated_client.delete(f"/api/v1/tags/types/{new_tag_type.id}")
        assert response.status_code == 204

    def test_delete_tag_type_not_found(self, authenticated_client, mock_current_user):
        """测试删除不存在的标签类型"""
        response = authenticated_client.delete("/api/v1/tags/types/9999")
        assert response.status_code == 404


class TestTagRouter:
    """标签路由测试"""

    def test_create_tag_success(
        self, authenticated_client, mock_current_user, sample_tag_type
    ):
        """测试创建标签API成功"""
        tag_data = {
            "tag_name": "API测试标签",
            "tag_code": "api_test_tag",
            "tag_slug": "api-test-tag",
            "tag_type_id": sample_tag_type.id,
            "description": "API测试用标签",
            "lifecycle_stage": "active",
        }

        response = authenticated_client.post("/api/v1/tags/", json=tag_data)

        assert response.status_code == 201
        response_data = response.json()
        assert response_data["tag_name"] == "API测试标签"
        assert response_data["tag_code"] == "api_test_tag"

    def test_get_tags_success(self, client):
        """测试获取标签列表成功"""
        response = client.get("/api/v1/tags/")

        assert response.status_code == 200
        response_data = response.json()
        assert "items" in response_data
        assert "total" in response_data

    def test_get_tags_with_search(self, client):
        """测试带搜索参数获取标签"""
        response = client.get("/api/v1/tags/?search=测试")

        assert response.status_code == 200
        response_data = response.json()
        assert "items" in response_data

    def test_search_tags_success(self, client):
        """测试标签搜索成功"""
        response = client.get("/api/v1/tags/search?q=测试")

        assert response.status_code == 200
        response_data = response.json()
        assert isinstance(response_data, list)

    def test_get_popular_tags_success(self, client):
        """测试获取热门标签成功"""
        response = client.get("/api/v1/tags/popular")

        assert response.status_code == 200
        response_data = response.json()
        assert isinstance(response_data, list)

    def test_get_popular_tags_with_params(self, client):
        """测试带参数获取热门标签"""
        response = client.get("/api/v1/tags/popular?limit=5&time_range=daily")

        assert response.status_code == 200
        response_data = response.json()
        assert isinstance(response_data, list)

    def test_get_tag_by_id_success(self, client, sample_tag):
        """测试根据ID获取标签成功"""
        response = client.get(f"/api/v1/tags/{sample_tag.id}")

        assert response.status_code == 200
        response_data = response.json()
        assert response_data["id"] == sample_tag.id

    def test_get_tag_with_relations(self, client, sample_tag):
        """测试获取标签及其关系"""
        response = client.get(f"/api/v1/tags/{sample_tag.id}?include_relations=true")

        assert response.status_code == 200
        # 响应应该是标签详情，包含关系信息
        response_data = response.json()
        assert response_data["id"] == sample_tag.id

    def test_update_tag_success(
        self, authenticated_client, mock_current_user, sample_tag
    ):
        """测试更新标签成功"""
        update_data = {"tag_name": "更新的标签名称", "description": "更新的描述"}

        response = authenticated_client.put(
            f"/api/v1/tags/{sample_tag.id}", json=update_data
        )

        assert response.status_code == 200
        response_data = response.json()
        assert response_data["tag_name"] == "更新的标签名称"
        assert response_data["description"] == "更新的描述"

    def test_delete_tag_success(
        self, authenticated_client, mock_current_user, db_session, sample_tag_type
    ):
        """测试删除标签成功"""
        # 创建一个新的标签用于删除测试
        from tests.services.tag_classification_service.conftest import Tag

        new_tag = Tag(
            tag_name="API删除测试标签",
            tag_code="api_delete_test",
            tag_slug="api-delete-test",
            tag_type_id=sample_tag_type.id,
            path="api_delete_test",
        )
        db_session.add(new_tag)
        db_session.commit()

        response = authenticated_client.delete(f"/api/v1/tags/{new_tag.id}")
        assert response.status_code == 204


class TestTagCategoryRouter:
    """标签分类路由测试"""

    def test_create_tag_category_success(self, authenticated_client, mock_current_user):
        """测试创建标签分类API成功"""
        category_data = {
            "category_code": "test_category",
            "category_name": "测试分类",
            "description": "API测试用分类",
        }

        response = authenticated_client.post(
            "/api/v1/tags/categories", json=category_data
        )

        assert response.status_code == 201
        response_data = response.json()
        assert response_data["category_code"] == "test_category"
        assert response_data["category_name"] == "测试分类"

    def test_get_tag_categories_tree(self, client):
        """测试获取标签分类树"""
        response = client.get("/api/v1/tags/categories/tree")

        assert response.status_code == 200
        response_data = response.json()
        assert isinstance(response_data, list)

    def test_get_tag_categories_success(self, client):
        """测试获取标签分类列表成功"""
        response = client.get("/api/v1/tags/categories")

        assert response.status_code == 200
        response_data = response.json()
        assert "items" in response_data

    def test_get_tag_category_by_id_success(self, client, sample_tag_category):
        """测试根据ID获取标签分类成功"""
        response = client.get(f"/api/v1/tags/categories/{sample_tag_category.id}")

        assert response.status_code == 200
        response_data = response.json()
        assert response_data["id"] == sample_tag_category.id

    def test_update_tag_category_success(
        self, authenticated_client, mock_current_user, sample_tag_category
    ):
        """测试更新标签分类成功"""
        update_data = {"category_name": "更新的分类名称", "description": "更新的描述"}

        response = authenticated_client.put(
            f"/api/v1/tags/categories/{sample_tag_category.id}", json=update_data
        )

        assert response.status_code == 200
        response_data = response.json()
        assert response_data["category_name"] == "更新的分类名称"
