"""
分类服务测试模块
测试分类维度、分类值和用户偏好相关功能
"""

from datetime import datetime
from decimal import Decimal

import pytest
from sqlalchemy.orm import Session

from src.services.tag_classification_service.models import (
    ClassificationDimension, ClassificationValue, UserClassificationPreference)
from src.services.tag_classification_service.schemas import (
    ClassificationDimensionCreate, ClassificationDimensionUpdate,
    ClassificationValueCreate, ClassificationValueUpdate,
    UserClassificationPreferenceCreateInternal,
    UserClassificationPreferenceUpdate)
from src.services.tag_classification_service.service import \
    ClassificationService


class TestClassificationService:
    """分类服务测试类"""

    def test_create_classification_dimension(
        self, db_session: Session, classification_service: ClassificationService
    ):
        """测试创建分类维度"""
        dimension_data = ClassificationDimensionCreate(
            dimension_name="industry",
            display_name="行业分类",
            description="金融相关行业分类",
            sort_order=1,
        )

        dimension = classification_service.create_classification_dimension(
            dimension_data
        )

        assert dimension is not None
        assert dimension.dimension_name == "industry"
        assert dimension.display_name == "行业分类"
        assert dimension.description == "金融相关行业分类"
        assert dimension.sort_order == 1
        assert dimension.is_active is True

    def test_create_duplicate_dimension_name(
        self, db_session: Session, classification_service: ClassificationService
    ):
        """测试创建重复维度名称"""
        dimension_data = ClassificationDimensionCreate(
            dimension_name="industry", display_name="行业分类"
        )

        # 创建第一个维度
        classification_service.create_classification_dimension(dimension_data)

        # 尝试创建重复名称的维度
        with pytest.raises(ValueError, match="already exists"):
            classification_service.create_classification_dimension(dimension_data)

    def test_get_classification_dimension_by_id(
        self, db_session: Session, classification_service: ClassificationService
    ):
        """测试根据ID获取分类维度"""
        dimension_data = ClassificationDimensionCreate(
            dimension_name="sentiment", display_name="情感倾向"
        )

        created_dimension = classification_service.create_classification_dimension(
            dimension_data
        )
        retrieved_dimension = classification_service.get_classification_dimension_by_id(
            created_dimension.id
        )

        assert retrieved_dimension is not None
        assert retrieved_dimension.id == created_dimension.id
        assert retrieved_dimension.dimension_name == "sentiment"

    def test_get_classification_dimension_by_name(
        self, db_session: Session, classification_service: ClassificationService
    ):
        """测试根据名称获取分类维度"""
        dimension_data = ClassificationDimensionCreate(
            dimension_name="urgency", display_name="紧急程度"
        )

        created_dimension = classification_service.create_classification_dimension(
            dimension_data
        )
        retrieved_dimension = (
            classification_service.get_classification_dimension_by_name("urgency")
        )

        assert retrieved_dimension is not None
        assert retrieved_dimension.id == created_dimension.id
        assert retrieved_dimension.dimension_name == "urgency"

    def test_get_classification_dimensions(
        self, db_session: Session, classification_service: ClassificationService
    ):
        """测试获取分类维度列表"""
        # 创建多个维度
        dimensions_data = [
            ClassificationDimensionCreate(
                dimension_name="industry", display_name="行业分类", sort_order=1
            ),
            ClassificationDimensionCreate(
                dimension_name="sentiment", display_name="情感倾向", sort_order=2
            ),
            ClassificationDimensionCreate(
                dimension_name="urgency", display_name="紧急程度", sort_order=3
            ),
        ]

        for dimension_data in dimensions_data:
            classification_service.create_classification_dimension(dimension_data)

        # 创建一个不活跃的维度
        inactive_dimension = ClassificationDimension(
            dimension_name="inactive_test", display_name="非活跃测试", is_active=False
        )
        db_session.add(inactive_dimension)
        db_session.commit()

        # 获取所有维度
        dimensions, total = classification_service.get_classification_dimensions()
        assert total == 4
        assert len(dimensions) == 4

        # 只获取活跃的维度
        active_dimensions, active_total = (
            classification_service.get_classification_dimensions(is_active=True)
        )
        assert active_total == 3
        assert len(active_dimensions) == 3

        # 测试分页
        paged_dimensions, paged_total = (
            classification_service.get_classification_dimensions(skip=1, limit=1)
        )
        assert paged_total == 4
        assert len(paged_dimensions) == 1

    def test_update_classification_dimension(
        self, db_session: Session, classification_service: ClassificationService
    ):
        """测试更新分类维度"""
        dimension_data = ClassificationDimensionCreate(
            dimension_name="region", display_name="地区分类"
        )

        created_dimension = classification_service.create_classification_dimension(
            dimension_data
        )

        update_data = ClassificationDimensionUpdate(
            display_name="地理区域分类",
            description="按地理位置划分的区域分类",
            sort_order=5,
        )

        updated_dimension = classification_service.update_classification_dimension(
            created_dimension.id, update_data
        )

        assert updated_dimension is not None
        assert updated_dimension.display_name == "地理区域分类"
        assert updated_dimension.description == "按地理位置划分的区域分类"
        assert updated_dimension.sort_order == 5

    def test_delete_classification_dimension(
        self, db_session: Session, classification_service: ClassificationService
    ):
        """测试删除分类维度"""
        dimension_data = ClassificationDimensionCreate(
            dimension_name="topic", display_name="主题分类"
        )

        created_dimension = classification_service.create_classification_dimension(
            dimension_data
        )
        dimension_id = created_dimension.id

        # 删除维度
        success = classification_service.delete_classification_dimension(dimension_id)
        assert success is True

        # 验证已删除
        retrieved_dimension = classification_service.get_classification_dimension_by_id(
            dimension_id
        )
        assert retrieved_dimension is None

    def test_delete_dimension_with_values(
        self, db_session: Session, classification_service: ClassificationService
    ):
        """测试删除有关联值的维度"""
        # 创建维度
        dimension_data = ClassificationDimensionCreate(
            dimension_name="industry", display_name="行业分类"
        )
        dimension = classification_service.create_classification_dimension(
            dimension_data
        )

        # 创建分类值
        value_data = ClassificationValueCreate(
            dimension_id=dimension.id, value_code="banking", display_name="银行业"
        )
        classification_service.create_classification_value(value_data)

        # 尝试删除有关联值的维度
        with pytest.raises(
            ValueError, match="classification values are using this dimension"
        ):
            classification_service.delete_classification_dimension(dimension.id)

    def test_create_classification_value(
        self, db_session: Session, classification_service: ClassificationService
    ):
        """测试创建分类值"""
        # 先创建维度
        dimension_data = ClassificationDimensionCreate(
            dimension_name="industry", display_name="行业分类"
        )
        dimension = classification_service.create_classification_dimension(
            dimension_data
        )

        # 创建分类值
        value_data = ClassificationValueCreate(
            dimension_id=dimension.id,
            value_code="banking",
            display_name="银行业",
            description="银行及相关金融服务",
            sort_order=1,
        )

        value = classification_service.create_classification_value(value_data)

        assert value is not None
        assert value.dimension_id == dimension.id
        assert value.value_code == "banking"
        assert value.display_name == "银行业"
        assert value.description == "银行及相关金融服务"
        assert value.level == 1
        assert value.sort_order == 1
        assert value.is_active is True

    def test_create_classification_value_with_parent(
        self, db_session: Session, classification_service: ClassificationService
    ):
        """测试创建有父级的分类值"""
        # 创建维度
        dimension_data = ClassificationDimensionCreate(
            dimension_name="industry", display_name="行业分类"
        )
        dimension = classification_service.create_classification_dimension(
            dimension_data
        )

        # 创建父分类值
        parent_data = ClassificationValueCreate(
            dimension_id=dimension.id, value_code="banking", display_name="银行业"
        )
        parent_value = classification_service.create_classification_value(parent_data)

        # 创建子分类值
        child_data = ClassificationValueCreate(
            dimension_id=dimension.id,
            value_code="commercial_bank",
            display_name="商业银行",
            parent_id=parent_value.id,
        )
        child_value = classification_service.create_classification_value(child_data)

        assert child_value.parent_id == parent_value.id
        assert child_value.level == 2

    def test_create_duplicate_value_code(
        self, db_session: Session, classification_service: ClassificationService
    ):
        """测试创建重复分类值代码"""
        # 创建维度
        dimension_data = ClassificationDimensionCreate(
            dimension_name="industry", display_name="行业分类"
        )
        dimension = classification_service.create_classification_dimension(
            dimension_data
        )

        # 创建分类值
        value_data = ClassificationValueCreate(
            dimension_id=dimension.id, value_code="banking", display_name="银行业"
        )
        classification_service.create_classification_value(value_data)

        # 尝试创建重复代码的分类值
        with pytest.raises(ValueError, match="already exists in this dimension"):
            classification_service.create_classification_value(value_data)

    def test_get_classification_values(
        self, db_session: Session, classification_service: ClassificationService
    ):
        """测试获取分类值列表"""
        # 创建维度
        dimension_data = ClassificationDimensionCreate(
            dimension_name="industry", display_name="行业分类"
        )
        dimension = classification_service.create_classification_dimension(
            dimension_data
        )

        # 创建多个分类值
        values_data = [
            ClassificationValueCreate(
                dimension_id=dimension.id,
                value_code="banking",
                display_name="银行业",
                sort_order=1,
            ),
            ClassificationValueCreate(
                dimension_id=dimension.id,
                value_code="securities",
                display_name="证券业",
                sort_order=2,
            ),
            ClassificationValueCreate(
                dimension_id=dimension.id,
                value_code="insurance",
                display_name="保险业",
                sort_order=3,
            ),
        ]

        created_values = []
        for value_data in values_data:
            value = classification_service.create_classification_value(value_data)
            created_values.append(value)

        # 获取分类值列表
        values, total = classification_service.get_classification_values(dimension_id=dimension.id)
        assert total == 3
        assert len(values) == 3

        # 测试分页
        paged_values, paged_total = classification_service.get_classification_values(
            dimension_id=dimension.id, skip=1, limit=1
        )
        assert paged_total == 3
        assert len(paged_values) == 1

    def test_get_classification_values_tree(
        self, db_session: Session, classification_service: ClassificationService
    ):
        """测试获取分类值树形结构"""
        # 创建维度
        dimension_data = ClassificationDimensionCreate(
            dimension_name="industry", display_name="行业分类"
        )
        dimension = classification_service.create_classification_dimension(
            dimension_data
        )

        # 创建父分类值
        parent_data = ClassificationValueCreate(
            dimension_id=dimension.id, value_code="banking", display_name="银行业"
        )
        parent_value = classification_service.create_classification_value(parent_data)

        # 创建子分类值
        child_data = ClassificationValueCreate(
            dimension_id=dimension.id,
            value_code="commercial_bank",
            display_name="商业银行",
            parent_id=parent_value.id,
        )
        classification_service.create_classification_value(child_data)

        # 获取树形结构
        tree = classification_service.get_classification_values_tree(dimension.id)
        assert len(tree) == 1  # 只有一个根节点
        assert tree[0].value_code == "banking"
        assert len(tree[0].children) == 1
        assert tree[0].children[0].value_code == "commercial_bank"

    def test_update_classification_value(
        self, db_session: Session, classification_service: ClassificationService
    ):
        """测试更新分类值"""
        # 创建维度和分类值
        dimension_data = ClassificationDimensionCreate(
            dimension_name="industry", display_name="行业分类"
        )
        dimension = classification_service.create_classification_dimension(
            dimension_data
        )

        value_data = ClassificationValueCreate(
            dimension_id=dimension.id, value_code="banking", display_name="银行业"
        )
        created_value = classification_service.create_classification_value(value_data)

        # 更新分类值
        update_data = ClassificationValueUpdate(
            display_name="银行及金融服务业",
            description="银行及相关金融服务机构",
            sort_order=10,
        )

        updated_value = classification_service.update_classification_value(
            created_value.id, update_data
        )

        assert updated_value is not None
        assert updated_value.display_name == "银行及金融服务业"
        assert updated_value.description == "银行及相关金融服务机构"
        assert updated_value.sort_order == 10

    def test_delete_classification_value(
        self, db_session: Session, classification_service: ClassificationService
    ):
        """测试删除分类值"""
        # 创建维度和分类值
        dimension_data = ClassificationDimensionCreate(
            dimension_name="industry", display_name="行业分类"
        )
        dimension = classification_service.create_classification_dimension(
            dimension_data
        )

        value_data = ClassificationValueCreate(
            dimension_id=dimension.id, value_code="banking", display_name="银行业"
        )
        created_value = classification_service.create_classification_value(value_data)
        value_id = created_value.id

        # 删除分类值
        success = classification_service.delete_classification_value(value_id)
        assert success is True

        # 验证已删除
        retrieved_value = classification_service.get_classification_value_by_id(
            value_id
        )
        assert retrieved_value is None

    def test_delete_value_with_children(
        self, db_session: Session, classification_service: ClassificationService
    ):
        """测试删除有子值的分类值"""
        # 创建维度
        dimension_data = ClassificationDimensionCreate(
            dimension_name="industry", display_name="行业分类"
        )
        dimension = classification_service.create_classification_dimension(
            dimension_data
        )

        # 创建父分类值
        parent_data = ClassificationValueCreate(
            dimension_id=dimension.id, value_code="banking", display_name="银行业"
        )
        parent_value = classification_service.create_classification_value(parent_data)

        # 创建子分类值
        child_data = ClassificationValueCreate(
            dimension_id=dimension.id,
            value_code="commercial_bank",
            display_name="商业银行",
            parent_id=parent_value.id,
        )
        classification_service.create_classification_value(child_data)

        # 尝试删除有子值的分类值
        with pytest.raises(ValueError, match="child values depend on it"):
            classification_service.delete_classification_value(parent_value.id)

    def test_create_user_classification_preference(
        self, db_session: Session, classification_service: ClassificationService
    ):
        """测试创建用户分类偏好"""
        # 创建维度和分类值
        dimension_data = ClassificationDimensionCreate(
            dimension_name="industry", display_name="行业分类"
        )
        dimension = classification_service.create_classification_dimension(
            dimension_data
        )

        value_data = ClassificationValueCreate(
            dimension_id=dimension.id, value_code="banking", display_name="银行业"
        )
        value = classification_service.create_classification_value(value_data)

        # 创建用户偏好
        preference_data = UserClassificationPreferenceCreateInternal(
            user_id=1001,
            dimension_id=dimension.id,
            value_id=value.id,
            preference_score=Decimal("0.8"),
            source="manual",
        )

        preference = classification_service.create_user_classification_preference(
            preference_data
        )

        assert preference is not None
        assert preference.user_id == 1001
        assert preference.dimension_id == dimension.id
        assert preference.value_id == value.id
        assert preference.preference_score == Decimal("0.8")
        assert preference.source == "manual"

    def test_create_duplicate_user_preference(
        self, db_session: Session, classification_service: ClassificationService
    ):
        """测试创建重复用户偏好（应该更新现有记录）"""
        # 创建维度和分类值
        dimension_data = ClassificationDimensionCreate(
            dimension_name="industry", display_name="行业分类"
        )
        dimension = classification_service.create_classification_dimension(
            dimension_data
        )

        value_data = ClassificationValueCreate(
            dimension_id=dimension.id, value_code="banking", display_name="银行业"
        )
        value = classification_service.create_classification_value(value_data)

        # 创建用户偏好
        preference_data = UserClassificationPreferenceCreateInternal(
            user_id=1001,
            dimension_id=dimension.id,
            value_id=value.id,
            preference_score=Decimal("0.6"),
            source="behavior",
        )

        first_preference = classification_service.create_user_classification_preference(
            preference_data
        )

        # 创建重复偏好（应该更新现有记录）
        preference_data.preference_score = Decimal("0.8")
        preference_data.source = "manual"

        second_preference = (
            classification_service.create_user_classification_preference(
                preference_data
            )
        )

        # 应该是同一个记录
        assert first_preference.id == second_preference.id
        assert second_preference.preference_score == Decimal("0.8")
        assert second_preference.source == "manual"

    def test_get_user_classification_preferences(
        self, db_session: Session, classification_service: ClassificationService
    ):
        """测试获取用户分类偏好"""
        # 创建维度和分类值
        dimension_data = ClassificationDimensionCreate(
            dimension_name="industry", display_name="行业分类"
        )
        dimension = classification_service.create_classification_dimension(
            dimension_data
        )

        value_data = ClassificationValueCreate(
            dimension_id=dimension.id, value_code="banking", display_name="银行业"
        )
        value = classification_service.create_classification_value(value_data)

        # 创建用户偏好
        preference_data = UserClassificationPreferenceCreateInternal(
            user_id=1001,
            dimension_id=dimension.id,
            value_id=value.id,
            preference_score=Decimal("0.8"),
        )
        classification_service.create_user_classification_preference(preference_data)

        # 获取用户偏好
        preferences = classification_service.get_user_classification_preferences(1001)
        assert len(preferences) == 1
        assert preferences[0].user_id == 1001
        assert preferences[0].preference_score == Decimal("0.8")

        # 按维度获取偏好
        dimension_preferences = (
            classification_service.get_user_classification_preferences(
                1001, dimension.id
            )
        )
        assert len(dimension_preferences) == 1

        # 获取不存在用户的偏好
        empty_preferences = classification_service.get_user_classification_preferences(
            9999
        )
        assert len(empty_preferences) == 0

    def test_update_user_classification_preference(
        self, db_session: Session, classification_service: ClassificationService
    ):
        """测试更新用户分类偏好"""
        # 创建维度、分类值和用户偏好
        dimension_data = ClassificationDimensionCreate(
            dimension_name="industry", display_name="行业分类"
        )
        dimension = classification_service.create_classification_dimension(
            dimension_data
        )

        value_data = ClassificationValueCreate(
            dimension_id=dimension.id, value_code="banking", display_name="银行业"
        )
        value = classification_service.create_classification_value(value_data)

        preference_data = UserClassificationPreferenceCreateInternal(
            user_id=1001,
            dimension_id=dimension.id,
            value_id=value.id,
            preference_score=Decimal("0.6"),
        )
        created_preference = (
            classification_service.create_user_classification_preference(
                preference_data
            )
        )

        # 更新用户偏好
        update_data = UserClassificationPreferenceUpdate(
            preference_score=Decimal("0.9"), source="manual"
        )

        updated_preference = (
            classification_service.update_user_classification_preference(
                created_preference.id, update_data
            )
        )

        assert updated_preference is not None
        assert updated_preference.preference_score == Decimal("0.9")
        assert updated_preference.source == "manual"

    def test_delete_user_classification_preference(
        self, db_session: Session, classification_service: ClassificationService
    ):
        """测试删除用户分类偏好"""
        # 创建维度、分类值和用户偏好
        dimension_data = ClassificationDimensionCreate(
            dimension_name="industry", display_name="行业分类"
        )
        dimension = classification_service.create_classification_dimension(
            dimension_data
        )

        value_data = ClassificationValueCreate(
            dimension_id=dimension.id, value_code="banking", display_name="银行业"
        )
        value = classification_service.create_classification_value(value_data)

        preference_data = UserClassificationPreferenceCreateInternal(
            user_id=1001,
            dimension_id=dimension.id,
            value_id=value.id,
            preference_score=Decimal("0.8"),
        )
        created_preference = (
            classification_service.create_user_classification_preference(
                preference_data
            )
        )
        preference_id = created_preference.id

        # 删除用户偏好
        success = classification_service.delete_user_classification_preference(
            preference_id
        )
        assert success is True

        # 验证已删除
        preferences = classification_service.get_user_classification_preferences(1001)
        assert len(preferences) == 0
