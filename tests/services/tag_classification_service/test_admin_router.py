"""
测试标签分类服务的管理端API路由
"""

import pytest
from fastapi.testclient import TestClient
from unittest.mock import Mock, patch

from src.main import app_b
from src.services.tag_classification_service.models import TagClassification, Tag
from src.services.tag_classification_service.schemas import ClassificationType


@pytest.fixture
def client():
    """创建测试客户端"""
    return TestClient(app_b)


@pytest.fixture
def mock_admin_user():
    """模拟管理员用户"""
    user = Mock()
    user.id = 1
    user.username = "admin"
    user.email = "<EMAIL>"
    user.is_active = True
    user.is_superuser = True
    return user


@pytest.fixture
def mock_tag_classification():
    """模拟标签分类"""
    classification = Mock()
    classification.id = 1
    classification.classification_code = "test.category"
    classification.classification_name = "测试分类"
    classification.classification_type = "category"
    classification.domain = "test"
    classification.level = 1
    classification.path = "test.category"
    classification.description = "测试分类描述"
    classification.is_active = True
    classification.is_system = False
    classification.sort_order = 0
    classification.business_rules = "{}"
    return classification


@pytest.fixture
def mock_tag():
    """模拟标签"""
    tag = Mock()
    tag.id = 1
    tag.tag_name = "测试标签"
    tag.tag_code = "test_tag"
    tag.tag_slug = "test-tag"
    tag.classification_id = 1
    tag.description = "测试标签描述"
    tag.is_active = True
    tag.is_system = False
    return tag


class TestTagClassificationAdminAPI:
    """测试标签分类管理API"""

    @patch('src.services.tag_classification_service.admin_router.get_current_active_user')
    @patch('src.services.tag_classification_service.admin_router.require_admin')
    @patch('src.services.tag_classification_service.admin_router.get_tag_classification_service')
    def test_get_tag_classifications(self, mock_service, mock_require_admin, mock_get_user, 
                                   client, mock_admin_user, mock_tag_classification):
        """测试获取标签分类列表"""
        # 设置mock
        mock_get_user.return_value = mock_admin_user
        mock_require_admin.return_value = None
        
        mock_service_instance = Mock()
        mock_service_instance.get_classifications.return_value = [mock_tag_classification]
        mock_service.return_value = mock_service_instance
        
        # 发送请求
        response = client.get("/api/v1/admin/tags/classifications")
        
        # 验证响应
        assert response.status_code == 200
        data = response.json()
        assert "items" in data
        assert "total" in data
        assert "page" in data
        assert "size" in data

    @patch('src.services.tag_classification_service.admin_router.get_current_active_user')
    @patch('src.services.tag_classification_service.admin_router.require_admin')
    @patch('src.services.tag_classification_service.admin_router.get_tag_classification_service')
    def test_get_tag_classification_by_id(self, mock_service, mock_require_admin, mock_get_user,
                                        client, mock_admin_user, mock_tag_classification):
        """测试根据ID获取标签分类"""
        # 设置mock
        mock_get_user.return_value = mock_admin_user
        mock_require_admin.return_value = None
        
        mock_service_instance = Mock()
        mock_service_instance.get_classification_by_id.return_value = mock_tag_classification
        mock_service.return_value = mock_service_instance
        
        # 发送请求
        response = client.get("/api/v1/admin/tags/classifications/1")
        
        # 验证响应
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == 1
        assert data["classification_code"] == "test.category"

    @patch('src.services.tag_classification_service.admin_router.get_current_active_user')
    @patch('src.services.tag_classification_service.admin_router.require_admin')
    @patch('src.services.tag_classification_service.admin_router.get_tag_classification_service')
    def test_create_tag_classification(self, mock_service, mock_require_admin, mock_get_user,
                                     client, mock_admin_user, mock_tag_classification):
        """测试创建标签分类"""
        # 设置mock
        mock_get_user.return_value = mock_admin_user
        mock_require_admin.return_value = None
        
        mock_service_instance = Mock()
        mock_service_instance.create_classification.return_value = mock_tag_classification
        mock_service.return_value = mock_service_instance
        
        # 准备请求数据
        classification_data = {
            "classification_code": "test.new",
            "classification_name": "新测试分类",
            "classification_type": "category",
            "domain": "test",
            "description": "新的测试分类"
        }
        
        # 发送请求
        response = client.post("/api/v1/admin/tags/classifications", json=classification_data)
        
        # 验证响应
        assert response.status_code == 201
        data = response.json()
        assert data["classification_code"] == "test.category"

    @patch('src.services.tag_classification_service.admin_router.get_current_active_user')
    @patch('src.services.tag_classification_service.admin_router.require_admin')
    @patch('src.services.tag_classification_service.admin_router.get_tag_classification_service')
    def test_update_tag_classification(self, mock_service, mock_require_admin, mock_get_user,
                                     client, mock_admin_user, mock_tag_classification):
        """测试更新标签分类"""
        # 设置mock
        mock_get_user.return_value = mock_admin_user
        mock_require_admin.return_value = None
        
        mock_service_instance = Mock()
        mock_service_instance.update_classification.return_value = mock_tag_classification
        mock_service.return_value = mock_service_instance
        
        # 准备更新数据
        update_data = {
            "classification_name": "更新后的分类名称",
            "description": "更新后的描述"
        }
        
        # 发送请求
        response = client.put("/api/v1/admin/tags/classifications/1", json=update_data)
        
        # 验证响应
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == 1

    @patch('src.services.tag_classification_service.admin_router.get_current_active_user')
    @patch('src.services.tag_classification_service.admin_router.require_admin')
    @patch('src.services.tag_classification_service.admin_router.get_tag_classification_service')
    def test_delete_tag_classification(self, mock_service, mock_require_admin, mock_get_user,
                                     client, mock_admin_user):
        """测试删除标签分类"""
        # 设置mock
        mock_get_user.return_value = mock_admin_user
        mock_require_admin.return_value = None
        
        mock_service_instance = Mock()
        mock_service_instance.delete_classification.return_value = True
        mock_service.return_value = mock_service_instance
        
        # 发送请求
        response = client.delete("/api/v1/admin/tags/classifications/1")
        
        # 验证响应
        assert response.status_code == 204

    @patch('src.services.tag_classification_service.admin_router.get_current_active_user')
    @patch('src.services.tag_classification_service.admin_router.require_admin')
    @patch('src.services.tag_classification_service.admin_router.get_tag_classification_service')
    def test_get_tag_classifications_tree(self, mock_service, mock_require_admin, mock_get_user,
                                        client, mock_admin_user, mock_tag_classification):
        """测试获取标签分类树"""
        # 设置mock
        mock_get_user.return_value = mock_admin_user
        mock_require_admin.return_value = None
        
        mock_service_instance = Mock()
        mock_service_instance.get_classifications.return_value = [mock_tag_classification]
        mock_service.return_value = mock_service_instance
        
        # 发送请求
        response = client.get("/api/v1/admin/tags/classifications/tree")
        
        # 验证响应
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)


class TestTagAdminAPI:
    """测试标签管理API"""

    @patch('src.services.tag_classification_service.admin_router.get_current_active_user')
    @patch('src.services.tag_classification_service.admin_router.require_admin')
    @patch('src.services.tag_classification_service.admin_router.get_tag_service')
    def test_get_tags(self, mock_service, mock_require_admin, mock_get_user,
                     client, mock_admin_user, mock_tag):
        """测试获取标签列表"""
        # 设置mock
        mock_get_user.return_value = mock_admin_user
        mock_require_admin.return_value = None
        
        mock_service_instance = Mock()
        mock_service_instance.list_tags.return_value = ([mock_tag], 1)
        mock_service.return_value = mock_service_instance
        
        # 发送请求
        response = client.get("/api/v1/admin/tags")
        
        # 验证响应
        assert response.status_code == 200
        data = response.json()
        assert "items" in data
        assert "total" in data

    @patch('src.services.tag_classification_service.admin_router.get_current_active_user')
    @patch('src.services.tag_classification_service.admin_router.require_admin')
    @patch('src.services.tag_classification_service.admin_router.get_tag_service')
    def test_search_tags(self, mock_service, mock_require_admin, mock_get_user,
                        client, mock_admin_user, mock_tag):
        """测试搜索标签"""
        # 设置mock
        mock_get_user.return_value = mock_admin_user
        mock_require_admin.return_value = None
        
        mock_service_instance = Mock()
        mock_service_instance.search_tags.return_value = [mock_tag]
        mock_service.return_value = mock_service_instance
        
        # 发送请求
        response = client.get("/api/v1/admin/tags/search?q=测试")
        
        # 验证响应
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)

    @patch('src.services.tag_classification_service.admin_router.get_current_active_user')
    @patch('src.services.tag_classification_service.admin_router.require_admin')
    @patch('src.services.tag_classification_service.admin_router.get_tag_service')
    def test_activate_tag(self, mock_service, mock_require_admin, mock_get_user,
                         client, mock_admin_user, mock_tag):
        """测试激活标签"""
        # 设置mock
        mock_get_user.return_value = mock_admin_user
        mock_require_admin.return_value = None
        
        mock_service_instance = Mock()
        mock_service_instance.activate_tag.return_value = True
        mock_service_instance.get_tag_by_id.return_value = mock_tag
        mock_service.return_value = mock_service_instance
        
        # 发送请求
        response = client.post("/api/v1/admin/tags/1/activate")
        
        # 验证响应
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == 1

    @patch('src.services.tag_classification_service.admin_router.get_current_active_user')
    @patch('src.services.tag_classification_service.admin_router.require_admin')
    @patch('src.services.tag_classification_service.admin_router.get_tag_service')
    def test_get_popular_tags(self, mock_service, mock_require_admin, mock_get_user,
                            client, mock_admin_user, mock_tag):
        """测试获取热门标签"""
        # 设置mock
        mock_get_user.return_value = mock_admin_user
        mock_require_admin.return_value = None
        
        mock_service_instance = Mock()
        mock_service_instance.get_popular_tags.return_value = [mock_tag]
        mock_service.return_value = mock_service_instance
        
        # 发送请求
        response = client.get("/api/v1/admin/tags/popular")
        
        # 验证响应
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
