"""
标签服务测试（更新版）
"""

from decimal import Decimal

import pytest

from src.services.tag_classification_service.models import Tag
from src.services.tag_classification_service.schemas import (
    LifecycleStage,
    TagCreate,
    TagListQuery,
    TagUpdate,
)


class TestTagService:
    """标签服务测试类"""

    def test_create_tag_success(self, tag_service, sample_tag_classification):
        """测试成功创建标签"""
        tag_data = TagCreate(
            tag_name="人工智能",
            tag_code="artificial_intelligence",
            tag_slug="artificial-intelligence",
            classification_id=sample_tag_classification.id,
            color="#FF9800",
            icon="ai",
            base_weight=0.8,
            description="人工智能相关标签",
            synonyms=["AI", "机器学习"],
        )

        tag = tag_service.create_tag(tag_data)

        assert tag is not None
        assert tag.tag_name == "人工智能"
        assert tag.tag_code == "artificial_intelligence"
        assert tag.tag_slug == "artificial-intelligence"
        assert tag.classification_id == sample_tag_classification.id
        assert tag.level == 1
        assert tag.path == "artificial_intelligence"
        assert tag.color == "#FF9800"
        assert tag.icon == "ai"
        assert tag.base_weight == Decimal("0.8")
        assert tag.description == "人工智能相关标签"
        assert tag.is_active is True
        assert tag.is_system is False

    def test_create_tag_with_parent(self, tag_service, sample_tag, sample_tag_classification):
        """测试创建有父标签的标签"""
        tag_data = TagCreate(
            tag_name="移动开发",
            tag_code="mobile_development",
            tag_slug="mobile-development",
            classification_id=sample_tag_classification.id,
            parent_id=sample_tag.id,
        )

        tag = tag_service.create_tag(tag_data)

        assert tag.parent_id == sample_tag.id
        assert tag.level == 2
        assert tag.path == f"{sample_tag.path}.mobile_development"

    def test_create_tag_duplicate_code(self, tag_service, sample_tag, sample_tag_classification):
        """测试创建重复代码的标签应该失败"""
        tag_data = TagCreate(
            tag_name="重复的科技",
            tag_code="technology",  # 与sample_tag相同
            tag_slug="duplicate-technology",
            classification_id=sample_tag_classification.id,
        )

        with pytest.raises(ValueError, match="already exists"):
            tag_service.create_tag(tag_data)

    def test_create_tag_duplicate_slug(self, tag_service, sample_tag, sample_tag_classification):
        """测试创建重复slug的标签应该失败"""
        tag_data = TagCreate(
            tag_name="重复的科技",
            tag_code="duplicate_technology",
            tag_slug="technology",  # 与sample_tag相同
            classification_id=sample_tag_classification.id,
        )

        with pytest.raises(ValueError, match="already exists"):
            tag_service.create_tag(tag_data)

    def test_create_tag_invalid_classification(self, tag_service):
        """测试创建标签时指定不存在的分类应该失败"""
        tag_data = TagCreate(
            tag_name="无效分类",
            tag_code="invalid_classification",
            tag_slug="invalid-classification",
            classification_id=9999,  # 不存在的分类ID
        )

        with pytest.raises(ValueError, match="not found"):
            tag_service.create_tag(tag_data)

    def test_create_tag_invalid_parent(self, tag_service, sample_tag_classification):
        """测试创建标签时指定不存在的父标签应该失败"""
        tag_data = TagCreate(
            tag_name="无效父标签",
            tag_code="invalid_parent",
            tag_slug="invalid-parent",
            classification_id=sample_tag_classification.id,
            parent_id=9999,  # 不存在的父标签ID
        )

        with pytest.raises(ValueError, match="not found"):
            tag_service.create_tag(tag_data)

    def test_get_tag_by_id(self, tag_service, sample_tag):
        """测试根据ID获取标签"""
        tag = tag_service.get_tag_by_id(sample_tag.id)

        assert tag is not None
        assert tag.id == sample_tag.id
        assert tag.tag_name == sample_tag.tag_name

    def test_get_tag_by_id_with_relations(self, tag_service, sample_tag):
        """测试根据ID获取标签（包含关联对象）"""
        tag = tag_service.get_tag_by_id(sample_tag.id, include_relations=True)

        assert tag is not None
        assert tag.classification is not None

    def test_get_tag_by_code(self, tag_service, sample_tag):
        """测试根据代码获取标签"""
        tag = tag_service.get_tag_by_code(sample_tag.tag_code)

        assert tag is not None
        assert tag.tag_code == sample_tag.tag_code

    def test_search_tags(self, tag_service, sample_tag):
        """测试搜索标签"""
        tags = tag_service.search_tags("科技", limit=10)

        assert len(tags) >= 1
        assert any(tag.tag_name == "科技" for tag in tags)

    def test_list_tags_with_filters(self, tag_service, sample_tag, sample_tag_classification):
        """测试带过滤条件获取标签列表"""
        query = TagListQuery(
            classification_id=sample_tag_classification.id,
            is_active=True,
        )

        tags, total = tag_service.list_tags(query)

        assert total >= 1
        assert all(tag.classification_id == sample_tag_classification.id for tag in tags)
        assert all(tag.is_active for tag in tags)

    def test_update_tag(self, tag_service, sample_tag):
        """测试更新标签"""
        update_data = TagUpdate(
            tag_name="更新后的科技",
            description="更新后的描述",
            color="#E91E63",
        )

        updated_tag = tag_service.update_tag(sample_tag.id, update_data)

        assert updated_tag is not None
        assert updated_tag.tag_name == "更新后的科技"
        assert updated_tag.description == "更新后的描述"
        assert updated_tag.color == "#E91E63"

    def test_update_tag_nonexistent(self, tag_service):
        """测试更新不存在的标签"""
        update_data = TagUpdate(tag_name="不存在的标签")

        result = tag_service.update_tag(9999, update_data)

        assert result is None

    def test_delete_tag_success(self, tag_service, db_session, sample_tag_classification):
        """测试成功删除标签"""
        # 创建一个用于删除的标签
        tag = Tag(
            tag_name="待删除",
            tag_code="to_delete",
            tag_slug="to-delete",
            classification_id=sample_tag_classification.id,
            path="to_delete",
        )
        db_session.add(tag)
        db_session.commit()
        db_session.refresh(tag)

        result = tag_service.delete_tag(tag.id)

        assert result is True

        # 验证标签已被删除
        deleted_tag = tag_service.get_tag_by_id(tag.id)
        assert deleted_tag is None

    def test_delete_tag_nonexistent(self, tag_service):
        """测试删除不存在的标签"""
        result = tag_service.delete_tag(9999)

        assert result is False

    def test_get_popular_tags(self, tag_service, db_session, sample_tag_classification):
        """测试获取热门标签"""
        # 创建一些有使用次数的标签
        tag1 = Tag(
            tag_name="热门1",
            tag_code="popular1",
            tag_slug="popular-1",
            classification_id=sample_tag_classification.id,
            path="popular1",
            usage_count=100,
        )
        tag2 = Tag(
            tag_name="热门2",
            tag_code="popular2",
            tag_slug="popular-2",
            classification_id=sample_tag_classification.id,
            path="popular2",
            usage_count=200,
        )
        db_session.add_all([tag1, tag2])
        db_session.commit()

        popular_tags = tag_service.get_popular_tags(limit=10)

        assert len(popular_tags) >= 2
        # 验证按使用次数排序
        assert popular_tags[0].usage_count >= popular_tags[1].usage_count

    def test_get_trending_tags(self, tag_service, db_session, sample_tag_classification):
        """测试获取趋势标签"""
        # 创建一个有日使用次数的标签
        tag = Tag(
            tag_name="趋势标签",
            tag_code="trending_tag",
            tag_slug="trending-tag",
            classification_id=sample_tag_classification.id,
            path="trending_tag",
            daily_usage_count=50,
        )
        db_session.add(tag)
        db_session.commit()

        trending_tags = tag_service.get_trending_tags(limit=10)

        assert len(trending_tags) >= 1
        assert any(tag.tag_name == "趋势标签" for tag in trending_tags)

    def test_activate_tag(self, tag_service, db_session, sample_tag_classification):
        """测试激活标签"""
        # 创建一个非活跃的标签
        tag = Tag(
            tag_name="非活跃标签",
            tag_code="inactive_tag",
            tag_slug="inactive-tag",
            classification_id=sample_tag_classification.id,
            path="inactive_tag",
            is_active=False,
        )
        db_session.add(tag)
        db_session.commit()
        db_session.refresh(tag)

        result = tag_service.activate_tag(tag.id)

        assert result is True
        
        # 验证标签已被激活
        activated_tag = tag_service.get_tag_by_id(tag.id)
        assert activated_tag.is_active is True

    def test_deactivate_tag(self, tag_service, sample_tag):
        """测试停用标签"""
        result = tag_service.deactivate_tag(sample_tag.id)

        assert result is True
        
        # 验证标签已被停用
        deactivated_tag = tag_service.get_tag_by_id(sample_tag.id)
        assert deactivated_tag.is_active is False
