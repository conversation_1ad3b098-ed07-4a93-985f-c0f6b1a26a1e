"""
简单测试标签分类服务的管理端API路由
"""

import pytest
from fastapi.testclient import TestClient
from unittest.mock import Mock, patch

from src.main import app_b


@pytest.fixture
def client():
    """创建测试客户端"""
    return TestClient(app_b)


def test_admin_router_routes_exist():
    """测试管理端路由是否存在"""
    client = TestClient(app_b)
    
    # 测试路由是否存在（不测试权限，只测试路由定义）
    # 这些请求会返回401或403，但不会返回404
    
    # 测试TagClassification相关路由
    response = client.get("/api/v1/admin/tags/classifications")
    assert response.status_code in [401, 403, 422]  # 不是404说明路由存在
    
    response = client.get("/api/v1/admin/tags/classifications/1")
    assert response.status_code in [401, 403, 422]
    
    response = client.post("/api/v1/admin/tags/classifications", json={})
    assert response.status_code in [401, 403, 422]
    
    response = client.get("/api/v1/admin/tags/classifications/tree")
    assert response.status_code in [401, 403, 422]
    
    # 测试Tag相关路由
    response = client.get("/api/v1/admin/tags")
    assert response.status_code in [401, 403, 422]
    
    response = client.get("/api/v1/admin/tags/1")
    assert response.status_code in [401, 403, 422]
    
    response = client.get("/api/v1/admin/tags/search?q=test")
    assert response.status_code in [401, 403, 422]
    
    response = client.post("/api/v1/admin/tags", json={})
    assert response.status_code in [401, 403, 422]
    
    # 测试标签激活/停用路由
    response = client.post("/api/v1/admin/tags/1/activate")
    assert response.status_code in [401, 403, 422]
    
    response = client.post("/api/v1/admin/tags/1/deactivate")
    assert response.status_code in [401, 403, 422]
    
    # 测试分类维度路由
    response = client.get("/api/v1/admin/classifications/dimensions")
    assert response.status_code in [401, 403, 422]
    
    response = client.post("/api/v1/admin/classifications/dimensions", json={})
    assert response.status_code in [401, 403, 422]
    
    # 测试分类值路由
    response = client.get("/api/v1/admin/classifications/values")
    assert response.status_code in [401, 403, 422]
    
    response = client.post("/api/v1/admin/classifications/values", json={})
    assert response.status_code in [401, 403, 422]
    
    # 测试统计路由
    response = client.get("/api/v1/admin/tags/statistics")
    assert response.status_code in [401, 403, 422]
    
    response = client.get("/api/v1/admin/tags/popular")
    assert response.status_code in [401, 403, 422]
    
    response = client.get("/api/v1/admin/tags/trending")
    assert response.status_code in [401, 403, 422]


def test_old_routes_not_exist():
    """测试旧的TagType和TagCategory路由不再存在"""
    client = TestClient(app_b)
    
    # 这些旧路由应该返回404
    response = client.get("/api/v1/admin/tags/types")
    assert response.status_code == 404
    
    response = client.get("/api/v1/admin/tags/categories")
    assert response.status_code == 404
    
    response = client.post("/api/v1/admin/tags/types", json={})
    assert response.status_code == 404
    
    response = client.post("/api/v1/admin/tags/categories", json={})
    assert response.status_code == 404


@patch('src.services.tag_classification_service.admin_router.require_admin')
@patch('src.services.tag_classification_service.admin_router.get_tag_classification_service')
def test_get_tag_classifications_with_mock(mock_service, mock_require_admin):
    """测试获取标签分类列表（使用mock）"""
    # 设置mock
    mock_require_admin.return_value = Mock(id=1, username="admin")
    
    mock_service_instance = Mock()
    mock_service_instance.get_classifications.return_value = []
    mock_service.return_value = mock_service_instance
    
    client = TestClient(app_b)
    
    # 这个测试可能仍然会失败，因为依赖注入的复杂性
    # 但至少可以验证路由的基本结构
    response = client.get("/api/v1/admin/tags/classifications")
    
    # 即使失败，也不应该是404错误
    assert response.status_code != 404


def test_api_documentation():
    """测试API文档是否包含新的路由"""
    client = TestClient(app_b)
    
    # 获取OpenAPI文档
    response = client.get("/openapi.json")
    assert response.status_code == 200
    
    openapi_doc = response.json()
    paths = openapi_doc.get("paths", {})
    
    # 验证新的TagClassification路由存在
    assert "/api/v1/admin/tags/classifications" in paths
    assert "/api/v1/admin/tags/classifications/{classification_id}" in paths
    assert "/api/v1/admin/tags/classifications/tree" in paths
    
    # 验证旧的TagType和TagCategory路由不存在
    assert "/api/v1/admin/tags/types" not in paths
    assert "/api/v1/admin/tags/categories" not in paths
    
    # 验证标签管理路由存在
    assert "/api/v1/admin/tags" in paths
    assert "/api/v1/admin/tags/{tag_id}" in paths
    assert "/api/v1/admin/tags/search" in paths
    
    # 验证新增的激活/停用路由存在
    assert "/api/v1/admin/tags/{tag_id}/activate" in paths
    assert "/api/v1/admin/tags/{tag_id}/deactivate" in paths
    
    # 验证统计路由存在
    assert "/api/v1/admin/tags/statistics" in paths
    assert "/api/v1/admin/tags/popular" in paths
    assert "/api/v1/admin/tags/trending" in paths


def test_route_methods():
    """测试路由的HTTP方法"""
    client = TestClient(app_b)
    
    # 获取OpenAPI文档
    response = client.get("/openapi.json")
    openapi_doc = response.json()
    paths = openapi_doc.get("paths", {})
    
    # 验证TagClassification路由的方法
    classifications_path = paths.get("/api/v1/admin/tags/classifications", {})
    assert "get" in classifications_path  # 获取列表
    assert "post" in classifications_path  # 创建
    
    classification_detail_path = paths.get("/api/v1/admin/tags/classifications/{classification_id}", {})
    assert "get" in classification_detail_path  # 获取详情
    assert "put" in classification_detail_path  # 更新
    assert "delete" in classification_detail_path  # 删除
    
    # 验证标签激活/停用路由的方法
    activate_path = paths.get("/api/v1/admin/tags/{tag_id}/activate", {})
    assert "post" in activate_path
    
    deactivate_path = paths.get("/api/v1/admin/tags/{tag_id}/deactivate", {})
    assert "post" in deactivate_path


if __name__ == "__main__":
    # 运行基本的路由存在性测试
    test_admin_router_routes_exist()
    test_old_routes_not_exist()
    test_api_documentation()
    test_route_methods()
    print("All basic tests passed!")
