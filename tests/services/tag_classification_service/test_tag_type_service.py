"""
标签类型服务测试
"""

import pytest

from src.services.tag_classification_service.models import TagType
from src.services.tag_classification_service.schemas import (TagTypeCreate,
                                                             TagTypeUpdate)


class TestTagTypeService:
    """标签类型服务测试类"""

    def test_create_tag_type_success(self, tag_type_service):
        """测试成功创建标签类型"""
        # 准备测试数据
        tag_type_data = TagTypeCreate(
            type_code="entity",
            type_name="实体标签",
            description="实体标签类型",
            icon="entity",
            color="#9C27B0",
            sort_order=5,
        )

        # 执行创建操作
        tag_type = tag_type_service.create_tag_type(tag_type_data)

        # 验证结果
        assert tag_type is not None
        assert tag_type.type_code == "entity"
        assert tag_type.type_name == "实体标签"
        assert tag_type.description == "实体标签类型"
        assert tag_type.icon == "entity"
        assert tag_type.color == "#9C27B0"
        assert tag_type.sort_order == 5
        assert tag_type.is_active is True
        assert tag_type.id is not None
        assert tag_type.created_at is not None

    def test_create_tag_type_duplicate_code(self, tag_type_service, sample_tag_type):
        """测试创建重复代码的标签类型应该失败"""
        # 尝试创建相同代码的标签类型
        tag_type_data = TagTypeCreate(
            type_code="general",  # 与sample_tag_type相同
            type_name="另一个通用标签",
            description="重复的代码",
        )

        # 验证抛出异常
        with pytest.raises(
            ValueError, match="Tag type with code 'general' already exists"
        ):
            tag_type_service.create_tag_type(tag_type_data)

    def test_get_tag_type_by_id_success(self, tag_type_service, sample_tag_type):
        """测试根据ID获取标签类型成功"""
        tag_type = tag_type_service.get_tag_type_by_id(sample_tag_type.id)

        assert tag_type is not None
        assert tag_type.id == sample_tag_type.id
        assert tag_type.type_code == sample_tag_type.type_code

    def test_get_tag_type_by_id_not_found(self, tag_type_service):
        """测试获取不存在的标签类型"""
        tag_type = tag_type_service.get_tag_type_by_id(9999)
        assert tag_type is None

    def test_get_tag_type_by_code_success(self, tag_type_service, sample_tag_type):
        """测试根据代码获取标签类型成功"""
        tag_type = tag_type_service.get_tag_type_by_code("general")

        assert tag_type is not None
        assert tag_type.type_code == "general"
        assert tag_type.id == sample_tag_type.id

    def test_get_tag_type_by_code_not_found(self, tag_type_service):
        """测试获取不存在代码的标签类型"""
        tag_type = tag_type_service.get_tag_type_by_code("nonexistent")
        assert tag_type is None

    def test_get_tag_types_all(self, tag_type_service, sample_tag_type):
        """测试获取所有标签类型"""
        tag_types, total = tag_type_service.get_tag_types()

        assert total >= 1
        assert len(tag_types) >= 1
        assert any(tt.id == sample_tag_type.id for tt in tag_types)

    def test_get_tag_types_active_only(self, tag_type_service, db_session):
        """测试只获取活跃的标签类型"""
        # 创建一个非活跃的标签类型
        inactive_tag_type = TagType(
            type_code="inactive", type_name="非活跃标签", is_active=False
        )
        db_session.add(inactive_tag_type)
        db_session.commit()

        # 获取活跃的标签类型
        tag_types, total = tag_type_service.get_tag_types(is_active=True)

        # 验证结果中不包含非活跃的标签类型
        assert not any(tt.type_code == "inactive" for tt in tag_types)

    def test_get_tag_types_pagination(self, tag_type_service, db_session):
        """测试标签类型分页"""
        # 创建多个标签类型
        for i in range(5):
            tag_type = TagType(type_code=f"test_{i}", type_name=f"测试标签{i}")
            db_session.add(tag_type)
        db_session.commit()

        # 测试分页
        tag_types, total = tag_type_service.get_tag_types(skip=0, limit=3)

        assert total >= 5
        assert len(tag_types) <= 3

    def test_update_tag_type_success(self, tag_type_service, sample_tag_type):
        """测试成功更新标签类型"""
        update_data = TagTypeUpdate(
            type_name="更新的通用标签", description="更新的描述", color="#FF5722"
        )

        updated_tag_type = tag_type_service.update_tag_type(
            sample_tag_type.id, update_data
        )

        assert updated_tag_type is not None
        assert updated_tag_type.type_name == "更新的通用标签"
        assert updated_tag_type.description == "更新的描述"
        assert updated_tag_type.color == "#FF5722"
        assert updated_tag_type.type_code == sample_tag_type.type_code  # 不变
        assert updated_tag_type.updated_at is not None

    def test_update_tag_type_not_found(self, tag_type_service):
        """测试更新不存在的标签类型"""
        update_data = TagTypeUpdate(type_name="不存在的标签")

        result = tag_type_service.update_tag_type(9999, update_data)
        assert result is None

    def test_update_tag_type_partial(self, tag_type_service, sample_tag_type):
        """测试部分更新标签类型"""
        original_description = sample_tag_type.description

        update_data = TagTypeUpdate(type_name="部分更新的标签")

        updated_tag_type = tag_type_service.update_tag_type(
            sample_tag_type.id, update_data
        )

        assert updated_tag_type.type_name == "部分更新的标签"
        assert (
            updated_tag_type.description == original_description
        )  # 未更新的字段保持不变

    def test_delete_tag_type_success(self, tag_type_service, db_session):
        """测试成功删除标签类型"""
        # 创建一个新的标签类型用于删除
        tag_type = TagType(type_code="to_delete", type_name="待删除标签")
        db_session.add(tag_type)
        db_session.commit()

        tag_type_id = tag_type.id

        # 执行删除
        result = tag_type_service.delete_tag_type(tag_type_id)

        assert result is True

        # 验证标签类型已被删除
        deleted_tag_type = tag_type_service.get_tag_type_by_id(tag_type_id)
        assert deleted_tag_type is None

    def test_delete_tag_type_not_found(self, tag_type_service):
        """测试删除不存在的标签类型"""
        result = tag_type_service.delete_tag_type(9999)
        assert result is False

    def test_delete_tag_type_with_tags(
        self, tag_type_service, sample_tag_type, sample_tag
    ):
        """测试删除有关联标签的标签类型应该失败"""
        # sample_tag关联了sample_tag_type，所以删除应该失败
        with pytest.raises(ValueError, match="Cannot delete tag type"):
            tag_type_service.delete_tag_type(sample_tag_type.id)

    def test_create_tag_type_minimal_data(self, tag_type_service):
        """测试使用最少数据创建标签类型"""
        tag_type_data = TagTypeCreate(type_code="minimal", type_name="最小数据标签")

        tag_type = tag_type_service.create_tag_type(tag_type_data)

        assert tag_type is not None
        assert tag_type.type_code == "minimal"
        assert tag_type.type_name == "最小数据标签"
        assert tag_type.description is None
        assert tag_type.icon is None
        assert tag_type.color is None
        assert tag_type.sort_order == 0  # 默认值
        assert tag_type.is_active is True  # 默认值

    def test_get_tag_types_ordering(self, tag_type_service, db_session):
        """测试标签类型排序"""
        # 创建具有不同排序权重的标签类型
        tag_type1 = TagType(type_code="sort1", type_name="排序1", sort_order=1)
        tag_type2 = TagType(type_code="sort2", type_name="排序2", sort_order=10)
        tag_type3 = TagType(type_code="sort3", type_name="排序3", sort_order=5)

        db_session.add_all([tag_type1, tag_type2, tag_type3])
        db_session.commit()

        # 获取标签类型
        tag_types, _ = tag_type_service.get_tag_types()

        # 验证排序（sort_order降序）
        sort_orders = [
            tt.sort_order for tt in tag_types if tt.type_code.startswith("sort")
        ]
        assert sort_orders == sorted(sort_orders, reverse=True)
