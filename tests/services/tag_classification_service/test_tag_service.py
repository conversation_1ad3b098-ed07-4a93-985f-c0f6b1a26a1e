"""
标签服务测试
"""

from decimal import Decimal

import pytest

from src.services.tag_classification_service.models import Tag
from src.services.tag_classification_service.schemas import (LifecycleStage,
                                                             TagCreate,
                                                             TagListQuery,
                                                             TagUpdate)


class TestTagService:
    """标签服务测试类"""

    def test_create_tag_success(
        self, tag_service, sample_tag_classification
    ):
        """测试成功创建标签"""
        tag_data = TagCreate(
            tag_name="人工智能",
            tag_code="artificial_intelligence",
            tag_slug="artificial-intelligence",
            classification_id=sample_tag_classification.id,
            color="#FF9800",
            icon="ai",
            base_weight=Decimal("0.9"),
            description="人工智能相关标签",
            synonyms=["AI", "机器学习", "深度学习"],
            lifecycle_stage=LifecycleStage.ACTIVE,
        )

        tag = tag_service.create_tag(tag_data)

        assert tag is not None
        assert tag.tag_name == "人工智能"
        assert tag.tag_code == "artificial_intelligence"
        assert tag.tag_slug == "artificial-intelligence"
        assert tag.tag_type_id == sample_tag_type.id
        assert tag.tag_category_id == sample_tag_category.id
        assert tag.level == 1
        assert tag.path == "artificial_intelligence"
        assert tag.color == "#FF9800"
        assert tag.icon == "ai"
        assert tag.base_weight == Decimal("0.9")
        assert tag.description == "人工智能相关标签"
        # synonyms在SQLite中存储为JSON字符串
        import json

        expected_synonyms = ["AI", "机器学习", "深度学习"]
        assert json.loads(tag.synonyms) == expected_synonyms
        assert tag.lifecycle_stage == "active"

    def test_create_tag_with_parent(self, tag_service, sample_tag, sample_tag_type):
        """测试创建有父标签的标签"""
        tag_data = TagCreate(
            tag_name="移动开发",
            tag_code="mobile_development",
            tag_slug="mobile-development",
            tag_type_id=sample_tag_type.id,
            parent_id=sample_tag.id,
        )

        tag = tag_service.create_tag(tag_data)

        assert tag.parent_id == sample_tag.id
        assert tag.level == sample_tag.level + 1
        assert tag.path == f"{sample_tag.path}.mobile_development"

    def test_create_tag_duplicate_code(self, tag_service, sample_tag, sample_tag_type):
        """测试创建重复代码的标签应该失败"""
        tag_data = TagCreate(
            tag_name="重复的科技",
            tag_code="technology",  # 与sample_tag相同
            tag_slug="duplicate-technology",
            tag_type_id=sample_tag_type.id,
        )

        with pytest.raises(
            ValueError, match="Tag with code 'technology' already exists"
        ):
            tag_service.create_tag(tag_data)

    def test_create_tag_duplicate_slug(self, tag_service, sample_tag, sample_tag_type):
        """测试创建重复slug的标签应该失败"""
        tag_data = TagCreate(
            tag_name="重复的科技",
            tag_code="duplicate_technology",
            tag_slug="technology",  # 与sample_tag相同
            tag_type_id=sample_tag_type.id,
        )

        with pytest.raises(
            ValueError, match="Tag with slug 'technology' already exists"
        ):
            tag_service.create_tag(tag_data)

    def test_create_tag_invalid_tag_type(self, tag_service):
        """测试创建标签时指定不存在的标签类型应该失败"""
        tag_data = TagCreate(
            tag_name="无效类型",
            tag_code="invalid_type",
            tag_slug="invalid-type",
            tag_type_id=9999,  # 不存在的类型ID
        )

        with pytest.raises(ValueError, match="Tag type with ID 9999 not found"):
            tag_service.create_tag(tag_data)

    def test_create_tag_invalid_parent(self, tag_service, sample_tag_type):
        """测试创建标签时指定不存在的父标签应该失败"""
        tag_data = TagCreate(
            tag_name="无效父标签",
            tag_code="invalid_parent",
            tag_slug="invalid-parent",
            tag_type_id=sample_tag_type.id,
            parent_id=9999,  # 不存在的父标签ID
        )

        with pytest.raises(ValueError, match="Parent tag with ID 9999 not found"):
            tag_service.create_tag(tag_data)

    def test_get_tag_by_id_success(self, tag_service, sample_tag):
        """测试根据ID获取标签成功"""
        tag = tag_service.get_tag_by_id(sample_tag.id)

        assert tag is not None
        assert tag.id == sample_tag.id
        assert tag.tag_code == sample_tag.tag_code

    def test_get_tag_by_id_with_relations(self, tag_service, sample_tag):
        """测试根据ID获取标签并包含关联对象"""
        tag = tag_service.get_tag_by_id(sample_tag.id, include_relations=True)

        assert tag is not None
        assert hasattr(tag, "tag_type")
        assert hasattr(tag, "tag_category")

    def test_get_tag_by_id_not_found(self, tag_service):
        """测试获取不存在的标签"""
        tag = tag_service.get_tag_by_id(9999)
        assert tag is None

    def test_get_tag_by_code_success(self, tag_service, sample_tag):
        """测试根据代码获取标签成功"""
        tag = tag_service.get_tag_by_code("technology")

        assert tag is not None
        assert tag.tag_code == "technology"
        assert tag.id == sample_tag.id

    def test_get_tag_by_code_not_found(self, tag_service):
        """测试获取不存在代码的标签"""
        tag = tag_service.get_tag_by_code("nonexistent")
        assert tag is None

    def test_search_tags_by_name(self, tag_service, sample_tag):
        """测试按名称搜索标签"""
        tags = tag_service.search_tags("科技")

        assert len(tags) >= 1
        assert any(tag.id == sample_tag.id for tag in tags)

    def test_search_tags_by_code(self, tag_service, sample_tag):
        """测试按代码搜索标签"""
        tags = tag_service.search_tags("technology")

        assert len(tags) >= 1
        assert any(tag.id == sample_tag.id for tag in tags)

    def test_search_tags_by_description(self, tag_service, sample_tag):
        """测试按描述搜索标签"""
        tags = tag_service.search_tags("相关")

        assert len(tags) >= 1
        assert any(tag.id == sample_tag.id for tag in tags)

    def test_search_tags_no_results(self, tag_service):
        """测试搜索无结果"""
        tags = tag_service.search_tags("不存在的关键词")
        assert len(tags) == 0

    def test_get_popular_tags(self, tag_service, db_session, sample_tag_type):
        """测试获取热门标签"""
        # 创建一些使用次数不同的标签
        tag1 = Tag(
            tag_name="热门标签1",
            tag_code="popular1",
            tag_slug="popular-1",
            tag_type_id=sample_tag_type.id,
            path="popular1",
            usage_count=100,
        )
        tag2 = Tag(
            tag_name="热门标签2",
            tag_code="popular2",
            tag_slug="popular-2",
            tag_type_id=sample_tag_type.id,
            path="popular2",
            usage_count=200,
        )
        db_session.add_all([tag1, tag2])
        db_session.commit()

        tags = tag_service.get_popular_tags(limit=10, time_range="all")

        # 验证按使用次数排序
        usage_counts = [tag.usage_count for tag in tags[:2]]
        assert usage_counts == sorted(usage_counts, reverse=True)

    def test_get_popular_tags_daily(self, tag_service, db_session, sample_tag_type):
        """测试按每日使用次数获取热门标签"""
        tag = Tag(
            tag_name="每日热门",
            tag_code="daily_popular",
            tag_slug="daily-popular",
            tag_type_id=sample_tag_type.id,
            path="daily_popular",
            daily_usage_count=50,
        )
        db_session.add(tag)
        db_session.commit()

        tags = tag_service.get_popular_tags(limit=10, time_range="daily")

        assert len(tags) >= 1

    def test_get_tags_with_filters(
        self, tag_service, sample_tag, sample_tag_type, sample_tag_category
    ):
        """测试带过滤条件获取标签列表"""
        query = TagListQuery(
            tag_type_id=sample_tag_type.id,
            tag_category_id=sample_tag_category.id,
            is_active=True,
        )

        tags, total = tag_service.get_tags(query)

        assert total >= 1
        assert all(tag.tag_type_id == sample_tag_type.id for tag in tags)
        assert all(tag.tag_category_id == sample_tag_category.id for tag in tags)
        assert all(tag.is_active for tag in tags)

    def test_get_tags_with_search(self, tag_service, sample_tag):
        """测试带搜索条件获取标签列表"""
        query = TagListQuery(search="科技")

        tags, total = tag_service.get_tags(query)

        assert total >= 1
        assert any(tag.id == sample_tag.id for tag in tags)

    def test_get_tags_with_parent_filter(
        self, tag_service, db_session, sample_tag, sample_tag_type
    ):
        """测试按父标签过滤"""
        # 创建子标签
        child_tag = Tag(
            tag_name="子标签",
            tag_code="child_tag",
            tag_slug="child-tag",
            tag_type_id=sample_tag_type.id,
            parent_id=sample_tag.id,
            level=2,
            path=f"{sample_tag.path}.child_tag",
        )
        db_session.add(child_tag)
        db_session.commit()

        query = TagListQuery(parent_id=sample_tag.id)
        tags, total = tag_service.get_tags(query)

        assert total >= 1
        assert all(tag.parent_id == sample_tag.id for tag in tags)

    def test_get_tags_ordering(self, tag_service, db_session, sample_tag_type):
        """测试标签排序"""
        # 创建具有不同权重的标签
        tag1 = Tag(
            tag_name="权重1",
            tag_code="weight1",
            tag_slug="weight-1",
            tag_type_id=sample_tag_type.id,
            path="weight1",
            base_weight=Decimal("0.3"),
        )
        tag2 = Tag(
            tag_name="权重2",
            tag_code="weight2",
            tag_slug="weight-2",
            tag_type_id=sample_tag_type.id,
            path="weight2",
            base_weight=Decimal("0.9"),
        )
        db_session.add_all([tag1, tag2])
        db_session.commit()

        # 手动设置computed_weight字段
        tag1.computed_weight = Decimal("0.3")
        tag2.computed_weight = Decimal("0.9")
        db_session.commit()

        query = TagListQuery(order_by="weight", order_direction="desc")
        tags, _ = tag_service.get_tags(query)

        # 验证权重排序
        weights = [
            tag.computed_weight for tag in tags[:2] if tag.tag_code.startswith("weight")
        ]
        assert len(weights) == 2
        # 确保computed_weight不为None，使用default值进行比较
        weight1 = float(weights[0]) if weights[0] is not None else 1.0
        weight2 = float(weights[1]) if weights[1] is not None else 1.0
        assert weight1 >= weight2

    def test_update_tag_success(self, tag_service, sample_tag):
        """测试成功更新标签"""
        update_data = TagUpdate(
            tag_name="更新的科技", description="更新的描述", color="#E91E63"
        )

        updated_tag = tag_service.update_tag(sample_tag.id, update_data)

        assert updated_tag is not None
        assert updated_tag.tag_name == "更新的科技"
        assert updated_tag.description == "更新的描述"
        assert updated_tag.color == "#E91E63"
        assert updated_tag.tag_code == sample_tag.tag_code  # 不变

    def test_update_tag_parent_change(
        self, tag_service, db_session, sample_tag, sample_tag_type
    ):
        """测试更新标签的父标签"""
        # 创建新的父标签
        new_parent = Tag(
            tag_name="新父标签",
            tag_code="new_parent",
            tag_slug="new-parent",
            tag_type_id=sample_tag_type.id,
            path="new_parent",
            level=1,
        )
        db_session.add(new_parent)
        db_session.commit()

        update_data = TagUpdate(parent_id=new_parent.id)
        updated_tag = tag_service.update_tag(sample_tag.id, update_data)

        assert updated_tag.parent_id == new_parent.id
        assert updated_tag.level == new_parent.level + 1
        assert updated_tag.path == f"{new_parent.path}.{sample_tag.tag_code}"

    def test_update_tag_not_found(self, tag_service):
        """测试更新不存在的标签"""
        update_data = TagUpdate(tag_name="不存在的标签")

        result = tag_service.update_tag(9999, update_data)
        assert result is None

    def test_delete_tag_success(self, tag_service, db_session, sample_tag_type):
        """测试成功删除标签"""
        # 创建一个新标签用于删除
        tag = Tag(
            tag_name="待删除标签",
            tag_code="to_delete",
            tag_slug="to-delete",
            tag_type_id=sample_tag_type.id,
            path="to_delete",
        )
        db_session.add(tag)
        db_session.commit()

        tag_id = tag.id

        result = tag_service.delete_tag(tag_id)
        assert result is True

        # 验证标签已被删除
        deleted_tag = tag_service.get_tag_by_id(tag_id)
        assert deleted_tag is None

    def test_delete_tag_not_found(self, tag_service):
        """测试删除不存在的标签"""
        result = tag_service.delete_tag(9999)
        assert result is False

    def test_delete_system_tag(self, tag_service, db_session, sample_tag_type):
        """测试删除系统标签应该失败"""
        # 创建系统标签
        system_tag = Tag(
            tag_name="系统标签",
            tag_code="system_tag",
            tag_slug="system-tag",
            tag_type_id=sample_tag_type.id,
            path="system_tag",
            is_system=True,
        )
        db_session.add(system_tag)
        db_session.commit()

        with pytest.raises(ValueError, match="Cannot delete system tag"):
            tag_service.delete_tag(system_tag.id)

    def test_delete_tag_with_children(
        self, tag_service, db_session, sample_tag, sample_tag_type
    ):
        """测试删除有子标签的标签应该失败"""
        # 创建子标签
        child_tag = Tag(
            tag_name="子标签",
            tag_code="child_tag",
            tag_slug="child-tag",
            tag_type_id=sample_tag_type.id,
            parent_id=sample_tag.id,
            level=2,
            path=f"{sample_tag.path}.child_tag",
        )
        db_session.add(child_tag)
        db_session.commit()

        with pytest.raises(ValueError, match="Cannot delete tag"):
            tag_service.delete_tag(sample_tag.id)

    def test_increment_tag_usage_success(self, tag_service, sample_tag):
        """测试成功增加标签使用次数"""
        original_usage = sample_tag.usage_count
        original_daily_usage = sample_tag.daily_usage_count

        result = tag_service.increment_tag_usage(sample_tag.id)
        assert result is True

        # 刷新对象以获取最新数据
        tag_service.db.refresh(sample_tag)

        assert sample_tag.usage_count == original_usage + 1
        assert sample_tag.daily_usage_count == original_daily_usage + 1
        assert sample_tag.last_used_at is not None
