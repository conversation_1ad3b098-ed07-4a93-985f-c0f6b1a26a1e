"""
测试标签分类服务管理端API的权限控制
"""

import pytest
from fastapi.testclient import TestClient

from src.main import app_b


@pytest.fixture
def client():
    """创建测试客户端"""
    return TestClient(app_b)


def test_admin_router_permissions_structure():
    """测试管理端路由的权限结构"""
    client = TestClient(app_b)
    
    # 获取OpenAPI文档
    response = client.get("/openapi.json")
    assert response.status_code == 200
    
    openapi_doc = response.json()
    paths = openapi_doc.get("paths", {})
    
    # 验证TagClassification路由存在且有正确的权限描述
    classifications_path = "/api/v1/admin/tags/classifications"
    assert classifications_path in paths
    
    # 检查GET方法的描述
    get_method = paths[classifications_path].get("get", {})
    assert "标签分类读取权限" in get_method.get("description", "")
    
    # 检查POST方法的描述
    post_method = paths[classifications_path].get("post", {})
    assert "标签分类创建权限" in post_method.get("description", "")
    
    # 验证标签路由存在且有正确的权限描述
    tags_path = "/api/v1/admin/tags"
    assert tags_path in paths
    
    # 检查标签GET方法的描述
    tags_get_method = paths[tags_path].get("get", {})
    assert "标签读取权限" in tags_get_method.get("description", "")
    
    # 检查标签POST方法的描述
    tags_post_method = paths[tags_path].get("post", {})
    assert "标签创建权限" in tags_post_method.get("description", "")


def test_tag_classification_routes_permissions():
    """测试标签分类路由的权限要求"""
    client = TestClient(app_b)
    
    # 测试所有TagClassification路由都需要权限（返回401或403，不是404）
    routes_to_test = [
        ("GET", "/api/v1/admin/tags/classifications"),
        ("GET", "/api/v1/admin/tags/classifications/1"),
        ("POST", "/api/v1/admin/tags/classifications"),
        ("PUT", "/api/v1/admin/tags/classifications/1"),
        ("DELETE", "/api/v1/admin/tags/classifications/1"),
        ("GET", "/api/v1/admin/tags/classifications/tree"),
    ]
    
    for method, path in routes_to_test:
        if method == "GET":
            response = client.get(path)
        elif method == "POST":
            response = client.post(path, json={})
        elif method == "PUT":
            response = client.put(path, json={})
        elif method == "DELETE":
            response = client.delete(path)
        
        # 应该返回401或403（权限问题），而不是404（路由不存在）
        assert response.status_code in [401, 403, 422], f"Route {method} {path} returned {response.status_code}"


def test_tag_routes_permissions():
    """测试标签路由的权限要求"""
    client = TestClient(app_b)
    
    # 测试所有Tag路由都需要权限
    routes_to_test = [
        ("GET", "/api/v1/admin/tags"),
        ("GET", "/api/v1/admin/tags/1"),
        ("POST", "/api/v1/admin/tags"),
        ("PUT", "/api/v1/admin/tags/1"),
        ("DELETE", "/api/v1/admin/tags/1"),
    ]
    
    for method, path in routes_to_test:
        if method == "GET":
            response = client.get(path)
        elif method == "POST":
            response = client.post(path, json={})
        elif method == "PUT":
            response = client.put(path, json={})
        elif method == "DELETE":
            response = client.delete(path)
        
        # 应该返回401或403（权限问题），而不是404（路由不存在）
        assert response.status_code in [401, 403, 422], f"Route {method} {path} returned {response.status_code}"


def test_classification_dimension_routes_permissions():
    """测试分类维度路由的权限要求"""
    client = TestClient(app_b)
    
    # 测试分类维度路由需要权限
    response = client.get("/api/v1/admin/classifications/dimensions")
    assert response.status_code in [401, 403, 422]


def test_permission_granularity():
    """测试权限的细粒度控制"""
    # 这个测试验证不同操作需要不同的权限
    # 在实际环境中，这些权限应该是：
    # - tag.classification.read: 读取标签分类
    # - tag.classification.create: 创建标签分类
    # - tag.classification.update: 更新标签分类
    # - tag.classification.delete: 删除标签分类
    # - tag.tag.read: 读取标签
    # - tag.tag.create: 创建标签
    # - tag.tag.update: 更新标签
    # - tag.tag.delete: 删除标签
    # - classification.dimension.read: 读取分类维度
    
    # 这里我们只能测试路由的存在性，实际的权限测试需要mock用户和权限系统
    client = TestClient(app_b)
    
    # 获取OpenAPI文档来验证权限描述
    response = client.get("/openapi.json")
    assert response.status_code == 200
    
    openapi_doc = response.json()
    paths = openapi_doc.get("paths", {})
    
    # 验证不同操作有不同的权限描述
    classifications_path = paths.get("/api/v1/admin/tags/classifications", {})
    
    # GET操作应该需要读取权限
    get_desc = classifications_path.get("get", {}).get("description", "")
    assert "读取权限" in get_desc
    
    # POST操作应该需要创建权限
    post_desc = classifications_path.get("post", {}).get("description", "")
    assert "创建权限" in post_desc
    
    # PUT操作应该需要更新权限
    classification_detail_path = paths.get("/api/v1/admin/tags/classifications/{classification_id}", {})
    put_desc = classification_detail_path.get("put", {}).get("description", "")
    assert "更新权限" in put_desc
    
    # DELETE操作应该需要删除权限
    delete_desc = classification_detail_path.get("delete", {}).get("description", "")
    assert "删除权限" in delete_desc


def test_route_security_consistency():
    """测试路由安全性的一致性"""
    client = TestClient(app_b)
    
    # 获取OpenAPI文档
    response = client.get("/openapi.json")
    assert response.status_code == 200
    
    openapi_doc = response.json()
    paths = openapi_doc.get("paths", {})
    
    # 检查所有admin路由都有安全要求
    admin_paths = [path for path in paths.keys() if path.startswith("/api/v1/admin/")]
    
    for path in admin_paths:
        path_info = paths[path]
        for method, method_info in path_info.items():
            if method.upper() in ["GET", "POST", "PUT", "DELETE"]:
                # 每个方法都应该有描述，并且描述中应该包含权限相关的信息
                description = method_info.get("description", "")
                assert "权限" in description, f"Route {method.upper()} {path} missing permission description"


if __name__ == "__main__":
    # 运行基本的权限测试
    test_admin_router_permissions_structure()
    test_tag_classification_routes_permissions()
    test_tag_routes_permissions()
    test_classification_dimension_routes_permissions()
    test_permission_granularity()
    test_route_security_consistency()
    print("All permission tests passed!")
