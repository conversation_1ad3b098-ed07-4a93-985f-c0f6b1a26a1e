"""
测试标签分类服务管理端API的完整接口
验证所有分类维度和分类值的增删改查接口
"""

import pytest
from fastapi.testclient import TestClient

from src.main import app_b


@pytest.fixture
def client():
    """创建测试客户端"""
    return TestClient(app_b)


def test_all_admin_routes_exist():
    """测试所有管理端路由是否存在"""
    client = TestClient(app_b)
    
    # 定义所有期望的路由
    expected_routes = {
        # TagClassification 路由 (6个)
        ("GET", "/api/v1/admin/tags/classifications"),
        ("GET", "/api/v1/admin/tags/classifications/{classification_id}"),
        ("POST", "/api/v1/admin/tags/classifications"),
        ("PUT", "/api/v1/admin/tags/classifications/{classification_id}"),
        ("DELETE", "/api/v1/admin/tags/classifications/{classification_id}"),
        ("GET", "/api/v1/admin/tags/classifications/tree"),
        
        # Tag 路由 (5个)
        ("GET", "/api/v1/admin/tags"),
        ("GET", "/api/v1/admin/tags/{tag_id}"),
        ("POST", "/api/v1/admin/tags"),
        ("PUT", "/api/v1/admin/tags/{tag_id}"),
        ("DELETE", "/api/v1/admin/tags/{tag_id}"),
        
        # Classification Dimension 路由 (5个)
        ("GET", "/api/v1/admin/classifications/dimensions"),
        ("GET", "/api/v1/admin/classifications/dimensions/{dimension_id}"),
        ("POST", "/api/v1/admin/classifications/dimensions"),
        ("PUT", "/api/v1/admin/classifications/dimensions/{dimension_id}"),
        ("DELETE", "/api/v1/admin/classifications/dimensions/{dimension_id}"),
        
        # Classification Value 路由 (5个)
        ("GET", "/api/v1/admin/classifications/values"),
        ("GET", "/api/v1/admin/classifications/values/{value_id}"),
        ("POST", "/api/v1/admin/classifications/values"),
        ("PUT", "/api/v1/admin/classifications/values/{value_id}"),
        ("DELETE", "/api/v1/admin/classifications/values/{value_id}"),
        
        # Statistics 路由 (3个)
        ("GET", "/api/v1/admin/tags/statistics"),
        ("GET", "/api/v1/admin/tags/popular"),
        ("GET", "/api/v1/admin/tags/trending"),
    }
    
    print(f"Testing {len(expected_routes)} expected routes...")
    
    # 测试每个路由
    for method, path in expected_routes:
        # 将路径参数替换为实际值
        test_path = path.replace("{classification_id}", "1").replace("{tag_id}", "1").replace("{dimension_id}", "1").replace("{value_id}", "1")
        
        if method == "GET":
            response = client.get(test_path)
        elif method == "POST":
            response = client.post(test_path, json={})
        elif method == "PUT":
            response = client.put(test_path, json={})
        elif method == "DELETE":
            response = client.delete(test_path)
        
        # 应该返回401/403/422（权限或参数问题），而不是404（路由不存在）
        assert response.status_code in [401, 403, 422], f"Route {method} {path} returned {response.status_code}, expected 401/403/422"
        print(f"✓ {method} {path}")


def test_classification_dimension_crud_routes():
    """测试分类维度CRUD路由"""
    client = TestClient(app_b)
    
    # 测试分类维度的所有CRUD操作
    crud_routes = [
        ("GET", "/api/v1/admin/classifications/dimensions", "获取分类维度列表"),
        ("GET", "/api/v1/admin/classifications/dimensions/1", "获取分类维度详情"),
        ("POST", "/api/v1/admin/classifications/dimensions", "创建分类维度"),
        ("PUT", "/api/v1/admin/classifications/dimensions/1", "更新分类维度"),
        ("DELETE", "/api/v1/admin/classifications/dimensions/1", "删除分类维度"),
    ]
    
    for method, path, description in crud_routes:
        if method == "GET":
            response = client.get(path)
        elif method == "POST":
            response = client.post(path, json={
                "dimension_name": "测试维度",
                "dimension_code": "test_dimension",
                "description": "测试分类维度"
            })
        elif method == "PUT":
            response = client.put(path, json={
                "dimension_name": "更新的测试维度"
            })
        elif method == "DELETE":
            response = client.delete(path)
        
        # 验证路由存在（不是404）
        assert response.status_code != 404, f"{description} 路由不存在"
        assert response.status_code in [401, 403, 422], f"{description} 返回了意外的状态码: {response.status_code}"
        print(f"✓ {description}: {method} {path}")


def test_classification_value_crud_routes():
    """测试分类值CRUD路由"""
    client = TestClient(app_b)
    
    # 测试分类值的所有CRUD操作
    crud_routes = [
        ("GET", "/api/v1/admin/classifications/values", "获取分类值列表"),
        ("GET", "/api/v1/admin/classifications/values/1", "获取分类值详情"),
        ("POST", "/api/v1/admin/classifications/values", "创建分类值"),
        ("PUT", "/api/v1/admin/classifications/values/1", "更新分类值"),
        ("DELETE", "/api/v1/admin/classifications/values/1", "删除分类值"),
    ]
    
    for method, path, description in crud_routes:
        if method == "GET":
            response = client.get(path)
        elif method == "POST":
            response = client.post(path, json={
                "value_name": "测试值",
                "value_code": "test_value",
                "dimension_id": 1
            })
        elif method == "PUT":
            response = client.put(path, json={
                "value_name": "更新的测试值"
            })
        elif method == "DELETE":
            response = client.delete(path)
        
        # 验证路由存在（不是404）
        assert response.status_code != 404, f"{description} 路由不存在"
        assert response.status_code in [401, 403, 422], f"{description} 返回了意外的状态码: {response.status_code}"
        print(f"✓ {description}: {method} {path}")


def test_statistics_routes():
    """测试统计相关路由"""
    client = TestClient(app_b)
    
    stats_routes = [
        ("GET", "/api/v1/admin/tags/statistics", "获取标签统计信息"),
        ("GET", "/api/v1/admin/tags/popular", "获取热门标签"),
        ("GET", "/api/v1/admin/tags/trending", "获取趋势标签"),
    ]
    
    for method, path, description in stats_routes:
        response = client.get(path)
        
        # 验证路由存在（不是404）
        assert response.status_code != 404, f"{description} 路由不存在"
        assert response.status_code in [401, 403, 422], f"{description} 返回了意外的状态码: {response.status_code}"
        print(f"✓ {description}: {method} {path}")


def test_route_permissions_granularity():
    """测试路由权限的细粒度控制"""
    client = TestClient(app_b)
    
    # 验证不同操作需要不同权限（通过路由描述或其他方式）
    # 这里我们主要验证路由的存在性和基本的权限保护
    
    permission_groups = {
        "tag.classification": [
            "/api/v1/admin/tags/classifications",
            "/api/v1/admin/tags/classifications/1",
            "/api/v1/admin/tags/classifications/tree"
        ],
        "tag.tag": [
            "/api/v1/admin/tags",
            "/api/v1/admin/tags/1",
            "/api/v1/admin/tags/statistics",
            "/api/v1/admin/tags/popular",
            "/api/v1/admin/tags/trending"
        ],
        "classification.dimension": [
            "/api/v1/admin/classifications/dimensions",
            "/api/v1/admin/classifications/dimensions/1"
        ],
        "classification.value": [
            "/api/v1/admin/classifications/values",
            "/api/v1/admin/classifications/values/1"
        ]
    }
    
    for permission_group, paths in permission_groups.items():
        print(f"\n测试 {permission_group} 权限组:")
        for path in paths:
            response = client.get(path)
            assert response.status_code in [401, 403, 422], f"路径 {path} 没有权限保护"
            print(f"  ✓ {path}")


def test_route_count_verification():
    """验证路由数量是否符合预期"""
    # 预期的路由数量：
    # - TagClassification: 6个
    # - Tag: 5个  
    # - ClassificationDimension: 5个
    # - ClassificationValue: 5个
    # - Statistics: 3个
    # 总计: 24个路由
    
    expected_total = 24
    
    # 这个测试主要是文档化的，实际验证需要能够导入router
    print(f"预期总路由数量: {expected_total}")
    print("包括:")
    print("- TagClassification CRUD: 6个路由")
    print("- Tag CRUD: 5个路由")
    print("- ClassificationDimension CRUD: 5个路由")
    print("- ClassificationValue CRUD: 5个路由")
    print("- Statistics: 3个路由")


if __name__ == "__main__":
    # 运行所有测试
    test_all_admin_routes_exist()
    test_classification_dimension_crud_routes()
    test_classification_value_crud_routes()
    test_statistics_routes()
    test_route_permissions_granularity()
    test_route_count_verification()
    print("\n🎉 所有接口测试通过！")
