"""
AI服务工具类测试
测试API密钥获取、模型配置验证等功能
"""

import os
import pytest
from unittest.mock import patch, MagicMock
from decimal import Decimal

from src.services.ai_service.utils import AIServiceUtils
from src.services.ai_service.models import AIModel, ModelStatus, ModelProvider, ModelType


class TestAPIKeyFromEnv:
    """测试环境变量API密钥获取功能"""
    
    def test_get_api_key_success(self):
        """测试成功获取API密钥"""
        with patch.dict(os.environ, {"TEST_API_KEY": "valid_key_123"}):
            result = AIServiceUtils.get_api_key_from_env("TEST_API_KEY")
            assert result == "valid_key_123"
    
    def test_get_api_key_empty_name(self):
        """测试空的环境变量名"""
        result = AIServiceUtils.get_api_key_from_env("")
        assert result is None
        
        result = AIServiceUtils.get_api_key_from_env(None)
        assert result is None
    
    def test_get_api_key_not_found(self):
        """测试环境变量不存在"""
        result = AIServiceUtils.get_api_key_from_env("NON_EXISTENT_KEY")
        assert result is None
    
    def test_get_api_key_empty_value(self):
        """测试环境变量为空值"""
        with patch.dict(os.environ, {"EMPTY_KEY": ""}):
            result = AIServiceUtils.get_api_key_from_env("EMPTY_KEY")
            assert result is None
    
    def test_get_api_key_placeholder_values(self):
        """测试占位符值"""
        placeholder_values = [
            "your_api_key_here",
            "your_key_here",
            "placeholder",
            "demo_key",
            "test_key"
        ]
        
        for placeholder in placeholder_values:
            with patch.dict(os.environ, {"PLACEHOLDER_KEY": placeholder}):
                result = AIServiceUtils.get_api_key_from_env("PLACEHOLDER_KEY")
                assert result is None
    
    def test_get_api_key_case_insensitive_placeholder(self):
        """测试大小写不敏感的占位符检查"""
        with patch.dict(os.environ, {"PLACEHOLDER_KEY": "YOUR_API_KEY_HERE"}):
            result = AIServiceUtils.get_api_key_from_env("PLACEHOLDER_KEY")
            assert result is None


class TestModelConfigValidation:
    """测试模型配置验证功能"""
    
    def create_valid_model(self):
        """创建有效的模型配置"""
        return AIModel(
            model_name="test-model",
            display_name="Test Model",
            model_type=ModelType.LLM,
            provider=ModelProvider.DEEPSEEK,
            model_version="v1.0",
            api_endpoint="https://api.example.com/v1/chat/completions",
            api_key_name="TEST_API_KEY",
            temperature=Decimal("0.7"),
            top_p=Decimal("0.9"),
            rate_limit_rpm=60,
            timeout_seconds=30,
            cost_per_1k_input_tokens=Decimal("0.001"),
            cost_per_1k_output_tokens=Decimal("0.002")
        )
    
    def test_validate_valid_model(self):
        """测试有效模型配置验证"""
        model = self.create_valid_model()
        
        with patch.dict(os.environ, {"TEST_API_KEY": "valid_key_123"}):
            result = AIServiceUtils.validate_model_config(model)
            
        assert result["is_valid"] is True
        assert len(result["errors"]) == 0
    
    def test_validate_missing_model_name(self):
        """测试缺少模型名称"""
        model = self.create_valid_model()
        model.model_name = None
        
        result = AIServiceUtils.validate_model_config(model)
        
        assert result["is_valid"] is False
        assert "模型名称不能为空" in result["errors"]
    
    def test_validate_missing_api_endpoint(self):
        """测试缺少API端点"""
        model = self.create_valid_model()
        model.api_endpoint = None
        
        result = AIServiceUtils.validate_model_config(model)
        
        assert result["is_valid"] is False
        assert "API接口地址不能为空" in result["errors"]
    
    def test_validate_invalid_api_key(self):
        """测试无效的API密钥配置"""
        model = self.create_valid_model()
        model.api_key_name = "NON_EXISTENT_KEY"
        
        result = AIServiceUtils.validate_model_config(model)
        
        assert result["is_valid"] is False
        assert "环境变量 NON_EXISTENT_KEY 未设置或无效" in result["errors"]
    
    def test_validate_missing_api_key_name(self):
        """测试缺少API密钥环境变量名"""
        model = self.create_valid_model()
        model.api_key_name = None
        
        result = AIServiceUtils.validate_model_config(model)
        
        assert "未配置API密钥环境变量名" in result["warnings"]
    
    def test_validate_invalid_temperature(self):
        """测试无效的温度参数"""
        model = self.create_valid_model()
        model.temperature = Decimal("2.5")  # 超出范围
        
        result = AIServiceUtils.validate_model_config(model)
        
        assert result["is_valid"] is False
        assert "温度参数必须在0-2之间" in result["errors"]
    
    def test_validate_invalid_top_p(self):
        """测试无效的top_p参数"""
        model = self.create_valid_model()
        model.top_p = Decimal("1.5")  # 超出范围
        
        result = AIServiceUtils.validate_model_config(model)
        
        assert result["is_valid"] is False
        assert "top_p参数必须在0-1之间" in result["errors"]
    
    def test_validate_invalid_rate_limit(self):
        """测试无效的限流配置"""
        model = self.create_valid_model()
        model.rate_limit_rpm = 0
        
        result = AIServiceUtils.validate_model_config(model)
        
        assert result["is_valid"] is False
        assert "每分钟请求数限制必须大于0" in result["errors"]
    
    def test_validate_invalid_timeout(self):
        """测试无效的超时配置"""
        model = self.create_valid_model()
        model.timeout_seconds = 0
        
        result = AIServiceUtils.validate_model_config(model)
        
        assert result["is_valid"] is False
        assert "超时时间必须大于0" in result["errors"]
    
    def test_validate_negative_cost(self):
        """测试负数成本配置"""
        model = self.create_valid_model()
        model.cost_per_1k_input_tokens = Decimal("-0.001")
        model.cost_per_1k_output_tokens = Decimal("-0.002")
        
        result = AIServiceUtils.validate_model_config(model)
        
        assert "输入token成本为负数，请检查配置" in result["warnings"]
        assert "输出token成本为负数，请检查配置" in result["warnings"]


class TestGetModelWithAPIKey:
    """测试获取模型配置和API密钥功能"""
    
    def test_get_model_with_api_key_success(self):
        """测试成功获取模型配置和API密钥"""
        # 创建模拟的数据库会话
        mock_db = MagicMock()
        mock_model = AIModel(
            id=1,
            model_name="test-model",
            api_endpoint="https://api.example.com/v1/chat/completions",
            api_key_name="TEST_API_KEY",
            model_params={"context_length": 4096},
            max_tokens=4096,
            temperature=Decimal("0.7"),
            top_p=Decimal("0.9"),
            frequency_penalty=Decimal("0.0"),
            presence_penalty=Decimal("0.0"),
            timeout_seconds=30,
            max_retries=3,
            status=ModelStatus.ACTIVE
        )
        
        mock_db.query.return_value.filter.return_value.first.return_value = mock_model
        
        with patch.dict(os.environ, {"TEST_API_KEY": "valid_key_123"}):
            result = AIServiceUtils.get_model_with_api_key(mock_db, 1)
        
        assert result is not None
        assert result["id"] == 1
        assert result["model_name"] == "test-model"
        assert result["api_key"] == "valid_key_123"
        assert result["api_key_available"] is True
        assert result["is_active"] is True
    
    def test_get_model_not_found(self):
        """测试模型不存在"""
        mock_db = MagicMock()
        mock_db.query.return_value.filter.return_value.first.return_value = None
        
        result = AIServiceUtils.get_model_with_api_key(mock_db, 999)
        
        assert result is None
    
    def test_get_model_without_api_key(self):
        """测试没有API密钥的模型"""
        mock_db = MagicMock()
        mock_model = AIModel(
            id=1,
            model_name="test-model",
            api_endpoint="https://api.example.com/v1/chat/completions",
            api_key_name=None,
            status=ModelStatus.ACTIVE
        )
        
        mock_db.query.return_value.filter.return_value.first.return_value = mock_model
        
        result = AIServiceUtils.get_model_with_api_key(mock_db, 1)
        
        assert result is not None
        assert result["api_key"] is None
        assert result["api_key_available"] is False


class TestModelConnectionTest:
    """测试模型连接测试功能"""
    
    def test_test_connection_missing_api_key(self):
        """测试缺少API密钥的连接测试"""
        model_data = {
            "api_key": None,
            "api_endpoint": "https://api.example.com/v1/chat/completions",
            "model_name": "test-model",
            "timeout_seconds": 30
        }
        
        result = AIServiceUtils.test_model_connection(model_data)
        
        assert result["success"] is False
        assert "API密钥未配置" in result["message"]
    
    @patch('aiohttp.ClientSession')
    def test_test_connection_success(self, mock_session):
        """测试成功的连接测试"""
        # 配置模拟响应
        mock_response = MagicMock()
        mock_response.status = 200
        mock_response.__aenter__.return_value = mock_response
        mock_response.__aexit__.return_value = None
        
        mock_session_instance = MagicMock()
        mock_session_instance.post.return_value = mock_response
        mock_session_instance.__aenter__.return_value = mock_session_instance
        mock_session_instance.__aexit__.return_value = None
        mock_session.return_value = mock_session_instance
        
        model_data = {
            "api_key": "valid_key_123",
            "api_endpoint": "https://api.example.com/v1/chat/completions",
            "model_name": "test-model",
            "timeout_seconds": 30
        }
        
        result = AIServiceUtils.test_model_connection(model_data)
        
        assert result["success"] is True
        assert "连接测试成功" in result["message"]
        assert result["latency_ms"] is not None
    
    @patch('aiohttp.ClientSession')
    def test_test_connection_failure(self, mock_session):
        """测试失败的连接测试"""
        # 配置模拟响应
        mock_response = MagicMock()
        mock_response.status = 401
        mock_response.text.return_value = "Unauthorized"
        mock_response.__aenter__.return_value = mock_response
        mock_response.__aexit__.return_value = None
        
        mock_session_instance = MagicMock()
        mock_session_instance.post.return_value = mock_response
        mock_session_instance.__aenter__.return_value = mock_session_instance
        mock_session_instance.__aexit__.return_value = None
        mock_session.return_value = mock_session_instance
        
        model_data = {
            "api_key": "invalid_key",
            "api_endpoint": "https://api.example.com/v1/chat/completions",
            "model_name": "test-model",
            "timeout_seconds": 30
        }
        
        result = AIServiceUtils.test_model_connection(model_data)
        
        assert result["success"] is False
        assert "连接测试失败: HTTP 401" in result["message"]


class TestEnvVarSuggestions:
    """测试环境变量建议功能"""
    
    def test_get_suggestions_deepseek(self):
        """测试DeepSeek的环境变量建议"""
        suggestions = AIServiceUtils.get_env_var_suggestions("deepseek")
        
        assert suggestions["api_key_name"] == "DEEPSEEK_API_KEY"
        assert "deepseek.com" in suggestions["api_endpoint"]
        assert "DeepSeek" in suggestions["description"]
    
    def test_get_suggestions_openai(self):
        """测试OpenAI的环境变量建议"""
        suggestions = AIServiceUtils.get_env_var_suggestions("openai")
        
        assert suggestions["api_key_name"] == "OPENAI_API_KEY"
        assert "openai.com" in suggestions["api_endpoint"]
        assert "OpenAI" in suggestions["description"]
    
    def test_get_suggestions_unknown_provider(self):
        """测试未知提供商的环境变量建议"""
        suggestions = AIServiceUtils.get_env_var_suggestions("unknown_provider")
        
        assert suggestions["api_key_name"] == "UNKNOWN_PROVIDER_API_KEY"
        assert suggestions["api_endpoint"] == ""
        assert "unknown_provider" in suggestions["description"]
    
    def test_get_suggestions_case_insensitive(self):
        """测试大小写不敏感的提供商匹配"""
        suggestions = AIServiceUtils.get_env_var_suggestions("DEEPSEEK")
        
        assert suggestions["api_key_name"] == "DEEPSEEK_API_KEY"
        assert "deepseek.com" in suggestions["api_endpoint"] 