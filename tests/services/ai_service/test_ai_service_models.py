"""
AI服务模型测试
测试AIModel和ModelMetrics模型的功能
"""

import pytest
from decimal import Decimal
from datetime import datetime
from sqlalchemy.orm import Session

from src.services.ai_service.models import (
    AIModel, ModelMetrics, ModelType, ModelProvider, ModelStatus
)


class TestAIModel:
    """测试AIModel模型"""
    
    def test_create_ai_model(self, db_session: Session):
        """测试创建AI模型"""
        model = AIModel(
            model_name="deepseek-chat",
            model_type=ModelType.CHAT,
            model_version="1.0.0",
            provider="deepseek",
            api_endpoint="https://api.deepseek.com/v1/chat/completions",
            api_key_name="DEEPSEEK_API_KEY",
            max_tokens=4096,
            temperature=0.7,
            top_p=0.9,
            frequency_penalty=0.0,
            presence_penalty=0.0,
            model_params={"context_length": 32768, "supports_streaming": True},
            status=ModelStatus.ACTIVE,
            is_default=True,
            description="DeepSeek聊天模型",
            input_cost_per_token=0.0001,
            output_cost_per_token=0.0002
        )
        
        db_session.add(model)
        db_session.commit()
        db_session.refresh(model)
        
        assert model.id is not None
        assert model.model_name == "deepseek-chat"
        assert model.model_type == ModelType.CHAT
        assert model.provider == "deepseek"
        assert model.status == ModelStatus.ACTIVE
        assert model.is_default is True
        assert model.max_tokens == 4096
        assert model.temperature == 0.7
        assert model.model_params["context_length"] == 32768
        assert model.created_at is not None
        assert model.updated_at is not None
    
    def test_ai_model_unique_constraint(self, db_session: Session):
        """测试模型名称唯一性约束"""
        # 创建第一个模型
        model1 = AIModel(
            model_name="duplicate-model",
            model_type=ModelType.LLM,
            provider=ModelProvider.DEEPSEEK,
            model_version="v1.0"
        )
        db_session.add(model1)
        db_session.commit()
        
        # 尝试创建同名模型
        model2 = AIModel(
            model_name="duplicate-model",
            model_type=ModelType.LLM,
            provider=ModelProvider.OPENAI,
            model_version="v2.0"
        )
        db_session.add(model2)
        
        with pytest.raises(Exception):  # 应该抛出唯一性约束异常
            db_session.commit()
    
    def test_ai_model_default_values(self, db_session: Session):
        """测试模型默认值"""
        model = AIModel(
            model_name="test-default-model",
            model_type=ModelType.LLM,
            provider=ModelProvider.DEEPSEEK,
            model_version="v1.0"
        )
        
        db_session.add(model)
        db_session.commit()
        db_session.refresh(model)
        
        # 检查默认值
        assert model.max_tokens == 4096
        assert model.temperature == Decimal("0.7")
        assert model.top_p == Decimal("0.9")
        assert model.frequency_penalty == Decimal("0.0")
        assert model.presence_penalty == Decimal("0.0")
        assert model.rate_limit_rpm == 60
        assert model.rate_limit_tpm == 100000
        assert model.timeout_seconds == 30
        assert model.max_retries == 3
        assert model.cost_per_1k_input_tokens == Decimal("0.0")
        assert model.cost_per_1k_output_tokens == Decimal("0.0")
        assert model.cost_per_request == Decimal("0.0")
        assert model.status == ModelStatus.ACTIVE
        assert model.is_default is False
        assert model.priority == 5
        assert model.total_requests == 0
        assert model.success_requests == 0
        assert model.failed_requests == 0
        assert model.total_input_tokens == 0
        assert model.total_output_tokens == 0
        assert model.total_cost == Decimal("0.0")
    
    def test_ai_model_computed_properties(self, db_session: Session):
        """测试模型计算属性"""
        model = AIModel(
            model_name="test-computed-model",
            model_type=ModelType.LLM,
            provider=ModelProvider.DEEPSEEK,
            model_version="v1.0",
            total_requests=100,
            success_requests=95,
            failed_requests=5,
            total_cost=Decimal("10.50")
        )
        
        db_session.add(model)
        db_session.commit()
        db_session.refresh(model)
        
        # 测试计算属性
        assert model.success_rate == 0.95  # 95/100
        assert model.failure_rate == 0.05  # 5/100
        assert model.average_cost_per_request == Decimal("0.105")  # 10.50/100
    
    def test_ai_model_zero_requests(self, db_session: Session):
        """测试零请求时的计算属性"""
        model = AIModel(
            model_name="test-zero-requests",
            model_type=ModelType.LLM,
            provider=ModelProvider.DEEPSEEK,
            model_version="v1.0",
            total_requests=0,
            success_requests=0,
            failed_requests=0,
            total_cost=Decimal("0.0")
        )
        
        db_session.add(model)
        db_session.commit()
        db_session.refresh(model)
        
        # 零请求时应该返回0而不是抛出异常
        assert model.success_rate == 0.0
        assert model.failure_rate == 0.0
        assert model.average_cost_per_request == Decimal("0.0")


class TestModelMetrics:
    """测试ModelMetrics模型"""
    
    def test_create_model_metrics(self, db_session: Session, sample_ai_model):
        """测试创建模型指标"""
        metrics = ModelMetrics(
            model_id=sample_ai_model.id,
            total_requests=100,
            success_requests=95,
            failed_requests=5,
            total_input_tokens=10000,
            total_output_tokens=5000,
            total_cost=1.5,
            avg_latency_ms=250,
            p95_latency_ms=400,
            p99_latency_ms=450,
            extra_metadata={"test_data": True, "version": "1.0"}
        )
        
        db_session.add(metrics)
        db_session.commit()
        db_session.refresh(metrics)
        
        assert metrics.id is not None
        assert metrics.model_id == sample_ai_model.id
        assert metrics.request_count == 100
        assert metrics.success_count == 95
        assert metrics.error_count == 5
        assert metrics.total_input_tokens == 10000
        assert metrics.total_output_tokens == 5000
        assert metrics.total_cost == 1.5
        assert metrics.avg_latency_ms == 250
        assert metrics.extra_metadata["test_data"] is True
        assert metrics.created_at is not None
        assert metrics.updated_at is not None
    
    def test_model_metrics_relationships(self, db_session: Session, sample_ai_model):
        """测试模型指标关联关系"""
        metrics = ModelMetrics(
            model_id=sample_ai_model.id,
            request_count=50,
            success_count=48,
            error_count=2
        )
        
        db_session.add(metrics)
        db_session.commit()
        
        # 测试关联关系
        assert metrics.ai_model.id == sample_ai_model.id
        assert metrics.ai_model.model_name == sample_ai_model.model_name
    
    def test_metrics_calculations(self, db_session: Session, sample_ai_model):
        """测试指标计算"""
        metrics = ModelMetrics(
            model_id=sample_ai_model.id,
            request_count=100,
            success_count=95,
            error_count=5,
            total_input_tokens=10000,
            total_output_tokens=5000,
            total_cost=1.5
        )
        
        # 计算成功率
        success_rate = metrics.success_count / metrics.request_count
        assert success_rate == 0.95
        
        # 计算错误率
        error_rate = metrics.error_count / metrics.request_count
        assert error_rate == 0.05
        
        # 计算平均每次请求成本
        avg_cost_per_request = metrics.total_cost / metrics.request_count
        assert avg_cost_per_request == 0.015
        
        # 计算总token数
        total_tokens = metrics.total_input_tokens + metrics.total_output_tokens
        assert total_tokens == 15000
    
    def test_metrics_repr(self, db_session: Session, sample_ai_model):
        """测试指标字符串表示"""
        metrics = ModelMetrics(
            model_id=sample_ai_model.id,
            request_count=100,
            success_count=95,
            error_count=5
        )
        
        expected = f"<ModelMetrics(id=None, model_id={sample_ai_model.id}, requests=100, success_rate=95.0%)>"
        assert repr(metrics) == expected


class TestModelEnums:
    """测试模型枚举类型"""
    
    def test_model_type_enum(self):
        """测试ModelType枚举"""
        assert ModelType.LLM == "llm"
        assert ModelType.CLASSIFICATION == "classification"
        assert ModelType.NER == "ner"
        assert ModelType.SENTIMENT == "sentiment"
        assert ModelType.EMBEDDING == "embedding"
        assert ModelType.RERANK == "rerank"
        assert ModelType.IMAGE == "image"
        assert ModelType.AUDIO == "audio"
    
    def test_model_provider_enum(self):
        """测试ModelProvider枚举"""
        assert ModelProvider.DEEPSEEK == "deepseek"
        assert ModelProvider.OPENAI == "openai"
        assert ModelProvider.ANTHROPIC == "anthropic"
        assert ModelProvider.ZHIPU == "zhipu"
        assert ModelProvider.QWEN == "qwen"
        assert ModelProvider.BAIDU == "baidu"
        assert ModelProvider.LOCAL == "local"
        assert ModelProvider.CUSTOM == "custom"
    
    def test_model_status_enum(self):
        """测试ModelStatus枚举"""
        assert ModelStatus.ACTIVE == "active"
        assert ModelStatus.INACTIVE == "inactive"
        assert ModelStatus.TESTING == "testing"
        assert ModelStatus.DEPRECATED == "deprecated" 