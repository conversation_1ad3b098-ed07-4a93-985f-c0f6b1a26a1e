"""
AI服务业务逻辑测试
测试AIModelService和ModelMetricsService的功能
"""

import pytest
from decimal import Decimal
from datetime import datetime, timedelta
from unittest.mock import patch, MagicMock
from sqlalchemy.orm import Session

from src.services.ai_service.service import AIModelService, ModelMetricsService
from src.services.ai_service.models import AIModel, ModelMetrics, ModelType, ModelProvider, ModelStatus
from src.services.ai_service.schemas import AIModelCreate, AIModelUpdate, ModelMetricsCreate


class TestAIModelService:
    """测试AI模型服务"""
    
    def test_create_model_success(self, db_session: Session):
        """测试成功创建AI模型"""
        service = AIModelService(db_session)
        
        model_data = AIModelCreate(
            model_name="test-create-model",
            display_name="测试创建模型",
            description="用于测试创建的模型",
            model_type=ModelType.LLM,
            provider=ModelProvider.DEEPSEEK,
            model_version="v1.0",
            api_endpoint="https://api.deepseek.com/v1/chat/completions",
            api_key_name="DEEPSEEK_API_KEY",
            model_params={"context_length": 32768},
            max_tokens=4096,
            temperature=Decimal("0.7"),
            top_p=Decimal("0.9"),
            rate_limit_rpm=60,
            rate_limit_tpm=100000,
            cost_per_1k_input_tokens=Decimal("0.0014"),
            cost_per_1k_output_tokens=Decimal("0.0028"),
            is_default=True,
            priority=1
        )
        
        result = service.create_model(model_data)
        
        assert result.id is not None
        assert result.model_name == "test-create-model"
        assert result.display_name == "测试创建模型"
        assert result.model_type == ModelType.LLM
        assert result.provider == ModelProvider.DEEPSEEK
        assert result.api_key_name == "DEEPSEEK_API_KEY"
        assert result.is_default is True
        assert result.priority == 1
    
    def test_create_model_duplicate_name(self, db_session: Session):
        """测试创建重复名称的模型"""
        service = AIModelService(db_session)
        
        # 先创建一个模型
        existing_model = AIModel(
            model_name="duplicate-model",
            model_type=ModelType.LLM,
            provider=ModelProvider.DEEPSEEK,
            model_version="v1.0"
        )
        db_session.add(existing_model)
        db_session.commit()
        
        # 尝试创建同名模型
        model_data = AIModelCreate(
            model_name="duplicate-model",
            model_type=ModelType.LLM,
            provider=ModelProvider.OPENAI,
            model_version="v2.0"
        )
        
        with pytest.raises(Exception):
            service.create_model(model_data)
    
    def test_get_model_by_id_success(self, db_session: Session):
        """测试通过ID获取模型"""
        service = AIModelService(db_session)
        
        # 创建测试模型
        model = AIModel(
            model_name="test-get-model",
            model_type=ModelType.LLM,
            provider=ModelProvider.DEEPSEEK,
            model_version="v1.0"
        )
        db_session.add(model)
        db_session.commit()
        db_session.refresh(model)
        
        result = service.get_model_by_id(model.id)
        
        assert result is not None
        assert result.id == model.id
        assert result.model_name == "test-get-model"
    
    def test_get_model_by_id_not_found(self, db_session: Session):
        """测试获取不存在的模型"""
        service = AIModelService(db_session)
        
        result = service.get_model_by_id(999)
        
        assert result is None
    
    def test_update_model_success(self, db_session: Session):
        """测试成功更新模型"""
        service = AIModelService(db_session)
        
        # 创建测试模型
        model = AIModel(
            model_name="test-update-model",
            display_name="原始显示名称",
            model_type=ModelType.LLM,
            provider=ModelProvider.DEEPSEEK,
            model_version="v1.0",
            temperature=Decimal("0.7")
        )
        db_session.add(model)
        db_session.commit()
        db_session.refresh(model)
        
        # 更新模型
        update_data = AIModelUpdate(
            display_name="更新后的显示名称",
            model_version="v1.1",
            temperature=Decimal("0.8")
        )
        
        result = service.update_model(model.id, update_data)
        
        assert result is not None
        assert result.display_name == "更新后的显示名称"
        assert result.model_version == "v1.1"
        assert result.temperature == Decimal("0.8")
        assert result.model_name == "test-update-model"  # 未更新的字段保持不变
    
    def test_update_model_not_found(self, db_session: Session):
        """测试更新不存在的模型"""
        service = AIModelService(db_session)
        
        update_data = AIModelUpdate(display_name="新名称")
        
        result = service.update_model(999, update_data)
        
        assert result is None
    
    def test_delete_model_success(self, db_session: Session):
        """测试成功删除模型"""
        service = AIModelService(db_session)
        
        # 创建测试模型
        model = AIModel(
            model_name="test-delete-model",
            model_type=ModelType.LLM,
            provider=ModelProvider.DEEPSEEK,
            model_version="v1.0"
        )
        db_session.add(model)
        db_session.commit()
        db_session.refresh(model)
        
        # 删除模型
        result = service.delete_model(model.id)
        
        assert result is True
        
        # 验证模型已被删除
        deleted_model = db_session.query(AIModel).filter(AIModel.id == model.id).first()
        assert deleted_model is None
    
    def test_delete_model_not_found(self, db_session: Session):
        """测试删除不存在的模型"""
        service = AIModelService(db_session)
        
        result = service.delete_model(999)
        
        assert result is False
    
    def test_get_models_list(self, db_session: Session):
        """测试获取模型列表"""
        service = AIModelService(db_session)
        
        # 创建多个测试模型
        models_data = [
            ("model-1", ModelType.LLM, ModelProvider.DEEPSEEK, ModelStatus.ACTIVE),
            ("model-2", ModelType.LLM, ModelProvider.OPENAI, ModelStatus.INACTIVE),
            ("model-3", ModelType.CLASSIFICATION, ModelProvider.DEEPSEEK, ModelStatus.ACTIVE),
        ]
        
        for name, model_type, provider, status in models_data:
            model = AIModel(
                model_name=name,
                model_type=model_type,
                provider=provider,
                model_version="v1.0",
                status=status
            )
            db_session.add(model)
        
        db_session.commit()
        
        # 测试获取所有模型
        result = service.get_models_list()
        assert len(result.items) == 3
        assert result.total == 3
        
        # 测试按类型过滤
        result = service.get_models_list(model_type=ModelType.LLM)
        assert len(result.items) == 2
        
        # 测试按状态过滤
        result = service.get_models_list(status=ModelStatus.ACTIVE)
        assert len(result.items) == 2
        
        # 测试按提供商过滤
        result = service.get_models_list(provider=ModelProvider.DEEPSEEK)
        assert len(result.items) == 2
        
        # 测试分页
        result = service.get_models_list(page=1, page_size=2)
        assert len(result.items) == 2
        assert result.page == 1
        assert result.page_size == 2
        assert result.total == 3
        assert result.total_pages == 2
    
    def test_set_default_model(self, db_session: Session):
        """测试设置默认模型"""
        service = AIModelService(db_session)
        
        # 创建多个相同类型的模型
        model1 = AIModel(
            model_name="model-1",
            model_type=ModelType.LLM,
            provider=ModelProvider.DEEPSEEK,
            model_version="v1.0",
            is_default=True  # 原来的默认模型
        )
        model2 = AIModel(
            model_name="model-2",
            model_type=ModelType.LLM,
            provider=ModelProvider.OPENAI,
            model_version="v1.0",
            is_default=False
        )
        
        db_session.add_all([model1, model2])
        db_session.commit()
        db_session.refresh(model1)
        db_session.refresh(model2)
        
        # 设置model2为默认模型
        result = service.set_default_model(model2.id, True)
        
        assert result is not None
        assert result.is_default is True
        
        # 验证原默认模型被取消
        db_session.refresh(model1)
        assert model1.is_default is False
    
    def test_get_default_model(self, db_session: Session):
        """测试获取默认模型"""
        service = AIModelService(db_session)
        
        # 创建默认模型
        model = AIModel(
            model_name="default-llm-model",
            model_type=ModelType.LLM,
            provider=ModelProvider.DEEPSEEK,
            model_version="v1.0",
            is_default=True
        )
        db_session.add(model)
        db_session.commit()
        db_session.refresh(model)
        
        result = service.get_default_model(ModelType.LLM)
        
        assert result is not None
        assert result.model_name == "default-llm-model"
        assert result.is_default is True
    
    def test_get_default_model_not_found(self, db_session: Session):
        """测试获取不存在的默认模型"""
        service = AIModelService(db_session)
        
        result = service.get_default_model(ModelType.CLASSIFICATION)
        
        assert result is None
    
    def test_get_available_models(self, db_session: Session):
        """测试获取可用模型"""
        service = AIModelService(db_session)
        
        # 创建不同状态的模型
        active_model = AIModel(
            model_name="active-model",
            model_type=ModelType.LLM,
            provider=ModelProvider.DEEPSEEK,
            model_version="v1.0",
            status=ModelStatus.ACTIVE
        )
        inactive_model = AIModel(
            model_name="inactive-model",
            model_type=ModelType.LLM,
            provider=ModelProvider.OPENAI,
            model_version="v1.0",
            status=ModelStatus.INACTIVE
        )
        
        db_session.add_all([active_model, inactive_model])
        db_session.commit()
        
        result = service.get_available_models(ModelType.LLM)
        
        assert len(result) == 1
        assert result[0].model_name == "active-model"
        assert result[0].status == ModelStatus.ACTIVE


class TestModelMetricsService:
    """测试模型指标服务"""
    
    def test_create_metrics_success(self, db_session: Session):
        """测试成功创建模型指标"""
        # 先创建AI模型
        ai_model = AIModel(
            model_name="test-metrics-model",
            model_type=ModelType.LLM,
            provider=ModelProvider.DEEPSEEK,
            model_version="v1.0"
        )
        db_session.add(ai_model)
        db_session.commit()
        db_session.refresh(ai_model)
        
        service = ModelMetricsService(db_session)
        
        metrics_data = ModelMetricsCreate(
            model_id=ai_model.id,
            metric_date=datetime(2024, 1, 15, 10, 0, 0),
            time_window="hour",
            total_requests=150,
            success_requests=145,
            failed_requests=5,
            avg_latency_ms=800,
            total_input_tokens=50000,
            total_output_tokens=25000,
            total_cost=Decimal("12.50"),
            accuracy_score=Decimal("0.95")
        )
        
        result = service.create_metrics(metrics_data)
        
        assert result.id is not None
        assert result.model_id == ai_model.id
        assert result.total_requests == 150
        assert result.success_requests == 145
        assert result.total_cost == Decimal("12.50")
        assert result.accuracy_score == Decimal("0.95")
    
    def test_get_metrics_by_model(self, db_session: Session):
        """测试获取模型指标"""
        # 创建AI模型
        ai_model = AIModel(
            model_name="test-get-metrics-model",
            model_type=ModelType.LLM,
            provider=ModelProvider.DEEPSEEK,
            model_version="v1.0"
        )
        db_session.add(ai_model)
        db_session.commit()
        db_session.refresh(ai_model)
        
        # 创建多个指标记录
        base_date = datetime(2024, 1, 15)
        for hour in range(3):
            metrics = ModelMetrics(
                model_id=ai_model.id,
                metric_date=base_date + timedelta(hours=hour),
                time_window="hour",
                total_requests=100 + hour * 10,
                success_requests=95 + hour * 10,
                failed_requests=5
            )
            db_session.add(metrics)
        
        db_session.commit()
        
        service = ModelMetricsService(db_session)
        
        # 测试获取指标
        result = service.get_metrics_by_model(
            ai_model.id,
            start_date=base_date,
            end_date=base_date + timedelta(days=1),
            time_window="hour"
        )
        
        assert len(result) == 3
        assert result[0].total_requests == 100
        assert result[1].total_requests == 110
        assert result[2].total_requests == 120
    
    def test_get_model_performance(self, db_session: Session):
        """测试获取模型性能"""
        # 创建AI模型
        ai_model = AIModel(
            model_name="test-performance-model",
            model_type=ModelType.LLM,
            provider=ModelProvider.DEEPSEEK,
            model_version="v1.0"
        )
        db_session.add(ai_model)
        db_session.commit()
        db_session.refresh(ai_model)
        
        # 创建指标记录
        metrics = ModelMetrics(
            model_id=ai_model.id,
            metric_date=datetime(2024, 1, 15, 10, 0, 0),
            time_window="day",
            total_requests=1000,
            success_requests=950,
            failed_requests=50,
            avg_latency_ms=500,
            total_cost=Decimal("25.00")
        )
        db_session.add(metrics)
        db_session.commit()
        
        service = ModelMetricsService(db_session)
        
        result = service.get_model_performance(ai_model.id, days=7)
        
        assert result is not None
        assert result.model_id == ai_model.id
        assert result.model_name == "test-performance-model"
        assert result.success_rate == 0.95  # 950/1000
        assert result.avg_latency_ms == 500
        assert result.total_cost == Decimal("25.00")
        assert result.total_requests == 1000
        assert len(result.metrics) == 1
    
    def test_batch_update_status(self, db_session: Session):
        """测试批量更新模型状态"""
        service = AIModelService(db_session)
        
        # 创建多个模型
        models = []
        for i in range(3):
            model = AIModel(
                model_name=f"batch-model-{i}",
                model_type=ModelType.LLM,
                provider=ModelProvider.DEEPSEEK,
                model_version="v1.0",
                status=ModelStatus.ACTIVE
            )
            db_session.add(model)
            models.append(model)
        
        db_session.commit()
        
        # 批量更新状态
        model_ids = [model.id for model in models]
        result = service.batch_update_status(model_ids, ModelStatus.INACTIVE)
        
        assert result["success_count"] == 3
        assert result["failed_count"] == 0
        assert len(result["success_ids"]) == 3
    
    def test_batch_delete_models(self, db_session: Session):
        """测试批量删除模型"""
        service = AIModelService(db_session)
        
        # 创建多个模型
        models = []
        for i in range(3):
            model = AIModel(
                model_name=f"delete-model-{i}",
                model_type=ModelType.LLM,
                provider=ModelProvider.DEEPSEEK,
                model_version="v1.0"
            )
            db_session.add(model)
            models.append(model)
        
        db_session.commit()
        
        # 批量删除
        model_ids = [model.id for model in models]
        result = service.batch_delete_models(model_ids)
        
        assert result["success"] is True
        assert result["deleted_count"] == 3
        
        # 验证模型已删除
        for model_id in model_ids:
            deleted_model = db_session.query(AIModel).filter(AIModel.id == model_id).first()
            assert deleted_model is None 