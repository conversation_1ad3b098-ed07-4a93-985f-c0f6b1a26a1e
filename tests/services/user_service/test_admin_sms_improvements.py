"""
B端用户模块改进功能测试
测试验证码发送接口的purpose参数和登录接口返回角色权限信息的功能
"""

import pytest
from unittest.mock import Mock, patch
from fastapi import FastAPI
from fastapi.testclient import TestClient

from src.services.user_service.admin_router import router as admin_router
from src.services.user_service.schemas import (
    AdminSendSmsRequest, AdminLoginRequest, 
    SmsCodePurpose, UserType, RiskLevel
)


@pytest.fixture
def app():
    """创建测试应用"""
    app = FastAPI()
    app.include_router(admin_router, prefix="/api/v1/admin/users")
    return app


@pytest.fixture  
def test_client(app):
    """创建测试客户端"""
    return TestClient(app)


@pytest.fixture
def mock_admin_user():
    """模拟管理员用户"""
    user = Mock()
    user.id = 1
    user.phone = "13800138000"
    user.username = "admin"
    user.email = "<EMAIL>"
    user.is_admin = True
    user.is_active = True
    user.is_verified = True
    user.user_type = UserType.ADVANCED
    user.risk_level = RiskLevel.MODERATE
    user.knowledge_level = 3
    user.first_login_at = None
    user.last_login_at = None
    user.created_at = "2024-01-01T00:00:00"
    user.updated_at = "2024-01-01T00:00:00"
    return user


@pytest.fixture
def mock_roles():
    """模拟用户角色"""
    role1 = Mock()
    role1.id = 1
    role1.name = "super_admin"
    role1.description = "超级管理员"
    
    role2 = Mock()
    role2.id = 2
    role2.name = "user_manager"
    role2.description = "用户管理员"
    
    return [role1, role2]


@pytest.fixture
def mock_permissions():
    """模拟用户权限"""
    perm1 = Mock()
    perm1.id = 1
    perm1.code = "user.list.read"
    perm1.name = "查看用户列表"
    perm1.module = "user"
    perm1.resource = "list"
    perm1.action = "read"
    
    perm2 = Mock()
    perm2.id = 2
    perm2.code = "user.create"
    perm2.name = "创建用户"
    perm2.module = "user"
    perm2.resource = "user"
    perm2.action = "create"
    
    return [perm1, perm2]


class TestAdminSmsImprovements:
    """B端SMS改进测试"""
    
    def test_send_verification_code_with_purpose_login(self, test_client, mock_admin_user):
        """测试发送登录验证码，指定purpose为login"""
        with patch("src.services.user_service.admin_router.get_db") as mock_get_db, \
             patch("src.services.user_service.admin_router.UserService") as mock_user_service_class:
            
            # 配置mock
            mock_db = Mock()
            mock_get_db.return_value = mock_db
            mock_user_service = Mock()
            mock_user_service_class.return_value = mock_user_service
            mock_user_service.get_user_by_phone.return_value = mock_admin_user
            mock_user_service.send_verification_code.return_value = "123456"
            
            # 发送请求
            response = test_client.post(
                "/api/v1/admin/users/auth/send-code",
                json={
                    "phone": "13800138000",
                    "purpose": "login"
                }
            )
            
            # 验证响应
            assert response.status_code == 200
            assert response.json()["message"] == "Verification code sent successfully"
            
            # 验证服务调用
            mock_user_service.send_verification_code.assert_called_once_with(
                phone="13800138000",
                purpose="login",
                client_type="admin"
            )
    
    def test_send_verification_code_with_purpose_reset_password(self, test_client, mock_admin_user):
        """测试发送重置密码验证码，指定purpose为reset_password"""
        with patch("src.services.user_service.admin_router.get_db") as mock_get_db, \
             patch("src.services.user_service.admin_router.UserService") as mock_user_service_class:
            
            # 配置mock
            mock_db = Mock()
            mock_get_db.return_value = mock_db
            mock_user_service = Mock()
            mock_user_service_class.return_value = mock_user_service
            mock_user_service.get_user_by_phone.return_value = mock_admin_user
            mock_user_service.send_verification_code.return_value = "123456"
            
            # 发送请求
            response = test_client.post(
                "/api/v1/admin/users/auth/send-code",
                json={
                    "phone": "13800138000",
                    "purpose": "reset_password"
                }
            )
            
            # 验证响应
            assert response.status_code == 200
            
            # 验证服务调用
            mock_user_service.send_verification_code.assert_called_once_with(
                phone="13800138000",
                purpose="reset_password",
                client_type="admin"
            )
    
    def test_send_verification_code_default_purpose(self, test_client, mock_admin_user):
        """测试发送验证码，不指定purpose时默认为login"""
        with patch("src.services.user_service.admin_router.get_db") as mock_get_db, \
             patch("src.services.user_service.admin_router.UserService") as mock_user_service_class:
            
            # 配置mock
            mock_db = Mock()
            mock_get_db.return_value = mock_db
            mock_user_service = Mock()
            mock_user_service_class.return_value = mock_user_service
            mock_user_service.get_user_by_phone.return_value = mock_admin_user
            mock_user_service.send_verification_code.return_value = "123456"
            
            # 发送请求（不指定purpose）
            response = test_client.post(
                "/api/v1/admin/users/auth/send-code",
                json={
                    "phone": "13800138000"
                }
            )
            
            # 验证响应
            assert response.status_code == 200
            
            # 验证服务调用（默认purpose为login）
            mock_user_service.send_verification_code.assert_called_once_with(
                phone="13800138000",
                purpose="login",
                client_type="admin"
            )
    
    def test_admin_login_returns_roles_and_permissions(
        self, test_client, mock_admin_user, mock_roles, mock_permissions
    ):
        """测试管理员登录返回角色和权限信息"""
        with patch("src.services.user_service.admin_router.get_db") as mock_get_db, \
             patch("src.services.user_service.admin_router.UserService") as mock_user_service_class, \
             patch("src.services.user_service.admin_router.get_client_info") as mock_get_client_info:
            
            # 配置mock
            mock_db = Mock()
            mock_get_db.return_value = mock_db
            mock_user_service = Mock()
            mock_user_service_class.return_value = mock_user_service
            mock_get_client_info.return_value = ("127.0.0.1", "test-agent")
            
            # 配置用户服务返回值
            mock_user_service.get_user_by_phone.return_value = mock_admin_user
            mock_user_service.sms_code_service.verify_code.return_value = True
            mock_user_service.create_user_tokens.return_value = {
                "access_token": "test_token",
                "token_type": "bearer"
            }
            mock_user_service.get_user_roles_and_permissions.return_value = (mock_roles, mock_permissions)
            mock_user_service.track_user_behavior = Mock()
            
            # 发送请求
            response = test_client.post(
                "/api/v1/admin/users/auth/login",
                json={
                    "phone": "13800138000",
                    "verification_code": "123456"
                }
            )
            
            # 验证响应
            assert response.status_code == 200
            response_data = response.json()
            
            # 验证基本字段
            assert response_data["access_token"] == "test_token"
            assert response_data["token_type"] == "bearer"
            
            # 验证用户信息
            user_data = response_data["user"]
            assert user_data["id"] == 1
            assert user_data["phone"] == "13800138000"
            assert user_data["is_admin"] is True
            
            # 验证角色信息
            assert len(user_data["roles"]) == 2
            assert user_data["roles"][0]["id"] == 1
            assert user_data["roles"][0]["name"] == "super_admin"
            assert user_data["roles"][0]["description"] == "超级管理员"
            assert user_data["roles"][1]["id"] == 2
            assert user_data["roles"][1]["name"] == "user_manager"
            
            # 验证权限信息
            assert len(user_data["permissions"]) == 2
            assert user_data["permissions"][0]["id"] == 1
            assert user_data["permissions"][0]["code"] == "user.list.read"
            assert user_data["permissions"][0]["name"] == "查看用户列表"
            assert user_data["permissions"][0]["module"] == "user"
            assert user_data["permissions"][0]["resource"] == "list"
            assert user_data["permissions"][0]["action"] == "read"
            
            # 验证服务调用（检查参数类型而非具体对象）
            mock_user_service.get_user_roles_and_permissions.assert_called_once()
            call_args = mock_user_service.get_user_roles_and_permissions.call_args
            assert call_args[0][1] == 1  # 验证用户ID
    
    def test_send_verification_code_invalid_purpose(self, test_client):
        """测试发送验证码时使用无效的purpose"""
        # 发送请求
        response = test_client.post(
            "/api/v1/admin/users/auth/send-code",
            json={
                "phone": "13800138000",
                "purpose": "invalid_purpose"
            }
        )
        
        # 验证响应
        assert response.status_code == 422
        assert "Invalid purpose" in str(response.json())


class TestSmsCodeIsolationWithPurpose:
    """SMS验证码隔离与purpose参数测试"""
    
    def test_different_purposes_create_different_keys(self):
        """测试不同purpose生成不同的Redis key"""
        from src.services.user_service.service import SmsCodeService
        from unittest.mock import Mock
        
        # 创建服务实例
        mock_redis = Mock()
        mock_sms = Mock()
        sms_code_service = SmsCodeService(mock_redis, mock_sms)
        
        # 测试不同purpose生成不同key
        login_key = sms_code_service._get_code_key("13800138000", "login", "admin")
        reset_key = sms_code_service._get_code_key("13800138000", "reset_password", "admin")
        
        assert login_key == "sms:code:admin:13800138000:login"
        assert reset_key == "sms:code:admin:13800138000:reset_password"
        assert login_key != reset_key
    
    def test_same_phone_different_client_type_different_purpose(self):
        """测试同一手机号，不同客户端类型和用途生成不同key"""
        from src.services.user_service.service import SmsCodeService
        from unittest.mock import Mock
        
        # 创建服务实例
        mock_redis = Mock()
        mock_sms = Mock()
        sms_code_service = SmsCodeService(mock_redis, mock_sms)
        
        # 测试同一手机号不同配置生成不同key
        admin_login_key = sms_code_service._get_code_key("13800138000", "login", "admin")
        user_login_key = sms_code_service._get_code_key("13800138000", "login", "user")
        admin_reset_key = sms_code_service._get_code_key("13800138000", "reset_password", "admin")
        
        assert admin_login_key == "sms:code:admin:13800138000:login"
        assert user_login_key == "sms:code:user:13800138000:login"
        assert admin_reset_key == "sms:code:admin:13800138000:reset_password"
        
        # 确保都不相同
        keys = [admin_login_key, user_login_key, admin_reset_key]
        assert len(set(keys)) == 3  # 所有key都不同 