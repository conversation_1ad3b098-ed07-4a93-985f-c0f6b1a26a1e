"""
用户服务基础服务测试模块
包含Redis、Password、JWT、Session等基础服务的测试
"""

from datetime import datetime, timezone
from unittest.mock import Mock, patch

import pytest

from src.services.user_service.schemas import SmsCodePurpose
from src.services.user_service.service import (JWTService, PasswordService,
                                               RedisService, SessionService,
                                               SmsCodeService)
from tests.services.user_service.models import User


def create_test_user(db_session, phone, username=None, email=None):
    """创建测试用户的辅助函数"""
    user = User(
        phone=phone,
        username=username,
        email=email,
        user_type=1,
        risk_level=3,
        knowledge_level=1,
        is_verified=True,
        first_login_at=datetime.now(timezone.utc),
        last_login_at=datetime.now(timezone.utc),
    )

    db_session.add(user)
    db_session.commit()
    db_session.refresh(user)

    return user


class TestRedisService:
    """Redis服务测试"""

    def test_redis_connection_success(self):
        """测试Redis连接成功"""
        with patch("redis.Redis") as mock_redis_class:
            mock_client = Mock()
            mock_client.info.return_value = {"redis_version": "6.0.0"}
            mock_redis_class.return_value = mock_client

            redis_service = RedisService()
            assert redis_service.redis_client is not None

    def test_set_value_success(self):
        """测试设置值成功"""
        redis_service = RedisService()
        redis_service.redis_client = Mock()
        redis_service.redis_client.setex.return_value = True

        result = redis_service.set_value("test_key", "test_value", 300)
        assert result is True


class TestPasswordService:
    """密码服务测试"""

    def test_hash_password(self):
        """测试密码哈希"""
        password_service = PasswordService()
        password = "test_password_123"
        hashed = password_service.hash_password(password)

        assert hashed != password
        assert len(hashed) > 20

    def test_verify_password_correct(self):
        """测试正确密码验证"""
        password_service = PasswordService()
        password = "test_password_123"
        hashed = password_service.hash_password(password)

        result = password_service.verify_password(password, hashed)
        assert result is True


class TestJWTService:
    """JWT服务测试"""

    def test_create_access_token(self):
        """测试创建访问令牌"""
        jwt_service = JWTService()
        data = {"sub": "test_user", "user_id": 123}
        token = jwt_service.create_access_token(data)

        assert isinstance(token, str)
        assert len(token) > 10

    def test_verify_token_valid(self):
        """测试验证有效令牌"""
        jwt_service = JWTService()
        data = {"sub": "test_user", "user_id": 123}
        token = jwt_service.create_access_token(data)

        payload = jwt_service.verify_token(token)
        assert payload is not None
        assert payload["sub"] == "test_user"
        assert payload["user_id"] == 123
