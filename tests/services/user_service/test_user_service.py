"""
用户服务主要功能测试模块
包含用户服务核心业务逻辑的测试
"""

import time
from datetime import datetime, timezone
from unittest.mock import Mock, patch

import pytest
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from src.services.user_service.schemas import (RiskLevel, SmsCodePurpose,
                                               UserBehaviorCreate,
                                               UserTagCreate, UserType)
from src.services.user_service.service import (JWTService, SmsCodeService,
                                               UserService)
from tests.services.user_service.models import User, UserBehavior
from tests.services.user_service.models import UserServiceBase as Base
from tests.services.user_service.models import UserTag

# 使用内存数据库进行测试
SQLALCHEMY_DATABASE_URL = "sqlite:///:memory:"
engine = create_engine(
    SQLALCHEMY_DATABASE_URL, connect_args={"check_same_thread": False}
)
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


@pytest.fixture(scope="function")
def user_db_session():
    """创建用户服务专用的数据库会话"""
    Base.metadata.create_all(bind=engine)
    session = TestingSessionLocal()
    try:
        yield session
    finally:
        session.close()
        Base.metadata.drop_all(bind=engine)


@pytest.fixture
def user_service_instance(mock_redis_service, mock_sms_service, user_db_session):
    """创建用户服务实例"""
    service = UserService(db_session=user_db_session)
    service.redis_service = mock_redis_service
    service.sms_service = mock_sms_service

    # 创建SMS代码服务的mock
    service.sms_code_service = SmsCodeService(mock_redis_service, mock_sms_service)
    service.sms_code_service.verify_code = Mock(return_value=True)
    service.sms_code_service.send_sms_code = Mock(return_value="123456")
    return service


def create_test_user(
    db_session,
    phone,
    username=None,
    email=None,
    user_type=UserType.NOVICE,
    risk_level=RiskLevel.MODERATE,
    knowledge_level=1,
):
    """创建测试用户的辅助函数"""
    user = User(
        phone=phone,
        username=username,
        email=email,
        user_type=user_type,
        risk_level=risk_level,
        knowledge_level=knowledge_level,
        is_verified=True,
        first_login_at=datetime.now(timezone.utc),
        last_login_at=datetime.now(timezone.utc),
    )

    db_session.add(user)
    db_session.commit()
    db_session.refresh(user)

    return user


class TestUserService:
    """用户服务测试"""

    def test_get_user_by_phone(
        self, user_service_instance, user_db_session, unique_phone
    ):
        """测试通过手机号获取用户"""
        # 创建测试用户
        test_user = create_test_user(user_db_session, unique_phone)

        # 测试获取用户
        user = user_service_instance.get_user_by_phone(
            db=user_db_session, phone=unique_phone
        )

        assert user is not None
        assert user.phone == unique_phone
        assert user.id == test_user.id

    def test_authenticate_with_phone_code_new_user(
        self, user_service_instance, user_db_session, unique_phone
    ):
        """测试新用户通过手机验证码认证"""
        user = user_service_instance.authenticate_with_phone_code(
            db=user_db_session, phone=unique_phone, verification_code="123456"
        )

        assert user is not None
        assert user.phone == unique_phone

    def test_authenticate_with_phone_code_existing_user(
        self, user_service_instance, user_db_session, unique_phone
    ):
        """测试现有用户通过手机验证码认证"""
        # 先创建用户
        existing_user = create_test_user(
            user_db_session, unique_phone, username="existing_user"
        )

        user = user_service_instance.authenticate_with_phone_code(
            db=user_db_session, phone=unique_phone, verification_code="123456"
        )

        assert user is not None
        assert user.id == existing_user.id
        assert user.username == "existing_user"

    def test_create_user_tokens(
        self, user_service_instance, user_db_session, unique_phone
    ):
        """测试创建用户令牌"""
        user = create_test_user(user_db_session, unique_phone)

        tokens = user_service_instance.create_user_tokens(
            user, ip_address="***********", user_agent="test-agent"
        )

        assert "access_token" in tokens
        assert "refresh_token" in tokens
        assert "token_type" in tokens
        assert tokens["token_type"] == "bearer"

    def test_create_user_tag(
        self, user_service_instance, user_db_session, unique_phone
    ):
        """测试创建用户标签"""
        user = create_test_user(user_db_session, unique_phone)

        tag_data = UserTagCreate(
            tag_category="preference",
            tag_name="risk_preference",
            tag_value="conservative",
            weight=0.8,
        )

        tag = user_service_instance.create_user_tag(
            db=user_db_session, user_id=user.id, tag_data=tag_data
        )

        assert tag is not None
        assert tag.user_id == user.id
        assert tag.tag_category == "preference"
        assert tag.tag_name == "risk_preference"
        assert tag.tag_value == "conservative"
        assert float(tag.weight) == 0.8

    def test_track_user_behavior(
        self, user_service_instance, user_db_session, unique_phone
    ):
        """测试跟踪用户行为"""
        user = create_test_user(user_db_session, unique_phone)

        behavior_data = UserBehaviorCreate(
            action_type="view_article",
            action_target="article",
            action_details={"title": "investment article", "category": "investment"},
            ip_address="***********",
            user_agent="test-agent",
        )

        behavior = user_service_instance.track_user_behavior(
            db=user_db_session, user_id=user.id, behavior_data=behavior_data
        )

        assert behavior is not None
        assert behavior.user_id == user.id
        assert behavior.action_type == "view_article"
        assert behavior.action_target == "article"

    def test_get_user_by_username(
        self, user_service_instance, user_db_session, unique_phone
    ):
        """测试通过用户名获取用户"""
        username = f"testuser_{unique_phone[-4:]}"
        create_test_user(user_db_session, unique_phone, username=username)

        user = user_service_instance.get_user_by_username(
            db=user_db_session, username=username
        )

        assert user is not None
        assert user.username == username
        assert user.phone == unique_phone

    def test_get_user_by_email(
        self, user_service_instance, user_db_session, unique_phone, unique_email
    ):
        """测试通过邮箱获取用户"""
        create_test_user(user_db_session, unique_phone, email=unique_email)

        user = user_service_instance.get_user_by_email(
            db=user_db_session, email=unique_email
        )

        assert user is not None
        assert user.email == unique_email
        assert user.phone == unique_phone

    def test_refresh_access_token(
        self, user_service_instance, user_db_session, unique_phone
    ):
        """测试刷新访问令牌"""
        from datetime import datetime, timedelta, timezone

        from tests.services.user_service.models import UserSession

        user = create_test_user(user_db_session, unique_phone)

        # 创建刷新令牌
        jwt_service = JWTService()
        refresh_token_data = {
            "sub": str(user.id),
            "phone": user.phone,
            "user_type": "novice",
            "type": "refresh",
        }
        refresh_token = jwt_service.create_refresh_token(refresh_token_data)

        # 创建一个模拟的UserSession记录
        test_session = UserSession(
            user_id=user.id,
            session_token="old_access_token",
            refresh_token=refresh_token,
            ip_address="***********",
            user_agent="test-agent",
            is_active=True,
            expires_at=datetime.now(timezone.utc) + timedelta(days=7),
        )
        user_db_session.add(test_session)
        user_db_session.commit()
        user_db_session.refresh(test_session)

        # Mock JWT验证和获取用户方法
        with patch.object(
            user_service_instance.jwt_service, "verify_token"
        ) as mock_verify, patch.object(
            user_service_instance, "get_user_by_id"
        ) as mock_get_user, patch.object(
            user_service_instance.jwt_service, "create_access_token"
        ) as mock_create_token, patch.object(
            user_service_instance.redis_service, "set_value"
        ) as mock_redis:

            mock_verify.return_value = {
                "sub": str(user.id),
                "phone": user.phone,
                "user_type": "novice",
                "type": "refresh",
            }
            mock_get_user.return_value = user
            mock_create_token.return_value = "new_access_token"
            mock_redis.return_value = True

            result = user_service_instance.refresh_access_token(
                refresh_token=refresh_token,
                db=user_db_session,
                ip_address="***********",
                user_agent="new-test-agent",
            )

            assert result is not None
            assert "access_token" in result
            assert result["access_token"] == "new_access_token"
            assert result["token_type"] == "bearer"
            assert "expires_in" in result


class TestUserTagTimestamps:
    """测试用户标签时间字段自动填充功能"""

    def test_user_tag_timestamps(self, user_db_session, unique_phone):
        """测试UserTag模型的时间字段自动填充"""
        # 创建用户
        user = create_test_user(user_db_session, unique_phone)

        # 创建用户标签
        tag = UserTag(
            user_id=user.id,
            tag_category="test",
            tag_name="测试标签",
            tag_value="测试值",
            weight=1.0,
        )
        user_db_session.add(tag)
        user_db_session.commit()
        user_db_session.refresh(tag)

        # 验证创建时间和更新时间已自动设置
        assert tag.created_at is not None
        assert tag.updated_at is not None
        assert tag.created_at == tag.updated_at

        # 记录初始时间
        initial_created_at = tag.created_at
        initial_updated_at = tag.updated_at

        # 等待一小段时间，然后更新
        time.sleep(0.1)

        # 更新标签
        tag.tag_value = "更新的测试值"
        user_db_session.commit()
        user_db_session.refresh(tag)

        # 验证created_at没有变化，updated_at已更新
        assert tag.created_at == initial_created_at
        assert tag.updated_at > initial_updated_at

    def test_user_timestamps(self, user_db_session, unique_phone):
        """测试User模型的时间字段自动填充"""
        # 创建用户
        user = create_test_user(user_db_session, unique_phone)

        # 验证创建时间和更新时间已自动设置
        assert user.created_at is not None
        assert user.updated_at is not None
        assert user.created_at == user.updated_at

        # 记录初始时间
        initial_created_at = user.created_at
        initial_updated_at = user.updated_at

        # 等待一小段时间，然后更新
        time.sleep(0.1)

        # 更新用户
        user.username = "test_user_updated"
        user_db_session.commit()
        user_db_session.refresh(user)

        # 验证created_at没有变化，updated_at已更新
        assert user.created_at == initial_created_at
        assert user.updated_at > initial_updated_at


class TestAPIEndpoints:
    """API端点测试"""

    def test_root_endpoint(self, test_client):
        """测试根端点"""
        response = test_client.get("/")
        assert response.status_code in [200, 404]  # 根据实际路由配置

    @patch("src.services.user_service.router.UserService")
    def test_send_sms_code_endpoint(self, mock_user_service_class, test_client):
        """测试发送短信验证码端点"""
        mock_service = Mock()
        mock_service.sms_code_service.send_sms_code.return_value = "123456"
        mock_user_service_class.return_value = mock_service

        response = test_client.post(
            "/api/v1/users/send-sms-code",
            json={"phone": "13812345678", "purpose": "login"},
        )

        # 根据实际API响应调整断言
        assert response.status_code in [200, 422, 404]

    @patch("src.services.user_service.router.UserService")
    def test_phone_login_endpoint(self, mock_user_service_class, test_client):
        """测试手机登录端点"""
        mock_service = Mock()
        mock_service.authenticate_with_phone_code.return_value = {
            "user": Mock(id=1, phone="13812345678"),
            "tokens": {"access_token": "test_token", "token_type": "bearer"},
        }
        mock_user_service_class.return_value = mock_service

        response = test_client.post(
            "/api/v1/users/phone-login", json={"phone": "13812345678", "code": "123456"}
        )

        # 根据实际API响应调整断言
        assert response.status_code in [200, 422, 404]
