"""
用户服务B端API测试模块
"""

from unittest.mock import Mock, patch

import pytest
from fastapi import FastAP<PERSON>
from fastapi.testclient import TestClient

from src.services.user_service.admin_router import router
from src.services.user_service.schemas import (AdminLoginRequest,
                                             AdminSendSmsRequest,
                                             SmsCodePurpose, UserType)

app = FastAPI()
app.include_router(router, prefix="/b/api/v1/admin/users")


@pytest.fixture
def test_client():
    """创建测试客户端"""
    return TestClient(app)


@pytest.fixture
def mock_current_admin():
    """模拟当前管理员用户"""
    from datetime import datetime, timezone
    from src.services.user_service.schemas import RiskLevel
    
    admin = Mock()
    admin.id = 1
    admin.phone = "13800138000"
    admin.username = "admin"
    admin.email = "<EMAIL>"
    admin.is_active = True
    admin.is_verified = True
    admin.is_admin = True
    admin.user_type = UserType.NOVICE
    admin.risk_level = RiskLevel.MODERATE
    admin.knowledge_level = 3
    admin.first_login_at = datetime.now(timezone.utc)
    admin.last_login_at = datetime.now(timezone.utc)
    admin.created_at = datetime.now(timezone.utc)
    admin.updated_at = datetime.now(timezone.utc)
    return admin


@pytest.fixture
def mock_user_service(mock_redis_service, mock_sms_service, db_session):
    """创建用户服务的mock"""
    with patch("src.services.user_service.admin_router.UserService") as mock_service:
        service_instance = Mock()
        service_instance.redis_service = mock_redis_service
        service_instance.sms_service = mock_sms_service
        service_instance.db_session = db_session
        mock_service.return_value = service_instance
        yield service_instance


class TestAdminRouter:
    """管理员路由测试"""

    def test_send_verification_code_success(self, test_client, mock_current_admin, mock_user_service):
        """测试成功发送验证码"""
        # 配置mock
        mock_user_service.get_user_by_phone.return_value = mock_current_admin
        mock_user_service.send_verification_code.return_value = "123456"

        # 发送请求
        response = test_client.post(
            "/b/api/v1/admin/users/auth/send-code",
            json={"phone": "13800138000"}
        )

        # 验证响应
        assert response.status_code == 200
        assert response.json()["message"] == "Verification code sent successfully"

        # 验证服务调用
        mock_user_service.get_user_by_phone.assert_called_once()
        mock_user_service.send_verification_code.assert_called_once_with(
            phone="13800138000",
            purpose="login",
            client_type="admin"
        )

    def test_send_verification_code_non_admin(self, test_client, mock_user_service):
        """测试非管理员用户发送验证码"""
        # 配置mock
        non_admin_user = Mock()
        non_admin_user.is_admin = False
        mock_user_service.get_user_by_phone.return_value = non_admin_user

        # 发送请求
        response = test_client.post(
            "/b/api/v1/admin/users/auth/send-code",
            json={"phone": "13800138000"}
        )

        # 验证响应
        assert response.status_code == 403
        assert "not authorized for admin access" in response.json()["detail"]

    def test_send_verification_code_user_not_found(self, test_client, mock_user_service):
        """测试用户不存在时发送验证码"""
        # 配置mock
        mock_user_service.get_user_by_phone.return_value = None

        # 发送请求
        response = test_client.post(
            "/b/api/v1/admin/users/auth/send-code",
            json={"phone": "13800138000"}
        )

        # 验证响应
        assert response.status_code == 404
        assert "User not found" in response.json()["detail"]

    def test_admin_login_success(self, test_client, mock_current_admin, mock_user_service):
        """测试管理员登录成功"""
        # 配置mock
        mock_user_service.get_user_by_phone.return_value = mock_current_admin
        mock_user_service.sms_code_service.verify_code.return_value = True
        mock_user_service.create_user_tokens.return_value = {
            "access_token": "test_token",
            "token_type": "bearer"
        }
        # 配置角色权限返回值
        mock_user_service.get_user_roles_and_permissions.return_value = ([], [])
        mock_user_service.track_user_behavior = Mock()

        # 发送请求
        response = test_client.post(
            "/b/api/v1/admin/users/auth/login",
            json={
                "phone": "13800138000",
                "verification_code": "123456"
            }
        )

        # 验证响应
        assert response.status_code == 200
        assert "access_token" in response.json()
        assert response.json()["token_type"] == "bearer"

        # 验证服务调用
        mock_user_service.get_user_by_phone.assert_called_once()
        mock_user_service.sms_code_service.verify_code.assert_called_once_with(
            phone="13800138000",
            code="123456",
            purpose="login",
            client_type="admin"
        )
        mock_user_service.create_user_tokens.assert_called_once()

    def test_admin_login_invalid_code(self, test_client, mock_current_admin, mock_user_service):
        """测试验证码无效时的登录"""
        # 配置mock
        mock_user_service.get_user_by_phone.return_value = mock_current_admin
        mock_user_service.sms_code_service.verify_code.return_value = False

        # 发送请求
        response = test_client.post(
            "/b/api/v1/admin/users/auth/login",
            json={
                "phone": "13800138000",
                "verification_code": "123456"
            }
        )

        # 验证响应
        assert response.status_code == 401
        assert "Invalid verification code" in response.json()["detail"]

    def test_admin_login_non_admin(self, test_client, mock_user_service):
        """测试非管理员用户登录"""
        # 配置mock
        non_admin_user = Mock()
        non_admin_user.is_admin = False
        mock_user_service.get_user_by_phone.return_value = non_admin_user

        # 发送请求
        response = test_client.post(
            "/b/api/v1/admin/users/auth/login",
            json={
                "phone": "13800138000",
                "verification_code": "123456"
            }
        )

        # 验证响应
        assert response.status_code == 403
        assert "not authorized for admin access" in response.json()["detail"]

    def test_admin_login_user_not_found(self, test_client, mock_user_service):
        """测试用户不存在时的登录"""
        # 配置mock
        mock_user_service.get_user_by_phone.return_value = None

        # 发送请求
        response = test_client.post(
            "/b/api/v1/admin/users/auth/login",
            json={
                "phone": "13800138000",
                "verification_code": "123456"
            }
        )

        # 验证响应
        assert response.status_code == 404
        assert "User not found" in response.json()["detail"] 