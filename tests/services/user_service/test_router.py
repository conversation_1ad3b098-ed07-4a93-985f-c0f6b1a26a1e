"""
用户服务C端API测试模块
"""

from unittest.mock import Mock, patch

import pytest
from fastapi import FastAP<PERSON>
from fastapi.testclient import TestClient

from src.services.user_service.router import router
from src.services.user_service.schemas import (PhoneLoginRequest,
                                             SendSmsCodeRequest,
                                             SmsCodePurpose,
                                             UserProfileUpdate,
                                             UserType)

app = FastAPI()
app.include_router(router, prefix="/api/v1/users")


@pytest.fixture
def test_client():
    """创建测试客户端"""
    return TestClient(app)


@pytest.fixture
def mock_current_user():
    """模拟当前用户"""
    from datetime import datetime, timezone
    from src.services.user_service.schemas import RiskLevel
    
    user = Mock()
    user.id = 1
    user.phone = "13800138000"
    user.username = "testuser"
    user.email = "<EMAIL>"
    user.is_active = True
    user.is_verified = True
    user.user_type = UserType.NOVICE
    user.risk_level = RiskLevel.MODERATE
    user.knowledge_level = 1
    user.first_login_at = datetime.now(timezone.utc)
    user.last_login_at = datetime.now(timezone.utc)
    user.created_at = datetime.now(timezone.utc)
    user.updated_at = datetime.now(timezone.utc)
    return user


@pytest.fixture
def mock_user_service(mock_redis_service, mock_sms_service, db_session):
    """创建用户服务的mock"""
    with patch("src.services.user_service.router.UserService") as mock_service:
        service_instance = Mock()
        service_instance.redis_service = mock_redis_service
        service_instance.sms_service = mock_sms_service
        service_instance.db_session = db_session
        mock_service.return_value = service_instance
        yield service_instance


class TestUserRouter:
    """用户路由测试"""

    def test_send_verification_code_success(self, test_client, mock_user_service):
        """测试成功发送验证码"""
        # 配置mock
        mock_user_service.send_verification_code.return_value = "123456"

        # 发送请求
        response = test_client.post(
            "/api/v1/users/send-sms-code",
            json={"phone": "13800138000", "purpose": "login"}
        )

        # 验证响应
        assert response.status_code == 200
        assert response.json()["message"] == "Verification code sent successfully"

        # 验证服务调用 (C端默认client_type为"user")
        mock_user_service.send_verification_code.assert_called_once_with(
            "13800138000",
            "login",
            "user"
        )

    def test_phone_login_success(self, test_client, mock_current_user, mock_user_service):
        """测试手机登录成功"""
        # 配置mock
        mock_user_service.authenticate_with_phone_code.return_value = mock_current_user
        mock_user_service.create_user_tokens.return_value = {
            "access_token": "test_token",
            "refresh_token": "test_refresh_token",
            "token_type": "bearer",
            "expires_in": 3600
        }
        mock_user_service.track_user_behavior.return_value = None

        # 发送请求
        response = test_client.post(
            "/api/v1/users/phone-login",
            json={
                "phone": "13800138000",
                "verification_code": "123456"
            }
        )

        # 验证响应
        assert response.status_code == 200
        assert "access_token" in response.json()
        assert response.json()["token_type"] == "bearer"

        # 验证服务调用
        mock_user_service.authenticate_with_phone_code.assert_called_once()
        mock_user_service.create_user_tokens.assert_called_once()

    def test_phone_login_invalid_code(self, test_client, mock_user_service):
        """测试验证码无效时的登录"""
        # 配置mock
        mock_user_service.authenticate_with_phone_code.return_value = None

        # 发送请求
        response = test_client.post(
            "/api/v1/users/phone-login",
            json={
                "phone": "13800138000",
                "verification_code": "123456"
            }
        )

        # 验证响应
        assert response.status_code == 401
        assert "Invalid verification code" in response.json()["detail"]

    def test_refresh_token_success(self, test_client, mock_current_user, mock_user_service):
        """测试成功刷新令牌"""
        # 配置mock
        mock_user_service.refresh_access_token.return_value = {
            "access_token": "new_test_token",
            "token_type": "bearer"
        }

        # 发送请求
        response = test_client.post(
            "/api/v1/users/refresh-token",
            json={"refresh_token": "old_test_token"}
        )

        # 验证响应
        assert response.status_code == 200
        assert "access_token" in response.json()
        assert response.json()["token_type"] == "bearer"
        assert response.json()["access_token"] == "new_test_token"

        # 验证服务调用
        mock_user_service.refresh_access_token.assert_called_once()

    def test_refresh_token_invalid(self, test_client, mock_user_service):
        """测试无效的刷新令牌"""
        # 配置mock
        mock_user_service.refresh_access_token.return_value = None

        # 发送请求
        response = test_client.post(
            "/api/v1/users/refresh-token",
            json={"refresh_token": "invalid_token"}
        )

        # 验证响应
        assert response.status_code == 401
        assert "Invalid refresh token" in response.json()["detail"]

    def test_get_user_profile_success(self, test_client, mock_current_user):
        """测试成功获取用户资料"""
        with patch("src.services.user_service.router.get_current_active_user") as mock_get_user:
            mock_get_user.return_value = mock_current_user

            # 发送请求
            response = test_client.get("/api/v1/users/profile")

            # 验证响应
            assert response.status_code == 200
            assert response.json()["phone"] == mock_current_user.phone
            assert response.json()["username"] == mock_current_user.username
            assert response.json()["email"] == mock_current_user.email

    def test_update_user_profile_success(self, test_client, mock_current_user, mock_user_service):
        """测试成功更新用户资料"""
        with patch("src.services.user_service.router.get_current_active_user") as mock_get_user:
            mock_get_user.return_value = mock_current_user
            
            # 配置mock
            updated_user = Mock()
            updated_user.id = 1
            updated_user.phone = "13800138000"
            updated_user.username = "new_username"
            updated_user.email = "<EMAIL>"
            updated_user.is_active = True
            updated_user.is_verified = True
            updated_user.user_type = UserType.NOVICE
            # 添加必要的属性
            from datetime import datetime, timezone
            from src.services.user_service.schemas import RiskLevel
            updated_user.risk_level = RiskLevel.MODERATE
            updated_user.knowledge_level = 1
            updated_user.first_login_at = datetime.now(timezone.utc)
            updated_user.last_login_at = datetime.now(timezone.utc)
            updated_user.created_at = datetime.now(timezone.utc)
            updated_user.updated_at = datetime.now(timezone.utc)
            
            mock_user_service.update_user_profile.return_value = updated_user
            mock_user_service.track_user_behavior.return_value = None

            # 发送请求
            response = test_client.put(
                "/api/v1/users/profile",
                json={
                    "username": "new_username",
                    "email": "<EMAIL>"
                }
            )

            # 验证响应
            assert response.status_code == 200
            assert response.json()["username"] == "new_username"
            assert response.json()["email"] == "<EMAIL>"

            # 验证服务调用
            mock_user_service.update_user_profile.assert_called_once()

    def test_update_user_profile_invalid_data(self, test_client, mock_current_user):
        """测试更新用户资料时的无效数据"""
        with patch("src.services.user_service.router.get_current_active_user") as mock_get_user:
            mock_get_user.return_value = mock_current_user

            # 发送请求（无效的邮箱格式）
            response = test_client.put(
                "/api/v1/users/profile",
                json={
                    "username": "new_username",
                    "email": "invalid_email"
                }
            )

            # 验证响应
            assert response.status_code == 422 