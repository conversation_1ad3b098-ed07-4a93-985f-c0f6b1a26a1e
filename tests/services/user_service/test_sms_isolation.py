"""
SMS验证码隔离测试
测试B端和C端验证码完全隔离的功能
"""

import pytest
from unittest.mock import Mock, patch

from src.services.user_service.service import SmsCodeService, RedisService
from src.services.user_service.schemas import SmsCodePurpose
from src.services.sms_service.service import SmsService


@pytest.fixture
def mock_redis_service():
    """创建Redis服务的mock"""
    mock_redis = Mock(spec=RedisService)
    mock_redis.set_value = Mock(return_value=True)
    mock_redis.get_value = Mock(return_value="123456")
    mock_redis.delete_key = Mock(return_value=True)
    mock_redis.increment_value = Mock(return_value=1)
    return mock_redis


@pytest.fixture
def mock_sms_service():
    """创建SMS服务的mock"""
    mock_sms = Mock(spec=SmsService)
    mock_result = Mock()
    mock_result.success = True
    mock_result.message = "发送成功"
    mock_sms.send_verification_code = Mock(return_value=mock_result)
    return mock_sms


@pytest.fixture
def sms_code_service(mock_redis_service, mock_sms_service):
    """创建SMS验证码服务实例"""
    return SmsCodeService(mock_redis_service, mock_sms_service)


class TestSmsCodeIsolation:
    """SMS验证码隔离测试"""

    def test_code_key_generation_for_different_clients(self, sms_code_service):
        """测试不同客户端的验证码key生成"""
        phone = "13800138000"
        purpose = SmsCodePurpose.LOGIN

        # 生成C端验证码key
        user_key = sms_code_service._get_code_key(phone, purpose, "user")
        
        # 生成B端验证码key
        admin_key = sms_code_service._get_code_key(phone, purpose, "admin")

        # 验证key不同
        assert user_key != admin_key
        assert user_key == f"sms:code:user:{phone}:{purpose}"
        assert admin_key == f"sms:code:admin:{phone}:{purpose}"

    def test_attempts_key_generation_for_different_clients(self, sms_code_service):
        """测试不同客户端的尝试次数key生成"""
        phone = "13800138000"
        purpose = SmsCodePurpose.LOGIN

        # 生成C端尝试次数key
        user_attempts_key = sms_code_service._get_attempts_key(phone, purpose, "user")
        
        # 生成B端尝试次数key
        admin_attempts_key = sms_code_service._get_attempts_key(phone, purpose, "admin")

        # 验证key不同
        assert user_attempts_key != admin_attempts_key
        assert user_attempts_key == f"sms:attempts:user:{phone}:{purpose}"
        assert admin_attempts_key == f"sms:attempts:admin:{phone}:{purpose}"

    def test_send_sms_code_isolation(self, sms_code_service, mock_redis_service):
        """测试发送验证码时的隔离"""
        phone = "13800138000"
        purpose = SmsCodePurpose.LOGIN

        # 发送C端验证码
        user_code = sms_code_service.send_sms_code(phone, purpose, "user")
        
        # 发送B端验证码
        admin_code = sms_code_service.send_sms_code(phone, purpose, "admin")

        # 验证Redis调用
        assert mock_redis_service.set_value.call_count == 2
        
        # 获取调用参数
        calls = mock_redis_service.set_value.call_args_list
        user_call_key = calls[0][0][0]  # 第一次调用的第一个参数
        admin_call_key = calls[1][0][0]  # 第二次调用的第一个参数

        # 验证使用了不同的key
        assert user_call_key == f"sms:code:user:{phone}:{purpose}"
        assert admin_call_key == f"sms:code:admin:{phone}:{purpose}"

    def test_verify_code_isolation(self, sms_code_service, mock_redis_service):
        """测试验证码验证时的隔离"""
        phone = "13800138000"
        purpose = SmsCodePurpose.LOGIN
        code = "123456"

        # 设置不同的存储值
        def mock_get_value(key):
            if "user" in key:
                return "123456"  # C端验证码
            elif "admin" in key:
                return "654321"  # B端验证码
            return None

        mock_redis_service.get_value.side_effect = mock_get_value

        # 验证C端验证码
        user_result = sms_code_service.verify_code(phone, code, purpose, "user")
        
        # 验证B端验证码（使用相同验证码应该失败）
        admin_result = sms_code_service.verify_code(phone, code, purpose, "admin")

        # C端验证应该成功，B端验证应该失败
        assert user_result is True
        assert admin_result is False

    def test_cross_client_verification_fails(self, sms_code_service, mock_redis_service):
        """测试跨客户端验证失败"""
        phone = "13800138000"
        purpose = SmsCodePurpose.LOGIN
        
        # 模拟只有C端有验证码，B端没有
        def mock_get_value(key):
            if "user" in key:
                return "123456"
            return None  # B端没有验证码

        mock_redis_service.get_value.side_effect = mock_get_value

        # 尝试用C端验证码登录B端（应该失败）
        admin_result = sms_code_service.verify_code(phone, "123456", purpose, "admin")
        
        # 验证失败
        assert admin_result is False

    def test_same_phone_different_client_codes_coexist(self, sms_code_service, mock_redis_service):
        """测试同一手机号在不同客户端的验证码可以并存"""
        phone = "13800138000"
        purpose = SmsCodePurpose.LOGIN

        # 存储不同的验证码值
        stored_codes = {}
        
        def mock_set_value(key, value, expire_time=None):
            stored_codes[key] = value
            return True
            
        def mock_get_value(key):
            return stored_codes.get(key)

        mock_redis_service.set_value.side_effect = mock_set_value
        mock_redis_service.get_value.side_effect = mock_get_value

        # 发送C端和B端验证码
        user_code = sms_code_service.send_sms_code(phone, purpose, "user")
        admin_code = sms_code_service.send_sms_code(phone, purpose, "admin")

        # 验证两个验证码都存在且不同
        user_key = f"sms:code:user:{phone}:{purpose}"
        admin_key = f"sms:code:admin:{phone}:{purpose}"
        
        assert user_key in stored_codes
        assert admin_key in stored_codes
        assert stored_codes[user_key] != stored_codes[admin_key]  # 验证码应该不同

        # 验证各自的验证码都有效
        user_verify_result = sms_code_service.verify_code(phone, stored_codes[user_key], purpose, "user")
        admin_verify_result = sms_code_service.verify_code(phone, stored_codes[admin_key], purpose, "admin")
        
        assert user_verify_result is True
        assert admin_verify_result is True

    def test_client_type_defaults_to_user(self, sms_code_service):
        """测试客户端类型默认为user"""
        phone = "13800138000"
        purpose = SmsCodePurpose.LOGIN

        # 不指定client_type
        key_default = sms_code_service._get_code_key(phone, purpose)
        
        # 明确指定为user
        key_user = sms_code_service._get_code_key(phone, purpose, "user")

        # 应该相同
        assert key_default == key_user
        assert key_default == f"sms:code:user:{phone}:{purpose}"

    def test_logging_includes_client_type(self, sms_code_service, mock_redis_service):
        """测试日志包含客户端类型信息"""
        phone = "13800138000"
        purpose = SmsCodePurpose.LOGIN

        with patch('src.services.user_service.service.logging') as mock_logging:
            # 发送验证码
            sms_code_service.send_sms_code(phone, purpose, "admin")
            
            # 验证日志调用包含客户端类型
            mock_logging.info.assert_called()
            log_message = mock_logging.info.call_args[0][0]
            assert "客户端类型: admin" in log_message 