"""
用户服务专用的SQLite兼容测试模型
"""

from datetime import datetime

from sqlalchemy import (DECIMAL, BigInteger, Boolean, Column, DateTime,
                        ForeignKey, Integer, String, Text, event)
from sqlalchemy.orm import declarative_base
from sqlalchemy.sql import func

# 为用户服务测试创建独立的Base，避免表名冲突
UserServiceBase = declarative_base()


def update_timestamp_on_update(mapper, connection, target):
    """SQLAlchemy事件处理器：在更新时自动设置updated_at字段"""
    if hasattr(target, "updated_at"):
        target.updated_at = datetime.now()


class User(UserServiceBase):
    """用户基础信息表"""

    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, nullable=True)
    email = Column(String(100), unique=True, nullable=True)
    phone = Column(String(20), unique=True, nullable=False, index=True)
    password_hash = Column(String(255), nullable=True)
    user_type = Column(Integer, nullable=False, default=1)
    risk_level = Column(Integer, nullable=False, default=3)
    knowledge_level = Column(Integer, nullable=False, default=1)
    is_active = Column(Boolean, default=True)
    is_verified = Column(Boolean, default=False)
    is_admin = Column(Boolean, default=False)
    first_login_at = Column(DateTime, nullable=True)
    last_login_at = Column(DateTime, nullable=True)
    created_at = Column(DateTime, default=func.current_timestamp())
    updated_at = Column(
        DateTime, default=func.current_timestamp(), onupdate=func.current_timestamp()
    )


class UserTag(UserServiceBase):
    """用户标签表"""

    __tablename__ = "user_tags"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, nullable=False)
    tag_category = Column(String(50), nullable=False)
    tag_name = Column(String(100), nullable=False)
    tag_value = Column(Text)
    weight = Column(DECIMAL(3, 2), default=1.0)
    created_at = Column(DateTime, default=func.current_timestamp())
    updated_at = Column(
        DateTime, default=func.current_timestamp(), onupdate=func.current_timestamp()
    )


class UserBehavior(UserServiceBase):
    """用户行为追踪表"""

    __tablename__ = "user_behaviors"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, nullable=False)
    action_type = Column(String(50), nullable=False)
    action_target = Column(String(100))
    action_details = Column(Text)
    ip_address = Column(String(45))
    user_agent = Column(Text)
    created_at = Column(DateTime, default=func.current_timestamp())


class UserSession(UserServiceBase):
    """用户会话表"""

    __tablename__ = "user_sessions"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, nullable=False)
    session_token = Column(String(255), unique=True, nullable=False)
    refresh_token = Column(String(255), unique=True, nullable=False)
    ip_address = Column(String(45))
    user_agent = Column(Text)
    is_active = Column(Boolean, default=True)
    expires_at = Column(DateTime, nullable=False)
    last_used_at = Column(
        DateTime, default=func.current_timestamp(), onupdate=func.current_timestamp()
    )
    created_at = Column(DateTime, default=func.current_timestamp())


class SmsVerificationCode(UserServiceBase):
    """短信验证码表"""

    __tablename__ = "sms_verification_codes"

    id = Column(Integer, primary_key=True, index=True)
    phone = Column(String(20), nullable=False, index=True)
    code = Column(String(10), nullable=False)
    purpose = Column(String(20), nullable=False)
    is_used = Column(Boolean, default=False)
    attempts = Column(Integer, default=0)
    expires_at = Column(DateTime, nullable=False)
    created_at = Column(DateTime, default=func.current_timestamp())


# 为User和UserTag模型注册更新事件
event.listen(User, "before_update", update_timestamp_on_update)
event.listen(UserTag, "before_update", update_timestamp_on_update)
