"""
SMS验证码隔离集成测试
测试B端和C端验证码完全隔离的完整流程
"""

import pytest
from unittest.mock import Mock, patch

from src.services.user_service.service import UserService
from src.services.user_service.schemas import SmsCodePurpose
from src.services.sms_service.service import SmsService


class TestSmsCodeIsolationIntegration:
    """SMS验证码隔离集成测试"""

    def test_b_c_sms_code_isolation_scenario(self):
        """
        测试B端和C端验证码隔离的完整场景
        
        场景：
        1. 同一手机号在B端发送验证码
        2. 同一手机号在C端发送验证码  
        3. B端验证码不能用于C端登录
        4. C端验证码不能用于B端登录
        5. 各自验证码只能用于各自的端
        """
        from src.services.user_service.service import RedisService, SmsCodeService
        
        # 创建mock服务
        mock_redis = Mock(spec=RedisService)
        mock_sms = Mock(spec=SmsService)
        
        # 存储验证码的字典
        stored_codes = {}
        stored_attempts = {}
        
        def mock_set_value(key, value, expire_time=None):
            stored_codes[key] = value
            return True
            
        def mock_get_value(key):
            return stored_codes.get(key)
            
        def mock_delete_key(key):
            if key in stored_codes:
                del stored_codes[key]
            if key in stored_attempts:
                del stored_attempts[key]
            return True
            
        def mock_increment_value(key, expire_time=None):
            stored_attempts[key] = stored_attempts.get(key, 0) + 1
            return stored_attempts[key]

        mock_redis.set_value.side_effect = mock_set_value
        mock_redis.get_value.side_effect = mock_get_value
        mock_redis.delete_key.side_effect = mock_delete_key
        mock_redis.increment_value.side_effect = mock_increment_value
        
        # Mock SMS发送成功
        mock_result = Mock()
        mock_result.success = True
        mock_result.message = "发送成功"
        mock_sms.send_verification_code.return_value = mock_result
        
        # 创建SMS验证码服务
        sms_code_service = SmsCodeService(mock_redis, mock_sms)
        
        phone = "13800138000"
        purpose = SmsCodePurpose.LOGIN
        
        # 步骤1: B端发送验证码
        admin_code = sms_code_service.send_sms_code(phone, purpose, "admin")
        print(f"B端验证码: {admin_code}")
        
        # 步骤2: C端发送验证码  
        user_code = sms_code_service.send_sms_code(phone, purpose, "user")
        print(f"C端验证码: {user_code}")
        
        # 验证存储的key不同
        admin_key = f"sms:code:admin:{phone}:{purpose}"
        user_key = f"sms:code:user:{phone}:{purpose}"
        
        assert admin_key in stored_codes
        assert user_key in stored_codes
        assert stored_codes[admin_key] == admin_code
        assert stored_codes[user_key] == user_code
        
        # 验证验证码不同（虽然不是必须的，但通常会不同）
        print(f"验证码是否不同: {admin_code != user_code}")
        
        # 步骤3: 尝试用B端验证码登录C端（应该失败）
        cross_verify_admin_to_user = sms_code_service.verify_code(
            phone, admin_code, purpose, "user"
        )
        assert cross_verify_admin_to_user is False, "B端验证码不应该能用于C端登录"
        
        # 步骤4: 尝试用C端验证码登录B端（应该失败）
        cross_verify_user_to_admin = sms_code_service.verify_code(
            phone, user_code, purpose, "admin"
        )
        assert cross_verify_user_to_admin is False, "C端验证码不应该能用于B端登录"
        
        # 步骤5: 验证各自的验证码在各自的端能正常使用
        admin_verify_success = sms_code_service.verify_code(
            phone, admin_code, purpose, "admin"
        )
        assert admin_verify_success is True, "B端验证码应该能在B端成功验证"
        
        # 由于验证成功后验证码会被删除，需要重新发送C端验证码
        user_code_new = sms_code_service.send_sms_code(phone, purpose, "user")
        user_verify_success = sms_code_service.verify_code(
            phone, user_code_new, purpose, "user"  
        )
        assert user_verify_success is True, "C端验证码应该能在C端成功验证"
        
        print("✅ 所有验证码隔离测试通过！")

    def test_user_service_integration_isolation(self):
        """
        测试通过UserService的完整验证码隔离流程
        """
        from src.services.user_service.service import RedisService, SmsCodeService
        
        # 创建mock数据库会话
        mock_db = Mock()
        
        # 创建UserService实例
        user_service = UserService(db_session=mock_db)
        
        # Mock Redis和SMS服务
        mock_redis = Mock(spec=RedisService)
        mock_sms = Mock(spec=SmsService)
        
        # 存储验证码的字典
        stored_codes = {}
        
        def mock_set_value(key, value, expire_time=None):
            stored_codes[key] = value
            return True
            
        def mock_get_value(key):
            return stored_codes.get(key)
            
        def mock_delete_key(key):
            if key in stored_codes:
                del stored_codes[key]
            return True

        mock_redis.set_value.side_effect = mock_set_value
        mock_redis.get_value.side_effect = mock_get_value
        mock_redis.delete_key.side_effect = mock_delete_key
        mock_redis.increment_value.return_value = 1
        
        # Mock SMS发送成功
        mock_result = Mock()
        mock_result.success = True
        mock_result.message = "发送成功"
        mock_sms.send_verification_code.return_value = mock_result
        
        # 替换UserService中的服务
        user_service.sms_code_service = SmsCodeService(mock_redis, mock_sms)
        
        phone = "13800138000"
        purpose = SmsCodePurpose.LOGIN
        
        # 通过UserService发送B端验证码
        admin_code = user_service.send_verification_code(phone, purpose, "admin")
        
        # 通过UserService发送C端验证码
        user_code = user_service.send_verification_code(phone, purpose, "user")
        
        # 验证存储隔离
        admin_key = f"sms:code:admin:{phone}:{purpose}"
        user_key = f"sms:code:user:{phone}:{purpose}"
        
        assert admin_key in stored_codes
        assert user_key in stored_codes
        assert stored_codes[admin_key] == admin_code
        assert stored_codes[user_key] == user_code
        
        # 验证跨端验证失败
        admin_verify_result = user_service.sms_code_service.verify_code(
            phone, admin_code, purpose, "user"
        )
        assert admin_verify_result is False
        
        user_verify_result = user_service.sms_code_service.verify_code(
            phone, user_code, purpose, "admin"
        )
        assert user_verify_result is False
        
        print("✅ UserService集成隔离测试通过！")
        
    def test_realistic_attack_scenario(self):
        """
        测试真实的攻击场景
        
        场景：恶意用户尝试利用C端验证码登录B端管理系统
        """
        from src.services.user_service.service import RedisService, SmsCodeService
        
        # 创建mock服务
        mock_redis = Mock(spec=RedisService)
        mock_sms = Mock(spec=SmsService)
        
        # 存储验证码的字典（模拟Redis）
        redis_storage = {}
        
        def mock_set_value(key, value, expire_time=None):
            redis_storage[key] = value
            return True
            
        def mock_get_value(key):
            return redis_storage.get(key)
            
        def mock_delete_key(key):
            if key in redis_storage:
                del redis_storage[key]
            return True

        mock_redis.set_value.side_effect = mock_set_value
        mock_redis.get_value.side_effect = mock_get_value
        mock_redis.delete_key.side_effect = mock_delete_key
        mock_redis.increment_value.return_value = 1
        
        # Mock SMS发送成功
        mock_result = Mock()
        mock_result.success = True
        mock_sms.send_verification_code.return_value = mock_result
        
        sms_code_service = SmsCodeService(mock_redis, mock_sms)
        
        phone = "13800138000"  # 假设这是管理员的手机号
        purpose = SmsCodePurpose.LOGIN
        
        # 1. 恶意用户在C端获取验证码
        malicious_code = sms_code_service.send_sms_code(phone, purpose, "user")
        print(f"恶意用户在C端获取的验证码: {malicious_code}")
        
        # 2. 恶意用户尝试使用该验证码登录B端管理系统
        attack_attempt = sms_code_service.verify_code(
            phone, malicious_code, purpose, "admin"
        )
        
        # 3. 攻击应该失败
        assert attack_attempt is False, "恶意用户不应该能用C端验证码登录B端"
        
        # 4. 验证正常的B端登录仍然有效
        admin_code = sms_code_service.send_sms_code(phone, purpose, "admin")
        legitimate_admin_login = sms_code_service.verify_code(
            phone, admin_code, purpose, "admin"
        )
        assert legitimate_admin_login is True, "正常的B端登录应该成功"
        
        print("✅ 攻击场景防护测试通过！恶意用户无法使用C端验证码登录B端管理系统") 