"""
SMS服务基础测试模块
包含SMS相关schemas和基础功能的测试
"""

from unittest.mock import Mock

import pytest

from src.services.sms_service.schemas import SmsSendResponse


class TestSmsSchemas:
    """SMS Schema测试类"""

    def test_sms_send_response_success(self):
        """测试SMS发送成功响应"""
        response = SmsSendResponse(
            success=True, message="验证码发送成功", request_id="test_request_id"
        )

        assert response.success is True
        assert response.message == "验证码发送成功"
        assert response.request_id == "test_request_id"

    def test_sms_send_response_failure(self):
        """测试SMS发送失败响应"""
        response = SmsSendResponse(
            success=False, message="发送失败", error_code="InvalidParameter"
        )

        assert response.success is False
        assert response.message == "发送失败"
        assert response.error_code == "InvalidParameter"

    def test_sms_send_response_minimal(self):
        """测试SMS发送最小响应"""
        response = SmsSendResponse(success=True, message="OK")

        assert response.success is True
        assert response.message == "OK"


# TODO: 添加更多SMS相关测试，如：
# - 验证码验证功能测试
# - 频率限制测试
# - 模板管理测试
# - 业务配置测试
