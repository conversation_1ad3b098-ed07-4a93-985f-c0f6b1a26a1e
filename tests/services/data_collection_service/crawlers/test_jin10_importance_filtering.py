"""
金十数据重要性过滤专项测试

验证金十数据网站的重要性过滤功能，确保只采集带有is-important类的快讯
"""

import asyncio
import pytest
from unittest.mock import Mock, patch, MagicMock
from bs4 import BeautifulSoup

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.services.data_collection_service.crawlers.web_crawler import WebCrawler
from src.services.data_collection_service.crawlers.base_crawler import BaseCrawler
from src.services.data_collection_service.models import DataSource, CrawlTask


class TestJin10ImportanceFiltering:
    """金十数据重要性过滤专项测试"""

    def setup_method(self):
        """测试前准备"""
        # 创建模拟的数据源配置
        self.mock_data_source = Mock(spec=DataSource)
        self.mock_data_source.id = 1
        self.mock_data_source.name = "金十数据快讯"
        self.mock_data_source.base_url = "https://www.jin10.com"
        self.mock_data_source.collection_method = "web_scraping"
        self.mock_data_source.content_category = "financial_news"
        
        # 创建模拟配置
        self.mock_config = Mock()
        self.mock_config.extraction_rules = {
            "data_extraction": {
                "news_items": {
                    "importance_filter": {
                        "enabled": True,
                        "container_importance_selector": ".jin-flash-item, .flash-item",
                        "importance_class_patterns": ["is-important", "important"],
                        "fallback_behavior": "accept_all",
                        "site_specific_configs": {
                            "jin10.com": {
                                "importance_selector": ".jin-flash-item.is-important",
                                "reject_without_importance": True
                            },
                            "default": {
                                "importance_selector": ".important",
                                "reject_without_importance": False
                            }
                        }
                    }
                }
            }
        }
        
        self.mock_config.validation_rules = {
            "importance_validation": {
                "enabled": True,
                "min_importance_level": 3,
                "allow_unknown_importance": False,
                "site_specific_rules": {
                    "jin10.com": {
                        "require_is_important_class": True,
                        "min_importance_level": 3
                    },
                    "default": {
                        "require_is_important_class": False,
                        "min_importance_level": 2
                    }
                }
            }
        }
        
        self.mock_config.selector_config = {
            "flash_selector": ".jin-flash-item-container",
            "news_title": ".right-common-title",
            "news_content": ".flash-text"
        }
        
        self.mock_data_source.configs = [self.mock_config]
        
        # 创建模拟任务
        self.mock_task = Mock(spec=CrawlTask)
        self.mock_task.id = 1
        self.mock_task.task_type = "flash_news"
        self.mock_task.target_url = "https://www.jin10.com"

    def test_important_message_html_detection(self):
        """测试重要消息HTML检测"""
        # 金十数据重要消息HTML（示例2）
        important_html = '''
        <div class="jin-flash-item-container is-normal">
            <div class="jin-flash-item flash is-important">
                <div class="item-time">18:01:44</div>
                <div class="item-right is-common">
                    <div class="right-top">
                        <div class="right-common">
                            <b class="right-common-title">金十数据整理：特朗普过去24小时都忙了什么？（2025-06-29）</b>
                        </div>
                        <div class="right-content">
                            <div class="flash-text">这是一条重要的快讯内容。</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        '''
        
        crawler = WebCrawler(self.mock_data_source, self.mock_task)
        soup = BeautifulSoup(important_html, "html.parser")
        container = soup.find("div", class_="jin-flash-item-container")
        
        # 获取重要性过滤配置
        filter_config = crawler._get_importance_filter_config()
        
        # 测试重要性检查
        result = crawler._check_item_importance(container, filter_config, "https://www.jin10.com")
        
        assert result is True, "包含is-important类的消息应该通过过滤"

    def test_normal_message_html_detection(self):
        """测试普通消息HTML检测"""
        # 金十数据普通消息HTML（示例1）
        normal_html = '''
        <div class="jin-flash-item-container is-normal">
            <div class="jin-flash-item flash">
                <div class="item-time">17:33:34</div>
                <div class="item-right is-common">
                    <div class="right-top">
                        <div class="right-common">
                            <b class="right-common-title">俄美情报部门高官通话 讨论"感兴趣的问题"</b>
                        </div>
                        <div class="right-content">
                            <div class="flash-text">金十数据6月29日讯，俄罗斯对外情报局局长纳雷什金透露。</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        '''
        
        crawler = WebCrawler(self.mock_data_source, self.mock_task)
        soup = BeautifulSoup(normal_html, "html.parser")
        container = soup.find("div", class_="jin-flash-item-container")
        
        # 获取重要性过滤配置
        filter_config = crawler._get_importance_filter_config()
        
        # 测试重要性检查
        result = crawler._check_item_importance(container, filter_config, "https://www.jin10.com")
        
        assert result is False, "不包含is-important类的消息应该被过滤"

    def test_importance_level_extraction_from_html(self):
        """测试从HTML中提取重要性级别"""
        crawler = WebCrawler(self.mock_data_source, self.mock_task)
        
        # 测试包含is-important类的数据
        important_data = {
            "source_html": '<div class="jin-flash-item is-important">重要内容</div>',
            "title": "重要消息",
            "content": "这是重要内容"
        }
        
        importance_level = crawler._extract_importance_level(important_data)
        assert importance_level == 3, f"期望重要性级别为3，实际为{importance_level}"
        
        # 测试不包含重要性类的数据
        normal_data = {
            "source_html": '<div class="jin-flash-item">普通内容</div>',
            "title": "普通消息",
            "content": "这是普通内容"
        }
        
        importance_level = crawler._extract_importance_level(normal_data)
        assert importance_level == 0, f"期望重要性级别为0，实际为{importance_level}"

    def test_importance_validation_for_jin10(self):
        """测试金十数据的重要性验证"""
        crawler = WebCrawler(self.mock_data_source, self.mock_task)
        
        # 测试重要消息验证通过
        important_processed = {
            "title": "重要消息",
            "content": "重要内容",
            "importance_level": 3,
            "source_url": "https://www.jin10.com/flash/123"
        }
        important_raw = {
            "source_html": '<div class="jin-flash-item is-important">content</div>'
        }
        
        result = crawler._validate_importance(important_processed, important_raw)
        assert result is True, "重要消息应该通过验证"
        
        # 测试普通消息验证失败
        normal_processed = {
            "title": "普通消息",
            "content": "普通内容",
            "importance_level": 0,
            "source_url": "https://www.jin10.com/flash/124"
        }
        normal_raw = {
            "source_html": '<div class="jin-flash-item">content</div>'
        }
        
        result = crawler._validate_importance(normal_processed, normal_raw)
        assert result is False, "普通消息应该验证失败"

    def test_site_domain_extraction(self):
        """测试站点域名提取"""
        crawler = WebCrawler(self.mock_data_source, self.mock_task)
        
        test_urls = [
            ("https://www.jin10.com", "jin10.com"),
            ("https://jin10.com/flash/123", "jin10.com"),
            ("https://www.jin10.com/news/detail/456", "jin10.com"),
            ("https://example.com", "example.com"),
            ("", "unknown")
        ]
        
        for url, expected in test_urls:
            result = crawler._get_site_domain(url)
            assert result == expected, f"URL {url} 的域名提取失败，期望 {expected}，实际 {result}"

    def test_element_importance_matching_nested(self):
        """测试嵌套元素的重要性匹配"""
        crawler = WebCrawler(self.mock_data_source, self.mock_task)
        
        # 测试父元素包含重要性类的情况
        nested_html = '''
        <div class="jin-flash-item-container">
            <div class="jin-flash-item is-important">
                <div class="item-content">
                    <span class="text">这是嵌套内容</span>
                </div>
            </div>
        </div>
        '''
        
        soup = BeautifulSoup(nested_html, "html.parser")
        # 检查内层元素
        inner_element = soup.find("span", class_="text")
        
        result = crawler._element_matches_importance(inner_element, ".is-important")
        assert result is True, "内层元素应该能检测到父元素的重要性类"

    def test_compatibility_with_other_sites(self):
        """测试与其他网站的兼容性"""
        crawler = WebCrawler(self.mock_data_source, self.mock_task)
        
        # 模拟其他网站的HTML
        other_site_html = '''
        <div class="news-item">
            <h3>其他网站新闻</h3>
            <p>这是其他网站的新闻内容</p>
        </div>
        '''
        
        soup = BeautifulSoup(other_site_html, "html.parser")
        element = soup.find("div", class_="news-item")
        
        # 获取重要性过滤配置
        filter_config = crawler._get_importance_filter_config()
        
        # 测试其他网站的内容（应该通过，因为使用默认配置）
        result = crawler._check_item_importance(element, filter_config, "https://example.com")
        
        assert result is True, "其他网站的内容应该通过过滤（兼容性）"

    @patch('src.services.data_collection_service.crawlers.web_crawler.logger')
    def test_logging_for_filtered_items(self, mock_logger):
        """测试被过滤项目的日志记录"""
        crawler = WebCrawler(self.mock_data_source, self.mock_task)
        
        normal_html = '''
        <div class="jin-flash-item-container">
            <div class="jin-flash-item flash">
                <div class="flash-text">普通内容</div>
            </div>
        </div>
        '''
        
        soup = BeautifulSoup(normal_html, "html.parser")
        container = soup.find("div", class_="jin-flash-item-container")
        
        filter_config = crawler._get_importance_filter_config()
        result = crawler._check_item_importance(container, filter_config, "https://www.jin10.com")
        
        # 验证日志记录
        assert result is False
        mock_logger.debug.assert_called()

    def test_config_loading(self):
        """测试配置加载"""
        crawler = WebCrawler(self.mock_data_source, self.mock_task)
        
        # 测试重要性过滤配置加载
        filter_config = crawler._get_importance_filter_config()
        
        assert filter_config.get("enabled") is True
        assert "jin10.com" in filter_config.get("site_specific_configs", {})
        assert filter_config["site_specific_configs"]["jin10.com"]["reject_without_importance"] is True

    def run_manual_test(self):
        """手动测试方法，用于调试"""
        print("开始金十数据重要性过滤测试...")
        
        # 测试重要消息
        print("\n1. 测试重要消息:")
        self.test_important_message_html_detection()
        print("✓ 重要消息检测通过")
        
        # 测试普通消息
        print("\n2. 测试普通消息:")
        self.test_normal_message_html_detection()
        print("✓ 普通消息过滤通过")
        
        # 测试重要性级别提取
        print("\n3. 测试重要性级别提取:")
        self.test_importance_level_extraction_from_html()
        print("✓ 重要性级别提取通过")
        
        # 测试重要性验证
        print("\n4. 测试重要性验证:")
        self.test_importance_validation_for_jin10()
        print("✓ 重要性验证通过")
        
        # 测试兼容性
        print("\n5. 测试兼容性:")
        self.test_compatibility_with_other_sites()
        print("✓ 兼容性测试通过")
        
        print("\n所有测试通过！✅")


def main():
    """主函数，用于手动运行测试"""
    test_instance = TestJin10ImportanceFiltering()
    test_instance.setup_method()
    test_instance.run_manual_test()


if __name__ == "__main__":
    main() 