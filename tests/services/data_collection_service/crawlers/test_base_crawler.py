"""
基础爬虫测试模块
"""

import asyncio
import time
from datetime import datetime
from decimal import Decimal
from unittest.mock import AsyncMock, Mock, patch

import pytest

from src.services.data_collection_service.crawlers.base_crawler import \
    BaseCrawler
from src.services.data_collection_service.models import CrawlTask, DataSource
from src.services.data_collection_service.schemas import (CollectionMethod,
                                                          ContentCategory,
                                                          CrawlMode,
                                                          CrawlResult,
                                                          TaskStatus,
                                                          TriggerType)


class MockCrawler(BaseCrawler):
    """用于测试的Mock爬虫类"""

    async def crawl(self) -> CrawlResult:
        """模拟爬取实现"""
        return CrawlResult(
            success=True,
            message="测试成功",
            data={"test": "data"},
        )


@pytest.fixture
def mock_data_source():
    """创建测试用数据源"""
    return DataSource(
        id=1,
        name="测试数据源",
        collection_method=CollectionMethod.API_JSON,
        content_category=ContentCategory.FINANCIAL_NEWS,
        base_url="https://test.example.com",
        crawl_mode=CrawlMode.INTERVAL,
        crawl_interval=3600,
        priority=5,
        max_concurrent_tasks=1,
        status="active",
        health_score=Decimal("0.95"),
        request_delay_min=2,
        request_delay_max=10,
        use_proxy=False,
        supports_realtime=False,
        event_driven_config={},
        max_consecutive_errors=10,
        error_count=0,
        consecutive_error_count=0,
        total_crawled_count=100,
        total_success_count=95,
        current_config_version=1,
        created_at=datetime.now(),
        updated_at=datetime.now(),
        tags=[],
    )


@pytest.fixture
def mock_crawl_task():
    """创建测试用采集任务"""
    return CrawlTask(
        id=1,
        source_id=1,
        task_type="news_list",
        trigger_type=TriggerType.INTERVAL,
        target_url="https://test.example.com/news",
        task_config={},
        status=TaskStatus.PENDING,
        progress=0,
        items_found=0,
        items_processed=0,
        items_success=0,
        items_failed=0,
        retry_count=0,
        max_retry_count=3,
        network_requests=0,
        created_at=datetime.now(),
        updated_at=datetime.now(),
    )


@pytest.fixture
def mock_crawler(mock_data_source, mock_crawl_task):
    """创建测试用爬虫实例"""
    return MockCrawler(mock_data_source, mock_crawl_task)


class TestBaseCrawler:
    """基础爬虫测试类"""

    def test_init(self, mock_data_source, mock_crawl_task):
        """测试爬虫初始化"""
        crawler = MockCrawler(mock_data_source, mock_crawl_task)

        assert crawler.data_source == mock_data_source
        assert crawler.task == mock_crawl_task
        assert crawler.session is None
        # MongoDB 连接由管理器处理，不在爬虫实例中
        assert crawler.items_found == 0
        assert crawler.items_success == 0
        assert crawler.start_time is None

    @pytest.mark.asyncio
    async def test_context_manager(self, mock_crawler):
        """测试异步上下文管理器"""
        with patch.object(mock_crawler, "setup") as mock_setup:
            with patch.object(mock_crawler, "cleanup") as mock_cleanup:
                async with mock_crawler:
                    assert mock_setup.called
                assert mock_cleanup.called

    @pytest.mark.asyncio
    async def test_setup(self, mock_crawler):
        """测试爬虫设置"""
        with patch("aiohttp.ClientSession") as mock_session_class:
            with patch(
                "src.core.mongodb.mongodb_manager.connect", return_value=True
            ) as mock_connect:
                mock_session = AsyncMock()
                mock_session_class.return_value = mock_session

                await mock_crawler.setup()

                assert mock_crawler.session == mock_session
                assert mock_crawler.start_time is not None
                # 验证MongoDB管理器被调用
                mock_connect.assert_called_once()

    def test_get_default_headers(self, mock_crawler):
        """测试默认请求头生成"""
        headers = mock_crawler._get_default_headers()

        assert "User-Agent" in headers
        assert "Accept" in headers
        assert "Accept-Language" in headers
        assert "Accept-Encoding" in headers

    def test_calculate_content_hash(self, mock_crawler):
        """测试内容哈希生成"""
        content = "测试内容"
        hash1 = mock_crawler._calculate_content_hash(content)
        hash2 = mock_crawler._calculate_content_hash(content)

        assert hash1 == hash2
        assert len(hash1) == 32  # MD5哈希长度

        # 不同内容应该产生不同哈希
        different_content = "不同的测试内容"
        hash3 = mock_crawler._calculate_content_hash(different_content)
        assert hash1 != hash3

    def test_extract_publish_time_dict(self, mock_crawler):
        """测试发布时间提取 - 字典输入"""
        # 测试字典格式
        data = {"publish_time": "2024-01-01T12:00:00Z"}
        result = mock_crawler._extract_publish_time(data)
        assert result is not None

        # 测试中文格式
        data = {"date": "2024年1月1日 12:00"}
        result = mock_crawler._extract_publish_time(data)
        # 可能无法解析中文格式，但不应该抛异常

        # 测试无效数据
        data = {"publish_time": "invalid time"}
        result = mock_crawler._extract_publish_time(data)
        assert result is None

    @pytest.mark.asyncio
    async def test_delay_request(self, mock_crawler):
        """测试请求延迟"""
        start_time = time.time()
        await mock_crawler._delay_request()
        end_time = time.time()

        # 延迟应该在配置的范围内
        delay = end_time - start_time
        assert delay >= mock_crawler.data_source.request_delay_min
        assert delay <= mock_crawler.data_source.request_delay_max + 0.1  # 允许小误差

    @pytest.mark.asyncio
    async def test_process_item_success(self, mock_crawler):
        """测试数据项处理成功"""
        test_data = {
            "title": "测试标题",
            "content": "测试内容" * 10,  # 确保内容足够长
            "author": "测试作者",
            "url": "https://test.com/article/1",
        }

        with patch(
            "src.core.mongodb.mongodb_manager.save_raw_content", return_value="mock_id"
        ):
            mongo_id = await mock_crawler.process_item(test_data)

            assert mongo_id == "mock_id"
            assert mock_crawler.items_processed == 1
            assert mock_crawler.items_success == 1

    def test_get_statistics(self, mock_crawler):
        """测试统计信息获取"""
        # 设置开始时间为时间戳
        mock_crawler.start_time = time.time()
        mock_crawler.items_found = 10
        mock_crawler.items_processed = 8
        mock_crawler.items_success = 6
        mock_crawler.items_failed = 2

        stats = mock_crawler.get_statistics()

        assert isinstance(stats, dict)
        assert stats["items_found"] == 10
        assert stats["items_processed"] == 8
        assert stats["items_success"] == 6
        assert stats["items_failed"] == 2
        assert "success_rate" in stats
        assert "duration_seconds" in stats

    @pytest.mark.asyncio
    async def test_cleanup(self, mock_crawler):
        """测试资源清理"""
        # 模拟已设置的资源
        mock_session = AsyncMock()
        mock_session.closed = False
        mock_crawler.session = mock_session

        # 保存原始的 session 对象引用
        original_session = mock_crawler.session
        
        await mock_crawler.cleanup()

        # 验证 close 方法被调用
        original_session.close.assert_called_once()
        
        # 验证 session 被设置为 None
        assert mock_crawler.session is None

    @pytest.mark.asyncio
    async def test_crawl_implementation_required(
        self, mock_data_source, mock_crawl_task
    ):
        """测试抽象方法必须实现"""
        # 这个测试应该验证抽象类不能直接实例化
        # 但由于我们有 MockCrawler 实现了 crawl 方法，这里测试是否正确实现
        crawler = MockCrawler(mock_data_source, mock_crawl_task)
        result = await crawler.crawl()
        assert result.success is True

    @pytest.mark.asyncio
    async def test_error_handling(self, mock_crawler):
        """测试错误处理"""
        test_data = {"title": "测试标题", "content": "足够长的内容用于测试"}

        # 模拟 MongoDB 管理器保存失败
        async def failing_save(data):
            raise Exception("数据库错误")

        with patch(
            "src.core.mongodb.mongodb_manager.save_raw_content",
            side_effect=failing_save,
        ):
            # 由于代码会抛出异常，我们需要捕获它
            with pytest.raises(Exception, match="数据库错误"):
                await mock_crawler.process_item(test_data)

            # 验证错误计数和错误记录
            assert mock_crawler.items_failed == 1
            assert len(mock_crawler.errors) > 0
            assert "数据库错误" in mock_crawler.errors[0]

    def test_extract_methods(self, mock_crawler):
        """测试数据提取方法"""
        test_data = {
            "title": "测试标题",
            "content": "测试内容",
            "author": "测试作者",
            "url": "https://test.com",
        }

        assert mock_crawler._extract_title(test_data) == "测试标题"
        assert mock_crawler._extract_content(test_data) == "测试内容"
        assert mock_crawler._extract_author(test_data) == "测试作者"
        assert mock_crawler._extract_source_url(test_data) == "https://test.com"

    def test_url_normalization(self, mock_crawler):
        """测试URL标准化"""
        # 测试完整URL
        url = mock_crawler._normalize_url("https://test.com/path")
        assert url == "https://test.com/path"

        # 测试相对URL
        url = mock_crawler._normalize_url("/path", "https://test.com")
        assert url == "https://test.com/path"

        # 测试空URL
        url = mock_crawler._normalize_url("")
        assert url == ""

    def test_get_domain(self, mock_crawler):
        """测试域名提取"""
        domain = mock_crawler._get_domain("https://test.com/path")
        assert domain == "test.com"

        domain = mock_crawler._get_domain("invalid-url")
        assert domain == ""

    @pytest.mark.asyncio
    async def test_process_raw_data(self, mock_crawler):
        """测试原始数据处理"""
        raw_data = {
            "title": "这是一个测试标题",
            "content": "这是一个足够长的测试内容，用于验证数据处理功能是否正常工作",
            "author": "测试作者",
        }

        processed = await mock_crawler._process_raw_data(raw_data)

        assert processed is not None
        assert processed["title"] == "这是一个测试标题"
        assert (
            processed["content"]
            == "这是一个足够长的测试内容，用于验证数据处理功能是否正常工作"
        )
        assert processed["author"] == "测试作者"
        # 注意：content_hash 是在 process_item 中添加的，不是在 _process_raw_data 中
        assert "crawl_time" in processed

    @pytest.mark.asyncio
    async def test_process_raw_data_insufficient_content(self, mock_crawler):
        """测试内容不足的数据处理"""
        raw_data = {"title": "", "content": "短"}  # 内容太短

        processed = await mock_crawler._process_raw_data(raw_data)
        assert processed is None
