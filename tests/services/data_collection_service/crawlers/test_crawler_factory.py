"""
爬虫工厂测试模块
"""

from datetime import datetime
from decimal import Decimal
from unittest.mock import Mock

import pytest

from src.services.data_collection_service.crawlers.api_crawler import \
    ApiCrawler
from src.services.data_collection_service.crawlers.base_crawler import \
    BaseCrawler
from src.services.data_collection_service.crawlers.crawler_factory import \
    CrawlerFactory
from src.services.data_collection_service.crawlers.rss_crawler import \
    RssCrawler
from src.services.data_collection_service.crawlers.web_crawler import \
    WebCrawler
from src.services.data_collection_service.models import CrawlTask, DataSource
from src.services.data_collection_service.schemas import (CollectionMethod,
                                                          ContentCategory,
                                                          CrawlMode,
                                                          TaskStatus,
                                                          TriggerType)


@pytest.fixture
def mock_crawl_task():
    """创建测试用采集任务"""
    return CrawlTask(
        id=1,
        source_id=1,
        task_type="news_list",
        trigger_type=TriggerType.INTERVAL,
        target_url="https://test.example.com/news",
        task_config={},
        status=TaskStatus.PENDING,
        progress=0,
        items_found=0,
        items_processed=0,
        items_success=0,
        items_failed=0,
        retry_count=0,
        max_retry_count=3,
        network_requests=0,
        created_at=datetime.now(),
        updated_at=datetime.now(),
    )


@pytest.fixture
def mock_data_source_api():
    """创建API类型的测试数据源"""
    return DataSource(
        id=1,
        name="API数据源",
        collection_method=CollectionMethod.API_JSON,
        content_category=ContentCategory.FINANCIAL_NEWS,
        base_url="https://api.test.com/news",
        crawl_mode=CrawlMode.INTERVAL,
        crawl_interval=3600,
        priority=5,
        max_concurrent_tasks=1,
        status="active",
        health_score=Decimal("0.95"),
        request_delay_min=1,
        request_delay_max=3,
        use_proxy=False,
        supports_realtime=False,
        event_driven_config={},
        max_consecutive_errors=5,
        error_count=0,
        consecutive_error_count=0,
        total_crawled_count=100,
        total_success_count=95,
        current_config_version=1,
        created_at=datetime.now(),
        updated_at=datetime.now(),
        tags=[],
    )


@pytest.fixture
def mock_data_source_web():
    """创建网页类型的测试数据源"""
    return DataSource(
        id=2,
        name="网页数据源",
        collection_method=CollectionMethod.WEB_SCRAPING,
        content_category=ContentCategory.FINANCIAL_NEWS,
        base_url="https://test.com/news",
        crawl_mode=CrawlMode.INTERVAL,
        crawl_interval=3600,
        priority=5,
        max_concurrent_tasks=1,
        status="active",
        health_score=Decimal("0.90"),
        request_delay_min=2,
        request_delay_max=5,
        use_proxy=False,
        supports_realtime=False,
        event_driven_config={},
        max_consecutive_errors=5,
        error_count=0,
        consecutive_error_count=0,
        total_crawled_count=50,
        total_success_count=45,
        current_config_version=1,
        created_at=datetime.now(),
        updated_at=datetime.now(),
        tags=[],
    )


@pytest.fixture
def mock_data_source_rss():
    """创建RSS类型的测试数据源"""
    return DataSource(
        id=3,
        name="RSS数据源",
        collection_method=CollectionMethod.API_RSS,
        content_category=ContentCategory.FINANCIAL_NEWS,
        base_url="https://test.com/rss.xml",
        crawl_mode=CrawlMode.INTERVAL,
        crawl_interval=3600,
        priority=5,
        max_concurrent_tasks=1,
        status="active",
        health_score=Decimal("0.98"),
        request_delay_min=1,
        request_delay_max=2,
        use_proxy=False,
        supports_realtime=False,
        event_driven_config={},
        max_consecutive_errors=3,
        error_count=0,
        consecutive_error_count=0,
        total_crawled_count=200,
        total_success_count=195,
        current_config_version=1,
        created_at=datetime.now(),
        updated_at=datetime.now(),
        tags=[],
    )


class TestCrawlerFactory:
    """爬虫工厂测试类"""

    def test_create_api_crawler(self, mock_data_source_api, mock_crawl_task):
        """测试创建API爬虫"""
        crawler = CrawlerFactory.create_crawler(mock_data_source_api, mock_crawl_task)

        assert isinstance(crawler, ApiCrawler)
        assert crawler.data_source == mock_data_source_api
        assert crawler.task == mock_crawl_task

    def test_create_web_crawler(self, mock_data_source_web, mock_crawl_task):
        """测试创建网页爬虫"""
        crawler = CrawlerFactory.create_crawler(mock_data_source_web, mock_crawl_task)

        assert isinstance(crawler, WebCrawler)
        assert crawler.data_source == mock_data_source_web
        assert crawler.task == mock_crawl_task

    def test_create_rss_crawler(self, mock_data_source_rss, mock_crawl_task):
        """测试创建RSS爬虫"""
        crawler = CrawlerFactory.create_crawler(mock_data_source_rss, mock_crawl_task)

        assert isinstance(crawler, RssCrawler)
        assert crawler.data_source == mock_data_source_rss
        assert crawler.task == mock_crawl_task

    def test_create_api_json_crawler(self, mock_crawl_task):
        """测试创建API JSON爬虫"""
        data_source = Mock()
        data_source.collection_method = CollectionMethod.API_JSON
        data_source.name = "API JSON数据源"

        crawler = CrawlerFactory.create_crawler(data_source, mock_crawl_task)
        assert isinstance(crawler, ApiCrawler)

    def test_create_api_xml_crawler(self, mock_crawl_task):
        """测试创建API XML爬虫"""
        data_source = Mock()
        data_source.collection_method = CollectionMethod.API_XML
        data_source.name = "API XML数据源"

        crawler = CrawlerFactory.create_crawler(data_source, mock_crawl_task)
        assert isinstance(crawler, ApiCrawler)

    def test_create_web_dynamic_crawler(self, mock_crawl_task):
        """测试创建动态网页爬虫"""
        data_source = Mock()
        data_source.collection_method = CollectionMethod.WEB_DYNAMIC
        data_source.name = "动态网页数据源"

        crawler = CrawlerFactory.create_crawler(data_source, mock_crawl_task)
        assert isinstance(crawler, WebCrawler)

    def test_unsupported_collection_method(self, mock_crawl_task):
        """测试不支持的采集方式"""
        data_source = Mock()
        data_source.collection_method = "unsupported_method"
        data_source.name = "不支持的数据源"

        with pytest.raises(ValueError):
            CrawlerFactory.create_crawler(data_source, mock_crawl_task)

    def test_get_supported_methods(self):
        """测试获取支持的采集方式"""
        methods = CrawlerFactory.get_supported_methods()

        assert isinstance(methods, list)
        assert "api_json" in methods
        assert "api_xml" in methods
        assert "api_rss" in methods
        assert "web_scraping" in methods
        assert "web_dynamic" in methods

    def test_validate_data_source_valid(self, mock_data_source_api):
        """测试有效数据源验证"""
        is_valid, message = CrawlerFactory.validate_data_source(mock_data_source_api)
        assert is_valid is True
        assert message == ""

    def test_validate_data_source_invalid_method(self):
        """测试无效采集方式验证"""
        data_source = Mock()
        data_source.collection_method = "invalid_method"
        data_source.base_url = "https://test.com"

        is_valid, message = CrawlerFactory.validate_data_source(data_source)
        assert is_valid is False
        assert "不支持的采集方式" in message

    def test_validate_data_source_missing_url_api(self):
        """测试缺少URL的API数据源验证"""
        data_source = Mock()
        data_source.collection_method = CollectionMethod.API_JSON
        data_source.base_url = None

        is_valid, message = CrawlerFactory.validate_data_source(data_source)
        assert is_valid is False
        assert "缺少基础URL" in message

    def test_validate_data_source_missing_url_web(self):
        """测试缺少URL的网页数据源验证"""
        data_source = Mock()
        data_source.collection_method = CollectionMethod.WEB_SCRAPING
        data_source.base_url = ""

        is_valid, message = CrawlerFactory.validate_data_source(data_source)
        assert is_valid is False
        assert "缺少基础URL" in message

    def test_register_custom_crawler(self, mock_crawl_task):
        """测试注册自定义爬虫"""

        class CustomCrawler(BaseCrawler):
            async def crawl(self):
                pass

        CrawlerFactory.register_crawler("custom_method", CustomCrawler)

        # 验证注册成功
        assert CrawlerFactory.is_supported("custom_method")
        assert CrawlerFactory.get_crawler_class("custom_method") == CustomCrawler

    def test_get_crawler_class_api(self):
        """测试获取API爬虫类"""
        crawler_class = CrawlerFactory.get_crawler_class(CollectionMethod.API_JSON)
        assert crawler_class == ApiCrawler

    def test_get_crawler_class_web(self):
        """测试获取网页爬虫类"""
        crawler_class = CrawlerFactory.get_crawler_class(CollectionMethod.WEB_SCRAPING)
        assert crawler_class == WebCrawler

    def test_get_crawler_class_rss(self):
        """测试获取RSS爬虫类"""
        crawler_class = CrawlerFactory.get_crawler_class(CollectionMethod.API_RSS)
        assert crawler_class == RssCrawler

    def test_get_crawler_class_unsupported(self):
        """测试获取不支持的爬虫类"""
        crawler_class = CrawlerFactory.get_crawler_class("unsupported_method")
        assert crawler_class is None

    def test_create_test_crawler(self):
        """测试创建测试爬虫"""
        test_crawler = CrawlerFactory.create_test_crawler(
            "api_json", "https://test.com"
        )

        assert isinstance(test_crawler, ApiCrawler)
        assert test_crawler.data_source.base_url == "https://test.com"

    def test_get_crawler_info(self):
        """测试获取爬虫信息"""
        info = CrawlerFactory.get_crawler_info("api_json")

        assert info["method"] == "api_json"
        assert info["class_name"] == "ApiCrawler"
        assert info["supported"] is True

    def test_get_crawler_info_unsupported(self):
        """测试获取不支持方法的爬虫信息"""
        info = CrawlerFactory.get_crawler_info("unsupported_method")
        assert info == {}

    def test_is_supported(self):
        """测试检查采集方式是否支持"""
        assert CrawlerFactory.is_supported("api_json") is True
        assert CrawlerFactory.is_supported("web_scraping") is True
        assert CrawlerFactory.is_supported("unsupported_method") is False

    def test_error_handling_invalid_data_source(self, mock_crawl_task):
        """测试处理无效数据源的错误"""
        with pytest.raises(AttributeError):
            CrawlerFactory.create_crawler(None, mock_crawl_task)

    def test_error_handling_invalid_task(self, mock_data_source_api):
        """测试处理无效任务的错误"""
        # 这个测试应该成功创建爬虫，因为工厂不验证任务
        crawler = CrawlerFactory.create_crawler(mock_data_source_api, None)
        assert isinstance(crawler, ApiCrawler)

    def test_crawler_type_mapping(self):
        """测试爬虫类型映射"""
        # 测试所有定义的映射关系
        mappings = {
            CollectionMethod.API_JSON: ApiCrawler,
            CollectionMethod.API_XML: ApiCrawler,
            CollectionMethod.API_RSS: RssCrawler,
            CollectionMethod.WEB_SCRAPING: WebCrawler,
            CollectionMethod.WEB_DYNAMIC: WebCrawler,
        }

        for method, expected_class in mappings.items():
            actual_class = CrawlerFactory.get_crawler_class(method)
            assert (
                actual_class == expected_class
            ), f"Method {method} should map to {expected_class}"
