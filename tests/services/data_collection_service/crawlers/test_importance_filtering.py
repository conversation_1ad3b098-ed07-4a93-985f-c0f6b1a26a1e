"""
重要性过滤功能测试

测试爬虫是否能正确识别和过滤重要信息，确保只采集重要内容
"""

import pytest
from unittest.mock import Mock, patch
from bs4 import BeautifulSoup

from src.services.data_collection_service.crawlers.web_crawler import WebCrawler
from src.services.data_collection_service.crawlers.base_crawler import BaseCrawler
from src.services.data_collection_service.models import DataSource, CrawlTask


class TestImportanceFiltering:
    """重要性过滤测试类"""

    @pytest.fixture
    def mock_data_source(self):
        """模拟数据源"""
        data_source = Mock(spec=DataSource)
        data_source.id = 1
        data_source.name = "金十数据快讯"
        data_source.base_url = "https://www.jin10.com"
        data_source.collection_method = "web_scraping"
        data_source.content_category = "financial_news"
        
        # 模拟配置
        config = Mock()
        config.extraction_rules = {
            "data_extraction": {
                "news_items": {
                    "importance_filter": {
                        "enabled": True,
                        "container_importance_selector": ".jin-flash-item",
                        "importance_class_patterns": ["is-important"],
                        "fallback_behavior": "accept_all",
                        "site_specific_configs": {
                            "jin10.com": {
                                "importance_selector": ".jin-flash-item.is-important",
                                "reject_without_importance": True
                            },
                            "default": {
                                "importance_selector": ".important",
                                "reject_without_importance": False
                            }
                        }
                    }
                }
            }
        }
        config.validation_rules = {
            "importance_validation": {
                "enabled": True,
                "min_importance_level": 3,
                "allow_unknown_importance": False,
                "site_specific_rules": {
                    "jin10.com": {
                        "require_is_important_class": True,
                        "min_importance_level": 3
                    },
                    "default": {
                        "require_is_important_class": False,
                        "min_importance_level": 2
                    }
                }
            }
        }
        config.selector_config = {}
        
        data_source.configs = [config]
        return data_source

    @pytest.fixture
    def mock_task(self):
        """模拟采集任务"""
        task = Mock(spec=CrawlTask)
        task.id = 1
        task.task_type = "flash_news"
        task.target_url = "https://www.jin10.com"
        return task

    @pytest.fixture
    def jin10_important_html(self):
        """金十数据重要消息HTML"""
        return '''
        <div class="jin-flash-item-container is-normal" data-v-16ef13d1="" data-v-52b9f822="" id="flash20250629180144367800">
            <div class="jin-flash-item flash is-important" data-v-16ef13d1="">
                <div class="item-time" data-v-16ef13d1="">18:01:44</div>
                <div class="item-right is-common" data-v-16ef13d1="">
                    <div class="right-top" data-v-16ef13d1="">
                        <div class="right-common" data-v-16ef13d1="">
                            <b class="right-common-title" data-v-16ef13d1="">金十数据整理：特朗普过去24小时都忙了什么？（2025-06-29）</b>
                        </div>
                        <div class="right-content" data-v-16ef13d1="">
                            <div class="flash-text" data-v-16ef13d1="">这是一条重要的快讯内容，涉及特朗普的最新动态。</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        '''

    @pytest.fixture
    def jin10_normal_html(self):
        """金十数据普通消息HTML"""
        return '''
        <div class="jin-flash-item-container is-normal" data-v-16ef13d1="" data-v-52b9f822="" id="flash20250629173334523800">
            <div class="jin-flash-item flash" data-v-16ef13d1="">
                <div class="item-time" data-v-16ef13d1="">17:33:34</div>
                <div class="item-right is-common" data-v-16ef13d1="">
                    <div class="right-top" data-v-16ef13d1="">
                        <div class="right-common" data-v-16ef13d1="">
                            <b class="right-common-title" data-v-16ef13d1="">俄美情报部门高官通话 讨论"感兴趣的问题"</b>
                        </div>
                        <div class="right-content" data-v-16ef13d1="">
                            <div class="flash-text" data-v-16ef13d1="">金十数据6月29日讯，俄罗斯对外情报局局长纳雷什金透露，他曾与美国中央情报局局长拉特克利夫通电话。</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        '''

    @pytest.fixture
    def other_site_html(self):
        """其他网站消息HTML"""
        return '''
        <div class="news-item">
            <div class="news-time">18:00:00</div>
            <div class="news-content">
                <h3 class="news-title">其他网站的新闻标题</h3>
                <p class="news-text">这是来自其他网站的新闻内容，没有特定的重要性标识。</p>
            </div>
        </div>
        '''

    def test_jin10_important_message_passes_filter(self, mock_data_source, mock_task, jin10_important_html):
        """测试金十数据重要消息通过过滤"""
        crawler = WebCrawler(mock_data_source, mock_task)
        
        # 解析HTML
        soup = BeautifulSoup(jin10_important_html, "html.parser")
        element = soup.find("div", class_="jin-flash-item-container")
        
        # 获取重要性过滤配置
        filter_config = crawler._get_importance_filter_config()
        
        # 测试重要性检查
        result = crawler._check_item_importance(element, filter_config, "https://www.jin10.com")
        
        assert result is True, "重要消息应该通过过滤"

    def test_jin10_normal_message_fails_filter(self, mock_data_source, mock_task, jin10_normal_html):
        """测试金十数据普通消息被过滤"""
        crawler = WebCrawler(mock_data_source, mock_task)
        
        # 解析HTML
        soup = BeautifulSoup(jin10_normal_html, "html.parser")
        element = soup.find("div", class_="jin-flash-item-container")
        
        # 获取重要性过滤配置
        filter_config = crawler._get_importance_filter_config()
        
        # 测试重要性检查
        result = crawler._check_item_importance(element, filter_config, "https://www.jin10.com")
        
        assert result is False, "普通消息应该被过滤"

    def test_other_site_message_passes_filter(self, mock_data_source, mock_task, other_site_html):
        """测试其他网站消息通过过滤（兼容性）"""
        crawler = WebCrawler(mock_data_source, mock_task)
        
        # 解析HTML
        soup = BeautifulSoup(other_site_html, "html.parser")
        element = soup.find("div", class_="news-item")
        
        # 获取重要性过滤配置
        filter_config = crawler._get_importance_filter_config()
        
        # 测试重要性检查（使用其他网站URL）
        result = crawler._check_item_importance(element, filter_config, "https://example.com")
        
        assert result is True, "其他网站消息应该通过过滤（兼容性）"

    def test_importance_level_extraction(self, mock_data_source, mock_task):
        """测试重要性级别提取"""
        crawler = WebCrawler(mock_data_source, mock_task)
        
        # 测试不同的重要性数据
        test_cases = [
            # 测试HTML中的is-important类
            {
                "source_html": '<div class="jin-flash-item is-important">content</div>',
                "expected": 3
            },
            # 测试直接的重要性值
            {
                "importance_level": 4,
                "expected": 4
            },
            # 测试字符串重要性
            {
                "importance": "urgent",
                "expected": 4
            },
            # 测试中文重要性标识
            {
                "importance": "沸",
                "expected": 3
            },
            # 测试无重要性标识
            {
                "content": "普通内容",
                "expected": 0
            }
        ]
        
        for test_case in test_cases:
            expected = test_case.pop("expected")
            result = crawler._extract_importance_level(test_case)
            assert result == expected, f"重要性级别提取失败，期望 {expected}，实际 {result}"

    def test_importance_validation(self, mock_data_source, mock_task):
        """测试重要性验证"""
        crawler = WebCrawler(mock_data_source, mock_task)
        
        # 测试通过验证的数据
        valid_data = {
            "title": "重要新闻",
            "content": "这是重要内容",
            "importance_level": 3,
            "source_url": "https://www.jin10.com/news/1"
        }
        raw_data = {
            "source_html": '<div class="jin-flash-item is-important">content</div>'
        }
        
        result = crawler._validate_importance(valid_data, raw_data)
        assert result is True, "重要性验证应该通过"
        
        # 测试不通过验证的数据
        invalid_data = {
            "title": "普通新闻",
            "content": "这是普通内容",
            "importance_level": 1,  # 低于最低要求
            "source_url": "https://www.jin10.com/news/2"
        }
        raw_data_normal = {
            "source_html": '<div class="jin-flash-item">content</div>'  # 无重要性类
        }
        
        result = crawler._validate_importance(invalid_data, raw_data_normal)
        assert result is False, "重要性验证应该失败"

    def test_site_domain_extraction(self, mock_data_source, mock_task):
        """测试站点域名提取"""
        crawler = WebCrawler(mock_data_source, mock_task)
        
        test_cases = [
            ("https://www.jin10.com/news", "jin10.com"),
            ("https://jin10.com", "jin10.com"),
            ("https://example.com/path", "example.com"),
            ("https://www.example.com", "example.com"),
            ("", "unknown"),
            ("invalid-url", "unknown")
        ]
        
        for url, expected in test_cases:
            result = crawler._get_site_domain(url)
            assert result == expected, f"站点域名提取失败，URL: {url}, 期望: {expected}, 实际: {result}"

    @patch('src.services.data_collection_service.crawlers.web_crawler.logger')
    def test_importance_filtering_with_logging(self, mock_logger, mock_data_source, mock_task, jin10_normal_html):
        """测试重要性过滤的日志记录"""
        crawler = WebCrawler(mock_data_source, mock_task)
        
        # 解析HTML
        soup = BeautifulSoup(jin10_normal_html, "html.parser")
        element = soup.find("div", class_="jin-flash-item-container")
        
        # 获取重要性过滤配置
        filter_config = crawler._get_importance_filter_config()
        
        # 测试重要性检查
        result = crawler._check_item_importance(element, filter_config, "https://www.jin10.com")
        
        # 验证日志记录
        mock_logger.debug.assert_called()
        assert result is False

    def test_element_importance_matching(self, mock_data_source, mock_task):
        """测试元素重要性匹配"""
        crawler = WebCrawler(mock_data_source, mock_task)
        
        # 测试包含重要性类的元素
        important_html = '<div class="jin-flash-item is-important">content</div>'
        soup = BeautifulSoup(important_html, "html.parser")
        element = soup.find("div")
        
        result = crawler._element_matches_importance(element, ".is-important")
        assert result is True, "应该匹配重要性选择器"
        
        # 测试不包含重要性类的元素
        normal_html = '<div class="jin-flash-item">content</div>'
        soup = BeautifulSoup(normal_html, "html.parser")
        element = soup.find("div")
        
        result = crawler._element_matches_importance(element, ".is-important")
        assert result is False, "不应该匹配重要性选择器"


if __name__ == "__main__":
    pytest.main([__file__, "-v"]) 