"""
数据采集服务B端管理接口测试
测试数据源、数据源配置、原始数据记录等的管理功能
"""

import pytest
from datetime import datetime, timezone
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from src.main import app_b
from src.services.data_collection_service.models import (
    DataSource,
    DataSourceConfig,
    RawDataRecord,
)
from src.services.user_service.models import User
from src.services.permission_service.models import Permission, Role, UserRole


def create_test_admin_user(db_session: Session) -> User:
    """创建测试管理员用户"""
    from src.services.user_service.models import User
    from src.services.permission_service.models import Role, UserRole

    # 检查是否已存在该用户
    existing_user = (
        db_session.query(User).filter(User.username == "test_admin").first()
    )
    if existing_user:
        return existing_user

    # 创建管理员角色（如果不存在）
    admin_role = db_session.query(Role).filter(Role.name == "admin").first()
    if not admin_role:
        admin_role = Role(
            name="admin",
            description="系统管理员",
            is_system=True
        )
        db_session.add(admin_role)
        db_session.commit()

    # 创建用户
    user = User(
        username="test_admin",
        email="<EMAIL>",
        phone="13800138000",
        password_hash="hashed_admin123",
        is_active=True,
        is_verified=True,
    )
    db_session.add(user)
    db_session.commit()

    # 分配角色
    user_role = UserRole(user_id=user.id, role_id=admin_role.id)
    db_session.add(user_role)
    db_session.commit()

    return user


def create_test_user(db_session: Session) -> User:
    """创建测试普通用户"""
    user = User(
        username="user_test",
        email="<EMAIL>",
        phone="13800000002",
        password_hash="hashed_password",
        is_active=True,
        is_verified=True,
        created_at=datetime.now(timezone.utc)
    )
    db_session.add(user)
    db_session.commit()
    db_session.refresh(user)
    return user


class TestDataSourceAdminAPI:
    """数据源管理接口测试"""

    def test_create_data_source_success(
        self, client: TestClient, db_session: Session, admin_headers: dict
    ):
        """测试创建数据源成功"""
        data_source_data = {
            "name": "测试数据源",
            "collection_method": "api_json",
            "content_category": "financial_news",
            "business_data_type": "news_article",
            "base_url": "https://api.example.com",
            "description": "测试用数据源",
            "processing_pipeline_id": None,
            "crawl_mode": "interval",
            "crawl_interval": 3600,
            "priority": 5,
            "max_concurrent_tasks": 1,
            "supports_realtime": False,
            "use_proxy": False,
            "request_delay_min": 2,
            "request_delay_max": 10,
            "max_consecutive_errors": 5,
            "created_by": "test_admin",
            "tags": ["test", "api"],
        }

        response = client.post(
            "/api/v1/admin/data-collection/data-sources",
            json=data_source_data,
            headers=admin_headers,
        )

        assert response.status_code == 201
        data = response.json()
        assert data["name"] == data_source_data["name"]
        assert data["collection_method"] == data_source_data["collection_method"]
        assert data["content_category"] == data_source_data["content_category"]
        assert data["business_data_type"] == data_source_data["business_data_type"]
        assert data["processing_pipeline_id"] == data_source_data["processing_pipeline_id"]
        assert data["processing_pipeline"] is None  # 因为没有关联管道

    def test_create_data_source_without_permission(
        self, client: TestClient, db_session: Session, user_headers: dict
    ):
        """测试无权限创建数据源"""
        data_source_data = {
            "name": "测试数据源",
            "collection_method": "api_json",
            "content_category": "financial_news",
            "base_url": "https://api.example.com",
            "description": "测试用数据源",
        }

        response = client.post(
            "/api/v1/admin/data-collection/data-sources",
            json=data_source_data,
            headers=user_headers,
        )

        assert response.status_code == 403
        assert "data_source.data_source.create" in response.json()["detail"]

    def test_create_data_source_with_pipeline(
        self, client: TestClient, db_session: Session, admin_headers: dict
    ):
        """测试创建带处理管道的数据源"""
        # 先创建一个处理管道
        from src.services.data_processing_service.models import ProcessingPipeline
        pipeline = ProcessingPipeline(
            pipeline_code="test_pipeline",
            pipeline_name="测试管道",
            description="测试用处理管道",
            processing_config={"test": "config"},
            validation_rules={"test": "rule"},
            is_active=True,
            version="1.0"
        )
        db_session.add(pipeline)
        db_session.commit()
        db_session.refresh(pipeline)

        data_source_data = {
            "name": "测试数据源带管道",
            "collection_method": "api_json",
            "content_category": "financial_news",
            "business_data_type": "news_article",
            "base_url": "https://api.example.com",
            "description": "测试用数据源",
            "processing_pipeline_id": pipeline.id,
            "crawl_mode": "interval",
            "crawl_interval": 3600,
            "priority": 5,
            "max_concurrent_tasks": 1,
            "supports_realtime": False,
            "use_proxy": False,
            "request_delay_min": 2,
            "request_delay_max": 10,
            "max_consecutive_errors": 5,
            "created_by": "test_admin",
            "tags": ["test", "api"],
        }

        response = client.post(
            "/api/v1/admin/data-collection/data-sources",
            json=data_source_data,
            headers=admin_headers,
        )

        assert response.status_code == 201
        data = response.json()
        assert data["name"] == data_source_data["name"]
        assert data["processing_pipeline_id"] == pipeline.id
        assert data["processing_pipeline"] is not None
        assert data["processing_pipeline"]["id"] == pipeline.id
        assert data["processing_pipeline"]["pipeline_code"] == pipeline.pipeline_code
        assert data["processing_pipeline"]["pipeline_name"] == pipeline.pipeline_name

    def test_get_data_sources_list(
        self, client: TestClient, db_session: Session, admin_headers: dict
    ):
        """测试获取数据源列表"""
        # 创建测试数据源
        data_source = DataSource(
            name="测试数据源1",
            collection_method="api_json",
            content_category="financial_news",
            base_url="https://api.example.com",
            description="测试用数据源",
            status="active",
        )
        db_session.add(data_source)
        db_session.commit()

        response = client.get(
            "/api/v1/admin/data-collection/data-sources",
            headers=admin_headers,
        )

        assert response.status_code == 200
        data = response.json()
        assert data["total"] >= 1
        assert len(data["items"]) >= 1
        assert data["items"][0]["name"] == "测试数据源1"

    def test_get_data_source_detail(
        self, client: TestClient, db_session: Session, admin_headers: dict
    ):
        """测试获取数据源详情"""
        # 创建测试数据源
        data_source = DataSource(
            name="测试数据源详情",
            collection_method="api_json",
            content_category="financial_news",
            base_url="https://api.example.com",
            description="测试用数据源详情",
            status="active",
        )
        db_session.add(data_source)
        db_session.commit()
        db_session.refresh(data_source)

        response = client.get(
            f"/api/v1/admin/data-collection/data-sources/{data_source.id}",
            headers=admin_headers,
        )

        assert response.status_code == 200
        data = response.json()
        assert data["id"] == data_source.id
        assert data["name"] == data_source.name

    def test_update_data_source(
        self, client: TestClient, db_session: Session, admin_headers: dict
    ):
        """测试更新数据源"""
        # 创建测试数据源
        data_source = DataSource(
            name="待更新数据源",
            collection_method="api_json",
            content_category="financial_news",
            base_url="https://api.example.com",
            description="待更新的测试数据源",
            status="active",
        )
        db_session.add(data_source)
        db_session.commit()
        db_session.refresh(data_source)

        update_data = {
            "description": "已更新的测试数据源",
            "priority": 8,
            "status": "inactive",
        }

        response = client.put(
            f"/api/v1/admin/data-collection/data-sources/{data_source.id}",
            json=update_data,
            headers=admin_headers,
        )

        assert response.status_code == 200
        data = response.json()
        assert data["description"] == update_data["description"]
        assert data["priority"] == update_data["priority"]
        assert data["status"] == update_data["status"]

    def test_delete_data_source(
        self, client: TestClient, db_session: Session, admin_headers: dict
    ):
        """测试删除数据源"""
        # 创建测试数据源
        data_source = DataSource(
            name="待删除数据源",
            collection_method="api_json",
            content_category="financial_news",
            base_url="https://api.example.com",
            description="待删除的测试数据源",
            status="active",
        )
        db_session.add(data_source)
        db_session.commit()
        db_session.refresh(data_source)

        response = client.delete(
            f"/api/v1/admin/data-collection/data-sources/{data_source.id}",
            headers=admin_headers,
        )

        assert response.status_code == 204

        # 验证数据源已被删除
        deleted_source = db_session.query(DataSource).filter(DataSource.id == data_source.id).first()
        assert deleted_source is None

    def test_get_data_source_stats(
        self, client: TestClient, db_session: Session, admin_headers: dict
    ):
        """测试获取数据源统计信息"""
        response = client.get(
            "/api/v1/admin/data-collection/data-sources/stats",
            headers=admin_headers,
        )

        assert response.status_code == 200
        data = response.json()
        assert "total_sources" in data
        assert "active_sources" in data
        assert "status_distribution" in data


class TestDataSourceConfigAdminAPI:
    """数据源配置管理接口测试"""

    def test_create_data_source_config(
        self, client: TestClient, db_session: Session, admin_headers: dict
    ):
        """测试创建数据源配置"""
        # 先创建数据源
        data_source = DataSource(
            name="配置测试数据源",
            collection_method="api_json",
            content_category="financial_news",
            base_url="https://api.example.com",
            description="用于配置测试的数据源",
            status="active",
        )
        db_session.add(data_source)
        db_session.commit()
        db_session.refresh(data_source)

        config_data = {
            "source_id": data_source.id,
            "version": 1,
            "selector_config": {
                "title": ".title",
                "content": ".content",
                "author": ".author",
            },
            "headers_config": {
                "User-Agent": "Test Bot",
                "Accept": "application/json",
            },
            "cookies_config": {},
            "request_params_config": {},
            "javascript_config": {},
            "anti_crawler_config": {},
            "retry_config": {
                "max_retries": 3,
                "retry_delay": 1
            },
            "proxy_config": {},
            "is_active": True,
            "change_reason": "测试配置",
            "changed_by": "test_admin",
        }

        response = client.post(
            "/api/v1/admin/data-collection/data-source/configs",
            json=config_data,
            headers=admin_headers,
        )

        assert response.status_code == 201
        data = response.json()
        assert data["source_id"] == config_data["source_id"]
        assert data["version"] == config_data["version"]
        assert data["is_active"] == config_data["is_active"]

    def test_get_data_source_configs_list(
        self, client: TestClient, db_session: Session, admin_headers: dict
    ):
        """测试获取数据源配置列表"""
        response = client.get(
            "/api/v1/admin/data-collection/data-source-configs",
            headers=admin_headers,
        )

        assert response.status_code == 200
        data = response.json()
        assert "total" in data
        assert "items" in data


class TestRawDataRecordAdminAPI:
    """原始数据记录管理接口测试"""

    def test_create_raw_data_record(
        self, client: TestClient, db_session: Session, admin_headers: dict
    ):
        """测试创建原始数据记录"""
        # 先创建数据源和任务
        data_source = DataSource(
            name="记录测试数据源",
            collection_method="api_json",
            content_category="financial_news",
            base_url="https://api.example.com",
            description="用于记录测试的数据源",
            status="active",
        )
        db_session.add(data_source)
        db_session.commit()
        db_session.refresh(data_source)

        # 创建一个模拟任务
        from src.services.data_collection_service.models import CrawlTask
        task = CrawlTask(
            source_id=data_source.id,
            task_type="manual",
            trigger_type="manual",
            target_url="https://example.com/test",
            status="completed",
        )
        db_session.add(task)
        db_session.commit()
        db_session.refresh(task)

        record_data = {
            "task_id": task.id,
            "source_id": data_source.id,
            "source_url": "https://example.com/test-article",
            "canonical_url": "https://example.com/test-article",
            "url_hash": "abcd1234567890abcd1234567890abcd12345678",
            "url_domain": "example.com",
            "content_hash": "efgh1234567890efgh1234567890efgh12345678",
            "content_length": 1500,
            "content_encoding": "utf-8",
            "title": "测试文章标题",
            "author": "测试作者",
            "mongodb_id": "507f1f77bcf86cd799439011",
            "mongodb_collection": "raw_content",
            "content_type": "text/html",
            "processing_status": "pending",
            "processing_priority": 5,
            "quality_score": 0.85,
            "category": "financial_news",
            "subcategory": "market_analysis",
            "language": "zh",
            "view_count": 100,
            "like_count": 10,
            "comment_count": 5,
            "share_count": 3,
        }

        response = client.post(
            "/api/v1/admin/data-collection/raw-data-records",
            json=record_data,
            headers=admin_headers,
        )

        assert response.status_code == 201
        data = response.json()
        assert data["task_id"] == record_data["task_id"]
        assert data["source_id"] == record_data["source_id"]
        assert data["title"] == record_data["title"]

    def test_get_raw_data_records_list(
        self, client: TestClient, db_session: Session, admin_headers: dict
    ):
        """测试获取原始数据记录列表"""
        response = client.get(
            "/api/v1/admin/data-collection/raw-data-records",
            headers=admin_headers,
        )

        assert response.status_code == 200
        data = response.json()
        assert "total" in data
        assert "items" in data

    def test_get_raw_data_record_stats(
        self, client: TestClient, db_session: Session, admin_headers: dict
    ):
        """测试获取原始数据记录统计信息"""
        response = client.get(
            "/api/v1/admin/data-collection/raw-data-records/stats",
            headers=admin_headers,
        )

        assert response.status_code == 200
        data = response.json()
        assert "total_records" in data
        assert "status_distribution" in data

    def test_update_raw_data_record(
        self, client: TestClient, db_session: Session, admin_headers: dict
    ):
        """测试更新原始数据记录"""
        # 先创建一个记录
        data_source = DataSource(
            name="更新测试数据源",
            collection_method="api_json",
            content_category="financial_news",
            base_url="https://api.example.com",
            status="active",
        )
        db_session.add(data_source)
        db_session.commit()
        db_session.refresh(data_source)

        from src.services.data_collection_service.models import CrawlTask
        task = CrawlTask(
            source_id=data_source.id,
            task_type="manual",
            trigger_type="manual",
            target_url="https://example.com/test",
            status="completed",
        )
        db_session.add(task)
        db_session.commit()
        db_session.refresh(task)

        record = RawDataRecord(
            task_id=task.id,
            source_id=data_source.id,
            source_url="https://example.com/update-test",
            url_hash="update1234567890update1234567890update12",
            mongodb_id="507f1f77bcf86cd799439012",
            processing_status="pending",
            processing_priority=5,
        )
        db_session.add(record)
        db_session.commit()
        db_session.refresh(record)

        update_data = {
            "processing_status": "processed",
            "quality_score": 0.92,
            "category": "market_data",
        }

        response = client.put(
            f"/api/v1/admin/data-collection/raw-data-records/{record.id}",
            json=update_data,
            headers=admin_headers,
        )

        assert response.status_code == 200
        data = response.json()
        assert data["processing_status"] == update_data["processing_status"]
        assert data["quality_score"] == update_data["quality_score"]
        assert data["category"] == update_data["category"]

    def test_archive_raw_data_record(
        self, client: TestClient, db_session: Session, admin_headers: dict
    ):
        """测试归档原始数据记录"""
        # 先创建一个记录
        data_source = DataSource(
            name="归档测试数据源",
            collection_method="api_json",
            content_category="financial_news",
            base_url="https://api.example.com",
            status="active",
        )
        db_session.add(data_source)
        db_session.commit()
        db_session.refresh(data_source)

        from src.services.data_collection_service.models import CrawlTask
        task = CrawlTask(
            source_id=data_source.id,
            task_type="manual",
            trigger_type="manual",
            target_url="https://example.com/test",
            status="completed",
        )
        db_session.add(task)
        db_session.commit()
        db_session.refresh(task)

        record = RawDataRecord(
            task_id=task.id,
            source_id=data_source.id,
            source_url="https://example.com/archive-test",
            url_hash="archive1234567890archive1234567890archive",
            mongodb_id="507f1f77bcf86cd799439013",
            processing_status="processed",
            processing_priority=5,
            is_archived=False,
        )
        db_session.add(record)
        db_session.commit()
        db_session.refresh(record)

        response = client.patch(
            f"/api/v1/admin/data-collection/raw-data-records/{record.id}/archive",
            headers=admin_headers,
        )

        assert response.status_code == 200
        data = response.json()
        assert data["is_archived"] is True
        assert data["archived_at"] is not None

    def test_batch_update_processing_status(
        self, client: TestClient, db_session: Session, admin_headers: dict
    ):
        """测试批量更新处理状态"""
        # 创建多个记录
        data_source = DataSource(
            name="批量测试数据源",
            collection_method="api_json",
            content_category="financial_news",
            base_url="https://api.example.com",
            status="active",
        )
        db_session.add(data_source)
        db_session.commit()
        db_session.refresh(data_source)

        from src.services.data_collection_service.models import CrawlTask
        task = CrawlTask(
            source_id=data_source.id,
            task_type="manual",
            trigger_type="manual",
            target_url="https://example.com/test",
            status="completed",
        )
        db_session.add(task)
        db_session.commit()
        db_session.refresh(task)

        records = []
        for i in range(3):
            record = RawDataRecord(
                task_id=task.id,
                source_id=data_source.id,
                source_url=f"https://example.com/batch-test-{i}",
                url_hash=f"batch{i:02d}1234567890batch{i:02d}1234567890batch{i:02d}",
                mongodb_id=f"507f1f77bcf86cd79943901{i}",
                processing_status="pending",
                processing_priority=5,
            )
            db_session.add(record)
            records.append(record)
        
        db_session.commit()
        for record in records:
            db_session.refresh(record)

        batch_request = {
            "record_ids": [record.id for record in records],
            "new_status": "processed",
        }

        response = client.post(
            "/api/v1/admin/data-collection/raw-data-records/batch-update-status",
            json=batch_request,
            headers=admin_headers,
        )

        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["affected_count"] == 3

    def test_get_duplicate_records(
        self, client: TestClient, db_session: Session, admin_headers: dict
    ):
        """测试获取重复记录"""
        response = client.get(
            "/api/v1/admin/data-collection/raw-data-records/duplicates",
            headers=admin_headers,
        )

        assert response.status_code == 200
        data = response.json()
        assert "duplicates" in data
        assert "total_groups" in data


# 测试夹具
@pytest.fixture
def client():
    """创建测试客户端"""
    with TestClient(app_b) as test_client:
        yield test_client


@pytest.fixture
def admin_headers(db_session: Session):
    """创建管理员认证头"""
    admin_user = create_test_admin_user(db_session)
    # 这里应该生成实际的JWT token，简化为模拟
    return {"Authorization": "Bearer admin_test_token"}


@pytest.fixture
def user_headers(db_session: Session):
    """创建普通用户认证头"""
    user = create_test_user(db_session)
    # 这里应该生成实际的JWT token，简化为模拟
    return {"Authorization": "Bearer user_test_token"} 