"""
数据源服务测试模块
测试数据源的增删改查和业务逻辑
"""

from datetime import datetime, timedelta, timezone
from unittest.mock import Mock

import pytest
from sqlalchemy.orm import Session

from src.services.data_collection_service.models import DataSource, BusinessDataType
from src.services.data_collection_service.schemas import (BusinessDataType as BusinessDataTypeSchema,
                                                          CollectionMethod,
                                                          ContentCategory,
                                                          CrawlMode,
                                                          DataSourceCreate,
                                                          DataSourceStatus,
                                                          DataSourceUpdate)
from src.services.data_collection_service.service import DataSourceService


class TestDataSourceService:
    """数据源服务测试类"""

    def setup_method(self):
        """测试前的设置"""
        self.service = DataSourceService()
        self.mock_db = Mock(spec=Session)

    def test_create_data_source_success(self):
        """测试创建数据源成功"""
        # 准备测试数据
        create_data = DataSourceCreate(
            name="测试数据源",
            collection_method=CollectionMethod.API_JSON,
            content_category=ContentCategory.FINANCIAL_NEWS,
            business_data_type=BusinessDataTypeSchema.FLASH_NEWS,
            base_url="https://api.example.com",
            description="测试用的数据源",
            processing_pipeline="flash_news_pipeline",
            crawl_mode=CrawlMode.INTERVAL,
            crawl_interval=3600,
            priority=5,
            max_concurrent_tasks=2,
            created_by="test_user",
        )

        # 模拟数据库操作
        mock_data_source = DataSource(
            id=1,
            name=create_data.name,
            collection_method=create_data.collection_method.value,
            content_category=create_data.content_category.value,
            business_data_type=create_data.business_data_type.value,
            base_url=create_data.base_url,
            description=create_data.description,
            processing_pipeline=create_data.processing_pipeline,
            crawl_mode=create_data.crawl_mode.value,
            crawl_interval=create_data.crawl_interval,
            priority=create_data.priority,
            max_concurrent_tasks=create_data.max_concurrent_tasks,
            created_by=create_data.created_by,
            created_at=datetime.now(timezone.utc),
            updated_at=datetime.now(timezone.utc),
        )

        self.mock_db.add.return_value = None
        self.mock_db.commit.return_value = None
        self.mock_db.refresh.return_value = None

        # 执行测试
        result = self.service.create_data_source(self.mock_db, create_data)

        # 验证结果
        self.mock_db.add.assert_called_once()
        self.mock_db.commit.assert_called_once()
        self.mock_db.refresh.assert_called_once()

    def test_create_data_source_with_different_business_types(self):
        """测试创建不同业务类型的数据源"""
        test_cases = [
            {
                "name": "快讯数据源",
                "business_data_type": BusinessDataTypeSchema.FLASH_NEWS,
                "content_category": ContentCategory.FINANCIAL_NEWS,
                "processing_pipeline": "flash_news_pipeline",
            },
            {
                "name": "新闻数据源", 
                "business_data_type": BusinessDataTypeSchema.NEWS_ARTICLE,
                "content_category": ContentCategory.FINANCIAL_NEWS,
                "processing_pipeline": "news_article_pipeline",
            },
            {
                "name": "研报数据源",
                "business_data_type": BusinessDataTypeSchema.RESEARCH_REPORT,
                "content_category": ContentCategory.RESEARCH_REPORT,
                "processing_pipeline": "research_report_pipeline",
            },
            {
                "name": "经济数据源",
                "business_data_type": BusinessDataTypeSchema.ECONOMIC_DATA,
                "content_category": ContentCategory.OFFICIAL_DATA,
                "processing_pipeline": "economic_data_pipeline",
            },
        ]

        for test_case in test_cases:
            create_data = DataSourceCreate(
                name=test_case["name"],
                collection_method=CollectionMethod.API_JSON,
                content_category=test_case["content_category"],
                business_data_type=test_case["business_data_type"],
                processing_pipeline=test_case["processing_pipeline"],
            )

            # 验证业务数据类型和内容分类的匹配约束
            assert create_data.business_data_type == test_case["business_data_type"]
            assert create_data.content_category == test_case["content_category"]
            assert create_data.processing_pipeline == test_case["processing_pipeline"]

    def test_create_data_source_duplicate_name(self):
        """测试创建重名数据源失败"""
        from sqlalchemy.exc import IntegrityError

        create_data = DataSourceCreate(
            name="重复数据源",
            collection_method=CollectionMethod.API_JSON,
            content_category=ContentCategory.FINANCIAL_NEWS,
            business_data_type=BusinessDataTypeSchema.FLASH_NEWS,
        )

        # 模拟完整性约束错误
        self.mock_db.add.side_effect = IntegrityError("", "", "")
        self.mock_db.rollback.return_value = None

        # 执行测试并验证异常
        with pytest.raises(ValueError, match="already exists"):
            self.service.create_data_source(self.mock_db, create_data)

        self.mock_db.rollback.assert_called_once()

    def test_get_data_source_by_id_success(self):
        """测试根据ID获取数据源成功"""
        # 模拟查询结果
        mock_data_source = DataSource(
            id=1,
            name="测试数据源",
            collection_method="api_json",
            content_category="financial_news",
            business_data_type="flash_news",
            processing_pipeline="flash_news_pipeline",
        )

        mock_query = Mock()
        mock_query.filter.return_value.first.return_value = mock_data_source
        self.mock_db.query.return_value = mock_query

        # 执行测试
        result = self.service.get_data_source_by_id(self.mock_db, 1)

        # 验证结果
        assert result == mock_data_source
        assert result.business_data_type == "flash_news"
        assert result.processing_pipeline == "flash_news_pipeline"
        self.mock_db.query.assert_called_once_with(DataSource)

    def test_get_data_source_by_id_not_found(self):
        """测试根据ID获取数据源未找到"""
        # 模拟查询结果为空
        mock_query = Mock()
        mock_query.filter.return_value.first.return_value = None
        self.mock_db.query.return_value = mock_query

        # 执行测试
        result = self.service.get_data_source_by_id(self.mock_db, 999)

        # 验证结果
        assert result is None

    def test_get_data_sources_with_filters(self):
        """测试获取数据源列表并应用过滤条件"""
        # 模拟查询结果
        mock_data_sources = [
            DataSource(
                id=1, 
                name="源1", 
                status="active",
                business_data_type="flash_news",
                processing_pipeline="flash_news_pipeline"
            ),
            DataSource(
                id=2, 
                name="源2", 
                status="active",
                business_data_type="news_article",
                processing_pipeline="news_article_pipeline"
            ),
        ]

        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.count.return_value = 2
        mock_query.order_by.return_value.offset.return_value.limit.return_value.all.return_value = (
            mock_data_sources
        )
        self.mock_db.query.return_value = mock_query

        # 执行测试
        result, total = self.service.get_data_sources(
            self.mock_db,
            skip=0,
            limit=10,
            status="active",
            collection_method="api_json",
        )

        # 验证结果
        assert len(result) == 2
        assert total == 2
        # 验证过滤条件被应用
        assert mock_query.filter.call_count >= 2  # status, collection_method

    def test_update_data_source_success(self):
        """测试更新数据源成功"""
        # 准备测试数据
        update_data = DataSourceUpdate(
            description="更新后的描述", 
            priority=8, 
            status=DataSourceStatus.ACTIVE,
            processing_pipeline="updated_pipeline",
        )

        mock_data_source = DataSource(
            id=1, 
            name="测试数据源", 
            description="原描述", 
            priority=5, 
            status="inactive",
            processing_pipeline="old_pipeline",
        )

        # 模拟查询和更新
        self.service.get_data_source_by_id = Mock(return_value=mock_data_source)
        self.mock_db.commit.return_value = None
        self.mock_db.refresh.return_value = None

        # 执行测试
        result = self.service.update_data_source(self.mock_db, 1, update_data)

        # 验证结果
        assert result == mock_data_source
        assert mock_data_source.description == "更新后的描述"
        assert mock_data_source.priority == 8
        assert mock_data_source.status == "active"
        assert mock_data_source.processing_pipeline == "updated_pipeline"
        self.mock_db.commit.assert_called_once()

    def test_update_data_source_not_found(self):
        """测试更新不存在的数据源"""
        update_data = DataSourceUpdate(description="更新描述")

        # 模拟数据源不存在
        self.service.get_data_source_by_id = Mock(return_value=None)

        # 执行测试
        result = self.service.update_data_source(self.mock_db, 999, update_data)

        # 验证结果
        assert result is None

    def test_delete_data_source_hard_delete(self):
        """测试硬删除数据源"""
        mock_data_source = DataSource(id=1, name="测试数据源")

        # 模拟查询结果和相关检查
        self.service.get_data_source_by_id = Mock(return_value=mock_data_source)

        # 模拟没有相关的采集任务
        mock_task_query = Mock()
        mock_task_query.filter.return_value.first.return_value = None
        self.mock_db.query.return_value = mock_task_query

        self.mock_db.delete.return_value = None
        self.mock_db.commit.return_value = None

        # 执行测试
        result = self.service.delete_data_source(self.mock_db, 1)

        # 验证结果
        assert result is True
        self.mock_db.delete.assert_called_once_with(mock_data_source)
        self.mock_db.commit.assert_called_once()

    def test_delete_data_source_soft_delete(self):
        """测试软删除数据源（有相关任务时）"""
        mock_data_source = DataSource(id=1, name="测试数据源", status="active")
        mock_task = Mock()  # 模拟存在相关任务

        # 模拟查询结果
        self.service.get_data_source_by_id = Mock(return_value=mock_data_source)

        # 模拟有相关的采集任务
        mock_task_query = Mock()
        mock_task_query.filter.return_value.first.return_value = mock_task
        self.mock_db.query.return_value = mock_task_query

        self.mock_db.commit.return_value = None

        # 执行测试
        result = self.service.delete_data_source(self.mock_db, 1)

        # 验证结果
        assert result is True
        assert mock_data_source.status == "disabled"
        self.mock_db.delete.assert_not_called()  # 不应该硬删除
        self.mock_db.commit.assert_called_once()

    def test_update_health_status_success(self):
        """测试更新健康状态成功"""
        mock_data_source = DataSource(
            id=1,
            name="测试数据源",
            health_score=50.0,
            error_count=0,
            consecutive_error_count=0,
            total_crawled_count=0,
            total_success_count=0,
        )

        self.service.get_data_source_by_id = Mock(return_value=mock_data_source)
        self.mock_db.commit.return_value = None
        self.mock_db.refresh.return_value = None

        # 执行测试 - 成功更新
        result = self.service.update_health_status(self.mock_db, 1, 85.0, True, None)

        # 验证结果
        assert result == mock_data_source
        assert mock_data_source.health_score == 85.0
        assert mock_data_source.consecutive_error_count == 0
        assert mock_data_source.last_success_time is not None

    def test_update_health_status_with_error(self):
        """测试更新健康状态带错误"""
        mock_data_source = DataSource(
            id=1,
            name="测试数据源",
            health_score=80.0,
            error_count=2,
            consecutive_error_count=2,
            max_consecutive_errors=5,
            status="active",
            total_crawled_count=10,
            total_success_count=8,
        )

        self.service.get_data_source_by_id = Mock(return_value=mock_data_source)
        self.mock_db.commit.return_value = None
        self.mock_db.refresh.return_value = None

        # 执行测试 - 错误更新
        result = self.service.update_health_status(
            self.mock_db, 1, 30.0, False, "连接超时"
        )

        # 验证结果
        assert result == mock_data_source
        assert mock_data_source.health_score == 30.0
        assert mock_data_source.error_count == 3
        assert mock_data_source.consecutive_error_count == 3
        assert mock_data_source.status == "active"  # 未超过最大错误次数

    def test_update_health_status_auto_disable(self):
        """测试健康状态更新导致自动禁用"""
        mock_data_source = DataSource(
            id=1,
            name="测试数据源",
            health_score=80.0,
            error_count=4,
            consecutive_error_count=4,
            max_consecutive_errors=5,
            status="active",
            total_crawled_count=20,
            total_success_count=16,
        )

        self.service.get_data_source_by_id = Mock(return_value=mock_data_source)
        self.mock_db.commit.return_value = None
        self.mock_db.refresh.return_value = None

        # 执行测试 - 达到最大错误次数
        result = self.service.update_health_status(
            self.mock_db, 1, 20.0, False, "连接失败"
        )

        # 验证结果
        assert result == mock_data_source
        assert mock_data_source.consecutive_error_count == 5
        assert mock_data_source.status == "disabled"  # 应该被自动禁用

    def test_get_data_sources_for_crawl(self):
        """测试获取需要采集的数据源"""
        mock_data_sources = [
            DataSource(
                id=1,
                name="源1",
                status="active",
                next_crawl_time=datetime.now(timezone.utc) - timedelta(minutes=5),
            ),
            DataSource(
                id=2,
                name="源2",
                status="active",
                next_crawl_time=datetime.now(timezone.utc) - timedelta(minutes=1),
            ),
        ]

        mock_query = Mock()
        mock_query.filter.return_value.order_by.return_value.all.return_value = (
            mock_data_sources
        )
        self.mock_db.query.return_value = mock_query

        # 执行测试
        result = self.service.get_data_sources_for_crawl(self.mock_db)

        # 验证结果
        assert len(result) == 2
        assert all(ds.status == "active" for ds in result)

    def test_get_data_source_stats(self):
        """测试获取数据源统计信息"""
        # 使用side_effect来模拟不同的查询返回不同的结果
        mock_query = Mock()

        # 模拟total_count查询
        mock_total_query = Mock()
        mock_total_query.count.return_value = 10

        # 模拟active_count查询
        mock_active_query = Mock()
        mock_active_query.count.return_value = 8

        # 模拟healthy_count查询
        mock_healthy_query = Mock()
        mock_healthy_query.count.return_value = 6

        # 模拟avg_health_score查询
        mock_avg_health_query = Mock()
        mock_avg_health_query.scalar.return_value = 75.5

        # 模拟avg_success_rate查询
        mock_success_rate_query = Mock()
        mock_success_rate_query.scalar.return_value = 85.2

        # 设置side_effect来返回不同的Mock对象
        self.mock_db.query.side_effect = [
            mock_total_query,  # total_count
            mock_active_query,  # active_count
            mock_healthy_query,  # healthy_count
            mock_avg_health_query,  # avg_health_score
            mock_success_rate_query,  # avg_success_rate
        ]

        # 设置filter方法的返回值
        mock_active_query.filter.return_value = mock_active_query
        mock_healthy_query.filter.return_value = mock_healthy_query

        # 执行测试
        result = self.service.get_data_source_stats(self.mock_db)

        # 验证结果
        assert result["total_count"] == 10
        assert result["active_count"] == 8
        assert result["healthy_count"] == 6
        assert result["avg_health_score"] == 75.5
        assert result["avg_success_rate"] == 85.2
