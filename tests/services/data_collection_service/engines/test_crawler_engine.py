"""
爬虫引擎测试模块
"""

import asyncio
from datetime import datetime
from decimal import Decimal
from unittest.mock import Async<PERSON><PERSON>, Mo<PERSON>, patch

import pytest

from src.services.data_collection_service.engines.crawler_engine import \
    CrawlerEngine
from src.services.data_collection_service.models import CrawlTask, DataSource
from src.services.data_collection_service.schemas import (CollectionMethod,
                                                          ContentCategory,
                                                          CrawlMode,
                                                          CrawlResult,
                                                          TaskStatus,
                                                          TriggerType)


@pytest.fixture
def mock_data_source():
    """创建测试用数据源"""
    return DataSource(
        id=1,
        name="测试数据源",
        collection_method=CollectionMethod.API_JSON,
        content_category=ContentCategory.FINANCIAL_NEWS,
        base_url="https://test.example.com",
        crawl_mode=CrawlMode.INTERVAL,
        crawl_interval=3600,
        priority=5,
        max_concurrent_tasks=2,
        status="active",
        health_score=Decimal("0.95"),
        request_delay_min=2,
        request_delay_max=10,
        use_proxy=False,
        supports_realtime=False,
        event_driven_config={},
        max_consecutive_errors=10,
        error_count=0,
        consecutive_error_count=0,
        total_crawled_count=100,
        total_success_count=95,
        current_config_version=1,
        created_at=datetime.now(),
        updated_at=datetime.now(),
        tags=[],
    )


@pytest.fixture
def mock_crawl_task():
    """创建测试用采集任务"""
    return CrawlTask(
        id=1,
        source_id=1,
        task_type="news_list",
        trigger_type=TriggerType.INTERVAL,
        target_url="https://test.example.com/news",
        task_config={},
        status=TaskStatus.PENDING,
        progress=0,
        items_found=0,
        items_processed=0,
        items_success=0,
        items_failed=0,
        retry_count=0,
        max_retry_count=3,
        network_requests=0,
        created_at=datetime.now(),
        updated_at=datetime.now(),
    )


@pytest.fixture
def crawler_engine():
    """创建测试用爬虫引擎"""
    return CrawlerEngine(max_workers=2, max_concurrent_tasks=100)


class TestCrawlerEngine:
    """爬虫引擎测试类"""

    def test_init_default(self):
        """测试默认初始化"""
        engine = CrawlerEngine()

        assert engine.max_workers == 10
        assert engine.max_concurrent_tasks == 50
        assert engine.is_running is False

    def test_init_custom(self):
        """测试自定义参数初始化"""
        engine = CrawlerEngine(max_workers=3, max_concurrent_tasks=500)

        assert engine.max_workers == 3
        assert engine.max_concurrent_tasks == 500

    @pytest.mark.asyncio
    async def test_start_engine(self, crawler_engine):
        """测试启动引擎"""
        await crawler_engine.start()

        assert crawler_engine.is_running is True
        assert len(crawler_engine.worker_threads) == crawler_engine.max_workers

        # 清理
        await crawler_engine.stop()

    @pytest.mark.asyncio
    async def test_stop_engine(self, crawler_engine):
        """测试停止引擎"""
        await crawler_engine.start()
        assert crawler_engine.is_running is True

        await crawler_engine.stop()

        assert crawler_engine.is_running is False

    @pytest.mark.asyncio
    async def test_submit_task(self, crawler_engine, mock_crawl_task):
        """测试提交任务"""
        await crawler_engine.start()

        result = await crawler_engine.submit_task(mock_crawl_task)

        assert result is True

        await crawler_engine.stop()

    @pytest.mark.asyncio
    async def test_get_status(self, crawler_engine):
        """测试获取引擎状态"""
        status = await crawler_engine.get_status()

        assert "is_running" in status
        assert "worker_count" not in status  # 应该是'workers'
        assert "queue_size" in status
        assert "total_processed" in status
        assert status["is_running"] is False
        assert "workers" in status

    @pytest.mark.asyncio
    async def test_health_check(self, crawler_engine):
        """测试健康检查"""
        await crawler_engine.start()

        health = await crawler_engine.health_check()

        assert "status" in health
        assert "workers" in health
        assert "queue" in health

        await crawler_engine.stop()
