"""
数据采集服务与数据处理服务实现方式比较测试
对比两个服务在任务调度、错误处理、监控等方面的差异

注意：数据采集服务已采用统一任务框架，支持直接执行和异步队列两种模式
"""

import pytest
import asyncio
from datetime import datetime, timezone
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from typing import Dict, Any, List

# 统一任务框架相关导入
from src.core.task_framework import TaskManager, TaskType, ExecutionMode
from src.core.task_framework.models import TaskContext, TaskStatus

# 数据采集服务相关导入（统一任务框架）
from src.services.data_collection_service.task_manager import CrawlerTaskExecutor, DataCollectionTaskManager
from src.services.data_collection_service.models import DataSource, CrawlTask

# 数据处理服务相关导入（传统Celery模式）
from src.services.data_processing_service.processing_scheduler import DataProcessingScheduler
from src.services.data_processing_service.task_manager import DataProcessingTaskManager
from src.services.data_processing_service.engine import DataProcessingEngine
from src.services.data_processing_service.performance_monitor import PerformanceMonitor
from src.services.data_processing_service.models import DataProcessingStatus


class TestServiceComparison:
    """服务实现方式比较测试类"""

    def setup_method(self):
        """测试前的设置"""
        self.mock_db = Mock()

    @pytest.mark.asyncio
    async def test_task_scheduling_mechanisms(self):
        """测试任务调度机制的差异"""

        # === 数据采集服务：统一任务框架（支持直接执行和异步队列） ===
        task_manager = TaskManager()

        # 测试直接执行模式
        direct_context = TaskContext(
            task_id="test_direct_001",
            task_type=TaskType.CRAWLER,
            execution_mode=ExecutionMode.DIRECT,
            priority=7,
            metadata={"data_source_id": 1, "max_pages": 5}
        )

        with patch.object(task_manager, 'execute_task') as mock_execute:
            mock_execute.return_value = {"success": True, "items_collected": 10}

            # 提交直接执行任务
            result = await task_manager.submit_task(direct_context)

            # 验证直接执行
            assert result is not None
            mock_execute.assert_called_once()

        # 测试异步队列模式
        async_context = TaskContext(
            task_id="test_async_001",
            task_type=TaskType.CRAWLER,
            execution_mode=ExecutionMode.ASYNC,
            priority=5,
            metadata={"data_source_id": 1, "max_pages": 100}
        )

        with patch('src.core.celery_app.celery_app.send_task') as mock_celery:
            mock_celery.return_value.id = "celery_task_123"

            # 提交异步任务
            result = await task_manager.submit_task(async_context)

            # 验证Celery任务被提交
            mock_celery.assert_called_once()

        # === 数据处理服务：传统Celery分布式任务队列 ===
        processing_task_manager = DataProcessingTaskManager()

        # 测试Celery任务提交
        with patch('src.services.data_processing_service.tasks.submit_processing_task') as mock_submit:
            mock_submit.return_value = "task_123"

            task_id = await processing_task_manager.submit_single_record_task(
                record_id=1, priority=5
            )

            # 验证Celery任务被提交
            assert task_id == "task_123"
            mock_submit.assert_called_once_with(1, 5)

        # === 比较结论 ===
        print("\n=== 任务调度机制比较 ===")
        print("数据采集服务：统一任务框架，支持直接执行(快速响应)和异步队列(高可靠性)两种模式")
        print("数据处理服务：传统Celery分布式任务队列，专注于CPU密集型数据处理任务")

    @pytest.mark.asyncio
    async def test_error_handling_strategies(self):
        """测试错误处理策略的差异"""
        
        # === 数据采集服务：基于健康评分的错误处理 ===
        mock_data_source = Mock()
        mock_data_source.id = 1
        mock_data_source.name = "测试数据源"
        mock_data_source.consecutive_error_count = 0
        mock_data_source.max_consecutive_errors = 3
        mock_data_source.health_score = 100.0
        mock_data_source.status = "active"
        
        # 模拟连续错误
        for i in range(4):  # 超过最大错误次数
            mock_data_source.consecutive_error_count += 1
            if mock_data_source.consecutive_error_count >= mock_data_source.max_consecutive_errors:
                mock_data_source.status = "disabled"
                mock_data_source.health_score = 0.0
        
        # 验证数据源被自动禁用
        assert mock_data_source.status == "disabled"
        assert mock_data_source.health_score == 0.0
        
        # === 数据处理服务：基于Celery的重试机制 ===
        processing_engine = DataProcessingEngine()
        
        # 模拟重试逻辑
        with patch.object(processing_engine, '_get_failed_records') as mock_get_failed:
            mock_failed_record = Mock()
            mock_failed_record.id = 1
            mock_failed_record.retry_count = 1
            mock_get_failed.return_value = [mock_failed_record]
            
            with patch.object(processing_engine, 'process_single_record') as mock_process:
                mock_process.return_value = {"success": True}
                
                # 执行重试
                stats = await processing_engine.retry_failed_records(max_retries=3)
                
                # 验证重试统计
                assert "retried" in stats
                assert "success" in stats
                assert "failed" in stats
        
        # === 比较结论 ===
        print("\n=== 错误处理策略比较 ===")
        print("数据采集服务：基于健康评分，自动禁用有问题的数据源，防止资源浪费")
        print("数据处理服务：基于Celery重试机制，支持指数退避和最大重试次数限制")

    @pytest.mark.asyncio
    async def test_monitoring_approaches(self):
        """测试监控方式的差异"""
        
        # === 数据采集服务：基于任务状态的监控 ===
        crawler_engine = CrawlerEngine()
        
        # 模拟引擎状态
        crawler_engine.is_running = True
        crawler_engine.start_time = datetime.now(timezone.utc)
        crawler_engine.running_tasks = {"task_1": Mock(), "task_2": Mock()}
        crawler_engine.completed_tasks = {"task_3": Mock()}
        
        # 获取引擎状态
        status = await crawler_engine.get_status()
        
        # 验证状态信息
        assert status["is_running"] is True
        assert status["running_tasks_count"] == 2
        assert status["completed_tasks_count"] == 1
        assert "uptime_seconds" in status
        
        # === 数据处理服务：基于性能指标的监控 ===
        performance_monitor = PerformanceMonitor()
        
        # 模拟性能监控
        with patch('psutil.cpu_percent', return_value=75.0):
            with patch('psutil.virtual_memory') as mock_memory:
                mock_memory.return_value.percent = 60.0
                mock_memory.return_value.available = 4 * 1024 * 1024 * 1024  # 4GB
                
                with patch('psutil.disk_usage') as mock_disk:
                    mock_disk.return_value.percent = 45.0
                    mock_disk.return_value.free = 100 * 1024 * 1024 * 1024  # 100GB
                    
                    # 启动监控
                    performance_monitor.start_monitoring(interval=1.0)
                    
                    # 等待一个监控周期
                    await asyncio.sleep(1.5)
                    
                    # 停止监控
                    performance_monitor.stop_monitoring()
                    
                    # 验证系统指标被收集
                    assert len(performance_monitor.system_metrics) > 0
                    
                    latest_metric = performance_monitor.system_metrics[-1]
                    assert latest_metric['cpu_percent'] == 75.0
                    assert latest_metric['memory_percent'] == 60.0
        
        # === 比较结论 ===
        print("\n=== 监控方式比较 ===")
        print("数据采集服务：关注任务执行状态、数据源健康度、爬取成功率")
        print("数据处理服务：关注系统资源使用率、处理性能指标、错误统计")

    @pytest.mark.asyncio
    async def test_task_execution_models(self):
        """测试任务执行模式的差异"""

        # === 数据采集服务：统一任务框架的双模式执行 ===

        # 1. 直接执行模式（适合快速任务）
        crawler_executor = CrawlerTaskExecutor()

        direct_context = TaskContext(
            task_id="direct_test_001",
            task_type=TaskType.CRAWLER,
            execution_mode=ExecutionMode.DIRECT,
            priority=8,
            metadata={"data_source_id": 1, "max_pages": 1}
        )

        with patch.object(crawler_executor, 'execute') as mock_direct_execute:
            mock_direct_execute.return_value = {
                "success": True,
                "items_collected": 5,
                "execution_time": 2.3,
                "mode": "direct"
            }

            # 直接执行
            result = await crawler_executor.execute(direct_context)
            assert result["mode"] == "direct"
            assert result["success"] is True

        # 2. 异步队列模式（适合大型任务）
        async_context = TaskContext(
            task_id="async_test_001",
            task_type=TaskType.CRAWLER,
            execution_mode=ExecutionMode.ASYNC,
            priority=5,
            metadata={"data_source_id": 1, "max_pages": 100}
        )

        with patch('src.core.celery_app.celery_app.send_task') as mock_celery_send:
            mock_celery_send.return_value.id = "celery_async_123"

            # 异步提交到Celery
            task_manager = TaskManager()
            result = await task_manager.submit_task(async_context)

            # 验证异步提交
            mock_celery_send.assert_called_once()

        # === 数据处理服务：传统同步任务模式（仅Celery） ===
        def simulate_sync_processing():
            """模拟数据处理服务的同步执行"""
            processing_results = []

            for i in range(3):
                # 模拟同步处理逻辑
                result = {
                    "record_id": i + 1,
                    "success": True,
                    "processing_time": 2.5,
                    "stage": "completed",
                    "mode": "celery_only"
                }
                processing_results.append(result)

            return processing_results

        # 执行同步任务
        sync_results = simulate_sync_processing()
        assert len(sync_results) == 3
        assert all(r["mode"] == "celery_only" for r in sync_results)

        # === 比较结论 ===
        print("\n=== 任务执行模式比较 ===")
        print("数据采集服务：统一任务框架，支持直接执行(快速响应)和异步队列(高可靠性)双模式")
        print("数据处理服务：单一Celery模式，专注于分布式CPU密集型处理")

    @pytest.mark.asyncio
    async def test_configuration_management(self):
        """测试配置管理方式的差异"""
        
        # === 数据采集服务：基于数据源的动态配置 ===
        mock_data_source = Mock()
        mock_data_source.id = 1
        mock_data_source.name = "测试数据源"
        mock_data_source.collection_method = "api_json"
        mock_data_source.crawl_interval = 3600
        mock_data_source.max_concurrent_tasks = 2
        mock_data_source.request_config = {
            "timeout": 30,
            "headers": {"User-Agent": "FinSight-Bot"},
            "retry_count": 3
        }
        
        # 验证动态配置
        assert mock_data_source.request_config["timeout"] == 30
        assert mock_data_source.max_concurrent_tasks == 2
        
        # === 数据处理服务：基于管道的配置管理 ===
        from src.services.data_processing_service.pipeline_configs import PipelineConfigManager
        
        config_manager = PipelineConfigManager()
        
        # 获取快讯处理管道配置
        flash_news_config = config_manager.get_config("flash_news_pipeline")
        
        # 验证管道配置结构
        assert "data_extraction_config" in flash_news_config
        assert "data_cleaning_config" in flash_news_config
        assert "data_transformation_config" in flash_news_config
        assert "data_validation_config" in flash_news_config
        assert "data_enrichment_config" in flash_news_config
        
        # 验证具体配置项
        extraction_config = flash_news_config["data_extraction_config"]
        assert "title_selectors" in extraction_config
        assert "content_selectors" in extraction_config
        
        # === 比较结论 ===
        print("\n=== 配置管理方式比较 ===")
        print("数据采集服务：基于数据源的动态配置，支持运行时修改爬虫参数")
        print("数据处理服务：基于管道的静态配置，预定义处理流程和规则")

    def test_architecture_summary(self):
        """架构总结对比"""
        
        print("\n" + "="*60)
        print("服务架构对比总结")
        print("="*60)
        
        comparison_table = [
            ["对比维度", "数据采集服务", "数据处理服务"],
            ["-"*20, "-"*35, "-"*30],
            ["任务调度", "统一任务框架(直接+异步)", "Celery分布式任务队列"],
            ["执行模式", "双模式(直接执行+异步队列)", "单一Celery模式"],
            ["响应速度", "直接模式立即响应", "异步队列延迟响应"],
            ["错误处理", "统一重试+健康评分", "重试机制+指数退避"],
            ["监控方式", "统一任务状态监控", "系统资源+性能指标"],
            ["配置管理", "数据源动态配置", "管道静态配置"],
            ["适用场景", "I/O密集+快速响应", "CPU密集型数据处理"],
            ["扩展性", "智能模式选择", "分布式水平扩展"],
            ["容错性", "多层级容错机制", "任务级别重试"],
        ]
        
        for row in comparison_table:
            print(f"{row[0]:<20} | {row[1]:<30} | {row[2]:<30}")
        
        print("\n" + "="*60)
        print("结论：数据采集服务采用统一任务框架实现智能双模式执行")
        print("     数据处理服务保持传统Celery模式专注分布式处理")
        print("="*60)

    @pytest.mark.asyncio
    async def test_unified_task_framework_features(self):
        """测试统一任务框架的特性"""

        print("\n=== 统一任务框架特性测试 ===")

        # 1. 智能模式选择
        def test_auto_mode_selection():
            """测试自动模式选择逻辑"""

            # 小任务 -> 直接执行
            small_task = TaskContext(
                task_id="small_001",
                task_type=TaskType.CRAWLER,
                execution_mode=ExecutionMode.AUTO,
                metadata={"max_pages": 1, "estimated_duration_minutes": 1}
            )

            # 大任务 -> 异步队列
            large_task = TaskContext(
                task_id="large_001",
                task_type=TaskType.CRAWLER,
                execution_mode=ExecutionMode.AUTO,
                metadata={"max_pages": 50, "estimated_duration_minutes": 30}
            )

            # 模拟智能选择逻辑
            def smart_mode_selection(context):
                metadata = context.metadata
                if (metadata.get("max_pages", 1) <= 10 and
                    metadata.get("estimated_duration_minutes", 1) <= 5):
                    return ExecutionMode.DIRECT
                else:
                    return ExecutionMode.ASYNC

            assert smart_mode_selection(small_task) == ExecutionMode.DIRECT
            assert smart_mode_selection(large_task) == ExecutionMode.ASYNC

            print("✅ 智能模式选择测试通过")

        test_auto_mode_selection()

        # 2. 非阻塞API响应
        async def test_non_blocking_api():
            """测试非阻塞API响应"""

            start_time = datetime.now()

            # 模拟API调用立即返回
            api_response = {
                "success": True,
                "message": "任务已提交",
                "data": {
                    "crawl_task_id": 123,
                    "execution_task_id": "crawler_20231201_143022_123456",
                    "execution_mode": "async",
                    "status": "submitted"
                }
            }

            response_time = (datetime.now() - start_time).total_seconds()

            # API应该在1秒内响应
            assert response_time < 1.0
            assert api_response["success"] is True
            assert api_response["data"]["status"] == "submitted"

            print("✅ 非阻塞API响应测试通过")

        await test_non_blocking_api()

        print("=== 统一任务框架优势 ===")
        print("1. 智能模式选择：根据任务特性自动选择最优执行方式")
        print("2. 非阻塞响应：API立即返回，不会因长任务阻塞")
        print("3. 高可靠性：基于Celery的分布式任务队列")
        print("4. 易扩展：统一架构便于添加新任务类型")
