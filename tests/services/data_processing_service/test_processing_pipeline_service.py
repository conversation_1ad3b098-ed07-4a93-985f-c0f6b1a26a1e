"""
处理管道服务测试
测试ProcessingPipelineService的业务逻辑
"""

import pytest
from datetime import datetime, timezone
from decimal import Decimal
from unittest.mock import MagicMock
from sqlalchemy.orm import Session

from src.services.data_processing_service.models import ProcessingPipeline
from src.services.data_processing_service.schemas import (
    ProcessingPipelineCreate,
    ProcessingPipelineUpdate
)
from src.services.data_processing_service.service import ProcessingPipelineService
from src.services.data_collection_service.models import DataSource


class TestProcessingPipelineService:
    """处理管道服务测试类"""

    @pytest.fixture
    def mock_db(self):
        """模拟数据库会话"""
        return MagicMock(spec=Session)

    @pytest.fixture
    def pipeline_service(self, mock_db):
        """处理管道服务实例"""
        return ProcessingPipelineService(db_session=mock_db)

    @pytest.fixture
    def sample_pipeline_create(self):
        """示例创建数据"""
        return ProcessingPipelineCreate(
            pipeline_code="test_pipeline",
            pipeline_name="测试处理管道",
            description="用于测试的处理管道",
            processing_config={
                "parsing_config": {"field_mapping": {"title": "$.title"}},
                "cleaning_config": {"remove_html": True},
                "transformation_config": {"time_format": "timestamp"},
                "validation_config": {"required_fields": ["title"]},
                "enrichment_config": {"extract_tags": True}
            },
            validation_rules={
                "required_fields": ["title", "content"],
                "min_content_length": 10
            },
            output_format="standard",
            is_active=True,
            version="1.0",
            created_by="admin"
        )

    @pytest.fixture
    def sample_pipeline_model(self):
        """示例管道模型"""
        return ProcessingPipeline(
            id=1,
            pipeline_code="test_pipeline",
            pipeline_name="测试处理管道",
            description="用于测试的处理管道",
            processing_config={
                "parsing_config": {"field_mapping": {"title": "$.title"}},
                "cleaning_config": {"remove_html": True}
            },
            validation_rules={
                "required_fields": ["title", "content"],
                "min_content_length": 10
            },
            output_format="standard",
            is_active=True,
            version="1.0",
            execution_count=10,
            success_count=8,
            total_processed_count=15,
            success_rate=Decimal("0.8000"),
            created_by="admin",
            created_at=datetime.now(timezone.utc),
            updated_at=datetime.now(timezone.utc)
        )

    # ==================== 创建管道测试 ====================

    def test_create_pipeline_success(self, pipeline_service, mock_db, sample_pipeline_create):
        """测试成功创建处理管道"""
        # 配置模拟
        mock_db.query.return_value.filter.return_value.first.return_value = None  # 不存在重复
        
        # 调用方法
        result = pipeline_service.create_pipeline(mock_db, sample_pipeline_create)
        
        # 验证结果
        assert result is not None
        mock_db.add.assert_called_once()
        mock_db.commit.assert_called_once()
        mock_db.refresh.assert_called_once()

    def test_create_pipeline_duplicate_code(self, pipeline_service, mock_db, sample_pipeline_create, sample_pipeline_model):
        """测试创建管道时代码重复"""
        # 配置模拟 - 存在重复管道
        mock_db.query.return_value.filter.return_value.first.return_value = sample_pipeline_model
        
        # 验证抛出异常
        with pytest.raises(ValueError, match="Pipeline code .* already exists"):
            pipeline_service.create_pipeline(mock_db, sample_pipeline_create)

    def test_create_pipeline_database_error(self, pipeline_service, mock_db, sample_pipeline_create):
        """测试创建管道时数据库错误"""
        # 配置模拟
        mock_db.query.return_value.filter.return_value.first.return_value = None
        mock_db.commit.side_effect = Exception("Database error")
        
        # 验证抛出异常
        with pytest.raises(Exception):
            pipeline_service.create_pipeline(mock_db, sample_pipeline_create)
            
        # 验证回滚被调用
        mock_db.rollback.assert_called_once()

    # ==================== 获取管道测试 ====================

    def test_get_pipeline_by_id_success(self, pipeline_service, mock_db, sample_pipeline_model):
        """测试根据ID成功获取管道"""
        # 配置模拟
        mock_db.query.return_value.filter.return_value.first.return_value = sample_pipeline_model
        
        # 调用方法
        result = pipeline_service.get_pipeline_by_id(mock_db, 1)
        
        # 验证结果
        assert result == sample_pipeline_model
        mock_db.query.assert_called_with(ProcessingPipeline)

    def test_get_pipeline_by_id_not_found(self, pipeline_service, mock_db):
        """测试根据ID获取不存在的管道"""
        # 配置模拟
        mock_db.query.return_value.filter.return_value.first.return_value = None
        
        # 调用方法
        result = pipeline_service.get_pipeline_by_id(mock_db, 999)
        
        # 验证结果
        assert result is None

    def test_get_pipeline_by_code_success(self, pipeline_service, mock_db, sample_pipeline_model):
        """测试根据代码成功获取管道"""
        # 配置模拟
        mock_db.query.return_value.filter.return_value.first.return_value = sample_pipeline_model
        
        # 调用方法
        result = pipeline_service.get_pipeline_by_code(mock_db, "test_pipeline")
        
        # 验证结果
        assert result == sample_pipeline_model

    def test_get_pipelines_with_pagination(self, pipeline_service, mock_db, sample_pipeline_model):
        """测试分页获取管道列表"""
        # 配置模拟
        mock_query = mock_db.query.return_value
        mock_query.count.return_value = 1
        mock_query.order_by.return_value.offset.return_value.limit.return_value.all.return_value = [sample_pipeline_model]
        
        # 调用方法
        pipelines, total = pipeline_service.get_pipelines(mock_db, skip=0, limit=10)
        
        # 验证结果
        assert total == 1
        assert len(pipelines) == 1
        assert pipelines[0] == sample_pipeline_model

    def test_get_pipelines_with_filters(self, pipeline_service, mock_db, sample_pipeline_model):
        """测试带过滤条件获取管道列表"""
        # 配置模拟
        mock_query = mock_db.query.return_value
        mock_query.filter.return_value = mock_query
        mock_query.count.return_value = 1
        mock_query.order_by.return_value.offset.return_value.limit.return_value.all.return_value = [sample_pipeline_model]
        
        # 调用方法
        pipelines, total = pipeline_service.get_pipelines(
            mock_db, 
            skip=0, 
            limit=10, 
            is_active=True, 
            search="test"
        )
        
        # 验证结果
        assert total == 1
        assert len(pipelines) == 1
        
        # 验证过滤器被应用
        assert mock_query.filter.call_count >= 2  # is_active和search过滤器

    # ==================== 更新管道测试 ====================

    def test_update_pipeline_success(self, pipeline_service, mock_db, sample_pipeline_model):
        """测试成功更新管道"""
        # 准备更新数据
        update_data = ProcessingPipelineUpdate(
            pipeline_name="更新后的名称",
            description="更新后的描述",
            is_active=False
        )
        
        # 配置模拟
        pipeline_service.get_pipeline_by_id = MagicMock(return_value=sample_pipeline_model)
        
        # 调用方法
        result = pipeline_service.update_pipeline(mock_db, 1, update_data)
        
        # 验证结果
        assert result == sample_pipeline_model
        assert sample_pipeline_model.pipeline_name == "更新后的名称"
        assert sample_pipeline_model.description == "更新后的描述"
        assert sample_pipeline_model.is_active is False
        mock_db.commit.assert_called_once()
        mock_db.refresh.assert_called_once()

    def test_update_pipeline_not_found(self, pipeline_service, mock_db):
        """测试更新不存在的管道"""
        # 准备更新数据
        update_data = ProcessingPipelineUpdate(pipeline_name="新名称")
        
        # 配置模拟
        pipeline_service.get_pipeline_by_id = MagicMock(return_value=None)
        
        # 调用方法
        result = pipeline_service.update_pipeline(mock_db, 999, update_data)
        
        # 验证结果
        assert result is None
        mock_db.commit.assert_not_called()

    def test_update_pipeline_database_error(self, pipeline_service, mock_db, sample_pipeline_model):
        """测试更新管道时数据库错误"""
        # 准备更新数据
        update_data = ProcessingPipelineUpdate(pipeline_name="新名称")
        
        # 配置模拟
        pipeline_service.get_pipeline_by_id = MagicMock(return_value=sample_pipeline_model)
        mock_db.commit.side_effect = Exception("Database error")
        
        # 验证抛出异常
        with pytest.raises(Exception):
            pipeline_service.update_pipeline(mock_db, 1, update_data)
            
        # 验证回滚被调用
        mock_db.rollback.assert_called_once()

    # ==================== 删除管道测试 ====================

    def test_delete_pipeline_success(self, pipeline_service, mock_db, sample_pipeline_model):
        """测试成功删除管道"""
        # 配置模拟
        pipeline_service.get_pipeline_by_id = MagicMock(return_value=sample_pipeline_model)
        mock_db.query.return_value.filter.return_value.count.return_value = 0  # 没有数据源使用此管道
        
        # 调用方法
        result = pipeline_service.delete_pipeline(mock_db, 1)
        
        # 验证结果
        assert result is True
        mock_db.delete.assert_called_once_with(sample_pipeline_model)
        mock_db.commit.assert_called_once()

    def test_delete_pipeline_not_found(self, pipeline_service, mock_db):
        """测试删除不存在的管道"""
        # 配置模拟
        pipeline_service.get_pipeline_by_id = MagicMock(return_value=None)
        
        # 调用方法
        result = pipeline_service.delete_pipeline(mock_db, 999)
        
        # 验证结果
        assert result is False
        mock_db.delete.assert_not_called()

    def test_delete_pipeline_in_use(self, pipeline_service, mock_db, sample_pipeline_model):
        """测试删除正在使用的管道"""
        # 配置模拟
        pipeline_service.get_pipeline_by_id = MagicMock(return_value=sample_pipeline_model)
        mock_db.query.return_value.filter.return_value.count.return_value = 2  # 有数据源使用此管道
        
        # 验证抛出异常
        with pytest.raises(ValueError, match="Cannot delete pipeline: .* data sources are using it"):
            pipeline_service.delete_pipeline(mock_db, 1)
            
        # 验证没有执行删除
        mock_db.delete.assert_not_called()

    def test_delete_pipeline_database_error(self, pipeline_service, mock_db, sample_pipeline_model):
        """测试删除管道时数据库错误"""
        # 配置模拟
        pipeline_service.get_pipeline_by_id = MagicMock(return_value=sample_pipeline_model)
        mock_db.query.return_value.filter.return_value.count.return_value = 0
        mock_db.commit.side_effect = Exception("Database error")
        
        # 验证抛出异常
        with pytest.raises(Exception):
            pipeline_service.delete_pipeline(mock_db, 1)
            
        # 验证回滚被调用
        mock_db.rollback.assert_called_once()

    # ==================== 获取活跃管道测试 ====================

    def test_get_active_pipelines(self, pipeline_service, mock_db, sample_pipeline_model):
        """测试获取活跃管道"""
        # 配置模拟
        mock_query = mock_db.query.return_value
        mock_query.filter.return_value.order_by.return_value.all.return_value = [sample_pipeline_model]
        
        # 调用方法
        result = pipeline_service.get_active_pipelines(mock_db)
        
        # 验证结果
        assert len(result) == 1
        assert result[0] == sample_pipeline_model
        
        # 验证查询条件
        mock_query.filter.assert_called_once()

    # ==================== 统计信息测试 ====================

    def test_get_pipeline_stats(self, pipeline_service, mock_db):
        """测试获取管道统计"""
        # 配置模拟
        mock_db.query.return_value.count.return_value = 10  # 总数
        mock_db.query.return_value.filter.return_value.count.return_value = 8  # 活跃数
        mock_db.query.return_value.filter.return_value.scalar.return_value = Decimal("0.8500")  # 平均成功率
        mock_db.query.return_value.scalar.return_value = 1500  # 总处理次数
        
        # 调用方法
        result = pipeline_service.get_pipeline_stats(mock_db)
        
        # 验证结果
        assert result["total_pipelines"] == 10
        assert result["active_pipelines"] == 8
        assert result["inactive_pipelines"] == 2
        assert result["avg_success_rate"] == 0.8500
        assert result["total_processed"] == 1500

    def test_get_pipeline_stats_no_data(self, pipeline_service, mock_db):
        """测试获取管道统计（无数据）"""
        # 配置模拟
        mock_db.query.return_value.count.return_value = 0
        mock_db.query.return_value.filter.return_value.count.return_value = 0
        mock_db.query.return_value.filter.return_value.scalar.return_value = None
        mock_db.query.return_value.scalar.return_value = None
        
        # 调用方法
        result = pipeline_service.get_pipeline_stats(mock_db)
        
        # 验证结果
        assert result["total_pipelines"] == 0
        assert result["active_pipelines"] == 0
        assert result["inactive_pipelines"] == 0
        assert result["avg_success_rate"] is None
        assert result["total_processed"] == 0

    # ==================== 更新统计信息测试 ====================

    def test_update_pipeline_stats_success(self, pipeline_service, mock_db, sample_pipeline_model):
        """测试成功更新管道统计"""
        # 配置模拟
        pipeline_service.get_pipeline_by_id = MagicMock(return_value=sample_pipeline_model)
        
        # 调用方法
        result = pipeline_service.update_pipeline_stats(mock_db, 1, success=True)
        
        # 验证结果
        assert result is True
        assert sample_pipeline_model.total_processed_count == 16  # 15 + 1
        mock_db.commit.assert_called_once()

    def test_update_pipeline_stats_failure(self, pipeline_service, mock_db, sample_pipeline_model):
        """测试更新管道统计（失败情况）"""
        # 配置模拟
        pipeline_service.get_pipeline_by_id = MagicMock(return_value=sample_pipeline_model)
        
        # 调用方法
        result = pipeline_service.update_pipeline_stats(mock_db, 1, success=False)
        
        # 验证结果
        assert result is True
        assert sample_pipeline_model.total_processed_count == 16  # 15 + 1
        # 成功率应该降低
        assert sample_pipeline_model.success_rate < Decimal("0.8000")

    def test_update_pipeline_stats_not_found(self, pipeline_service, mock_db):
        """测试更新不存在管道的统计"""
        # 配置模拟
        pipeline_service.get_pipeline_by_id = MagicMock(return_value=None)
        
        # 调用方法
        result = pipeline_service.update_pipeline_stats(mock_db, 999, success=True)
        
        # 验证结果
        assert result is False
        mock_db.commit.assert_not_called()

    def test_update_pipeline_stats_database_error(self, pipeline_service, mock_db, sample_pipeline_model):
        """测试更新统计时数据库错误"""
        # 配置模拟
        pipeline_service.get_pipeline_by_id = MagicMock(return_value=sample_pipeline_model)
        mock_db.commit.side_effect = Exception("Database error")
        
        # 调用方法
        result = pipeline_service.update_pipeline_stats(mock_db, 1, success=True)
        
        # 验证结果
        assert result is False
        mock_db.rollback.assert_called_once()


if __name__ == "__main__":
    pytest.main([__file__]) 