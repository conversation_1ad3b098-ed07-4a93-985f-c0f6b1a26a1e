"""
数据处理服务集成测试
测试完整的数据处理流程
"""

import pytest
import asyncio
from datetime import datetime, timezone
from unittest.mock import Mock, AsyncMock, patch
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from src.core.database import Base
from src.services.data_processing_service.engine import DataProcessingEngine
from src.services.data_processing_service.models import (
    DataProcessingPipeline, DataProcessingStatus, ProcessingStage, ProcessingResult,
    FlashNews, NewsArticle, UnifiedContentTags
)
from src.services.data_collection_service.models import (
    RawDataRecord, DataSource, BusinessDataType
)
from src.services.tag_classification_service.models import Tag, TagClassification


@pytest.fixture
def db_session():
    """创建测试数据库会话"""
    engine = create_engine("sqlite:///:memory:")
    Base.metadata.create_all(engine)
    SessionLocal = sessionmaker(bind=engine)
    session = SessionLocal()
    yield session
    session.close()


@pytest.fixture
def complete_test_data(db_session):
    """创建完整的测试数据"""
    
    # 1. 创建数据源
    data_source = DataSource(
        name="测试财经网站",
        collection_method="api_json",
        content_category="financial_news",
        business_data_type=BusinessDataType.FLASH_NEWS,
        base_url="https://test-finance.com",
        is_active=True
    )
    db_session.add(data_source)
    db_session.commit()
    
    # 2. 创建处理管道
    pipeline = DataProcessingPipeline(
        pipeline_code="flash_news_complete",
        pipeline_name="快讯完整处理流程",
        source_id=data_source.id,
        target_business_type=BusinessDataType.FLASH_NEWS,
        is_active=True,
        field_mapping={
            "title": "title",
            "content": "content",
            "publish_time": "publish_time",
            "author": "author"
        },
        data_extraction_config={
            "content_cleaning": {
                "remove_html": True,
                "normalize_whitespace": True
            },
            "time_processing": {
                "publish_time": {}
            },
            "metadata_extraction": {
                "calculate_word_count": True,
                "extract_domain": True
            }
        },
        data_transformation_config={
            "title_cleaning": {
                "apply_patterns": True,
                "max_length": 500
            },
            "content_cleaning": {
                "remove_html": True,
                "normalize_whitespace": True,
                "remove_ads": True
            },
            "importance_detection": True,
            "category_mapping": {
                "news_category": {
                    "source_field": "category",
                    "mapping": {
                        "monetary": "货币政策",
                        "market": "市场分析"
                    }
                }
            }
        },
        data_validation_config={
            "required_fields": ["title", "content"],
            "field_lengths": {
                "title": 500,
                "content": 10000
            },
            "format_checks": {}
        },
        data_enrichment_config={
            "generate_summary": True,
            "extract_tags": True,
            "classify_content": True,
            "extract_entities": True,
            "sentiment_analysis": True,
            "importance_scoring": True
        }
    )
    db_session.add(pipeline)
    db_session.commit()
    
    # 3. 创建原始数据记录
    raw_record = RawDataRecord(
        source_id=data_source.id,
        source_url="https://test-finance.com/news/central-bank-policy",
        url_hash="test_hash_central_bank",
        url_domain="test-finance.com",
        content_hash="content_hash_central_bank",
        title="【快讯】央行宣布降准0.5个百分点",
        author="财经记者",
        publish_time=datetime.now(timezone.utc),
        mongodb_id="507f1f77bcf86cd799439012",
        content_type="financial_news",
        processing_status="pending",
        processing_priority=5
    )
    db_session.add(raw_record)
    db_session.commit()
    
    # 4. 创建标签分类和标签
    tag_classification = TagClassification(
        classification_code="financial.test",
        classification_name="财经测试标签",
        classification_type="type",
        domain="financial",
        level=1,
        path="financial.test"
    )
    db_session.add(tag_classification)
    db_session.commit()

    tags = [
        Tag(
            tag_name="央行",
            tag_code="central_bank",
            tag_slug="central-bank",
            classification_id=tag_classification.id,
            synonyms=["中央银行", "人民银行"]
        ),
        Tag(
            tag_name="货币政策",
            tag_code="monetary_policy",
            tag_slug="monetary-policy",
            classification_id=tag_classification.id
        ),
        Tag(
            tag_name="降准",
            tag_code="rrr_cut",
            tag_slug="rrr-cut",
            classification_id=tag_classification.id,
            synonyms=["存款准备金率下调"]
        )
    ]
    
    for tag in tags:
        db_session.add(tag)
    db_session.commit()
    
    return {
        "data_source": data_source,
        "pipeline": pipeline,
        "raw_record": raw_record,
        "tags": tags
    }


@pytest.fixture
def mock_mongodb_content():
    """模拟MongoDB内容"""
    return {
        "title": "【快讯】央行宣布降准0.5个百分点",
        "content": """
        <p>中国人民银行今日宣布，决定于2025年2月1日降准0.5个百分点，释放长期资金约1万亿元。</p>
        
        <p>此次降准旨在保持银行体系流动性合理充裕，支持实体经济发展。央行表示，此举将有效降低银行资金成本，促进银行增加对实体经济的信贷投放。</p>
        
        <p>市场分析师认为，此举将对股市产生积极影响，特别是银行股和地产股可能受益。</p>
        """,
        "publish_time": "2025-01-15 14:30:00",
        "author": "央行新闻发言人",
        "source_url": "https://test-finance.com/news/central-bank-policy",
        "category": "monetary"
    }


class TestDataProcessingIntegration:
    """数据处理集成测试"""

    @pytest.fixture(autouse=True)
    def setup(self):
        """设置测试环境"""
        self.engine = DataProcessingEngine()

    @patch('src.services.data_processing_service.engine.SessionLocal')
    @patch('src.services.data_processing_service.engine.mongodb_manager')
    async def test_complete_processing_pipeline(
        self,
        mock_mongodb,
        mock_session_local,
        db_session,
        complete_test_data,
        mock_mongodb_content
    ):
        """测试完整的数据处理流程"""
        
        # 模拟数据库会话
        mock_session_local.return_value = db_session
        
        # 模拟MongoDB内容获取
        mock_mongodb.get_raw_content = AsyncMock(return_value=mock_mongodb_content)
        
        # 获取测试数据
        raw_record = complete_test_data["raw_record"]
        
        # 执行处理
        result = await self.engine.process_single_record(raw_record)
        
        # 验证处理结果
        assert result["success"] is True
        assert "business_record_id" in result
        assert result["business_table"] == "flash_news"
        
        # 验证业务数据
        flash_news = db_session.query(FlashNews).filter(
            FlashNews.raw_data_id == raw_record.id
        ).first()
        
        assert flash_news is not None
        assert flash_news.title == "央行宣布降准0.5个百分点"  # 应该去除【快讯】前缀
        assert flash_news.content is not None
        assert "<p>" not in flash_news.content  # HTML标签应该被去除
        assert flash_news.urgency_level == 2  # 包含"央行"关键词，应该是重要级别
        assert flash_news.summary is not None  # AI生成的摘要
        assert flash_news.news_category == "货币政策"  # 分类映射结果
        
        # 验证处理状态
        processing_status = db_session.query(DataProcessingStatus).filter(
            DataProcessingStatus.raw_data_id == raw_record.id
        ).first()
        
        assert processing_status is not None
        assert processing_status.processing_stage == ProcessingStage.COMPLETED
        assert processing_status.processing_result == ProcessingResult.SUCCESS
        assert processing_status.progress_percentage == 100
        assert processing_status.target_table_id == flash_news.id
        assert processing_status.target_table_name == "flash_news"
        
        # 验证原始记录状态更新
        db_session.refresh(raw_record)
        assert raw_record.processing_status == "processed"

    @patch('src.services.data_processing_service.engine.SessionLocal')
    @patch('src.services.data_processing_service.engine.mongodb_manager')
    async def test_spam_content_deletion(
        self,
        mock_mongodb,
        mock_session_local,
        db_session,
        complete_test_data
    ):
        """测试垃圾内容删除流程"""
        
        # 模拟数据库会话
        mock_session_local.return_value = db_session
        
        # 模拟垃圾内容
        spam_content = {
            "title": "免费推广优惠活动",
            "content": "点击链接获取免费优惠，微信联系，立即下载APP，推广代理加盟",
            "publish_time": "2025-01-15 14:30:00",
            "author": "推广员"
        }
        
        # 模拟MongoDB内容获取和删除
        mock_mongodb.get_raw_content = AsyncMock(return_value=spam_content)
        mock_mongodb.delete_raw_content = AsyncMock()
        
        # 获取测试数据
        raw_record = complete_test_data["raw_record"]
        original_record_id = raw_record.id
        
        # 执行处理
        result = await self.engine.process_single_record(raw_record)
        
        # 验证处理结果
        assert result["success"] is True
        assert result["action"] == "deleted"
        assert result["reason"] == "spam_content"
        
        # 验证MongoDB删除被调用
        mock_mongodb.delete_raw_content.assert_called_once_with(raw_record.mongodb_id)
        
        # 验证原始记录被删除
        deleted_record = db_session.query(RawDataRecord).filter(
            RawDataRecord.id == original_record_id
        ).first()
        assert deleted_record is None
        
        # 验证处理状态记录
        processing_status = db_session.query(DataProcessingStatus).filter(
            DataProcessingStatus.raw_data_id == original_record_id
        ).first()
        
        assert processing_status is not None
        assert processing_status.processing_result == ProcessingResult.DELETED

    @patch('src.services.data_processing_service.engine.SessionLocal')
    async def test_batch_processing(
        self,
        mock_session_local,
        db_session,
        complete_test_data
    ):
        """测试批量处理"""
        
        # 模拟数据库会话
        mock_session_local.return_value = db_session
        
        # 创建多个待处理记录
        data_source = complete_test_data["data_source"]
        
        additional_records = []
        for i in range(3):
            record = RawDataRecord(
                source_id=data_source.id,
                source_url=f"https://test-finance.com/news/{i}",
                url_hash=f"test_hash_{i}",
                url_domain="test-finance.com",
                content_hash=f"content_hash_{i}",
                title=f"测试新闻 {i}",
                author="测试作者",
                publish_time=datetime.now(timezone.utc),
                mongodb_id=f"507f1f77bcf86cd79943901{i}",
                content_type="financial_news",
                processing_status="pending",
                processing_priority=i + 1
            )
            db_session.add(record)
            additional_records.append(record)
        
        db_session.commit()
        
        # 模拟单个记录处理成功
        with patch.object(self.engine, 'process_single_record') as mock_process:
            mock_process.return_value = {"success": True}
            
            # 执行批量处理
            stats = await self.engine.process_pending_records(batch_size=5)
            
            # 验证处理统计
            assert stats["processed"] == 4  # 原始记录 + 3个新记录
            assert stats["success"] == 4
            assert stats["failed"] == 0
            
            # 验证处理调用次数
            assert mock_process.call_count == 4

    @patch('src.services.data_processing_service.engine.SessionLocal')
    async def test_failure_recovery(
        self,
        mock_session_local,
        db_session,
        complete_test_data
    ):
        """测试失败恢复机制"""
        
        # 模拟数据库会话
        mock_session_local.return_value = db_session
        
        # 创建失败的处理状态
        raw_record = complete_test_data["raw_record"]
        
        failed_status = DataProcessingStatus(
            raw_data_id=raw_record.id,
            target_business_type=BusinessDataType.FLASH_NEWS,
            processing_stage=ProcessingStage.FAILED,
            retry_count=0,
            error_message="模拟处理失败",
            started_at=datetime.now(timezone.utc)
        )
        db_session.add(failed_status)
        db_session.commit()
        
        # 模拟重试成功
        with patch.object(self.engine, 'process_single_record') as mock_process:
            mock_process.return_value = {"success": True}
            
            # 执行重试
            stats = await self.engine.retry_failed_records(max_retries=3)
            
            # 验证重试结果
            assert stats["retried"] == 1
            assert stats["success"] == 1
            assert stats["failed"] == 0
            
            # 验证重试计数增加
            db_session.refresh(failed_status)
            assert failed_status.retry_count == 1
            assert failed_status.processing_stage == ProcessingStage.PENDING

    async def test_data_validation_failure(
        self,
        db_session,
        complete_test_data
    ):
        """测试数据验证失败"""
        
        # 测试无效数据
        invalid_data = {
            "title": "",  # 必填字段为空
            "content": ""  # 必填字段为空
        }
        
        pipeline = complete_test_data["pipeline"]
        
        # 执行验证
        validation_result = await self.engine._validate_data(invalid_data, pipeline)
        
        # 验证结果
        assert validation_result["valid"] is False
        assert len(validation_result["errors"]) >= 2  # 至少有两个必填字段错误

    async def test_ai_enhancement_integration(self, complete_test_data):
        """测试AI增强集成"""
        
        test_data = {
            "title": "央行宣布重要政策",
            "content": "中国人民银行今日宣布重要货币政策调整"
        }
        
        pipeline = complete_test_data["pipeline"]
        
        # 执行AI增强
        enhanced_data = await self.engine._enhance_data(test_data, pipeline)
        
        # 验证AI增强结果
        assert "summary" in enhanced_data
        assert "ai_extracted_tags" in enhanced_data
        assert "ai_classifications" in enhanced_data
        assert "importance_score" in enhanced_data
        
        # 验证标签提取
        assert isinstance(enhanced_data["ai_extracted_tags"], list)
        assert len(enhanced_data["ai_extracted_tags"]) > 0
