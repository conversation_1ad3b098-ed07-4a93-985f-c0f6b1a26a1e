"""
数据处理服务B端管理API测试（修复版）
测试所有API接口的功能和权限控制
"""

import pytest
from unittest.mock import Mock, patch, MagicMock
from fastapi.testclient import TestClient
from fastapi import FastAPI
from decimal import Decimal

from src.services.data_processing_service.admin_router import router
from src.services.data_collection_service.models import BusinessDataType


# 创建测试应用
app = FastAPI()

# 模拟权限验证依赖
def mock_require_permission(permission: str):
    def dependency():
        user = Mock()
        user.id = 1
        user.username = "test_admin"
        user.is_admin = True
        return user
    return dependency

def mock_get_service():
    return Mock()

def mock_get_db():
    return Mock()

# 覆盖依赖项
app.dependency_overrides[
    "src.services.data_processing_service.admin_router.require_permission"
] = mock_require_permission
app.dependency_overrides[
    "src.services.data_processing_service.admin_router.get_data_processing_rule_service"
] = mock_get_service
app.dependency_overrides[
    "src.services.data_processing_service.admin_router.get_db"
] = mock_get_db

app.include_router(router, prefix="/admin/data-processing")

@pytest.fixture
def client():
    """测试客户端"""
    return TestClient(app)

@pytest.fixture
def mock_service():
    """模拟服务"""
    with patch('src.services.data_processing_service.admin_router.get_data_processing_rule_service') as mock:
        service = Mock()
        mock.return_value = service
        yield service

@pytest.fixture
def sample_rule_data():
    """示例规则数据"""
    return {
        "rule_name": "test_flash_news_rule",
        "business_data_type": "flash_news",
        "field_mapping": {
            "title": "$.content",
            "content": "$.content",
            "publish_time": "$.time"
        },
        "transformation_rules": {
            "publish_time": {"format": "timestamp", "timezone": "Asia/Shanghai"}
        },
        "validation_rules": {
            "required_fields": ["title", "content", "publish_time"],
            "max_content_length": 500
        },
        "source_criteria": {
            "source_names": ["金十数据快讯"],
            "collection_methods": ["api_json"]
        },
        "priority": 8,
        "created_by": "test_admin"
    }


class TestDataProcessingRuleAPI:
    """数据处理规则API测试类"""

    def test_create_processing_rule_success(self, client, mock_service, sample_rule_data):
        """测试成功创建数据处理规则"""
        # 模拟服务返回
        mock_rule = Mock()
        mock_rule.id = 1
        mock_rule.rule_name = sample_rule_data["rule_name"]
        mock_rule.business_data_type = sample_rule_data["business_data_type"]
        mock_rule.priority = sample_rule_data["priority"]
        mock_rule.is_active = True
        mock_rule.execution_count = 0
        mock_rule.success_count = 0
        mock_rule.created_by = sample_rule_data["created_by"]
        mock_rule.created_at = "2024-01-01T00:00:00Z"
        mock_rule.updated_at = "2024-01-01T00:00:00Z"
        
        mock_service.create_rule.return_value = mock_rule
        
        # 执行测试
        with patch('src.services.data_processing_service.admin_router.require_permission') as mock_perm:
            mock_perm.return_value = lambda: Mock()
            response = client.post("/admin/data-processing/rules", json=sample_rule_data)
        
        # 验证结果
        assert response.status_code == 201

    def test_get_processing_rules_list(self, client, mock_service):
        """测试获取规则列表"""
        # 模拟服务返回
        mock_rules = []
        for i in range(3):
            rule = Mock()
            rule.id = i + 1
            rule.rule_name = f"rule_{i + 1}"
            rule.business_data_type = "flash_news"
            rule.priority = 5
            rule.is_active = True
            rule.execution_count = 0
            rule.success_count = 0
            rule.created_at = "2024-01-01T00:00:00Z"
            rule.updated_at = "2024-01-01T00:00:00Z"
            mock_rules.append(rule)
        
        mock_service.get_rules.return_value = (mock_rules, 3)
        
        # 执行测试
        with patch('src.services.data_processing_service.admin_router.require_permission') as mock_perm:
            mock_perm.return_value = lambda: Mock()
            response = client.get("/admin/data-processing/rules?skip=0&limit=10")
        
        # 验证结果
        assert response.status_code == 200
        response_data = response.json()
        assert response_data["total"] == 3
        assert len(response_data["items"]) == 3

    def test_get_processing_rule_by_id_success(self, client, mock_service):
        """测试根据ID获取规则成功"""
        # 模拟服务返回
        mock_rule = Mock()
        mock_rule.id = 1
        mock_rule.rule_name = "test_rule"
        mock_rule.business_data_type = "flash_news"
        mock_rule.priority = 5
        mock_rule.is_active = True
        mock_rule.execution_count = 0
        mock_rule.success_count = 0
        mock_rule.created_at = "2024-01-01T00:00:00Z"
        mock_rule.updated_at = "2024-01-01T00:00:00Z"
        
        mock_service.get_rule_by_id.return_value = mock_rule
        
        # 执行测试
        with patch('src.services.data_processing_service.admin_router.require_permission') as mock_perm:
            mock_perm.return_value = lambda: Mock()
            response = client.get("/admin/data-processing/rules/1")
        
        # 验证结果
        assert response.status_code == 200
        response_data = response.json()
        assert response_data["id"] == 1

    def test_get_processing_rule_by_id_not_found(self, client, mock_service):
        """测试根据ID获取规则未找到"""
        # 模拟服务返回None
        mock_service.get_rule_by_id.return_value = None
        
        # 执行测试
        with patch('src.services.data_processing_service.admin_router.require_permission') as mock_perm:
            mock_perm.return_value = lambda: Mock()
            response = client.get("/admin/data-processing/rules/999")
        
        # 验证结果
        assert response.status_code == 404

    def test_update_processing_rule_success(self, client, mock_service):
        """测试成功更新规则"""
        # 模拟服务返回
        mock_rule = Mock()
        mock_rule.id = 1
        mock_rule.rule_name = "updated_rule"
        mock_rule.business_data_type = "flash_news"
        mock_rule.priority = 9
        mock_rule.is_active = False
        mock_rule.execution_count = 0
        mock_rule.success_count = 0
        mock_rule.created_at = "2024-01-01T00:00:00Z"
        mock_rule.updated_at = "2024-01-01T00:00:00Z"
        
        mock_service.update_rule.return_value = mock_rule
        
        # 准备更新数据
        update_data = {
            "rule_name": "updated_rule",
            "priority": 9,
            "is_active": False
        }
        
        # 执行测试
        with patch('src.services.data_processing_service.admin_router.require_permission') as mock_perm:
            mock_perm.return_value = lambda: Mock()
            response = client.put("/admin/data-processing/rules/1", json=update_data)
        
        # 验证结果
        assert response.status_code == 200
        response_data = response.json()
        assert response_data["rule_name"] == "updated_rule"

    def test_delete_processing_rule_success(self, client, mock_service):
        """测试成功删除规则"""
        # 模拟服务返回
        mock_service.delete_rule.return_value = True
        
        # 执行测试
        with patch('src.services.data_processing_service.admin_router.require_permission') as mock_perm:
            mock_perm.return_value = lambda: Mock()
            response = client.delete("/admin/data-processing/rules/1")
        
        # 验证结果
        assert response.status_code == 204

    def test_get_processing_rules_stats(self, client, mock_service):
        """测试获取规则统计"""
        # 模拟服务返回
        mock_stats = Mock()
        mock_stats.total_rules = 10
        mock_stats.active_rules = 8
        mock_stats.inactive_rules = 2
        mock_stats.rules_by_type = {"flash_news": 5, "news_article": 3, "research_report": 2}
        mock_stats.average_priority = 6.5
        mock_stats.total_executions = 1000
        mock_stats.total_successes = 950
        mock_stats.overall_success_rate = 95.0
        mock_service.get_rules_stats.return_value = mock_stats
        
        # 执行测试
        with patch('src.services.data_processing_service.admin_router.require_permission') as mock_perm:
            mock_perm.return_value = lambda: Mock()
            response = client.get("/admin/data-processing/rules/stats")
        
        # 验证结果
        assert response.status_code == 200
        response_data = response.json()
        assert response_data["total_rules"] == 10
        assert response_data["active_rules"] == 8

    def test_create_rule_with_invalid_data(self, client):
        """测试使用无效数据创建规则"""
        invalid_data = {
            "rule_name": "",  # 空名称
            "business_data_type": "flash_news",
            "field_mapping": {}  # 空映射
        }
        
        # 执行测试
        with patch('src.services.data_processing_service.admin_router.require_permission') as mock_perm:
            mock_perm.return_value = lambda: Mock()
            response = client.post("/admin/data-processing/rules", json=invalid_data)
        
        # 验证结果
        assert response.status_code == 422  # 验证错误

    def test_batch_update_rules_status(self, client, mock_service):
        """测试批量更新规则状态"""
        # 模拟服务返回
        mock_result = Mock()
        mock_result.success_count = 3
        mock_result.failed_count = 0
        mock_result.failed_ids = []
        mock_result.message = "批量更新完成"
        mock_service.batch_update_status.return_value = mock_result
        
        # 准备批量更新数据
        batch_data = {
            "rule_ids": [1, 2, 3],
            "is_active": False
        }
        
        # 执行测试
        with patch('src.services.data_processing_service.admin_router.require_permission') as mock_perm:
            mock_perm.return_value = lambda: Mock()
            response = client.patch("/admin/data-processing/rules/batch/status", json=batch_data)
        
        # 验证结果
        assert response.status_code == 200
        response_data = response.json()
        assert response_data["success_count"] == 3

    def test_batch_update_rules_priority(self, client, mock_service):
        """测试批量更新规则优先级"""
        # 模拟服务返回
        mock_result = Mock()
        mock_result.success_count = 2
        mock_result.failed_count = 0
        mock_result.failed_ids = []
        mock_result.message = "批量更新优先级完成"
        mock_service.batch_update_priority.return_value = mock_result
        
        # 准备批量更新数据
        batch_data = {
            "rule_ids": [1, 2],
            "priority": 9
        }
        
        # 执行测试
        with patch('src.services.data_processing_service.admin_router.require_permission') as mock_perm:
            mock_perm.return_value = lambda: Mock()
            response = client.patch("/admin/data-processing/rules/batch/priority", json=batch_data)
        
        # 验证结果
        assert response.status_code == 200
        response_data = response.json()
        assert response_data["success_count"] == 2 