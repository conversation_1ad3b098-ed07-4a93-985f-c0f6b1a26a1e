"""
测试ProcessingStage枚举修复后的功能
"""

import pytest
import asyncio
from datetime import datetime, timezone
from unittest.mock import Mock, AsyncMock, patch
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from src.core.database import Base
from src.services.data_processing_service.models import (
    ProcessingStage, ProcessingResult, DataProcessingStatus, FlashNews
)
from src.services.data_collection_service.models import (
    RawDataRecord, DataSource, BusinessDataType
)


@pytest.fixture
def db_session():
    """创建测试数据库会话"""
    engine = create_engine("sqlite:///:memory:")
    Base.metadata.create_all(engine)
    SessionLocal = sessionmaker(bind=engine)
    session = SessionLocal()
    yield session
    session.close()


class TestProcessingStagesFix:
    """测试ProcessingStage枚举修复"""
    
    def test_processing_stage_enum_values(self):
        """测试ProcessingStage枚举包含所有必需的值"""
        # 测试所有必需的阶段都存在
        required_stages = [
            'PENDING', 'EXTRACTING', 'CLEANING', 'TRANSFORMING', 
            'PARSING', 'CLASSIFYING', 'TAGGING', 'VALIDATING', 
            'ENHANCING', 'SAVING', 'COMPLETED', 'FAILED'
        ]
        
        for stage_name in required_stages:
            assert hasattr(ProcessingStage, stage_name), f"ProcessingStage缺少{stage_name}阶段"
            stage = getattr(ProcessingStage, stage_name)
            assert isinstance(stage.value, str), f"{stage_name}的值应该是字符串"
        
        # 测试特定值
        assert ProcessingStage.EXTRACTING.value == "extracting"
        assert ProcessingStage.CLEANING.value == "cleaning"
        assert ProcessingStage.TRANSFORMING.value == "transforming"
        assert ProcessingStage.ENHANCING.value == "enhancing"
        assert ProcessingStage.SAVING.value == "saving"
    
    def test_processing_status_creation_with_new_stages(self, db_session):
        """测试使用新阶段创建处理状态"""
        # 创建数据源
        data_source = DataSource(
            name="测试数据源",
            collection_method="api_json",
            content_category="financial_news",
            business_data_type=BusinessDataType.FLASH_NEWS,
            base_url="https://test.com"
        )
        db_session.add(data_source)
        db_session.commit()
        
        # 创建原始数据记录
        raw_record = RawDataRecord(
            source_id=data_source.id,
            title="测试标题",
            source_url="https://test.com/news/1",
            mongodb_id="test_mongo_id",
            processing_status="pending"
        )
        db_session.add(raw_record)
        db_session.commit()
        
        # 测试创建不同阶段的处理状态
        test_stages = [
            ProcessingStage.EXTRACTING,
            ProcessingStage.CLEANING,
            ProcessingStage.TRANSFORMING,
            ProcessingStage.ENHANCING,
            ProcessingStage.SAVING
        ]
        
        for i, stage in enumerate(test_stages):
            status = DataProcessingStatus(
                raw_data_id=raw_record.id,
                target_business_type=BusinessDataType.FLASH_NEWS,
                processing_stage=stage,
                progress_percentage=i * 20,
                current_step=f"测试步骤{i+1}"
            )
            db_session.add(status)
        
        db_session.commit()
        
        # 验证所有状态都成功创建
        statuses = db_session.query(DataProcessingStatus).all()
        assert len(statuses) == len(test_stages)
        
        for i, status in enumerate(statuses):
            assert status.processing_stage == test_stages[i]
            assert status.progress_percentage == i * 20
    
    def test_flash_news_creation_without_source_url(self, db_session):
        """测试创建FlashNews记录（不包含source_url字段）"""
        # 创建数据源
        data_source = DataSource(
            name="测试数据源",
            collection_method="api_json",
            content_category="financial_news",
            business_data_type=BusinessDataType.FLASH_NEWS,
            base_url="https://test.com"
        )
        db_session.add(data_source)
        db_session.commit()
        
        # 创建原始数据记录
        raw_record = RawDataRecord(
            source_id=data_source.id,
            title="测试快讯标题",
            source_url="https://test.com/news/1",
            mongodb_id="test_mongo_id",
            processing_status="pending"
        )
        db_session.add(raw_record)
        db_session.commit()
        
        # 创建FlashNews记录（使用正确的字段）
        flash_news = FlashNews(
            title="测试快讯标题",
            content="测试快讯内容",
            summary="测试摘要",
            publish_time=datetime.now(timezone.utc),
            process_time=datetime.now(timezone.utc),
            status="published",
            importance_score=0.5,
            raw_data_id=raw_record.id,
            news_category="财经快讯",

            urgency_level=2,
            impact_scope="domestic"
        )
        
        db_session.add(flash_news)
        db_session.commit()
        
        # 验证记录创建成功
        saved_flash_news = db_session.query(FlashNews).first()
        assert saved_flash_news is not None
        assert saved_flash_news.title == "测试快讯标题"
        assert saved_flash_news.content == "测试快讯内容"
        assert saved_flash_news.raw_data_id == raw_record.id
        assert saved_flash_news.news_category == "财经快讯"
        # 相关关键词现在通过统一标签系统管理
    
    def test_processing_stage_transitions(self, db_session):
        """测试处理阶段转换"""
        # 创建数据源和原始记录
        data_source = DataSource(
            name="测试数据源",
            collection_method="api_json",
            content_category="financial_news",
            business_data_type=BusinessDataType.FLASH_NEWS,
            base_url="https://test.com"
        )
        db_session.add(data_source)
        db_session.commit()
        
        raw_record = RawDataRecord(
            source_id=data_source.id,
            title="测试标题",
            source_url="https://test.com/news/1",
            mongodb_id="test_mongo_id",
            processing_status="pending"
        )
        db_session.add(raw_record)
        db_session.commit()
        
        # 创建处理状态并测试阶段转换
        status = DataProcessingStatus(
            raw_data_id=raw_record.id,
            target_business_type=BusinessDataType.FLASH_NEWS,
            processing_stage=ProcessingStage.PENDING,
            progress_percentage=0
        )
        db_session.add(status)
        db_session.commit()
        
        # 模拟处理流程的阶段转换
        stage_progression = [
            (ProcessingStage.EXTRACTING, 10),
            (ProcessingStage.CLEANING, 30),
            (ProcessingStage.TRANSFORMING, 50),
            (ProcessingStage.VALIDATING, 70),
            (ProcessingStage.ENHANCING, 80),
            (ProcessingStage.SAVING, 90),
            (ProcessingStage.COMPLETED, 100)
        ]
        
        for stage, progress in stage_progression:
            status.processing_stage = stage
            status.progress_percentage = progress
            db_session.commit()
            
            # 验证更新成功
            db_session.refresh(status)
            assert status.processing_stage == stage
            assert status.progress_percentage == progress
    
    def test_business_data_type_consistency(self):
        """测试BusinessDataType枚举一致性"""
        # 验证ECONOMIC_DATA存在（修复后的版本）
        assert hasattr(BusinessDataType, 'ECONOMIC_DATA')
        assert BusinessDataType.ECONOMIC_DATA.value == "economic_data"
        
        # 验证FLASH_NEWS存在
        assert hasattr(BusinessDataType, 'FLASH_NEWS')
        assert BusinessDataType.FLASH_NEWS.value == "flash_news"
