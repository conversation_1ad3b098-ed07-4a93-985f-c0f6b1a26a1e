"""
数据处理服务B端管理API测试
测试所有API接口的功能和权限控制
"""

import pytest
from unittest.mock import Mock, patch
from fastapi.testclient import TestClient
from fastapi import FastAPI, HTTPException, status

from src.services.data_processing_service.admin_router import router
from src.services.data_processing_service.models import DataProcessingRule
from src.services.data_processing_service.schemas import DataProcessingRuleResponse
from src.services.data_collection_service.models import BusinessDataType


# 创建测试应用
app = FastAPI()
app.include_router(router, prefix="/admin/data-processing")

@pytest.fixture
def client():
    """测试客户端"""
    return TestClient(app)

@pytest.fixture
def mock_user():
    """模拟用户"""
    user = Mock()
    user.id = 1
    user.username = "test_admin"
    user.is_admin = True
    return user

@pytest.fixture
def mock_service():
    """模拟服务"""
    return Mock()

@pytest.fixture
def mock_db():
    """模拟数据库会话"""
    return Mock()

@pytest.fixture
def sample_rule_data():
    """示例规则数据"""
    return {
        "rule_name": "test_flash_news_rule",
        "business_data_type": "flash_news",
        "field_mapping": {
            "title": "$.content",
            "content": "$.content",
            "publish_time": "$.time"
        },
        "transformation_rules": {
            "publish_time": {"format": "timestamp", "timezone": "Asia/Shanghai"}
        },
        "validation_rules": {
            "required_fields": ["title", "content", "publish_time"],
            "max_content_length": 500
        },
        "source_criteria": {
            "source_names": ["金十数据快讯"],
            "collection_methods": ["api_json"]
        },
        "priority": 8,
        "created_by": "test_admin"
    }

@pytest.fixture
def sample_rule_response():
    """示例规则响应"""
    return {
        "id": 1,
        "rule_name": "test_flash_news_rule",
        "business_data_type": "flash_news",
        "field_mapping": {
            "title": "$.content",
            "content": "$.content",
            "publish_time": "$.time"
        },
        "transformation_rules": {
            "publish_time": {"format": "timestamp", "timezone": "Asia/Shanghai"}
        },
        "validation_rules": {
            "required_fields": ["title", "content", "publish_time"],
            "max_content_length": 500
        },
        "source_criteria": {
            "source_names": ["金十数据快讯"],
            "collection_methods": ["api_json"]
        },
        "priority": 8,
        "is_active": True,
        "execution_count": 0,
        "success_count": 0,
        "created_by": "test_admin",
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z"
    }


class TestDataProcessingRuleAdminRouter:
    """数据处理规则B端管理API测试类"""

    def test_create_processing_rule_success(
        self, client, sample_rule_data, sample_rule_response
    ):
        """测试成功创建数据处理规则"""
        with patch('src.services.data_processing_service.admin_router.require_permission') as mock_permission, \
             patch('src.services.data_processing_service.admin_router.get_data_processing_rule_service') as mock_get_service, \
             patch('src.services.data_processing_service.admin_router.get_db') as mock_get_db:
            
            # 配置模拟 - 让权限验证通过
            mock_user = Mock()
            mock_user.username = "test_admin"
            mock_permission.return_value = lambda: mock_user
            
            mock_service = Mock()
            mock_get_service.return_value = mock_service
            mock_get_db.return_value = Mock()
        
        # 模拟服务返回
        mock_rule = Mock()
        for key, value in sample_rule_response.items():
            setattr(mock_rule, key, value)
        mock_service.create_rule.return_value = mock_rule
        
        # 执行测试
        response = client.post("/admin/data-processing/rules", json=sample_rule_data)
        
        # 验证结果
        assert response.status_code == 201
        response_data = response.json()
        assert response_data["rule_name"] == sample_rule_data["rule_name"]
        assert response_data["business_data_type"] == sample_rule_data["business_data_type"]
        assert response_data["priority"] == sample_rule_data["priority"]

    @patch('src.services.data_processing_service.admin_router.require_permission')
    @patch('src.services.data_processing_service.admin_router.get_data_processing_rule_service')
    @patch('src.services.data_processing_service.admin_router.get_db')
    def test_create_processing_rule_duplicate_name(
        self, mock_get_db, mock_get_service, mock_require_permission,
        client, mock_user, mock_service, mock_db, sample_rule_data
    ):
        """测试创建重复名称规则"""
        # 配置模拟
        mock_require_permission.return_value = mock_user
        mock_get_service.return_value = mock_service
        mock_get_db.return_value = mock_db
        
        # 模拟服务抛出异常
        mock_service.create_rule.side_effect = ValueError("规则名称 'test_flash_news_rule' 已存在")
        
        # 执行测试
        response = client.post("/admin/data-processing/rules", json=sample_rule_data)
        
        # 验证结果
        assert response.status_code == 400
        assert "规则名称" in response.json()["detail"]
        assert "已存在" in response.json()["detail"]

    @patch('src.services.data_processing_service.admin_router.require_permission')
    @patch('src.services.data_processing_service.admin_router.get_data_processing_rule_service')
    @patch('src.services.data_processing_service.admin_router.get_db')
    def test_get_processing_rules_list(
        self, mock_get_db, mock_get_service, mock_require_permission,
        client, mock_user, mock_service, mock_db
    ):
        """测试获取规则列表"""
        # 配置模拟
        mock_require_permission.return_value = mock_user
        mock_get_service.return_value = mock_service
        mock_get_db.return_value = mock_db
        
        # 模拟服务返回
        mock_rules = [Mock(), Mock(), Mock()]
        for i, rule in enumerate(mock_rules):
            rule.id = i + 1
            rule.rule_name = f"rule_{i + 1}"
            rule.business_data_type = "flash_news"
            rule.priority = 5
        
        mock_service.get_rules.return_value = (mock_rules, 3)
        
        # 执行测试
        response = client.get("/admin/data-processing/rules?skip=0&limit=10")
        
        # 验证结果
        assert response.status_code == 200
        response_data = response.json()
        assert response_data["total"] == 3
        assert len(response_data["items"]) == 3
        assert response_data["page"] == 1
        assert response_data["size"] == 10

    @patch('src.services.data_processing_service.admin_router.require_permission')
    @patch('src.services.data_processing_service.admin_router.get_data_processing_rule_service')
    @patch('src.services.data_processing_service.admin_router.get_db')
    def test_get_processing_rules_with_filters(
        self, mock_get_db, mock_get_service, mock_require_permission,
        client, mock_user, mock_service, mock_db
    ):
        """测试使用过滤器获取规则列表"""
        # 配置模拟
        mock_require_permission.return_value = mock_user
        mock_get_service.return_value = mock_service
        mock_get_db.return_value = mock_db
        
        # 模拟服务返回
        mock_rules = [Mock()]
        mock_rules[0].id = 1
        mock_rules[0].rule_name = "flash_news_rule"
        mock_rules[0].business_data_type = "flash_news"
        mock_service.get_rules.return_value = (mock_rules, 1)
        
        # 执行测试
        response = client.get(
            "/admin/data-processing/rules"
            "?business_data_type=flash_news"
            "&is_active=true"
            "&priority_min=5"
            "&priority_max=10"
            "&search=flash"
        )
        
        # 验证结果
        assert response.status_code == 200
        response_data = response.json()
        assert response_data["total"] == 1
        
        # 验证服务被正确调用
        mock_service.get_rules.assert_called_once()
        call_args = mock_service.get_rules.call_args
        filters = call_args[1]['filters']
        assert filters.business_data_type == BusinessDataType.FLASH_NEWS
        assert filters.is_active is True
        assert filters.priority_min == 5
        assert filters.priority_max == 10
        assert filters.search == "flash"

    @patch('src.services.data_processing_service.admin_router.require_permission')
    @patch('src.services.data_processing_service.admin_router.get_data_processing_rule_service')
    @patch('src.services.data_processing_service.admin_router.get_db')
    def test_get_processing_rule_by_id_success(
        self, mock_get_db, mock_get_service, mock_require_permission,
        client, mock_user, mock_service, mock_db, sample_rule_response
    ):
        """测试根据ID获取规则成功"""
        # 配置模拟
        mock_require_permission.return_value = mock_user
        mock_get_service.return_value = mock_service
        mock_get_db.return_value = mock_db
        
        # 模拟服务返回
        mock_rule = Mock()
        for key, value in sample_rule_response.items():
            setattr(mock_rule, key, value)
        mock_service.get_rule_by_id.return_value = mock_rule
        
        # 执行测试
        response = client.get("/admin/data-processing/rules/1")
        
        # 验证结果
        assert response.status_code == 200
        response_data = response.json()
        assert response_data["id"] == 1
        assert response_data["rule_name"] == sample_rule_response["rule_name"]

    @patch('src.services.data_processing_service.admin_router.require_permission')
    @patch('src.services.data_processing_service.admin_router.get_data_processing_rule_service')
    @patch('src.services.data_processing_service.admin_router.get_db')
    def test_get_processing_rule_by_id_not_found(
        self, mock_get_db, mock_get_service, mock_require_permission,
        client, mock_user, mock_service, mock_db
    ):
        """测试根据ID获取规则未找到"""
        # 配置模拟
        mock_require_permission.return_value = mock_user
        mock_get_service.return_value = mock_service
        mock_get_db.return_value = mock_db
        
        # 模拟服务返回None
        mock_service.get_rule_by_id.return_value = None
        
        # 执行测试
        response = client.get("/admin/data-processing/rules/999")
        
        # 验证结果
        assert response.status_code == 404
        assert "不存在" in response.json()["detail"]

    @patch('src.services.data_processing_service.admin_router.require_permission')
    @patch('src.services.data_processing_service.admin_router.get_data_processing_rule_service')
    @patch('src.services.data_processing_service.admin_router.get_db')
    def test_update_processing_rule_success(
        self, mock_get_db, mock_get_service, mock_require_permission,
        client, mock_user, mock_service, mock_db, sample_rule_response
    ):
        """测试成功更新规则"""
        # 配置模拟
        mock_require_permission.return_value = mock_user
        mock_get_service.return_value = mock_service
        mock_get_db.return_value = mock_db
        
        # 模拟服务返回
        mock_rule = Mock()
        for key, value in sample_rule_response.items():
            setattr(mock_rule, key, value)
        mock_rule.rule_name = "updated_rule"  # 更新后的名称
        mock_service.update_rule.return_value = mock_rule
        
        # 准备更新数据
        update_data = {
            "rule_name": "updated_rule",
            "priority": 9,
            "is_active": False
        }
        
        # 执行测试
        response = client.put("/admin/data-processing/rules/1", json=update_data)
        
        # 验证结果
        assert response.status_code == 200
        response_data = response.json()
        assert response_data["rule_name"] == "updated_rule"

    @patch('src.services.data_processing_service.admin_router.require_permission')
    @patch('src.services.data_processing_service.admin_router.get_data_processing_rule_service')
    @patch('src.services.data_processing_service.admin_router.get_db')
    def test_delete_processing_rule_success(
        self, mock_get_db, mock_get_service, mock_require_permission,
        client, mock_user, mock_service, mock_db
    ):
        """测试成功删除规则"""
        # 配置模拟
        mock_require_permission.return_value = mock_user
        mock_get_service.return_value = mock_service
        mock_get_db.return_value = mock_db
        
        # 模拟服务返回
        mock_service.delete_rule.return_value = True
        
        # 执行测试
        response = client.delete("/admin/data-processing/rules/1")
        
        # 验证结果
        assert response.status_code == 204

    @patch('src.services.data_processing_service.admin_router.require_permission')
    @patch('src.services.data_processing_service.admin_router.get_data_processing_rule_service')
    @patch('src.services.data_processing_service.admin_router.get_db')
    def test_get_processing_rules_stats(
        self, mock_get_db, mock_get_service, mock_require_permission,
        client, mock_user, mock_service, mock_db
    ):
        """测试获取规则统计"""
        # 配置模拟
        mock_require_permission.return_value = mock_user
        mock_get_service.return_value = mock_service
        mock_get_db.return_value = mock_db
        
        # 模拟服务返回
        mock_stats = Mock()
        mock_stats.total_rules = 10
        mock_stats.active_rules = 8
        mock_stats.inactive_rules = 2
        mock_stats.rules_by_type = {"flash_news": 5, "news_article": 3, "research_report": 2}
        mock_stats.average_priority = 6.5
        mock_stats.total_executions = 1000
        mock_stats.total_successes = 950
        mock_stats.overall_success_rate = 95.0
        mock_service.get_rules_stats.return_value = mock_stats
        
        # 执行测试
        response = client.get("/admin/data-processing/rules/stats")
        
        # 验证结果
        assert response.status_code == 200
        response_data = response.json()
        assert response_data["total_rules"] == 10
        assert response_data["active_rules"] == 8
        assert response_data["overall_success_rate"] == 95.0

    @patch('src.services.data_processing_service.admin_router.require_permission')
    @patch('src.services.data_processing_service.admin_router.get_data_processing_rule_service')
    @patch('src.services.data_processing_service.admin_router.get_db')
    def test_get_active_rules_by_type(
        self, mock_get_db, mock_get_service, mock_require_permission,
        client, mock_user, mock_service, mock_db
    ):
        """测试获取指定类型的活跃规则"""
        # 配置模拟
        mock_require_permission.return_value = mock_user
        mock_get_service.return_value = mock_service
        mock_get_db.return_value = mock_db
        
        # 模拟服务返回
        mock_rules = [Mock(), Mock()]
        for i, rule in enumerate(mock_rules):
            rule.id = i + 1
            rule.rule_name = f"flash_rule_{i + 1}"
            rule.business_data_type = "flash_news"
            rule.is_active = True
        mock_service.get_active_rules_by_type.return_value = mock_rules
        
        # 执行测试
        response = client.get("/admin/data-processing/rules/business-type/flash_news/active")
        
        # 验证结果
        assert response.status_code == 200
        response_data = response.json()
        assert len(response_data) == 2
        assert all(rule["business_data_type"] == "flash_news" for rule in response_data)

    @patch('src.services.data_processing_service.admin_router.require_permission')
    @patch('src.services.data_processing_service.admin_router.get_data_processing_rule_service')
    @patch('src.services.data_processing_service.admin_router.get_db')
    def test_batch_update_rules_status(
        self, mock_get_db, mock_get_service, mock_require_permission,
        client, mock_user, mock_service, mock_db
    ):
        """测试批量更新规则状态"""
        # 配置模拟
        mock_require_permission.return_value = mock_user
        mock_get_service.return_value = mock_service
        mock_get_db.return_value = mock_db
        
        # 模拟服务返回
        mock_result = Mock()
        mock_result.success_count = 3
        mock_result.failed_count = 0
        mock_result.failed_ids = []
        mock_result.message = "批量更新完成，成功: 3，失败: 0"
        mock_service.batch_update_status.return_value = mock_result
        
        # 准备批量更新数据
        batch_data = {
            "rule_ids": [1, 2, 3],
            "is_active": False
        }
        
        # 执行测试
        response = client.patch("/admin/data-processing/rules/batch/status", json=batch_data)
        
        # 验证结果
        assert response.status_code == 200
        response_data = response.json()
        assert response_data["success_count"] == 3
        assert response_data["failed_count"] == 0

    @patch('src.services.data_processing_service.admin_router.require_permission')
    @patch('src.services.data_processing_service.admin_router.get_data_processing_rule_service')
    @patch('src.services.data_processing_service.admin_router.get_db')
    def test_batch_update_rules_priority(
        self, mock_get_db, mock_get_service, mock_require_permission,
        client, mock_user, mock_service, mock_db
    ):
        """测试批量更新规则优先级"""
        # 配置模拟
        mock_require_permission.return_value = mock_user
        mock_get_service.return_value = mock_service
        mock_get_db.return_value = mock_db
        
        # 模拟服务返回
        mock_result = Mock()
        mock_result.success_count = 2
        mock_result.failed_count = 0
        mock_result.failed_ids = []
        mock_result.message = "批量更新优先级完成，成功: 2，失败: 0"
        mock_service.batch_update_priority.return_value = mock_result
        
        # 准备批量更新数据
        batch_data = {
            "rule_ids": [1, 2],
            "priority": 9
        }
        
        # 执行测试
        response = client.patch("/admin/data-processing/rules/batch/priority", json=batch_data)
        
        # 验证结果
        assert response.status_code == 200
        response_data = response.json()
        assert response_data["success_count"] == 2
        assert response_data["failed_count"] == 0

    @patch('src.services.data_processing_service.admin_router.require_permission')
    @patch('src.services.data_processing_service.admin_router.get_data_processing_rule_service')
    @patch('src.services.data_processing_service.admin_router.get_db')
    def test_test_processing_rule(
        self, mock_get_db, mock_get_service, mock_require_permission,
        client, mock_user, mock_service, mock_db, sample_rule_response
    ):
        """测试规则测试功能"""
        # 配置模拟
        mock_require_permission.return_value = mock_user
        mock_get_service.return_value = mock_service
        mock_get_db.return_value = mock_db
        
        # 模拟服务返回
        mock_rule = Mock()
        for key, value in sample_rule_response.items():
            setattr(mock_rule, key, value)
        mock_service.get_rule_by_id.return_value = mock_rule
        
        # 准备测试数据
        test_data = {
            "content": "这是一条测试快讯",
            "time": "2024-01-01T12:00:00Z",
            "importance": "重要"
        }
        
        # 执行测试
        response = client.post("/admin/data-processing/rules/1/test", json=test_data)
        
        # 验证结果
        assert response.status_code == 200
        response_data = response.json()
        assert response_data["rule_id"] == 1
        assert response_data["test_result"] == "success"
        assert "mapped_fields" in response_data

    def test_invalid_business_data_type(self, client):
        """测试无效的业务数据类型"""
        rule_data = {
            "rule_name": "test_rule",
            "business_data_type": "invalid_type",  # 无效类型
            "field_mapping": {"title": "$.title"}
        }
        
        # 执行测试
        response = client.post("/admin/data-processing/rules", json=rule_data)
        
        # 验证结果
        assert response.status_code == 422  # 验证错误

    def test_invalid_priority(self, client):
        """测试无效的优先级"""
        rule_data = {
            "rule_name": "test_rule",
            "business_data_type": "flash_news",
            "field_mapping": {"title": "$.title"},
            "priority": 15  # 超出范围
        }
        
        # 执行测试
        response = client.post("/admin/data-processing/rules", json=rule_data)
        
        # 验证结果
        assert response.status_code == 422  # 验证错误

    def test_permission_required(self, client):
        """测试权限验证"""
        # 不提供权限时应该返回权限错误
        # 这里只是演示，实际的权限测试需要集成真实的权限系统
        rule_data = {
            "rule_name": "test_rule",
            "business_data_type": "flash_news",
            "field_mapping": {"title": "$.title"}
        }
        
        # 执行测试（无权限）
        with patch('src.services.data_processing_service.admin_router.require_permission') as mock_permission:
            mock_permission.side_effect = HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission required: data_processing.rule.create"
            )
            
            response = client.post("/admin/data-processing/rules", json=rule_data)
            
            # 验证结果
            assert response.status_code == 403 