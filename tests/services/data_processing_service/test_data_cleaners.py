"""
测试数据清洗和过滤服务
"""

import pytest
from datetime import datetime, timezone

from src.services.data_processing_service.data_cleaners import DataCleaner, ContentFilter


class TestDataCleaner:
    """测试数据清洗器"""

    @pytest.fixture(autouse=True)
    def setup(self):
        """设置测试环境"""
        self.cleaner = DataCleaner()

    def test_clean_text_basic(self):
        """测试基础文本清洗"""
        
        rules = {
            "remove_html": True,
            "normalize_whitespace": True,
            "max_length": 50
        }
        
        # 测试HTML标签去除
        html_text = "<p>这是一段<strong>重要</strong>的文本</p>"
        cleaned = self.cleaner._clean_text(html_text, rules)
        assert "<p>" not in cleaned
        assert "<strong>" not in cleaned
        assert "这是一段重要的文本" == cleaned
        
        # 测试空格规范化
        spaced_text = "这是   一段    有很多空格的   文本"
        cleaned = self.cleaner._clean_text(spaced_text, rules)
        assert "这是 一段 有很多空格的 文本" == cleaned
        
        # 测试长度限制
        long_text = "这是一段很长的文本" * 10
        cleaned = self.cleaner._clean_text(long_text, rules)
        assert len(cleaned) <= 53  # 50 + "..."

    def test_clean_title(self):
        """测试标题清洗"""
        
        config = {
            "title_cleaning": {
                "apply_patterns": True,
                "max_length": 100
            }
        }
        
        # 测试去除前缀标记
        titles = [
            "【快讯】央行宣布降准政策",
            "[财经]股市今日大涨",
            "快讯：重要经济数据发布",
            "财经:市场分析报告"
        ]
        
        expected = [
            "央行宣布降准政策",
            "股市今日大涨", 
            "重要经济数据发布",
            "市场分析报告"
        ]
        
        for title, expected_result in zip(titles, expected):
            cleaned = self.cleaner._clean_title(title, config)
            assert cleaned == expected_result

    def test_clean_content_text(self):
        """测试正文内容清洗"""
        
        config = {
            "content_cleaning": {
                "remove_html": True,
                "normalize_whitespace": True,
                "remove_ads": True
            }
        }
        
        # 测试HTML和广告内容去除
        content = """
        <p>这是正文内容</p>
        
        【广告】这是广告内容【/广告】
        
        <div>更多正文内容</div>
        
        [广告]推广内容[/广告]
        """
        
        cleaned = self.cleaner._clean_content_text(content, config)
        
        assert "<p>" not in cleaned
        assert "【广告】" not in cleaned
        assert "[广告]" not in cleaned
        assert "这是正文内容" in cleaned
        assert "更多正文内容" in cleaned
        assert "广告内容" not in cleaned
        assert "推广内容" not in cleaned

    async def test_process_time_fields(self):
        """测试时间字段处理"""
        
        data = {
            "publish_time": "2025-01-15 14:30:00",
            "update_time": "14:30",
            "invalid_time": "不是时间"
        }
        
        config = {
            "publish_time": {},
            "update_time": {},
            "invalid_time": {}
        }
        
        result = self.cleaner._process_time_fields(data, config)
        
        # 验证时间解析
        assert isinstance(result["publish_time"], datetime)
        assert result["publish_time"].year == 2025
        assert result["publish_time"].month == 1
        assert result["publish_time"].day == 15
        
        # 验证只有时间的情况
        assert isinstance(result["update_time"], datetime)
        assert result["update_time"].hour == 14
        assert result["update_time"].minute == 30
        
        # 验证无效时间
        assert result["invalid_time"] == "不是时间"  # 保持原值

    async def test_extract_metadata(self):
        """测试元数据提取"""
        
        data = {
            "content": "这是一段测试内容，用于计算字数。包含中文和English混合内容。",
            "source_url": "https://example.com/news/123"
        }
        
        config = {
            "calculate_word_count": True,
            "extract_domain": True
        }
        
        result = self.cleaner._extract_metadata(data, config)
        
        assert "word_count" in result
        assert result["word_count"] == len(data["content"])
        
        assert "url_domain" in result
        assert result["url_domain"] == "example.com"

    async def test_detect_urgency(self):
        """测试紧急程度检测"""
        
        # 测试紧急内容
        urgent_data = {
            "title": "突发：央行重大政策调整",
            "content": "央行紧急宣布重要政策"
        }
        urgency = self.cleaner._detect_urgency(urgent_data)
        assert urgency == 3  # 紧急
        
        # 测试重要内容
        important_data = {
            "title": "央行宣布降准政策",
            "content": "央行今日宣布降准措施"
        }
        urgency = self.cleaner._detect_urgency(important_data)
        assert urgency == 2  # 重要
        
        # 测试普通内容
        normal_data = {
            "title": "市场分析报告",
            "content": "今日市场表现平稳"
        }
        urgency = self.cleaner._detect_urgency(normal_data)
        assert urgency == 1  # 普通

    async def test_apply_category_mapping(self):
        """测试分类映射"""
        
        data = {
            "source_category": "monetary_policy",
            "source_level": "high"
        }
        
        mapping = {
            "target_category": {
                "source_field": "source_category",
                "mapping": {
                    "monetary_policy": "货币政策",
                    "market_analysis": "市场分析"
                }
            },
            "target_level": {
                "source_field": "source_level",
                "mapping": {
                    "high": "高",
                    "medium": "中",
                    "low": "低"
                }
            }
        }
        
        result = self.cleaner._apply_category_mapping(data, mapping)
        
        assert result["target_category"] == "货币政策"
        assert result["target_level"] == "高"

    async def test_validate_data(self):
        """测试数据验证"""
        
        config = {
            "required_fields": ["title", "content"],
            "field_lengths": {
                "title": 100,
                "content": 1000
            },
            "format_checks": {
                "email": r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"
            }
        }
        
        # 测试有效数据
        valid_data = {
            "title": "有效标题",
            "content": "有效内容",
            "email": "<EMAIL>"
        }
        
        result = await self.cleaner.validate_data(valid_data, config)
        assert result["valid"] is True
        assert len(result["errors"]) == 0
        
        # 测试无效数据
        invalid_data = {
            "title": "",  # 必填字段为空
            "content": "x" * 1500,  # 超过长度限制
            "email": "invalid-email"  # 格式不正确
        }
        
        result = await self.cleaner.validate_data(invalid_data, config)
        assert result["valid"] is False
        assert len(result["errors"]) >= 2  # 至少有必填字段和格式错误
        assert len(result["warnings"]) >= 1  # 至少有长度警告


class TestContentFilter:
    """测试内容过滤器"""

    @pytest.fixture(autouse=True)
    def setup(self):
        """设置测试环境"""
        self.filter = ContentFilter()

    async def test_is_spam_keywords(self):
        """测试垃圾关键词检测"""
        
        # 测试包含多个垃圾关键词的内容
        spam_data = {
            "title": "免费推广优惠活动",
            "content": "点击链接获取免费优惠，微信联系，立即下载"
        }
        
        is_spam = await self.filter.is_spam(spam_data)
        assert is_spam is True
        
        # 测试正常内容
        normal_data = {
            "title": "央行宣布降准政策",
            "content": "中国人民银行今日宣布降准措施，释放流动性"
        }
        
        is_spam = await self.filter.is_spam(normal_data)
        assert is_spam is False

    async def test_is_spam_patterns(self):
        """测试垃圾模式检测"""
        
        # 测试内容过短
        short_data = {
            "title": "短",
            "content": "很短"
        }
        
        is_spam = await self.filter.is_spam(short_data)
        assert is_spam is True
        
        # 测试重复字符
        repeat_data = {
            "title": "正常标题",
            "content": "aaaaaaaaaaaaaaaaaaaaaa"  # 重复字符
        }
        
        is_spam = await self.filter.is_spam(repeat_data)
        assert is_spam is True

    def test_calculate_repetition_ratio(self):
        """测试重复内容比例计算"""
        
        # 测试高重复内容
        high_repeat_text = "这是重复的句子。这是重复的句子。这是重复的句子。这是重复的句子。"
        ratio = self.filter._calculate_repetition_ratio(high_repeat_text)
        assert ratio > 0.5
        
        # 测试低重复内容
        low_repeat_text = "这是第一句。这是第二句。这是第三句。这是第四句。"
        ratio = self.filter._calculate_repetition_ratio(low_repeat_text)
        assert ratio < 0.5

    def test_is_meaningless_content(self):
        """测试无意义内容检测"""
        
        # 测试主要由数字和符号组成的内容
        meaningless_text = "123456789!@#$%^&*()_+{}|:<>?[]\\;'\",./"
        is_meaningless = self.filter._is_meaningless_content(meaningless_text)
        assert is_meaningless is True
        
        # 测试正常内容
        meaningful_text = "这是一段有意义的财经新闻内容，包含了重要的市场信息。"
        is_meaningless = self.filter._is_meaningless_content(meaningful_text)
        assert is_meaningless is False

    async def test_is_low_quality_content(self):
        """测试低质量内容检测"""
        
        # 测试内容过短
        short_data = {"title": "短", "content": "短内容"}
        is_low_quality = self.filter._is_low_quality_content(short_data)
        assert is_low_quality is True
        
        # 测试高重复内容
        repeat_data = {
            "title": "重复标题",
            "content": "重复的内容。" * 20
        }
        is_low_quality = self.filter._is_low_quality_content(repeat_data)
        assert is_low_quality is True
        
        # 测试正常内容
        normal_data = {
            "title": "央行政策分析",
            "content": "这是一段详细的央行政策分析内容，包含了多个方面的深入讨论和专业见解。"
        }
        is_low_quality = self.filter._is_low_quality_content(normal_data)
        assert is_low_quality is False

    async def test_comprehensive_spam_detection(self):
        """测试综合垃圾内容检测"""
        
        test_cases = [
            # 正常财经新闻
            {
                "data": {
                    "title": "央行宣布降准0.5个百分点",
                    "content": "中国人民银行今日宣布，决定于2025年2月1日降准0.5个百分点，释放长期资金约1万亿元。"
                },
                "expected": False
            },
            # 广告推广内容
            {
                "data": {
                    "title": "免费股票推荐，立即获取",
                    "content": "免费推荐优质股票，微信联系，点击链接下载APP，立即获取推广优惠。"
                },
                "expected": True
            },
            # 内容过短
            {
                "data": {
                    "title": "短",
                    "content": "短"
                },
                "expected": True
            },
            # 重复内容
            {
                "data": {
                    "title": "重复标题",
                    "content": "同样的内容。同样的内容。同样的内容。同样的内容。同样的内容。"
                },
                "expected": True
            }
        ]
        
        for case in test_cases:
            result = await self.filter.is_spam(case["data"])
            assert result == case["expected"], f"测试失败: {case['data']['title']}"
