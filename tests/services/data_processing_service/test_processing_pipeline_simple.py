"""
简单的处理管道API测试
验证路由配置和mock设置
"""

import pytest
from unittest.mock import Mock, patch
from fastapi.testclient import TestClient
from fastapi import FastAPI

from src.services.data_processing_service.admin_router import router

# 创建测试应用
app = FastAPI()
app.include_router(router, prefix="/admin/data-processing")

@pytest.fixture
def client():
    """测试客户端"""
    return TestClient(app)

def test_route_registration(client):
    """测试路由是否正确注册"""
    response = client.get("/admin/data-processing/processing-pipelines")
    # 应该返回403(权限错误)或其他错误，而不是404(未找到路由)
    assert response.status_code != 404
    print(f"响应状态码: {response.status_code}")
    print(f"响应内容: {response.text}")

@patch('src.services.data_processing_service.admin_router.require_permission')
@patch('src.services.data_processing_service.admin_router.get_processing_pipeline_service')
@patch('src.services.data_processing_service.admin_router.get_db')
def test_simple_pipeline_get(mock_get_db, mock_get_service, mock_require_permission, client):
    """简单测试获取管道列表"""
    # 配置mock
    mock_user = Mock()
    mock_user.id = 1
    mock_user.username = "test_admin"
    
    mock_require_permission.return_value = mock_user
    mock_get_db.return_value = Mock()
    
    mock_service = Mock()
    mock_service.get_pipelines.return_value = ([], 0)
    mock_get_service.return_value = mock_service
    
    # 发送请求
    response = client.get("/admin/data-processing/processing-pipelines")
    
    print(f"响应状态码: {response.status_code}")
    print(f"响应内容: {response.text}")
    
    # 应该返回200而不是403
    assert response.status_code == 200

if __name__ == "__main__":
    pytest.main([__file__, "-v", "-s"]) 