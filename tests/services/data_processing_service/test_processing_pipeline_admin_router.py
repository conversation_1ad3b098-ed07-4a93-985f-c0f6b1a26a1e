"""
处理管道管理API测试
测试数据处理服务的处理管道管理相关接口
"""

import pytest
from datetime import datetime
from decimal import Decimal
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session
from unittest.mock import MagicMock, patch

from src.services.data_processing_service.models import ProcessingPipeline
from src.services.data_processing_service.schemas import (
    ProcessingPipelineCreate,
    ProcessingPipelineUpdate,
    ProcessingPipelineResponse,
    ProcessingPipelineListResponse,
)
from src.services.data_processing_service.service import ProcessingPipelineService
from src.services.user_service.models import User


class TestProcessingPipelineAdminRouter:
    """处理管道管理接口测试类"""

    @pytest.fixture
    def mock_pipeline_service(self):
        """模拟处理管道服务"""
        service = MagicMock(spec=ProcessingPipelineService)
        return service

    @pytest.fixture
    def mock_admin_user(self):
        """模拟管理员用户"""
        user = MagicMock(spec=User)
        user.id = 1
        user.phone = "13800138000"
        user.username = "admin"
        user.is_admin = True
        user.is_active = True
        return user

    @pytest.fixture
    def sample_pipeline_data(self):
        """示例处理管道数据"""
        return {
            "pipeline_code": "test_pipeline",
            "pipeline_name": "测试处理管道",
            "description": "用于测试的处理管道",
            "processing_config": {
                "parsing_config": {"field_mapping": {"title": "$.title"}},
                "cleaning_config": {"remove_html": True},
                "transformation_config": {"time_format": "timestamp"},
                "validation_config": {"required_fields": ["title"]},
                "enrichment_config": {"extract_tags": True}
            },
            "validation_rules": {
                "required_fields": ["title", "content"],
                "min_content_length": 10
            },
            "output_format": "standard",
            "is_active": True,
            "version": "1.0"
        }

    @pytest.fixture
    def sample_pipeline_model(self, sample_pipeline_data):
        """示例处理管道模型"""
        pipeline = ProcessingPipeline(
            id=1,
            pipeline_code=sample_pipeline_data["pipeline_code"],
            pipeline_name=sample_pipeline_data["pipeline_name"],
            description=sample_pipeline_data["description"],
            processing_config=sample_pipeline_data["processing_config"],
            validation_rules=sample_pipeline_data["validation_rules"],
            output_format=sample_pipeline_data["output_format"],
            is_active=sample_pipeline_data["is_active"],
            version=sample_pipeline_data["version"],
            execution_count=10,
            success_count=8,
            total_processed_count=15,
            success_rate=Decimal("0.8000"),
            created_by="admin",
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        return pipeline

    # ==================== 创建处理管道测试 ====================

    def test_create_processing_pipeline_success(
        self, client: TestClient, mock_pipeline_service, mock_admin_user, sample_pipeline_data, sample_pipeline_model
    ):
        """测试成功创建处理管道"""
        # 配置模拟
        mock_pipeline_service.create_pipeline.return_value = sample_pipeline_model

        with patch("src.services.data_processing_service.dependencies.get_processing_pipeline_service", return_value=mock_pipeline_service):
            with patch("src.services.permission_service.dependencies.require_permission") as mock_require_permission:
                mock_require_permission.return_value = mock_admin_user

                # 发送请求
                response = client.post(
                    "/b/api/v1/admin/data-processing/processing-pipelines",
                    json=sample_pipeline_data,
                    headers={"Authorization": "Bearer test-token"}
                )

                # 验证响应
                assert response.status_code == 201
                data = response.json()
                assert data["pipeline_code"] == sample_pipeline_data["pipeline_code"]
                assert data["pipeline_name"] == sample_pipeline_data["pipeline_name"]
                assert data["is_active"] == sample_pipeline_data["is_active"]

                # 验证服务调用
                mock_pipeline_service.create_pipeline.assert_called_once()
                call_args = mock_pipeline_service.create_pipeline.call_args[0]
                pipeline_data = call_args[1]
                assert pipeline_data.pipeline_code == sample_pipeline_data["pipeline_code"]
                assert pipeline_data.created_by == mock_admin_user.phone

    def test_create_processing_pipeline_duplicate_code(
        self, client: TestClient, mock_pipeline_service, mock_admin_user, sample_pipeline_data
    ):
        """测试创建处理管道时管道代码重复"""
        # 配置模拟
        mock_pipeline_service.create_pipeline.side_effect = ValueError("Pipeline code 'test_pipeline' already exists")

        with patch("src.services.data_processing_service.dependencies.get_processing_pipeline_service", return_value=mock_pipeline_service):
            with patch("src.services.permission_service.dependencies.require_permission") as mock_require_permission:
                mock_require_permission.return_value = mock_admin_user

                # 发送请求
                response = client.post(
                    "/b/api/v1/admin/data-processing/processing-pipelines",
                    json=sample_pipeline_data,
                    headers={"Authorization": "Bearer test-token"}
                )

                # 验证响应
                assert response.status_code == 400
                assert "Pipeline code" in response.json()["detail"]

    def test_create_processing_pipeline_permission_denied(
        self, client: TestClient, sample_pipeline_data
    ):
        """测试创建处理管道权限被拒绝"""
        with patch("src.services.permission_service.dependencies.require_permission") as mock_require_permission:
            mock_require_permission.side_effect = Exception("Permission denied")

            # 发送请求
            response = client.post(
                "/b/api/v1/admin/data-processing/processing-pipelines",
                json=sample_pipeline_data,
                headers={"Authorization": "Bearer test-token"}
            )

            # 验证响应
            assert response.status_code == 403 or response.status_code == 401

    # ==================== 获取处理管道列表测试 ====================

    def test_get_processing_pipelines_success(
        self, client: TestClient, mock_pipeline_service, mock_admin_user, sample_pipeline_model
    ):
        """测试成功获取处理管道列表"""
        # 配置模拟
        pipelines = [sample_pipeline_model]
        total = 1
        mock_pipeline_service.get_pipelines.return_value = (pipelines, total)

        with patch("src.services.data_processing_service.dependencies.get_processing_pipeline_service", return_value=mock_pipeline_service):
            with patch("src.services.permission_service.dependencies.require_permission") as mock_require_permission:
                mock_require_permission.return_value = mock_admin_user

                # 发送请求
                response = client.get(
                    "/b/api/v1/admin/data-processing/processing-pipelines?skip=0&limit=10",
                    headers={"Authorization": "Bearer test-token"}
                )

                # 验证响应
                assert response.status_code == 200
                data = response.json()
                assert data["total"] == 1
                assert data["page"] == 1
                assert data["size"] == 10
                assert data["pages"] == 1
                assert len(data["items"]) == 1
                assert data["items"][0]["pipeline_code"] == sample_pipeline_model.pipeline_code

                # 验证服务调用
                mock_pipeline_service.get_pipelines.assert_called_once()

    def test_get_processing_pipelines_with_filters(
        self, client: TestClient, mock_pipeline_service, mock_admin_user, sample_pipeline_model
    ):
        """测试使用过滤条件获取处理管道列表"""
        # 配置模拟
        pipelines = [sample_pipeline_model]
        total = 1
        mock_pipeline_service.get_pipelines.return_value = (pipelines, total)

        with patch("src.services.data_processing_service.dependencies.get_processing_pipeline_service", return_value=mock_pipeline_service):
            with patch("src.services.permission_service.dependencies.require_permission") as mock_require_permission:
                mock_require_permission.return_value = mock_admin_user

                # 发送请求
                response = client.get(
                    "/b/api/v1/admin/data-processing/processing-pipelines?is_active=true&search=test",
                    headers={"Authorization": "Bearer test-token"}
                )

                # 验证响应
                assert response.status_code == 200

                # 验证服务调用参数
                call_kwargs = mock_pipeline_service.get_pipelines.call_args[1]
                assert call_kwargs["is_active"] is True
                assert call_kwargs["search"] == "test"

    # ==================== 获取处理管道详情测试 ====================

    def test_get_processing_pipeline_success(
        self, client: TestClient, mock_pipeline_service, mock_admin_user, sample_pipeline_model
    ):
        """测试成功获取处理管道详情"""
        # 配置模拟
        mock_pipeline_service.get_pipeline_by_id.return_value = sample_pipeline_model

        with patch("src.services.data_processing_service.dependencies.get_processing_pipeline_service", return_value=mock_pipeline_service):
            with patch("src.services.permission_service.dependencies.require_permission") as mock_require_permission:
                mock_require_permission.return_value = mock_admin_user

                # 发送请求
                response = client.get(
                    "/b/api/v1/admin/data-processing/processing-pipelines/1",
                    headers={"Authorization": "Bearer test-token"}
                )

                # 验证响应
                assert response.status_code == 200
                data = response.json()
                assert data["id"] == 1
                assert data["pipeline_code"] == sample_pipeline_model.pipeline_code
                assert data["pipeline_name"] == sample_pipeline_model.pipeline_name

                # 验证服务调用
                mock_pipeline_service.get_pipeline_by_id.assert_called_once_with(mock.ANY, 1)

    def test_get_processing_pipeline_not_found(
        self, client: TestClient, mock_pipeline_service, mock_admin_user
    ):
        """测试获取不存在的处理管道"""
        # 配置模拟
        mock_pipeline_service.get_pipeline_by_id.return_value = None

        with patch("src.services.data_processing_service.dependencies.get_processing_pipeline_service", return_value=mock_pipeline_service):
            with patch("src.services.permission_service.dependencies.require_permission") as mock_require_permission:
                mock_require_permission.return_value = mock_admin_user

                # 发送请求
                response = client.get(
                    "/b/api/v1/admin/data-processing/processing-pipelines/999",
                    headers={"Authorization": "Bearer test-token"}
                )

                # 验证响应
                assert response.status_code == 404
                assert "不存在" in response.json()["detail"]

    # ==================== 更新处理管道测试 ====================

    def test_update_processing_pipeline_success(
        self, client: TestClient, mock_pipeline_service, mock_admin_user, sample_pipeline_model
    ):
        """测试成功更新处理管道"""
        # 准备更新数据
        update_data = {
            "pipeline_name": "更新后的管道名称",
            "description": "更新后的描述",
            "is_active": False
        }

        # 更新模型数据
        updated_pipeline = sample_pipeline_model
        updated_pipeline.pipeline_name = update_data["pipeline_name"]
        updated_pipeline.description = update_data["description"]
        updated_pipeline.is_active = update_data["is_active"]

        # 配置模拟
        mock_pipeline_service.update_pipeline.return_value = updated_pipeline

        with patch("src.services.data_processing_service.dependencies.get_processing_pipeline_service", return_value=mock_pipeline_service):
            with patch("src.services.permission_service.dependencies.require_permission") as mock_require_permission:
                mock_require_permission.return_value = mock_admin_user

                # 发送请求
                response = client.put(
                    "/b/api/v1/admin/data-processing/processing-pipelines/1",
                    json=update_data,
                    headers={"Authorization": "Bearer test-token"}
                )

                # 验证响应
                assert response.status_code == 200
                data = response.json()
                assert data["pipeline_name"] == update_data["pipeline_name"]
                assert data["description"] == update_data["description"]
                assert data["is_active"] == update_data["is_active"]

                # 验证服务调用
                mock_pipeline_service.update_pipeline.assert_called_once()

    def test_update_processing_pipeline_not_found(
        self, client: TestClient, mock_pipeline_service, mock_admin_user
    ):
        """测试更新不存在的处理管道"""
        # 配置模拟
        mock_pipeline_service.update_pipeline.return_value = None

        update_data = {"pipeline_name": "更新后的名称"}

        with patch("src.services.data_processing_service.dependencies.get_processing_pipeline_service", return_value=mock_pipeline_service):
            with patch("src.services.permission_service.dependencies.require_permission") as mock_require_permission:
                mock_require_permission.return_value = mock_admin_user

                # 发送请求
                response = client.put(
                    "/b/api/v1/admin/data-processing/processing-pipelines/999",
                    json=update_data,
                    headers={"Authorization": "Bearer test-token"}
                )

                # 验证响应
                assert response.status_code == 404

    # ==================== 删除处理管道测试 ====================

    def test_delete_processing_pipeline_success(
        self, client: TestClient, mock_pipeline_service, mock_admin_user
    ):
        """测试成功删除处理管道"""
        # 配置模拟
        mock_pipeline_service.delete_pipeline.return_value = True

        with patch("src.services.data_processing_service.dependencies.get_processing_pipeline_service", return_value=mock_pipeline_service):
            with patch("src.services.permission_service.dependencies.require_permission") as mock_require_permission:
                mock_require_permission.return_value = mock_admin_user

                # 发送请求
                response = client.delete(
                    "/b/api/v1/admin/data-processing/processing-pipelines/1",
                    headers={"Authorization": "Bearer test-token"}
                )

                # 验证响应
                assert response.status_code == 204

                # 验证服务调用
                mock_pipeline_service.delete_pipeline.assert_called_once_with(mock.ANY, 1)

    def test_delete_processing_pipeline_not_found(
        self, client: TestClient, mock_pipeline_service, mock_admin_user
    ):
        """测试删除不存在的处理管道"""
        # 配置模拟
        mock_pipeline_service.delete_pipeline.return_value = False

        with patch("src.services.data_processing_service.dependencies.get_processing_pipeline_service", return_value=mock_pipeline_service):
            with patch("src.services.permission_service.dependencies.require_permission") as mock_require_permission:
                mock_require_permission.return_value = mock_admin_user

                # 发送请求
                response = client.delete(
                    "/b/api/v1/admin/data-processing/processing-pipelines/999",
                    headers={"Authorization": "Bearer test-token"}
                )

                # 验证响应
                assert response.status_code == 404

    def test_delete_processing_pipeline_in_use(
        self, client: TestClient, mock_pipeline_service, mock_admin_user
    ):
        """测试删除正在使用的处理管道"""
        # 配置模拟
        mock_pipeline_service.delete_pipeline.side_effect = ValueError("Cannot delete pipeline: 2 data sources are using it")

        with patch("src.services.data_processing_service.dependencies.get_processing_pipeline_service", return_value=mock_pipeline_service):
            with patch("src.services.permission_service.dependencies.require_permission") as mock_require_permission:
                mock_require_permission.return_value = mock_admin_user

                # 发送请求
                response = client.delete(
                    "/b/api/v1/admin/data-processing/processing-pipelines/1",
                    headers={"Authorization": "Bearer test-token"}
                )

                # 验证响应
                assert response.status_code == 400
                assert "data sources are using it" in response.json()["detail"]

    # ==================== 获取活跃处理管道测试 ====================

    def test_get_active_processing_pipelines_success(
        self, client: TestClient, mock_pipeline_service, mock_admin_user, sample_pipeline_model
    ):
        """测试成功获取活跃处理管道"""
        # 配置模拟
        pipelines = [sample_pipeline_model]
        mock_pipeline_service.get_active_pipelines.return_value = pipelines

        with patch("src.services.data_processing_service.dependencies.get_processing_pipeline_service", return_value=mock_pipeline_service):
            with patch("src.services.permission_service.dependencies.require_permission") as mock_require_permission:
                mock_require_permission.return_value = mock_admin_user

                # 发送请求
                response = client.get(
                    "/b/api/v1/admin/data-processing/processing-pipelines/active",
                    headers={"Authorization": "Bearer test-token"}
                )

                # 验证响应
                assert response.status_code == 200
                data = response.json()
                assert len(data) == 1
                assert data[0]["pipeline_code"] == sample_pipeline_model.pipeline_code
                assert data[0]["is_active"] is True

    # ==================== 获取处理管道统计测试 ====================

    def test_get_processing_pipeline_stats_success(
        self, client: TestClient, mock_pipeline_service, mock_admin_user
    ):
        """测试成功获取处理管道统计"""
        # 配置模拟
        stats = {
            "total_pipelines": 10,
            "active_pipelines": 8,
            "inactive_pipelines": 2,
            "avg_success_rate": 0.8500,
            "total_processed": 1500
        }
        mock_pipeline_service.get_pipeline_stats.return_value = stats

        with patch("src.services.data_processing_service.dependencies.get_processing_pipeline_service", return_value=mock_pipeline_service):
            with patch("src.services.permission_service.dependencies.require_permission") as mock_require_permission:
                mock_require_permission.return_value = mock_admin_user

                # 发送请求
                response = client.get(
                    "/b/api/v1/admin/data-processing/processing-pipelines/stats",
                    headers={"Authorization": "Bearer test-token"}
                )

                # 验证响应
                assert response.status_code == 200
                data = response.json()
                assert data["total_pipelines"] == 10
                assert data["active_pipelines"] == 8
                assert data["avg_success_rate"] == 0.8500
                assert data["total_processed"] == 1500

    # ==================== 权限验证测试 ====================

    def test_create_pipeline_requires_create_permission(self, client: TestClient, sample_pipeline_data):
        """测试创建处理管道需要创建权限"""
        with patch("src.services.permission_service.dependencies.require_permission") as mock_require_permission:
            mock_require_permission.side_effect = Exception("Permission denied")

            response = client.post(
                "/b/api/v1/admin/data-processing/processing-pipelines",
                json=sample_pipeline_data,
                headers={"Authorization": "Bearer test-token"}
            )

            # 验证权限检查被调用
            mock_require_permission.assert_called_with("data_processing.pipeline.create")

    def test_read_pipeline_requires_read_permission(self, client: TestClient):
        """测试读取处理管道需要读取权限"""
        with patch("src.services.permission_service.dependencies.require_permission") as mock_require_permission:
            mock_require_permission.side_effect = Exception("Permission denied")

            response = client.get(
                "/b/api/v1/admin/data-processing/processing-pipelines/1",
                headers={"Authorization": "Bearer test-token"}
            )

            # 验证权限检查被调用
            mock_require_permission.assert_called_with("data_processing.pipeline.read")

    def test_update_pipeline_requires_update_permission(self, client: TestClient):
        """测试更新处理管道需要更新权限"""
        update_data = {"pipeline_name": "新名称"}

        with patch("src.services.permission_service.dependencies.require_permission") as mock_require_permission:
            mock_require_permission.side_effect = Exception("Permission denied")

            response = client.put(
                "/b/api/v1/admin/data-processing/processing-pipelines/1",
                json=update_data,
                headers={"Authorization": "Bearer test-token"}
            )

            # 验证权限检查被调用
            mock_require_permission.assert_called_with("data_processing.pipeline.update")

    def test_delete_pipeline_requires_delete_permission(self, client: TestClient):
        """测试删除处理管道需要删除权限"""
        with patch("src.services.permission_service.dependencies.require_permission") as mock_require_permission:
            mock_require_permission.side_effect = Exception("Permission denied")

            response = client.delete(
                "/b/api/v1/admin/data-processing/processing-pipelines/1",
                headers={"Authorization": "Bearer test-token"}
            )

            # 验证权限检查被调用
            mock_require_permission.assert_called_with("data_processing.pipeline.delete")

    def test_stats_requires_stats_permission(self, client: TestClient):
        """测试获取统计需要统计权限"""
        with patch("src.services.permission_service.dependencies.require_permission") as mock_require_permission:
            mock_require_permission.side_effect = Exception("Permission denied")

            response = client.get(
                "/b/api/v1/admin/data-processing/processing-pipelines/stats",
                headers={"Authorization": "Bearer test-token"}
            )

            # 验证权限检查被调用
            mock_require_permission.assert_called_with("data_processing.stats.read")


if __name__ == "__main__":
    pytest.main([__file__]) 