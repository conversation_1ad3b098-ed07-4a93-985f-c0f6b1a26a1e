"""
数据处理规则管理B端API测试模块 - 正确版本
"""

from unittest.mock import Mock, patch, MagicMock
from typing import List

import pytest
from fastapi import FastAPI
from fastapi.testclient import TestClient

from src.services.data_processing_service.admin_router import router

# 创建测试应用
app = FastAPI()

# 模拟权限验证函数
def mock_current_user():
    user = Mock()
    user.id = 1
    user.username = "test_admin"
    user.is_admin = True
    return user

# 模拟数据库会话
def mock_get_db():
    return Mock()

# 模拟服务
def mock_get_service():
    return Mock()

# 添加路由到测试应用
app.include_router(router, prefix="/admin/data-processing")


@pytest.fixture
def client():
    """创建测试客户端"""
    return TestClient(app)


@pytest.fixture
def sample_rule_data():
    """示例规则数据"""
    return {
        "rule_name": "test_flash_news_rule",
        "business_data_type": "flash_news",
        "field_mapping": {
            "title": "$.content",
            "content": "$.content",
            "publish_time": "$.time"
        },
        "transformation_rules": {
            "publish_time": {"format": "timestamp", "timezone": "Asia/Shanghai"}
        },
        "validation_rules": {
            "required_fields": ["title", "content", "publish_time"],
            "max_content_length": 500
        },
        "source_criteria": {
            "source_names": ["金十数据快讯"]
        },
        "priority": 8,
        "is_active": True,
        "created_by": "test_admin"
    }


class TestDataProcessingRuleAPI:
    """数据处理规则API测试"""

    @patch('src.services.data_processing_service.admin_router.require_permission')
    @patch('src.services.data_processing_service.admin_router.get_data_processing_rule_service')
    @patch('src.services.data_processing_service.admin_router.get_db')
    def test_create_processing_rule_success(self, mock_get_db, mock_get_service, mock_require_permission, client, sample_rule_data):
        """测试成功创建数据处理规则"""
        # 配置权限模拟
        mock_require_permission.return_value = mock_current_user
        mock_get_db.return_value = Mock()
        
        # 配置服务模拟
        mock_service = Mock()
        mock_get_service.return_value = mock_service
        
        # 模拟创建成功的规则
        mock_rule = Mock()
        mock_rule.id = 1
        mock_rule.rule_name = sample_rule_data["rule_name"]
        mock_rule.business_data_type = sample_rule_data["business_data_type"]
        mock_rule.priority = sample_rule_data["priority"]
        mock_rule.is_active = True
        mock_rule.execution_count = 0
        mock_rule.success_count = 0
        mock_rule.created_by = sample_rule_data["created_by"]
        mock_rule.created_at = "2024-01-01T00:00:00Z"
        mock_rule.updated_at = "2024-01-01T00:00:00Z"
        mock_rule.field_mapping = sample_rule_data["field_mapping"]
        mock_rule.transformation_rules = sample_rule_data["transformation_rules"]
        mock_rule.validation_rules = sample_rule_data["validation_rules"]
        mock_rule.source_criteria = sample_rule_data["source_criteria"]
        
        mock_service.create_rule.return_value = mock_rule

        # 执行测试
        response = client.post("/admin/data-processing/rules", json=sample_rule_data)

        # 验证结果
        assert response.status_code == 201
        response_data = response.json()
        assert response_data["rule_name"] == sample_rule_data["rule_name"]
        assert response_data["business_data_type"] == sample_rule_data["business_data_type"]
        assert response_data["priority"] == sample_rule_data["priority"]
        
        # 验证服务调用
        mock_service.create_rule.assert_called_once()

    @patch('src.services.data_processing_service.admin_router.require_permission')
    @patch('src.services.data_processing_service.admin_router.get_data_processing_rule_service')
    @patch('src.services.data_processing_service.admin_router.get_db')
    def test_get_processing_rules_list(self, mock_get_db, mock_get_service, mock_require_permission, client):
        """测试获取规则列表"""
        # 配置权限模拟
        mock_require_permission.return_value = mock_current_user
        mock_get_db.return_value = Mock()
        
        # 配置服务模拟
        mock_service = Mock()
        mock_get_service.return_value = mock_service
        
        # 模拟服务返回
        mock_rules = []
        for i in range(3):
            rule = Mock()
            rule.id = i + 1
            rule.rule_name = f"rule_{i + 1}"
            rule.business_data_type = "flash_news"
            rule.priority = 5
            rule.is_active = True
            rule.execution_count = 0
            rule.success_count = 0
            rule.created_at = "2024-01-01T00:00:00Z"
            rule.updated_at = "2024-01-01T00:00:00Z"
            rule.field_mapping = {"title": "$.title"}
            rule.transformation_rules = {}
            rule.validation_rules = {}
            rule.source_criteria = {}
            rule.created_by = "admin"
            mock_rules.append(rule)

        mock_service.get_rules.return_value = (mock_rules, 3)

        # 执行测试
        response = client.get("/admin/data-processing/rules?skip=0&limit=10")

        # 验证结果
        assert response.status_code == 200
        response_data = response.json()
        assert response_data["total"] == 3
        assert len(response_data["items"]) == 3
        
        # 验证服务调用
        mock_service.get_rules.assert_called_once()

    @patch('src.services.data_processing_service.admin_router.require_permission')
    @patch('src.services.data_processing_service.admin_router.get_data_processing_rule_service')
    @patch('src.services.data_processing_service.admin_router.get_db')
    def test_get_processing_rule_by_id_success(self, mock_get_db, mock_get_service, mock_require_permission, client):
        """测试根据ID获取规则成功"""
        # 配置权限模拟
        mock_require_permission.return_value = mock_current_user
        mock_get_db.return_value = Mock()
        
        # 配置服务模拟
        mock_service = Mock()
        mock_get_service.return_value = mock_service
        
        # 模拟服务返回
        mock_rule = Mock()
        mock_rule.id = 1
        mock_rule.rule_name = "test_rule"
        mock_rule.business_data_type = "flash_news"
        mock_rule.priority = 5
        mock_rule.is_active = True
        mock_rule.execution_count = 0
        mock_rule.success_count = 0
        mock_rule.created_at = "2024-01-01T00:00:00Z"
        mock_rule.updated_at = "2024-01-01T00:00:00Z"
        mock_rule.field_mapping = {"title": "$.title"}
        mock_rule.transformation_rules = {}
        mock_rule.validation_rules = {}
        mock_rule.source_criteria = {}
        mock_rule.created_by = "admin"

        mock_service.get_rule_by_id.return_value = mock_rule

        # 执行测试
        response = client.get("/admin/data-processing/rules/1")

        # 验证结果
        assert response.status_code == 200
        response_data = response.json()
        assert response_data["id"] == 1
        assert response_data["rule_name"] == "test_rule"
        
        # 验证服务调用
        mock_service.get_rule_by_id.assert_called_once_with(1)

    @patch('src.services.data_processing_service.admin_router.require_permission')
    @patch('src.services.data_processing_service.admin_router.get_data_processing_rule_service')
    @patch('src.services.data_processing_service.admin_router.get_db')
    def test_get_processing_rule_by_id_not_found(self, mock_get_db, mock_get_service, mock_require_permission, client):
        """测试根据ID获取规则未找到"""
        # 配置权限模拟
        mock_require_permission.return_value = mock_current_user
        mock_get_db.return_value = Mock()
        
        # 配置服务模拟
        mock_service = Mock()
        mock_get_service.return_value = mock_service
        
        # 模拟服务返回None
        mock_service.get_rule_by_id.return_value = None

        # 执行测试
        response = client.get("/admin/data-processing/rules/999")

        # 验证结果
        assert response.status_code == 404
        
        # 验证服务调用
        mock_service.get_rule_by_id.assert_called_once_with(999)

    @patch('src.services.data_processing_service.admin_router.require_permission')
    @patch('src.services.data_processing_service.admin_router.get_data_processing_rule_service')
    @patch('src.services.data_processing_service.admin_router.get_db')
    def test_update_processing_rule_success(self, mock_get_db, mock_get_service, mock_require_permission, client):
        """测试成功更新规则"""
        # 配置权限模拟
        mock_require_permission.return_value = mock_current_user
        mock_get_db.return_value = Mock()
        
        # 配置服务模拟
        mock_service = Mock()
        mock_get_service.return_value = mock_service
        
        # 模拟更新后的规则
        mock_rule = Mock()
        mock_rule.id = 1
        mock_rule.rule_name = "updated_rule"
        mock_rule.business_data_type = "flash_news"
        mock_rule.priority = 9
        mock_rule.is_active = False
        mock_rule.execution_count = 0
        mock_rule.success_count = 0
        mock_rule.created_at = "2024-01-01T00:00:00Z"
        mock_rule.updated_at = "2024-01-01T00:00:00Z"
        mock_rule.field_mapping = {"title": "$.title"}
        mock_rule.transformation_rules = {}
        mock_rule.validation_rules = {}
        mock_rule.source_criteria = {}
        mock_rule.created_by = "admin"

        mock_service.update_rule.return_value = mock_rule

        # 准备更新数据
        update_data = {
            "rule_name": "updated_rule",
            "priority": 9,
            "is_active": False
        }

        # 执行测试
        response = client.put("/admin/data-processing/rules/1", json=update_data)

        # 验证结果
        assert response.status_code == 200
        response_data = response.json()
        assert response_data["rule_name"] == "updated_rule"
        assert response_data["priority"] == 9
        assert response_data["is_active"] == False
        
        # 验证服务调用
        mock_service.update_rule.assert_called_once()

    @patch('src.services.data_processing_service.admin_router.require_permission')
    @patch('src.services.data_processing_service.admin_router.get_data_processing_rule_service')
    @patch('src.services.data_processing_service.admin_router.get_db')
    def test_delete_processing_rule_success(self, mock_get_db, mock_get_service, mock_require_permission, client):
        """测试成功删除规则"""
        # 配置权限模拟
        mock_require_permission.return_value = mock_current_user
        mock_get_db.return_value = Mock()
        
        # 配置服务模拟
        mock_service = Mock()
        mock_get_service.return_value = mock_service
        
        # 模拟删除成功
        mock_service.delete_rule.return_value = True

        # 执行测试
        response = client.delete("/admin/data-processing/rules/1")

        # 验证结果
        assert response.status_code == 204
        
        # 验证服务调用
        mock_service.delete_rule.assert_called_once_with(1)

    @patch('src.services.data_processing_service.admin_router.require_permission')
    @patch('src.services.data_processing_service.admin_router.get_data_processing_rule_service')
    @patch('src.services.data_processing_service.admin_router.get_db')
    def test_get_processing_rules_stats(self, mock_get_db, mock_get_service, mock_require_permission, client):
        """测试获取规则统计"""
        # 配置权限模拟
        mock_require_permission.return_value = mock_current_user
        mock_get_db.return_value = Mock()
        
        # 配置服务模拟
        mock_service = Mock()
        mock_get_service.return_value = mock_service
        
        # 模拟统计数据
        mock_stats = Mock()
        mock_stats.total_rules = 10
        mock_stats.active_rules = 8
        mock_stats.inactive_rules = 2
        mock_stats.rules_by_type = {"flash_news": 5, "news_article": 3, "research_report": 2}
        mock_stats.average_priority = 6.5
        mock_stats.total_executions = 1000
        mock_stats.total_successes = 950
        mock_stats.overall_success_rate = 95.0
        mock_service.get_rules_stats.return_value = mock_stats

        # 执行测试
        response = client.get("/admin/data-processing/rules/stats")

        # 验证结果
        assert response.status_code == 200
        response_data = response.json()
        assert response_data["total_rules"] == 10
        assert response_data["active_rules"] == 8
        assert response_data["overall_success_rate"] == 95.0
        
        # 验证服务调用
        mock_service.get_rules_stats.assert_called_once()

    @patch('src.services.data_processing_service.admin_router.require_permission')
    @patch('src.services.data_processing_service.admin_router.get_data_processing_rule_service')
    @patch('src.services.data_processing_service.admin_router.get_db')
    def test_batch_update_rules_status(self, mock_get_db, mock_get_service, mock_require_permission, client):
        """测试批量更新规则状态"""
        # 配置权限模拟
        mock_require_permission.return_value = mock_current_user
        mock_get_db.return_value = Mock()
        
        # 配置服务模拟
        mock_service = Mock()
        mock_get_service.return_value = mock_service
        
        # 模拟批量更新结果
        mock_result = Mock()
        mock_result.success_count = 3
        mock_result.failed_count = 0
        mock_result.failed_ids = []
        mock_result.message = "批量更新完成"
        mock_service.batch_update_status.return_value = mock_result

        # 准备批量更新数据
        batch_data = {
            "rule_ids": [1, 2, 3],
            "is_active": False
        }

        # 执行测试
        response = client.patch("/admin/data-processing/rules/batch/status", json=batch_data)

        # 验证结果
        assert response.status_code == 200
        response_data = response.json()
        assert response_data["success_count"] == 3
        assert response_data["failed_count"] == 0
        
        # 验证服务调用
        mock_service.batch_update_status.assert_called_once()

    def test_create_rule_with_invalid_data(self, client):
        """测试使用无效数据创建规则"""
        with patch('src.services.data_processing_service.admin_router.require_permission') as mock_perm:
            mock_perm.return_value = mock_current_user
            
            invalid_data = {
                "rule_name": "",  # 空名称
                "business_data_type": "flash_news",
                "field_mapping": {}  # 空映射
            }

            # 执行测试
            response = client.post("/admin/data-processing/rules", json=invalid_data)

            # 验证结果
            assert response.status_code == 422  # 验证错误 