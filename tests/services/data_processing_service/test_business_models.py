"""
测试业务数据模型
"""

import pytest
from datetime import datetime, timezone
from decimal import Decimal
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from src.core.database import Base
from src.services.data_processing_service.models import (
    FlashNews, NewsArticle, ResearchReport, 
    EconomicIndicatorBase, EconomicIndicatorData,
    AITagMatches, UnifiedContentTags, UnifiedContentClassifications,
    DataProcessingStatus, ProcessingStage, ProcessingResult
)
from src.services.data_collection_service.models import (
    RawDataRecord, DataSource, BusinessDataType
)
from src.services.tag_classification_service.models import (
    Tag, TagClassification, ClassificationDimension, ClassificationValue
)


@pytest.fixture
def db_session():
    """创建测试数据库会话"""
    engine = create_engine("sqlite:///:memory:")
    Base.metadata.create_all(engine)
    SessionLocal = sessionmaker(bind=engine)
    session = SessionLocal()
    yield session
    session.close()


@pytest.fixture
def sample_raw_data_record(db_session):
    """创建示例原始数据记录"""
    # 先创建数据源
    data_source = DataSource(
        name="测试数据源",
        collection_method="api_json",
        content_category="financial_news",
        business_data_type=BusinessDataType.FLASH_NEWS,
        base_url="https://test.com"
    )
    db_session.add(data_source)
    db_session.commit()
    
    # 创建原始数据记录
    record = RawDataRecord(
        source_id=data_source.id,
        source_url="https://test.com/news/123",
        url_hash="test_hash_123",
        url_domain="test.com",
        content_hash="content_hash_123",
        title="测试新闻标题",
        author="测试作者",
        publish_time=datetime.now(timezone.utc),
        mongodb_id="507f1f77bcf86cd799439011",
        content_type="financial_news",
        processing_status="pending"
    )
    db_session.add(record)
    db_session.commit()
    return record


class TestFlashNews:
    """测试快讯模型"""

    def test_create_flash_news(self, db_session, sample_raw_data_record):
        """测试创建快讯记录"""
        flash_news = FlashNews(
            raw_data_id=sample_raw_data_record.id,
            title="央行宣布降准0.5个百分点",
            content="中国人民银行今日宣布，决定于2025年2月1日降准0.5个百分点",
            summary="央行降准释放流动性",
            publish_time=datetime.now(timezone.utc),
            urgency_level=3,
            importance_score=Decimal("0.95"),
            impact_scope="domestic",
            news_category="monetary_policy",

            status="published",
            is_breaking=True
        )
        
        db_session.add(flash_news)
        db_session.commit()
        
        # 验证数据
        assert flash_news.id is not None
        assert flash_news.title == "央行宣布降准0.5个百分点"
        assert flash_news.urgency_level == 3
        assert flash_news.importance_score == Decimal("0.95")
        assert flash_news.is_breaking is True
        # 相关标签现在通过统一标签系统管理，不再直接存储在字段中

    def test_flash_news_constraints(self, db_session, sample_raw_data_record):
        """测试快讯约束条件"""
        # 测试紧急度级别约束
        with pytest.raises(Exception):  # 应该抛出约束违反异常
            flash_news = FlashNews(
                raw_data_id=sample_raw_data_record.id,
                title="测试标题",
                content="测试内容",
                publish_time=datetime.now(timezone.utc),
                urgency_level=5  # 超出范围 1-3
            )
            db_session.add(flash_news)
            db_session.commit()


class TestNewsArticle:
    """测试新闻文章模型"""

    def test_create_news_article(self, db_session, sample_raw_data_record):
        """测试创建新闻文章记录"""
        article = NewsArticle(
            raw_data_id=sample_raw_data_record.id,
            title="央行降准对股市影响分析",
            subtitle="专家解读货币政策调整对资本市场的深远影响",
            abstract="央行此次降准将释放大量流动性，预计将对股市产生积极影响",
            content="详细分析内容...",
            author="张经济",
            source_media="新浪财经",
            publish_time=datetime.now(timezone.utc),
            word_count=1580,
            content_quality_score=Decimal("0.88"),
            primary_category="market_analysis",
            secondary_categories=["monetary_policy", "stock_market"],
            mentioned_companies=["中国人民银行", "上海证券交易所"],
            mentioned_people=["易纲", "周小川"],

            status="published",
            is_featured=True
        )
        
        db_session.add(article)
        db_session.commit()
        
        # 验证数据
        assert article.id is not None
        assert article.title == "央行降准对股市影响分析"
        assert article.word_count == 1580
        assert article.is_featured is True
        assert "monetary_policy" in article.secondary_categories


class TestResearchReport:
    """测试研究报告模型"""

    def test_create_research_report(self, db_session, sample_raw_data_record):
        """测试创建研究报告记录"""
        report = ResearchReport(
            raw_data_id=sample_raw_data_record.id,
            title="平安银行2025年投资价值分析",
            report_type="company_report",
            executive_summary="平安银行基本面稳健，建议买入",
            institution_name="中信证券",
            institution_code="CITIC",
            analyst_name="李分析",
            analyst_team=["李分析", "王研究"],
            target_stock_code="000001.SZ",
            target_stock_name="平安银行",
            target_industry="银行业",
            investment_rating="买入",
            target_price=Decimal("25.50"),
            target_price_currency="CNY",
            publish_time=datetime.now(timezone.utc),
            pe_valuation=Decimal("8.5"),
            pb_valuation=Decimal("1.2"),
            key_topics=["数字化转型", "零售银行", "风险管理"],
            status="published"
        )
        
        db_session.add(report)
        db_session.commit()
        
        # 验证数据
        assert report.id is not None
        assert report.report_type == "company_report"
        assert report.target_stock_code == "000001.SZ"
        assert report.investment_rating == "买入"
        assert report.target_price == Decimal("25.50")


class TestEconomicIndicator:
    """测试经济指标模型"""

    def test_create_economic_indicator_base(self, db_session):
        """测试创建经济指标基础信息"""
        indicator_base = EconomicIndicatorBase(
            indicator_name="当周初请失业金人数",
            indicator_paraphrase="统计过去一周初次申请领取失业金的人数",
            country="美国",
            release_institution="美国劳工部",
            release_frequency="每周四公布",
            importance_star=4,
            indicator_code="US_INITIAL_JOBLESS_CLAIMS",
            indicator_name_en="Initial Jobless Claims",
            category="就业数据",
            data_unit="万人"
        )
        
        db_session.add(indicator_base)
        db_session.commit()
        
        assert indicator_base.id is not None
        assert indicator_base.country == "美国"
        assert indicator_base.importance_star == 4

    def test_create_economic_indicator_data(self, db_session, sample_raw_data_record):
        """测试创建经济指标数据记录"""
        # 先创建基础指标
        indicator_base = EconomicIndicatorBase(
            indicator_name="当周初请失业金人数",
            country="美国",
            release_institution="美国劳工部",
            importance_star=4
        )
        db_session.add(indicator_base)
        db_session.commit()
        
        # 创建指标数据
        indicator_data = EconomicIndicatorData(
            indicator_id=indicator_base.id,
            raw_data_id=sample_raw_data_record.id,
            time_period="至7月5日",
            current_release_time=datetime.now(timezone.utc),
            previous_value=Decimal("23.3"),
            consensus_value=Decimal("23.5"),
            actual_value=Decimal("22.7"),
            unit="万人",
            data_type="absolute",
            surprise_index=Decimal("-0.8"),
            mom_change_pct=Decimal("-2.6"),
            status="published"
        )
        
        db_session.add(indicator_data)
        db_session.commit()
        
        # 验证数据
        assert indicator_data.id is not None
        assert indicator_data.actual_value == Decimal("22.7")
        assert indicator_data.surprise_index == Decimal("-0.8")


class TestUnifiedContentTags:
    """测试统一内容标签关联模型"""

    def test_create_content_tag_association(self, db_session, sample_raw_data_record):
        """测试创建内容标签关联"""
        # 先创建标签分类和标签
        tag_classification = TagClassification(
            classification_code="general.test",
            classification_name="通用测试标签",
            classification_type="type",
            domain="general",
            level=1,
            path="general.test"
        )
        db_session.add(tag_classification)
        db_session.commit()

        tag = Tag(
            tag_name="央行",
            tag_code="central_bank",
            tag_slug="central-bank",
            classification_id=tag_classification.id,
            base_weight=Decimal("1.0")
        )
        db_session.add(tag)
        db_session.commit()
        
        # 创建快讯
        flash_news = FlashNews(
            raw_data_id=sample_raw_data_record.id,
            title="央行政策",
            content="央行相关内容",
            publish_time=datetime.now(timezone.utc)
        )
        db_session.add(flash_news)
        db_session.commit()
        
        # 创建内容标签关联
        content_tag = UnifiedContentTags(
            content_type=BusinessDataType.FLASH_NEWS,
            content_id=flash_news.id,
            tag_id=tag.id,
            relevance_score=Decimal("0.95"),
            confidence_score=Decimal("0.90"),
            importance_score=Decimal("0.85"),
            source="ai",
            mention_count=2
        )
        
        db_session.add(content_tag)
        db_session.commit()
        
        # 验证数据
        assert content_tag.id is not None
        assert content_tag.content_type == BusinessDataType.FLASH_NEWS
        assert content_tag.relevance_score == Decimal("0.95")


class TestDataProcessingStatus:
    """测试数据处理状态模型"""

    def test_create_processing_status(self, db_session, sample_raw_data_record):
        """测试创建处理状态记录"""
        status = DataProcessingStatus(
            raw_data_id=sample_raw_data_record.id,
            target_business_type=BusinessDataType.FLASH_NEWS,
            processing_stage=ProcessingStage.EXTRACTING,
            progress_percentage=25,
            current_step="数据提取中",
            started_at=datetime.now(timezone.utc),
            data_quality_score=Decimal("0.85"),
            tag_extraction_count=5
        )
        
        db_session.add(status)
        db_session.commit()
        
        # 验证数据
        assert status.id is not None
        assert status.processing_stage == ProcessingStage.EXTRACTING
        assert status.progress_percentage == 25
        assert status.tag_extraction_count == 5

    def test_processing_status_completion(self, db_session, sample_raw_data_record):
        """测试处理状态完成"""
        status = DataProcessingStatus(
            raw_data_id=sample_raw_data_record.id,
            target_business_type=BusinessDataType.FLASH_NEWS,
            processing_stage=ProcessingStage.COMPLETED,
            progress_percentage=100,
            processing_result=ProcessingResult.SUCCESS,
            completed_at=datetime.now(timezone.utc),
            target_table_id=123,
            target_table_name="flash_news"
        )
        
        db_session.add(status)
        db_session.commit()
        
        # 验证数据
        assert status.processing_result == ProcessingResult.SUCCESS
        assert status.target_table_name == "flash_news"
        assert status.completed_at is not None
