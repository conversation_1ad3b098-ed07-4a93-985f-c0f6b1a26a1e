"""
测试data_processing_service与TagClassification的集成
"""

import pytest
from datetime import datetime, timezone
from decimal import Decimal
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from src.core.database import Base
from src.services.data_processing_service.models import (
    FlashNews, UnifiedContentTags, AITagMatches
)
from src.services.data_collection_service.models import (
    RawDataRecord, DataSource, BusinessDataType
)
from src.services.tag_classification_service.models import (
    Tag, TagClassification
)


@pytest.fixture
def db_session():
    """创建测试数据库会话"""
    engine = create_engine("sqlite:///:memory:", echo=False)
    Base.metadata.create_all(engine)
    
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    session = SessionLocal()
    
    yield session
    
    session.close()


@pytest.fixture
def sample_tag_classification(db_session):
    """创建示例标签分类"""
    classification = TagClassification(
        classification_code="financial.news",
        classification_name="财经新闻",
        classification_type="category",
        domain="financial",
        level=1,
        path="financial.news",
        description="财经新闻相关标签分类"
    )
    db_session.add(classification)
    db_session.commit()
    db_session.refresh(classification)
    return classification


@pytest.fixture
def sample_tag(db_session, sample_tag_classification):
    """创建示例标签"""
    tag = Tag(
        tag_name="央行政策",
        tag_code="central_bank_policy",
        tag_slug="central-bank-policy",
        classification_id=sample_tag_classification.id,
        description="央行政策相关标签",
        base_weight=Decimal("0.8")
    )
    db_session.add(tag)
    db_session.commit()
    db_session.refresh(tag)
    return tag


@pytest.fixture
def sample_raw_data_record(db_session):
    """创建示例原始数据记录"""
    # 先创建数据源
    data_source = DataSource(
        name="测试数据源",
        collection_method="api_json",
        content_category="financial_news",
        business_data_type=BusinessDataType.FLASH_NEWS
    )
    db_session.add(data_source)
    db_session.commit()
    
    # 创建原始数据记录
    raw_data = RawDataRecord(
        source_id=data_source.id,
        source_url="http://test.com/news/1",
        url_hash="test_url_hash_123",
        content_hash="test_hash_123",
        title="央行政策",
        mongodb_id="507f1f77bcf86cd799439011"  # 示例MongoDB ObjectId
    )
    db_session.add(raw_data)
    db_session.commit()
    db_session.refresh(raw_data)
    return raw_data


class TestTagClassificationIntegration:
    """测试TagClassification与data_processing_service的集成"""

    def test_create_flash_news_with_tag_classification(self, db_session, sample_raw_data_record, sample_tag):
        """测试创建快讯并关联TagClassification标签"""
        # 创建快讯
        flash_news = FlashNews(
            raw_data_id=sample_raw_data_record.id,
            title="央行政策新闻",
            content="央行发布了新的货币政策",
            publish_time=datetime.now(timezone.utc),
            importance_score=Decimal("0.8")
        )
        db_session.add(flash_news)
        db_session.commit()
        db_session.refresh(flash_news)

        # 创建标签关联
        content_tag = UnifiedContentTags(
            content_type=BusinessDataType.FLASH_NEWS,
            content_id=flash_news.id,
            tag_id=sample_tag.id,
            relevance_score=Decimal("0.9"),
            confidence_score=Decimal("0.8"),
            importance_score=Decimal("0.7"),
            source="manual"
        )
        db_session.add(content_tag)
        db_session.commit()

        # 验证关联创建成功
        assert content_tag.id is not None
        assert content_tag.content_type == BusinessDataType.FLASH_NEWS
        assert content_tag.content_id == flash_news.id
        assert content_tag.tag_id == sample_tag.id

        # 验证可以通过关联查询到标签和分类信息
        result = db_session.query(
            UnifiedContentTags,
            Tag.tag_name,
            Tag.classification_id,
            TagClassification.classification_name
        ).join(
            Tag, UnifiedContentTags.tag_id == Tag.id
        ).join(
            TagClassification, Tag.classification_id == TagClassification.id
        ).filter(
            UnifiedContentTags.content_id == flash_news.id
        ).first()

        assert result is not None
        content_tag_result, tag_name, classification_id, classification_name = result
        assert tag_name == "央行政策"
        assert classification_name == "财经新闻"

    def test_ai_tag_match_with_classification(self, db_session, sample_tag):
        """测试AI标签匹配与TagClassification的集成"""
        # 创建AI标签匹配记录
        ai_tag_match = AITagMatches(
            ai_tag_text="央行",
            standard_tag_id=sample_tag.id,
            confidence_score=Decimal("0.95"),
            match_method="exact"
        )
        db_session.add(ai_tag_match)
        db_session.commit()

        # 验证匹配记录创建成功
        assert ai_tag_match.id is not None
        assert ai_tag_match.ai_tag_text == "央行"
        assert ai_tag_match.standard_tag_id == sample_tag.id
        assert ai_tag_match.confidence_score == Decimal("0.95")

        # 验证可以通过匹配记录查询到标签分类信息
        result = db_session.query(
            AITagMatches,
            Tag.tag_name,
            TagClassification.classification_name,
            TagClassification.domain
        ).join(
            Tag, AITagMatches.standard_tag_id == Tag.id
        ).join(
            TagClassification, Tag.classification_id == TagClassification.id
        ).filter(
            AITagMatches.ai_tag_text == "央行"
        ).first()

        assert result is not None
        match_result, tag_name, classification_name, domain = result
        assert tag_name == "央行政策"
        assert classification_name == "财经新闻"
        assert domain == "financial"

    def test_tag_classification_hierarchy(self, db_session):
        """测试TagClassification层次结构在data_processing中的应用"""
        # 创建父分类
        parent_classification = TagClassification(
            classification_code="financial",
            classification_name="金融",
            classification_type="domain",
            level=1,
            path="financial"
        )
        db_session.add(parent_classification)
        db_session.commit()

        # 创建子分类
        child_classification = TagClassification(
            classification_code="monetary_policy",
            classification_name="货币政策",
            classification_type="category",
            domain="financial",
            parent_id=parent_classification.id,
            level=2,
            path="financial.monetary_policy"
        )
        db_session.add(child_classification)
        db_session.commit()

        # 创建标签
        tag = Tag(
            tag_name="降准",
            tag_code="rrr_cut",
            tag_slug="rrr-cut",
            classification_id=child_classification.id,
            description="存款准备金率下调"
        )
        db_session.add(tag)
        db_session.commit()

        # 验证层次结构查询
        result = db_session.query(
            Tag.tag_name,
            TagClassification.classification_name.label("child_name"),
            TagClassification.level.label("child_level"),
            TagClassification.path.label("child_path")
        ).join(
            TagClassification, Tag.classification_id == TagClassification.id
        ).filter(
            Tag.tag_code == "rrr_cut"
        ).first()

        assert result is not None
        assert result.child_name == "货币政策"
        assert result.child_level == 2
        assert result.child_path == "financial.monetary_policy"

    def test_business_rules_integration(self, db_session):
        """测试业务规则在TagClassification中的应用"""
        # 创建带业务规则的分类
        classification = TagClassification(
            classification_code="urgent.news",
            classification_name="紧急新闻",
            classification_type="type",
            domain="news",
            level=1,
            path="urgent.news",
            business_rules='{"min_importance": 0.8, "auto_notify": true}'
        )
        db_session.add(classification)
        db_session.commit()

        # 创建标签
        tag = Tag(
            tag_name="突发事件",
            tag_code="breaking_news",
            tag_slug="breaking-news",
            classification_id=classification.id
        )
        db_session.add(tag)
        db_session.commit()

        # 验证业务规则可以被查询和使用
        result = db_session.query(
            TagClassification.business_rules
        ).filter(
            TagClassification.id == classification.id
        ).scalar()

        assert result is not None
        assert "min_importance" in result
        assert "auto_notify" in result
