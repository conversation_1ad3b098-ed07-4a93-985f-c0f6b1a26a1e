"""
处理管道管理API测试
测试数据处理服务的处理管道管理相关接口
参考现有data_processing_service测试模式
"""

import pytest
from unittest.mock import Mock, patch
from fastapi.testclient import TestClient
from fastapi import FastAPI

from src.services.data_processing_service.admin_router import router
from src.services.data_processing_service.models import ProcessingPipeline
from src.services.data_processing_service.schemas import (
    ProcessingPipelineCreate,
    ProcessingPipelineUpdate,
    ProcessingPipelineResponse
)
from decimal import Decimal
from datetime import datetime


# 创建测试应用
app = FastAPI()
app.include_router(router, prefix="/admin/data-processing")

@pytest.fixture
def client():
    """测试客户端"""
    return TestClient(app)

@pytest.fixture
def mock_user():
    """模拟用户"""
    user = Mock()
    user.id = 1
    user.username = "test_admin"
    user.phone = "13800138000"
    user.is_admin = True
    return user

@pytest.fixture
def mock_pipeline_service():
    """模拟处理管道服务"""
    return Mock()

@pytest.fixture
def mock_db():
    """模拟数据库会话"""
    return Mock()

@pytest.fixture
def sample_pipeline_data():
    """示例处理管道数据"""
    return {
        "pipeline_code": "test_pipeline",
        "pipeline_name": "测试处理管道",
        "description": "用于测试的处理管道",
        "processing_config": {
            "parsing_config": {"field_mapping": {"title": "$.title"}},
            "cleaning_config": {"remove_html": True},
            "transformation_config": {"time_format": "timestamp"},
            "validation_config": {"required_fields": ["title"]},
            "enrichment_config": {"extract_tags": True}
        },
        "validation_rules": {
            "required_fields": ["title", "content"],
            "min_content_length": 10
        },
        "output_format": "standard",
        "is_active": True,
        "version": "1.0"
    }

@pytest.fixture
def sample_pipeline_model(sample_pipeline_data):
    """示例处理管道模型"""
    pipeline = ProcessingPipeline(
        id=1,
        pipeline_code=sample_pipeline_data["pipeline_code"],
        pipeline_name=sample_pipeline_data["pipeline_name"],
        description=sample_pipeline_data["description"],
        processing_config=sample_pipeline_data["processing_config"],
        validation_rules=sample_pipeline_data["validation_rules"],
        output_format=sample_pipeline_data["output_format"],
        is_active=sample_pipeline_data["is_active"],
        version=sample_pipeline_data["version"],
        execution_count=10,
        success_count=8,
        total_processed_count=15,
        success_rate=Decimal("0.8000"),
        created_by="admin",
        created_at=datetime.now(),
        updated_at=datetime.now()
    )
    return pipeline

# 全局mock数据
mock_db = Mock()
mock_current_user = Mock()
mock_current_user.id = 1
mock_current_user.username = "test_admin"
mock_current_user.phone = "13800138000"
mock_current_user.is_admin = True


class TestProcessingPipelineAdminRouter:
    """处理管道管理接口测试类"""

    @patch('src.services.data_processing_service.admin_router.require_permission')
    @patch('src.services.data_processing_service.admin_router.get_processing_pipeline_service')
    @patch('src.services.data_processing_service.admin_router.get_db')
    def test_create_processing_pipeline_success(
        self, mock_get_db, mock_get_service, mock_require_permission, 
        client, sample_pipeline_data, sample_pipeline_model
    ):
        """测试成功创建处理管道"""
        # 配置权限模拟
        mock_require_permission.return_value = mock_current_user
        mock_get_db.return_value = mock_db
        
        # 配置服务模拟
        mock_service = Mock()
        mock_service.create_pipeline.return_value = sample_pipeline_model
        mock_get_service.return_value = mock_service
        
        # 发送请求
        response = client.post(
            "/admin/data-processing/pipelines",
            json=sample_pipeline_data
        )
        
        # 验证响应
        assert response.status_code == 201
        data = response.json()
        assert data["pipeline_code"] == sample_pipeline_data["pipeline_code"]
        assert data["pipeline_name"] == sample_pipeline_data["pipeline_name"]
        
        # 验证服务调用
        mock_service.create_pipeline.assert_called_once()

    @patch('src.services.data_processing_service.admin_router.require_permission')
    @patch('src.services.data_processing_service.admin_router.get_processing_pipeline_service')
    @patch('src.services.data_processing_service.admin_router.get_db')
    def test_create_processing_pipeline_duplicate_code(
        self, mock_get_db, mock_get_service, mock_require_permission,
        client, sample_pipeline_data
    ):
        """测试创建处理管道时管道代码重复"""
        # 配置权限模拟
        mock_require_permission.return_value = mock_current_user
        mock_get_db.return_value = mock_db
        
        # 配置服务模拟
        mock_service = Mock()
        mock_service.create_pipeline.side_effect = ValueError("Pipeline code 'test_pipeline' already exists")
        mock_get_service.return_value = mock_service
        
        # 发送请求
        response = client.post(
            "/admin/data-processing/pipelines",
            json=sample_pipeline_data
        )
        
        # 验证响应
        assert response.status_code == 400
        assert "already exists" in response.json()["detail"]

    @patch('src.services.data_processing_service.admin_router.require_permission')
    @patch('src.services.data_processing_service.admin_router.get_processing_pipeline_service')
    @patch('src.services.data_processing_service.admin_router.get_db')
    def test_get_processing_pipelines_success(
        self, mock_get_db, mock_get_service, mock_require_permission,
        client, sample_pipeline_model
    ):
        """测试成功获取处理管道列表"""
        # 配置权限模拟
        mock_require_permission.return_value = mock_current_user
        mock_get_db.return_value = mock_db
        
        # 配置服务模拟
        mock_service = Mock()
        mock_service.get_pipelines.return_value = ([sample_pipeline_model], 1)
        mock_get_service.return_value = mock_service
        
        # 发送请求
        response = client.get(
            "/admin/data-processing/pipelines?skip=0&limit=10"
        )
        
        # 验证响应
        assert response.status_code == 200
        data = response.json()
        assert data["total"] == 1
        assert len(data["items"]) == 1
        assert data["items"][0]["pipeline_code"] == sample_pipeline_model.pipeline_code

    @patch('src.services.data_processing_service.admin_router.require_permission')
    @patch('src.services.data_processing_service.admin_router.get_processing_pipeline_service')
    @patch('src.services.data_processing_service.admin_router.get_db')
    def test_get_processing_pipeline_success(
        self, mock_get_db, mock_get_service, mock_require_permission,
        client, sample_pipeline_model
    ):
        """测试成功获取处理管道详情"""
        # 配置权限模拟
        mock_require_permission.return_value = mock_current_user
        mock_get_db.return_value = mock_db
        
        # 配置服务模拟
        mock_service = Mock()
        mock_service.get_pipeline_by_id.return_value = sample_pipeline_model
        mock_get_service.return_value = mock_service
        
        # 发送请求
        response = client.get(
            "/admin/data-processing/pipelines/1"
        )
        
        # 验证响应
        assert response.status_code == 200
        data = response.json()
        assert data["pipeline_code"] == sample_pipeline_model.pipeline_code
        assert data["pipeline_name"] == sample_pipeline_model.pipeline_name

    @patch('src.services.data_processing_service.admin_router.require_permission')
    @patch('src.services.data_processing_service.admin_router.get_processing_pipeline_service')
    @patch('src.services.data_processing_service.admin_router.get_db')
    def test_get_processing_pipeline_not_found(
        self, mock_get_db, mock_get_service, mock_require_permission,
        client
    ):
        """测试获取不存在的处理管道"""
        # 配置权限模拟
        mock_require_permission.return_value = mock_current_user
        mock_get_db.return_value = mock_db
        
        # 配置服务模拟
        mock_service = Mock()
        mock_service.get_pipeline_by_id.return_value = None
        mock_get_service.return_value = mock_service
        
        # 发送请求
        response = client.get(
            "/admin/data-processing/pipelines/999"
        )
        
        # 验证响应
        assert response.status_code == 404
        assert "不存在" in response.json()["detail"]

    @patch('src.services.data_processing_service.admin_router.require_permission')
    @patch('src.services.data_processing_service.admin_router.get_processing_pipeline_service')
    @patch('src.services.data_processing_service.admin_router.get_db')
    def test_update_processing_pipeline_success(
        self, mock_get_db, mock_get_service, mock_require_permission,
        client, sample_pipeline_model
    ):
        """测试成功更新处理管道"""
        update_data = {
            "pipeline_name": "更新后的管道名称",
            "description": "更新后的描述",
            "is_active": False
        }
        
        # 更新模型数据
        updated_pipeline = sample_pipeline_model
        updated_pipeline.pipeline_name = update_data["pipeline_name"]
        updated_pipeline.description = update_data["description"]
        updated_pipeline.is_active = update_data["is_active"]
        
        # 配置权限模拟
        mock_require_permission.return_value = mock_current_user
        mock_get_db.return_value = mock_db
        
        # 配置服务模拟
        mock_service = Mock()
        mock_service.update_pipeline.return_value = updated_pipeline
        mock_get_service.return_value = mock_service
        
        # 发送请求
        response = client.put(
            "/admin/data-processing/pipelines/1",
            json=update_data
        )
        
        # 验证响应
        assert response.status_code == 200
        data = response.json()
        assert data["pipeline_name"] == update_data["pipeline_name"]
        assert data["description"] == update_data["description"]
        assert data["is_active"] == update_data["is_active"]

    @patch('src.services.data_processing_service.admin_router.require_permission')
    @patch('src.services.data_processing_service.admin_router.get_processing_pipeline_service')
    @patch('src.services.data_processing_service.admin_router.get_db')
    def test_update_processing_pipeline_not_found(
        self, mock_get_db, mock_get_service, mock_require_permission,
        client
    ):
        """测试更新不存在的处理管道"""
        update_data = {"pipeline_name": "更新后的名称"}
        
        # 配置权限模拟
        mock_require_permission.return_value = mock_current_user
        mock_get_db.return_value = mock_db
        
        # 配置服务模拟
        mock_service = Mock()
        mock_service.update_pipeline.return_value = None
        mock_get_service.return_value = mock_service
        
        # 发送请求
        response = client.put(
            "/admin/data-processing/pipelines/999",
            json=update_data
        )
        
        # 验证响应
        assert response.status_code == 404
        assert "不存在" in response.json()["detail"]

    @patch('src.services.data_processing_service.admin_router.require_permission')
    @patch('src.services.data_processing_service.admin_router.get_processing_pipeline_service')
    @patch('src.services.data_processing_service.admin_router.get_db')
    def test_delete_processing_pipeline_success(
        self, mock_get_db, mock_get_service, mock_require_permission,
        client
    ):
        """测试成功删除处理管道"""
        # 配置权限模拟
        mock_require_permission.return_value = mock_current_user
        mock_get_db.return_value = mock_db
        
        # 配置服务模拟
        mock_service = Mock()
        mock_service.delete_pipeline.return_value = True
        mock_get_service.return_value = mock_service
        
        # 发送请求
        response = client.delete(
            "/admin/data-processing/pipelines/1"
        )
        
        # 验证响应
        assert response.status_code == 204

    @patch('src.services.data_processing_service.admin_router.require_permission')
    @patch('src.services.data_processing_service.admin_router.get_processing_pipeline_service')
    @patch('src.services.data_processing_service.admin_router.get_db')
    def test_delete_processing_pipeline_not_found(
        self, mock_get_db, mock_get_service, mock_require_permission,
        client
    ):
        """测试删除不存在的处理管道"""
        # 配置权限模拟
        mock_require_permission.return_value = mock_current_user
        mock_get_db.return_value = mock_db
        
        # 配置服务模拟
        mock_service = Mock()
        mock_service.delete_pipeline.return_value = False
        mock_get_service.return_value = mock_service
        
        # 发送请求
        response = client.delete(
            "/admin/data-processing/pipelines/999"
        )
        
        # 验证响应
        assert response.status_code == 404
        assert "不存在" in response.json()["detail"]

    @patch('src.services.data_processing_service.admin_router.require_permission')
    @patch('src.services.data_processing_service.admin_router.get_processing_pipeline_service')
    @patch('src.services.data_processing_service.admin_router.get_db')
    def test_delete_processing_pipeline_in_use(
        self, mock_get_db, mock_get_service, mock_require_permission,
        client
    ):
        """测试删除正在使用的处理管道"""
        # 配置权限模拟
        mock_require_permission.return_value = mock_current_user
        mock_get_db.return_value = mock_db
        
        # 配置服务模拟
        mock_service = Mock()
        mock_service.delete_pipeline.side_effect = ValueError("Cannot delete pipeline: 2 data sources are using it")
        mock_get_service.return_value = mock_service
        
        # 发送请求
        response = client.delete(
            "/admin/data-processing/pipelines/1"
        )
        
        # 验证响应
        assert response.status_code == 400
        assert "Cannot delete pipeline" in response.json()["detail"]

    @patch('src.services.data_processing_service.admin_router.require_permission')
    @patch('src.services.data_processing_service.admin_router.get_processing_pipeline_service')
    @patch('src.services.data_processing_service.admin_router.get_db')
    def test_get_active_processing_pipelines_success(
        self, mock_get_db, mock_get_service, mock_require_permission,
        client, sample_pipeline_model
    ):
        """测试成功获取活跃处理管道"""
        # 配置权限模拟
        mock_require_permission.return_value = mock_current_user
        mock_get_db.return_value = mock_db
        
        # 配置服务模拟
        mock_service = Mock()
        mock_service.get_active_pipelines.return_value = [sample_pipeline_model]
        mock_get_service.return_value = mock_service
        
        # 发送请求
        response = client.get(
            "/admin/data-processing/pipelines/active"
        )
        
        # 验证响应
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 1
        assert data[0]["pipeline_code"] == sample_pipeline_model.pipeline_code

    @patch('src.services.data_processing_service.admin_router.require_permission')
    @patch('src.services.data_processing_service.admin_router.get_processing_pipeline_service')
    @patch('src.services.data_processing_service.admin_router.get_db')
    def test_get_processing_pipeline_stats_success(
        self, mock_get_db, mock_get_service, mock_require_permission,
        client
    ):
        """测试成功获取处理管道统计"""
        stats = {
            "total_pipelines": 10,
            "active_pipelines": 8,
            "inactive_pipelines": 2,
            "avg_success_rate": 0.8500,
            "total_processed": 1500
        }
        
        # 配置权限模拟
        mock_require_permission.return_value = mock_current_user
        mock_get_db.return_value = mock_db
        
        # 配置服务模拟
        mock_service = Mock()
        mock_service.get_pipeline_stats.return_value = stats
        mock_get_service.return_value = mock_service
        
        # 发送请求
        response = client.get(
            "/admin/data-processing/pipelines/stats"
        )
        
        # 验证响应
        assert response.status_code == 200
        data = response.json()
        assert data["total_pipelines"] == 10
        assert data["active_pipelines"] == 8
        assert data["avg_success_rate"] == 0.8500 