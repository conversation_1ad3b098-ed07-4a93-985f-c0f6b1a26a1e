"""
测试数据处理引擎
"""

import pytest
import asyncio
from datetime import datetime, timezone
from unittest.mock import Mock, AsyncMock, patch
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from src.core.database import Base
from src.services.data_processing_service.engine import DataProcessingEngine
from src.services.data_processing_service.models import (
    DataProcessingPipeline, DataProcessingStatus, ProcessingStage, ProcessingResult,
    FlashNews
)
from src.services.data_collection_service.models import (
    RawDataRecord, DataSource, BusinessDataType
)


@pytest.fixture
def db_session():
    """创建测试数据库会话"""
    engine = create_engine("sqlite:///:memory:")
    Base.metadata.create_all(engine)
    SessionLocal = sessionmaker(bind=engine)
    session = SessionLocal()
    yield session
    session.close()


@pytest.fixture
def sample_data_source(db_session):
    """创建示例数据源"""
    data_source = DataSource(
        name="测试财经网站",
        collection_method="api_json",
        content_category="financial_news",
        business_data_type=BusinessDataType.FLASH_NEWS,
        base_url="https://test-finance.com"
    )
    db_session.add(data_source)
    db_session.commit()
    return data_source


@pytest.fixture
def sample_raw_record(db_session, sample_data_source):
    """创建示例原始数据记录"""
    record = RawDataRecord(
        source_id=sample_data_source.id,
        source_url="https://test-finance.com/news/123",
        url_hash="test_hash_123",
        url_domain="test-finance.com",
        content_hash="content_hash_123",
        title="央行宣布降准政策",
        author="财经记者",
        publish_time=datetime.now(timezone.utc),
        mongodb_id="507f1f77bcf86cd799439011",
        content_type="financial_news",
        processing_status="pending",
        processing_priority=5
    )
    db_session.add(record)
    db_session.commit()
    return record


@pytest.fixture
def sample_pipeline(db_session, sample_data_source):
    """创建示例处理管道"""
    pipeline = DataProcessingPipeline(
        pipeline_code="flash_news_basic",
        pipeline_name="快讯基础处理",
        source_id=sample_data_source.id,
        target_business_type=BusinessDataType.FLASH_NEWS,
        is_active=True,
        field_mapping={
            "title": "title",
            "content": "content",
            "publish_time": "publish_time"
        },
        data_extraction_config={
            "content_cleaning": {
                "remove_html": True,
                "normalize_whitespace": True
            }
        },
        data_transformation_config={
            "title_cleaning": {
                "apply_patterns": True,
                "max_length": 500
            }
        },
        data_validation_config={
            "required_fields": ["title", "content"],
            "field_lengths": {
                "title": 500,
                "content": 10000
            }
        },
        data_enrichment_config={
            "generate_summary": True,
            "extract_tags": True,
            "classify_content": True
        }
    )
    db_session.add(pipeline)
    db_session.commit()
    return pipeline


@pytest.fixture
def mock_mongodb_content():
    """模拟MongoDB内容"""
    return {
        "title": "央行宣布降准0.5个百分点",
        "content": "中国人民银行今日宣布，决定于2025年2月1日降准0.5个百分点，释放长期资金约1万亿元。",
        "publish_time": "2025-01-15 14:30:00",
        "author": "央行新闻发言人",
        "source_url": "https://test-finance.com/news/123"
    }


class TestDataProcessingEngine:
    """测试数据处理引擎"""

    @pytest.fixture(autouse=True)
    def setup(self):
        """设置测试环境"""
        self.engine = DataProcessingEngine()

    @patch('src.services.data_processing_service.engine.SessionLocal')
    @patch('src.services.data_processing_service.engine.mongodb_manager')
    async def test_process_single_record_success(
        self, 
        mock_mongodb, 
        mock_session_local,
        db_session,
        sample_raw_record,
        sample_pipeline,
        mock_mongodb_content
    ):
        """测试成功处理单个记录"""
        
        # 模拟数据库会话
        mock_session_local.return_value = db_session
        
        # 模拟MongoDB内容获取
        mock_mongodb.get_raw_content = AsyncMock(return_value=mock_mongodb_content)
        
        # 模拟AI服务
        with patch.object(self.engine.ai_analyzer, 'analyze_content') as mock_ai:
            mock_ai.return_value = {
                **mock_mongodb_content,
                "summary": "央行降准释放流动性",
                "ai_extracted_tags": ["央行", "降准", "货币政策"],
                "ai_classifications": {
                    "primary_category": "monetary_policy",
                    "impact_scope": "domestic",
                    "urgency_level": "high"
                },
                "importance_score": 0.95
            }
            
            # 模拟内容过滤
            with patch.object(self.engine.content_filter, 'is_spam') as mock_filter:
                mock_filter.return_value = False
                
                # 执行处理
                result = await self.engine.process_single_record(sample_raw_record)
                
                # 验证结果
                assert result["success"] is True
                assert "business_record_id" in result
                assert result["business_table"] == "flash_news"
                
                # 验证数据库中的记录
                flash_news = db_session.query(FlashNews).filter(
                    FlashNews.raw_data_id == sample_raw_record.id
                ).first()
                
                assert flash_news is not None
                assert flash_news.title == "央行宣布降准0.5个百分点"
                assert flash_news.summary == "央行降准释放流动性"

    @patch('src.services.data_processing_service.engine.SessionLocal')
    @patch('src.services.data_processing_service.engine.mongodb_manager')
    async def test_process_single_record_spam_content(
        self,
        mock_mongodb,
        mock_session_local,
        db_session,
        sample_raw_record,
        sample_pipeline,
        mock_mongodb_content
    ):
        """测试处理垃圾内容"""
        
        # 模拟数据库会话
        mock_session_local.return_value = db_session
        
        # 模拟MongoDB内容获取
        mock_mongodb.get_raw_content = AsyncMock(return_value=mock_mongodb_content)
        mock_mongodb.delete_raw_content = AsyncMock()
        
        # 模拟内容过滤器检测为垃圾内容
        with patch.object(self.engine.content_filter, 'is_spam') as mock_filter:
            mock_filter.return_value = True
            
            # 执行处理
            result = await self.engine.process_single_record(sample_raw_record)
            
            # 验证结果
            assert result["success"] is True
            assert result["action"] == "deleted"
            assert result["reason"] == "spam_content"
            
            # 验证MongoDB删除被调用
            mock_mongodb.delete_raw_content.assert_called_once_with(
                sample_raw_record.mongodb_id
            )

    @patch('src.services.data_processing_service.engine.SessionLocal')
    @patch('src.services.data_processing_service.engine.mongodb_manager')
    async def test_process_single_record_no_mongodb_content(
        self,
        mock_mongodb,
        mock_session_local,
        db_session,
        sample_raw_record,
        sample_pipeline
    ):
        """测试MongoDB内容不存在的情况"""
        
        # 模拟数据库会话
        mock_session_local.return_value = db_session
        
        # 模拟MongoDB内容获取失败
        mock_mongodb.get_raw_content = AsyncMock(return_value=None)
        
        # 执行处理
        result = await self.engine.process_single_record(sample_raw_record)
        
        # 验证结果
        assert result["success"] is False
        assert "无法获取MongoDB原始内容" in result["error"]

    @patch('src.services.data_processing_service.engine.SessionLocal')
    async def test_process_single_record_no_pipeline(
        self,
        mock_session_local,
        db_session,
        sample_raw_record
    ):
        """测试没有找到适用管道的情况"""
        
        # 模拟数据库会话
        mock_session_local.return_value = db_session
        
        # 模拟管道服务返回None
        with patch.object(self.engine.pipeline_service, 'find_processing_pipeline') as mock_pipeline:
            mock_pipeline.return_value = None
            
            # 执行处理
            result = await self.engine.process_single_record(sample_raw_record)
            
            # 验证结果
            assert result["success"] is False
            assert "未找到适用的处理管道" in result["error"]

    @patch('src.services.data_processing_service.engine.SessionLocal')
    async def test_process_pending_records(
        self,
        mock_session_local,
        db_session,
        sample_raw_record
    ):
        """测试批量处理待处理记录"""
        
        # 模拟数据库会话
        mock_session_local.return_value = db_session
        
        # 模拟单个记录处理
        with patch.object(self.engine, 'process_single_record') as mock_process:
            mock_process.return_value = {"success": True}
            
            # 执行批量处理
            stats = await self.engine.process_pending_records(batch_size=5)
            
            # 验证结果
            assert stats["processed"] == 1
            assert stats["success"] == 1
            assert stats["failed"] == 0

    async def test_extract_data(self, sample_pipeline, mock_mongodb_content):
        """测试数据提取"""
        
        result = await self.engine._extract_data(mock_mongodb_content, sample_pipeline)
        
        # 验证提取结果
        assert "title" in result
        assert "content" in result
        assert result["title"] == mock_mongodb_content["title"]

    async def test_clean_data(self, sample_pipeline):
        """测试数据清洗"""
        
        test_data = {
            "title": "  【快讯】央行政策  ",
            "content": "<p>央行内容</p>  \n\n  更多内容"
        }
        
        result = await self.engine._clean_data(test_data, sample_pipeline)
        
        # 验证清洗结果
        assert result["title"].strip() == "央行政策"  # 去除前缀和空格
        assert "<p>" not in result["content"]  # 去除HTML标签

    async def test_transform_data(self, sample_pipeline):
        """测试数据转换"""
        
        test_data = {
            "title": "突发：央行重大政策",
            "content": "央行宣布重要政策调整"
        }
        
        result = await self.engine._transform_data(test_data, sample_pipeline)
        
        # 验证转换结果
        assert "urgency_level" in result
        assert result["urgency_level"] == 3  # 包含"突发"关键词，应该是紧急级别

    async def test_validate_data(self, sample_pipeline):
        """测试数据验证"""
        
        # 测试有效数据
        valid_data = {
            "title": "有效标题",
            "content": "有效内容"
        }
        
        result = await self.engine._validate_data(valid_data, sample_pipeline)
        assert result["valid"] is True
        assert len(result["errors"]) == 0
        
        # 测试无效数据
        invalid_data = {
            "title": "",  # 缺少必填字段
        }
        
        result = await self.engine._validate_data(invalid_data, sample_pipeline)
        assert result["valid"] is False
        assert len(result["errors"]) > 0

    @patch('src.services.data_processing_service.engine.SessionLocal')
    async def test_retry_failed_records(
        self,
        mock_session_local,
        db_session,
        sample_raw_record
    ):
        """测试重试失败记录"""
        
        # 模拟数据库会话
        mock_session_local.return_value = db_session
        
        # 创建失败的处理状态
        failed_status = DataProcessingStatus(
            raw_data_id=sample_raw_record.id,
            target_business_type=BusinessDataType.FLASH_NEWS,
            processing_stage=ProcessingStage.FAILED,
            retry_count=0,
            error_message="测试错误"
        )
        db_session.add(failed_status)
        db_session.commit()
        
        # 模拟重试成功
        with patch.object(self.engine, 'process_single_record') as mock_process:
            mock_process.return_value = {"success": True}
            
            # 执行重试
            stats = await self.engine.retry_failed_records(max_retries=3)
            
            # 验证结果
            assert stats["retried"] == 1
            assert stats["success"] == 1
            assert stats["failed"] == 0
            
            # 验证重试计数增加
            db_session.refresh(failed_status)
            assert failed_status.retry_count == 1
