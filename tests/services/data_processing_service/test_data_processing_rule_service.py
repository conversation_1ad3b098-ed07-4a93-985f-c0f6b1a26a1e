"""
数据处理规则服务测试
测试数据处理规则的CRUD操作、批量操作、统计功能等
"""

import pytest
from datetime import datetime, timezone
from decimal import Decimal
from unittest.mock import Mock, patch

from sqlalchemy.orm import Session

from src.services.data_processing_service.models import DataProcessingRule
from src.services.data_processing_service.service import DataProcessingRuleService
from src.services.data_processing_service.schemas import (
    DataProcessingRuleCreate,
    DataProcessingRuleUpdate,
    DataProcessingRuleFilter,
    BatchRuleStatusUpdate,
    BatchRulePriorityUpdate,
)
from src.services.data_collection_service.models import BusinessDataType


class TestDataProcessingRuleService:
    """数据处理规则服务测试类"""

    def setup_method(self):
        """测试前的设置"""
        self.mock_db = Mock(spec=Session)
        self.service = DataProcessingRuleService()

    def test_create_rule_success(self):
        """测试成功创建数据处理规则"""
        # 准备测试数据
        rule_data = DataProcessingRuleCreate(
            rule_name="test_flash_news_rule",
            business_data_type=BusinessDataType.FLASH_NEWS,
            field_mapping={
                "title": "$.content",
                "content": "$.content",
                "publish_time": "$.time",
            },
            transformation_rules={
                "publish_time": {"format": "timestamp", "timezone": "Asia/Shanghai"}
            },
            validation_rules={
                "required_fields": ["title", "content", "publish_time"],
                "max_content_length": 500,
            },
            source_criteria={
                "source_names": ["金十数据快讯"],
                "collection_methods": ["api_json"]
            },
            priority=8,
            created_by="test_user",
        )

        # 模拟数据库操作
        self.mock_db.query.return_value.filter.return_value.first.return_value = None  # 规则名称不存在
        mock_rule = Mock()
        mock_rule.id = 1
        mock_rule.rule_name = rule_data.rule_name
        self.mock_db.add = Mock()
        self.mock_db.commit = Mock()
        self.mock_db.refresh = Mock()
        
        # 模拟创建的规则对象
        created_rule = DataProcessingRule(
            id=1,
            rule_name=rule_data.rule_name,
            business_data_type=rule_data.business_data_type.value,
            field_mapping=rule_data.field_mapping,
            transformation_rules=rule_data.transformation_rules,
            validation_rules=rule_data.validation_rules,
            source_criteria=rule_data.source_criteria,
            priority=rule_data.priority,
            is_active=True,
            created_by=rule_data.created_by,
            created_at=datetime.now(timezone.utc),
            updated_at=datetime.now(timezone.utc),
        )
        
        # 执行测试
        with patch.object(self.service, 'get_rule_by_name', return_value=None):
            with patch('src.services.data_processing_service.service.DataProcessingRule', return_value=created_rule):
                result = self.service.create_rule(self.mock_db, rule_data)

        # 验证结果
        assert result is not None
        assert result.rule_name == rule_data.rule_name
        assert result.business_data_type == rule_data.business_data_type.value
        assert result.priority == rule_data.priority
        assert result.created_by == rule_data.created_by
        
        # 验证数据库操作
        self.mock_db.add.assert_called_once()
        self.mock_db.commit.assert_called_once()
        self.mock_db.refresh.assert_called_once()

    def test_create_rule_duplicate_name(self):
        """测试创建重复名称的规则"""
        rule_data = DataProcessingRuleCreate(
            rule_name="existing_rule",
            business_data_type=BusinessDataType.NEWS_ARTICLE,
            field_mapping={"title": "$.title"},
        )

        # 模拟已存在的规则
        existing_rule = Mock()
        existing_rule.rule_name = "existing_rule"
        
        with patch.object(self.service, 'get_rule_by_name', return_value=existing_rule):
            with pytest.raises(ValueError, match="规则名称 'existing_rule' 已存在"):
                self.service.create_rule(self.mock_db, rule_data)

    def test_get_rule_by_id_success(self):
        """测试根据ID获取规则成功"""
        rule_id = 1
        mock_rule = Mock()
        mock_rule.id = rule_id
        mock_rule.rule_name = "test_rule"
        
        self.mock_db.query.return_value.filter.return_value.first.return_value = mock_rule
        
        result = self.service.get_rule_by_id(self.mock_db, rule_id)
        
        assert result == mock_rule
        self.mock_db.query.assert_called_with(DataProcessingRule)

    def test_get_rule_by_id_not_found(self):
        """测试根据ID获取规则未找到"""
        rule_id = 999
        self.mock_db.query.return_value.filter.return_value.first.return_value = None
        
        result = self.service.get_rule_by_id(self.mock_db, rule_id)
        
        assert result is None

    def test_get_rule_by_name_success(self):
        """测试根据名称获取规则成功"""
        rule_name = "test_rule"
        mock_rule = Mock()
        mock_rule.rule_name = rule_name
        
        self.mock_db.query.return_value.filter.return_value.first.return_value = mock_rule
        
        result = self.service.get_rule_by_name(self.mock_db, rule_name)
        
        assert result == mock_rule
        self.mock_db.query.assert_called_with(DataProcessingRule)

    def test_get_rules_with_filters(self):
        """测试使用过滤器获取规则列表"""
        # 模拟查询结果
        mock_rules = [Mock(), Mock(), Mock()]
        mock_query = Mock()
        mock_query.count.return_value = 3
        mock_query.order_by.return_value.offset.return_value.limit.return_value.all.return_value = mock_rules
        
        # 设置过滤器链
        mock_query.filter.return_value = mock_query
        self.mock_db.query.return_value = mock_query
        
        # 创建过滤器
        filters = DataProcessingRuleFilter(
            business_data_type=BusinessDataType.FLASH_NEWS,
            is_active=True,
            priority_min=5,
            priority_max=10,
            search="flash",
        )
        
        # 执行测试
        rules, total = self.service.get_rules(
            self.mock_db, skip=0, limit=10, filters=filters
        )
        
        # 验证结果
        assert len(rules) == 3
        assert total == 3
        assert rules == mock_rules
        
        # 验证查询被调用
        self.mock_db.query.assert_called_with(DataProcessingRule)
        
        # 验证过滤器被应用（filter被调用多次）
        assert mock_query.filter.call_count >= 4  # 至少应用了4个过滤条件

    def test_update_rule_success(self):
        """测试成功更新规则"""
        rule_id = 1
        update_data = DataProcessingRuleUpdate(
            rule_name="updated_rule",
            priority=9,
            is_active=False,
        )
        
        # 模拟现有规则
        mock_rule = Mock()
        mock_rule.id = rule_id
        mock_rule.rule_name = "old_rule"
        mock_rule.priority = 5
        mock_rule.is_active = True
        
        # 模拟数据库操作
        with patch.object(self.service, 'get_rule_by_id', return_value=mock_rule):
            with patch.object(self.service, 'get_rule_by_name', return_value=None):
                result = self.service.update_rule(self.mock_db, rule_id, update_data)
        
        # 验证结果
        assert result == mock_rule
        self.mock_db.commit.assert_called_once()
        self.mock_db.refresh.assert_called_once_with(mock_rule)

    def test_update_rule_not_found(self):
        """测试更新不存在的规则"""
        rule_id = 999
        update_data = DataProcessingRuleUpdate(rule_name="new_name")
        
        with patch.object(self.service, 'get_rule_by_id', return_value=None):
            result = self.service.update_rule(self.mock_db, rule_id, update_data)
        
        assert result is None

    def test_delete_rule_success(self):
        """测试成功删除规则"""
        rule_id = 1
        mock_rule = Mock()
        mock_rule.id = rule_id
        
        with patch.object(self.service, 'get_rule_by_id', return_value=mock_rule):
            result = self.service.delete_rule(self.mock_db, rule_id)
        
        assert result is True
        self.mock_db.delete.assert_called_once_with(mock_rule)
        self.mock_db.commit.assert_called_once()

    def test_delete_rule_not_found(self):
        """测试删除不存在的规则"""
        rule_id = 999
        
        with patch.object(self.service, 'get_rule_by_id', return_value=None):
            result = self.service.delete_rule(self.mock_db, rule_id)
        
        assert result is False
        self.mock_db.delete.assert_not_called()
        self.mock_db.commit.assert_not_called()

    def test_get_active_rules_by_type(self):
        """测试获取指定业务类型的活跃规则"""
        business_type = BusinessDataType.FLASH_NEWS
        mock_rules = [Mock(), Mock()]
        
        mock_query = Mock()
        mock_query.filter.return_value.order_by.return_value.all.return_value = mock_rules
        self.mock_db.query.return_value = mock_query
        
        result = self.service.get_active_rules_by_type(self.mock_db, business_type)
        
        assert result == mock_rules
        self.mock_db.query.assert_called_with(DataProcessingRule)

    def test_get_rules_stats(self):
        """测试获取规则统计信息"""
        # 模拟基础统计查询
        self.mock_db.query.return_value.count.return_value = 10  # 总规则数
        self.mock_db.query.return_value.filter.return_value.count.return_value = 8  # 活跃规则数
        
        # 模拟按类型分组统计
        type_stats = [('flash_news', 3), ('news_article', 5), ('research_report', 2)]
        self.mock_db.query.return_value.group_by.return_value.all.return_value = type_stats
        
        # 模拟平均优先级
        self.mock_db.query.return_value.scalar.return_value = 6.5
        
        # 模拟执行统计
        execution_stats = Mock()
        execution_stats.total_executions = 1000
        execution_stats.total_successes = 950
        self.mock_db.query.return_value.first.return_value = execution_stats
        
        result = self.service.get_rules_stats(self.mock_db)
        
        assert result.total_rules == 10
        assert result.active_rules == 8
        assert result.inactive_rules == 2
        assert result.rules_by_type == {'flash_news': 3, 'news_article': 5, 'research_report': 2}
        assert result.average_priority == 6.5
        assert result.total_executions == 1000
        assert result.total_successes == 950
        assert result.overall_success_rate == 95.0

    def test_batch_update_status(self):
        """测试批量更新规则状态"""
        update_data = BatchRuleStatusUpdate(
            rule_ids=[1, 2, 3],
            is_active=False
        )
        
        # 模拟规则查询
        mock_rules = [Mock(), Mock(), Mock()]
        for i, rule in enumerate(mock_rules):
            rule.id = i + 1
            rule.is_active = True
        
        def get_rule_side_effect(db, rule_id):
            return mock_rules[rule_id - 1] if rule_id <= 3 else None
        
        with patch.object(self.service, 'get_rule_by_id', side_effect=get_rule_side_effect):
            result = self.service.batch_update_status(self.mock_db, update_data)
        
        assert result.success_count == 3
        assert result.failed_count == 0
        assert len(result.failed_ids) == 0
        self.mock_db.commit.assert_called_once()

    def test_batch_update_priority(self):
        """测试批量更新规则优先级"""
        update_data = BatchRulePriorityUpdate(
            rule_ids=[1, 2],
            priority=9
        )
        
        # 模拟规则查询
        mock_rules = [Mock(), Mock()]
        for i, rule in enumerate(mock_rules):
            rule.id = i + 1
            rule.priority = 5
        
        def get_rule_side_effect(db, rule_id):
            return mock_rules[rule_id - 1] if rule_id <= 2 else None
        
        with patch.object(self.service, 'get_rule_by_id', side_effect=get_rule_side_effect):
            result = self.service.batch_update_priority(self.mock_db, update_data)
        
        assert result.success_count == 2
        assert result.failed_count == 0
        assert len(result.failed_ids) == 0
        self.mock_db.commit.assert_called_once()

    def test_update_execution_stats_success(self):
        """测试更新执行统计成功"""
        rule_id = 1
        mock_rule = Mock()
        mock_rule.execution_count = 10
        mock_rule.success_count = 8
        
        with patch.object(self.service, 'get_rule_by_id', return_value=mock_rule):
            result = self.service.update_execution_stats(self.mock_db, rule_id, success=True)
        
        assert result is True
        assert mock_rule.execution_count == 11
        assert mock_rule.success_count == 9
        self.mock_db.commit.assert_called_once()

    def test_update_execution_stats_failure(self):
        """测试更新执行统计失败"""
        rule_id = 1
        mock_rule = Mock()
        mock_rule.execution_count = 10
        mock_rule.success_count = 8
        
        with patch.object(self.service, 'get_rule_by_id', return_value=mock_rule):
            result = self.service.update_execution_stats(self.mock_db, rule_id, success=False)
        
        assert result is True
        assert mock_rule.execution_count == 11
        assert mock_rule.success_count == 8  # 失败时不增加成功次数
        self.mock_db.commit.assert_called_once()

    def test_get_rules_by_source_criteria(self):
        """测试根据数据源条件获取匹配规则"""
        source_name = "金十数据快讯"
        collection_method = "api_json"
        
        # 模拟规则数据
        mock_rule1 = Mock()
        mock_rule1.source_criteria = {
            "source_names": ["金十数据快讯"],
            "collection_methods": ["api_json"]
        }
        
        mock_rule2 = Mock()
        mock_rule2.source_criteria = {
            "source_names": ["新浪财经"],
            "collection_methods": ["web_scraping"]
        }
        
        mock_rule3 = Mock()
        mock_rule3.source_criteria = {
            "source_names": ["金十数据快讯"],
            "collection_methods": ["web_scraping"]
        }
        
        mock_rules = [mock_rule1, mock_rule2, mock_rule3]
        
        mock_query = Mock()
        mock_query.filter.return_value.order_by.return_value.all.return_value = mock_rules
        self.mock_db.query.return_value = mock_query
        
        result = self.service.get_rules_by_source_criteria(
            self.mock_db, source_name, collection_method
        )
        
        # 应该只匹配 mock_rule1（源名称和采集方法都匹配）
        assert len(result) == 1
        assert result[0] == mock_rule1

    def test_field_mapping_validation(self):
        """测试字段映射验证"""
        # 测试空字段映射
        with pytest.raises(ValueError, match="字段映射规则不能为空"):
            DataProcessingRuleCreate(
                rule_name="test_rule",
                business_data_type=BusinessDataType.FLASH_NEWS,
                field_mapping={},  # 空字段映射
            )

    def test_rule_name_validation(self):
        """测试规则名称验证"""
        from pydantic_core import ValidationError
        
        # 测试空规则名称
        with pytest.raises(ValidationError):
            DataProcessingRuleCreate(
                rule_name="",
                business_data_type=BusinessDataType.FLASH_NEWS,
                field_mapping={"title": "$.title"},
            )
        
        # 测试包含特殊字符的规则名称
        with pytest.raises(ValueError, match="规则名称只能包含字母、数字、下划线和中文字符"):
            DataProcessingRuleCreate(
                rule_name="test-rule@#",
                business_data_type=BusinessDataType.FLASH_NEWS,
                field_mapping={"title": "$.title"},
            )

    def test_priority_validation(self):
        """测试优先级验证"""
        from pydantic_core import ValidationError
        
        # 测试优先级超出范围
        with pytest.raises(ValidationError):
            DataProcessingRuleCreate(
                rule_name="test_rule",
                business_data_type=BusinessDataType.FLASH_NEWS,
                field_mapping={"title": "$.title"},
                priority=11,  # 超出范围
            )
        
        with pytest.raises(ValidationError):
            DataProcessingRuleCreate(
                rule_name="test_rule",
                business_data_type=BusinessDataType.FLASH_NEWS,
                field_mapping={"title": "$.title"},
                priority=0,  # 低于范围
            ) 