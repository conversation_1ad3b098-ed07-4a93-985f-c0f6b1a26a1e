"""
财经日历服务测试
测试财经事件、国家地区、经济指标等功能
"""

from datetime import datetime, timedelta, timezone
from decimal import Decimal

import pytest
from sqlalchemy.orm import Session

from src.core.database import Base, engine
from src.services.financial_calendar_service.models import (
    CountryRegion, EconomicIndicator, EventAffectedMarket, EventRelatedEvent,
    EventType, FinancialEvent)
from src.services.financial_calendar_service.schemas import (
    FinancialEventCreate, FinancialEventFilter, FinancialEventUpdate,
    PaginationParams)
from src.services.financial_calendar_service.service import \
    FinancialCalendarService


@pytest.fixture(scope="function")
def db_session():
    """创建测试数据库会话"""
    Base.metadata.create_all(bind=engine)
    from src.core.database import SessionLocal

    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()
        Base.metadata.drop_all(bind=engine)


@pytest.fixture
def sample_country(db_session: Session):
    """创建示例国家数据"""
    country = CountryRegion(
        country_code="US",
        country_name="美国",
        country_name_en="United States",
        region="North America",
        time_zone="America/New_York",
        primary_currency="USD",
        currency_symbol="$",
    )
    db_session.add(country)
    db_session.commit()
    db_session.refresh(country)
    return country


@pytest.fixture
def sample_event_type(db_session: Session):
    """创建示例事件类型数据"""
    event_type = EventType(
        type_code="employment_data",
        type_name="就业数据",
        type_name_en="Employment Data",
        category="economic_indicator",
        default_importance=3,
        typical_market_impact="high",
    )
    db_session.add(event_type)
    db_session.commit()
    db_session.refresh(event_type)
    return event_type


@pytest.fixture
def sample_economic_indicator(db_session: Session, sample_country, sample_event_type):
    """创建示例经济指标数据"""
    indicator = EconomicIndicator(
        indicator_code="NFP",
        indicator_name="非农就业人数",
        indicator_name_en="Non-Farm Payrolls",
        country_id=sample_country.id,
        event_type_id=sample_event_type.id,
        unit="千人",
        data_type="integer",
        release_agency="美国劳工部",
        release_frequency="monthly",
        market_importance=3,
        volatility_impact=Decimal("0.9"),
    )
    db_session.add(indicator)
    db_session.commit()
    db_session.refresh(indicator)
    return indicator


@pytest.fixture
def financial_calendar_service(db_session: Session):
    """创建财经日历服务实例"""
    return FinancialCalendarService(db_session)


class TestFinancialCalendarService:
    """财经日历服务测试类"""

    def test_create_financial_event(
        self,
        financial_calendar_service: FinancialCalendarService,
        sample_country,
        sample_event_type,
        sample_economic_indicator,
    ):
        """测试创建财经事件"""
        event_data = FinancialEventCreate(
            event_title="美国非农就业人数",
            event_title_en="US Non-Farm Payrolls",
            event_type_id=sample_event_type.id,
            country_id=sample_country.id,
            indicator_id=sample_economic_indicator.id,
            scheduled_time=datetime.now(timezone.utc) + timedelta(days=1),
            importance_level=3,
            market_impact="high",
            previous_value=Decimal("250.5"),
            forecast_value=Decimal("260.0"),
            value_unit="千人",
            event_description="美国劳工部发布的非农就业人数数据",
        )

        result = financial_calendar_service.create_financial_event(event_data)

        assert result.id is not None
        assert result.event_title == event_data.event_title
        assert result.event_title_en == event_data.event_title_en
        assert result.event_type_id == event_data.event_type_id
        assert result.country_id == event_data.country_id
        assert result.indicator_id == event_data.indicator_id
        assert result.importance_level == event_data.importance_level
        assert result.market_impact == event_data.market_impact
        assert result.previous_value == event_data.previous_value
        assert result.forecast_value == event_data.forecast_value
        assert result.event_status == "scheduled"

    def test_create_financial_event_invalid_country(
        self, financial_calendar_service: FinancialCalendarService, sample_event_type
    ):
        """测试创建财经事件时国家不存在的情况"""
        event_data = FinancialEventCreate(
            event_title="测试事件",
            event_type_id=sample_event_type.id,
            country_id=99999,  # 不存在的国家ID
            scheduled_time=datetime.now(timezone.utc) + timedelta(days=1),
            importance_level=2,
        )

        with pytest.raises(ValueError) as exc_info:
            financial_calendar_service.create_financial_event(event_data)

        assert "Country with id 99999 not found or inactive" in str(exc_info.value)

    def test_create_financial_event_invalid_event_type(
        self, financial_calendar_service: FinancialCalendarService, sample_country
    ):
        """测试创建财经事件时事件类型不存在的情况"""
        event_data = FinancialEventCreate(
            event_title="测试事件",
            event_type_id=99999,  # 不存在的事件类型ID
            country_id=sample_country.id,
            scheduled_time=datetime.now(timezone.utc) + timedelta(days=1),
            importance_level=2,
        )

        with pytest.raises(ValueError) as exc_info:
            financial_calendar_service.create_financial_event(event_data)

        assert "Event type with id 99999 not found or inactive" in str(exc_info.value)

    def test_get_financial_event(
        self,
        financial_calendar_service: FinancialCalendarService,
        sample_country,
        sample_event_type,
    ):
        """测试获取单个财经事件"""
        # 先创建一个事件
        event_data = FinancialEventCreate(
            event_title="测试事件",
            event_type_id=sample_event_type.id,
            country_id=sample_country.id,
            scheduled_time=datetime.now(timezone.utc) + timedelta(days=1),
            importance_level=2,
        )
        created_event = financial_calendar_service.create_financial_event(event_data)

        # 获取事件
        result = financial_calendar_service.get_financial_event(created_event.id)

        assert result is not None
        assert result.id == created_event.id
        assert result.event_title == event_data.event_title
        assert result.event_type is not None
        assert result.country is not None

    def test_get_financial_event_not_found(
        self, financial_calendar_service: FinancialCalendarService
    ):
        """测试获取不存在的财经事件"""
        result = financial_calendar_service.get_financial_event(99999)
        assert result is None

    def test_update_financial_event(
        self,
        financial_calendar_service: FinancialCalendarService,
        sample_country,
        sample_event_type,
    ):
        """测试更新财经事件"""
        # 先创建一个事件
        event_data = FinancialEventCreate(
            event_title="原始标题",
            event_type_id=sample_event_type.id,
            country_id=sample_country.id,
            scheduled_time=datetime.now(timezone.utc) + timedelta(days=1),
            importance_level=2,
        )
        created_event = financial_calendar_service.create_financial_event(event_data)

        # 更新事件
        update_data = FinancialEventUpdate(
            event_title="更新后的标题",
            importance_level=3,
            actual_value=Decimal("123.45"),
        )

        result = financial_calendar_service.update_financial_event(
            created_event.id, update_data
        )

        assert result is not None
        assert result.event_title == "更新后的标题"
        assert result.importance_level == 3
        assert result.actual_value == Decimal("123.45")

    def test_delete_financial_event(
        self,
        financial_calendar_service: FinancialCalendarService,
        sample_country,
        sample_event_type,
    ):
        """测试删除财经事件"""
        # 先创建一个事件
        event_data = FinancialEventCreate(
            event_title="待删除的事件",
            event_type_id=sample_event_type.id,
            country_id=sample_country.id,
            scheduled_time=datetime.now(timezone.utc) + timedelta(days=1),
            importance_level=2,
        )
        created_event = financial_calendar_service.create_financial_event(event_data)

        # 删除事件
        success = financial_calendar_service.delete_financial_event(created_event.id)
        assert success is True

        # 验证事件已被删除
        result = financial_calendar_service.get_financial_event(created_event.id)
        assert result is None

    def test_delete_financial_event_not_found(
        self, financial_calendar_service: FinancialCalendarService
    ):
        """测试删除不存在的财经事件"""
        success = financial_calendar_service.delete_financial_event(99999)
        assert success is False

    def test_list_financial_events(
        self,
        financial_calendar_service: FinancialCalendarService,
        sample_country,
        sample_event_type,
    ):
        """测试获取财经事件列表"""
        # 创建多个事件
        for i in range(5):
            event_data = FinancialEventCreate(
                event_title=f"测试事件 {i+1}",
                event_type_id=sample_event_type.id,
                country_id=sample_country.id,
                scheduled_time=datetime.now(timezone.utc) + timedelta(days=i + 1),
                importance_level=2,
            )
            financial_calendar_service.create_financial_event(event_data)

        # 获取事件列表
        pagination = PaginationParams(page=1, size=3)
        result = financial_calendar_service.list_financial_events(
            filters=None, pagination=pagination
        )

        assert result.total == 5
        assert len(result.items) == 3
        assert result.page == 1
        assert result.size == 3
        assert result.pages == 2

    def test_list_financial_events_with_filters(
        self,
        financial_calendar_service: FinancialCalendarService,
        sample_country,
        sample_event_type,
        db_session: Session,
    ):
        """测试带过滤条件的事件列表查询"""
        # 创建另一个国家
        other_country = CountryRegion(
            country_code="CN",
            country_name="中国",
            time_zone="Asia/Shanghai",
            primary_currency="CNY",
        )
        db_session.add(other_country)
        db_session.commit()
        db_session.refresh(other_country)

        # 创建不同国家的事件
        base_time = datetime.now(timezone.utc)

        # 美国事件
        us_event_data = FinancialEventCreate(
            event_title="美国事件",
            event_type_id=sample_event_type.id,
            country_id=sample_country.id,
            scheduled_time=base_time + timedelta(days=1),
            importance_level=3,
        )
        financial_calendar_service.create_financial_event(us_event_data)

        # 中国事件
        cn_event_data = FinancialEventCreate(
            event_title="中国事件",
            event_type_id=sample_event_type.id,
            country_id=other_country.id,
            scheduled_time=base_time + timedelta(days=2),
            importance_level=2,
        )
        financial_calendar_service.create_financial_event(cn_event_data)

        # 按国家过滤
        filters = FinancialEventFilter(country_ids=[sample_country.id])
        result = financial_calendar_service.list_financial_events(filters=filters)

        assert result.total == 1
        assert result.items[0].country_id == sample_country.id

        # 按重要性级别过滤
        filters = FinancialEventFilter(importance_levels=[3])
        result = financial_calendar_service.list_financial_events(filters=filters)

        assert result.total == 1
        assert result.items[0].importance_level == 3

    def test_get_upcoming_events(
        self,
        financial_calendar_service: FinancialCalendarService,
        sample_country,
        sample_event_type,
    ):
        """测试获取即将到来的事件"""
        base_time = datetime.now(timezone.utc)

        # 创建不同时间的事件
        # 1小时后
        event_1h = FinancialEventCreate(
            event_title="1小时后的事件",
            event_type_id=sample_event_type.id,
            country_id=sample_country.id,
            scheduled_time=base_time + timedelta(hours=1),
            importance_level=2,
        )
        financial_calendar_service.create_financial_event(event_1h)

        # 25小时后（超出24小时范围）
        event_25h = FinancialEventCreate(
            event_title="25小时后的事件",
            event_type_id=sample_event_type.id,
            country_id=sample_country.id,
            scheduled_time=base_time + timedelta(hours=25),
            importance_level=2,
        )
        financial_calendar_service.create_financial_event(event_25h)

        # 获取24小时内的事件
        result = financial_calendar_service.get_upcoming_events(hours=24)

        assert len(result) == 1
        assert result[0].event_title == "1小时后的事件"

    def test_get_events_by_country(
        self,
        financial_calendar_service: FinancialCalendarService,
        sample_country,
        sample_event_type,
        db_session: Session,
    ):
        """测试按国家获取事件"""
        # 创建其他国家
        other_country = CountryRegion(
            country_code="JP",
            country_name="日本",
            time_zone="Asia/Tokyo",
            primary_currency="JPY",
        )
        db_session.add(other_country)
        db_session.commit()
        db_session.refresh(other_country)

        # 创建不同国家的事件
        us_event = FinancialEventCreate(
            event_title="美国事件",
            event_type_id=sample_event_type.id,
            country_id=sample_country.id,
            scheduled_time=datetime.now(timezone.utc) + timedelta(days=1),
            importance_level=2,
        )
        financial_calendar_service.create_financial_event(us_event)

        jp_event = FinancialEventCreate(
            event_title="日本事件",
            event_type_id=sample_event_type.id,
            country_id=other_country.id,
            scheduled_time=datetime.now(timezone.utc) + timedelta(days=2),
            importance_level=2,
        )
        financial_calendar_service.create_financial_event(jp_event)

        # 获取美国事件
        result = financial_calendar_service.get_events_by_country(sample_country.id)

        assert len(result) == 1
        assert result[0].country_id == sample_country.id
        assert result[0].event_title == "美国事件"

    def test_update_event_actual_value(
        self,
        financial_calendar_service: FinancialCalendarService,
        sample_country,
        sample_event_type,
    ):
        """测试更新事件实际值"""
        # 创建事件
        event_data = FinancialEventCreate(
            event_title="待更新实际值的事件",
            event_type_id=sample_event_type.id,
            country_id=sample_country.id,
            scheduled_time=datetime.now(timezone.utc) + timedelta(days=1),
            importance_level=2,
            forecast_value=Decimal("100.0"),
        )
        created_event = financial_calendar_service.create_financial_event(event_data)

        # 更新实际值
        actual_time = datetime.now(timezone.utc)
        result = financial_calendar_service.update_event_actual_value(
            created_event.id, 120.5, actual_time
        )

        assert result is not None
        assert result.actual_value == Decimal("120.5")
        # 修复：确保时间都带有时区信息，避免naive和aware datetime比较错误
        assert result.actual_time is not None
        if result.actual_time.tzinfo is None:
            # 如果返回的时间是naive datetime，将其转换为UTC
            result_time = result.actual_time.replace(tzinfo=timezone.utc)
        else:
            result_time = result.actual_time

        # 时间差应该在合理范围内（考虑到数据库操作的延迟）
        time_diff = abs((result_time - actual_time).total_seconds())
        assert time_diff < 2  # 允许2秒的误差
        assert result.event_status == "published"

    def test_get_countries(
        self, financial_calendar_service: FinancialCalendarService, sample_country
    ):
        """测试获取国家列表"""
        result = financial_calendar_service.get_countries()

        assert len(result) >= 1
        assert any(country.country_code == "US" for country in result)

    def test_get_event_types(
        self, financial_calendar_service: FinancialCalendarService, sample_event_type
    ):
        """测试获取事件类型列表"""
        result = financial_calendar_service.get_event_types()

        assert len(result) >= 1
        assert any(event_type.type_code == "employment_data" for event_type in result)

    def test_search_events(
        self,
        financial_calendar_service: FinancialCalendarService,
        sample_country,
        sample_event_type,
    ):
        """测试搜索事件"""
        # 创建事件
        event_data = FinancialEventCreate(
            event_title="美国非农就业人数",
            event_title_en="US Non-Farm Payrolls",
            event_type_id=sample_event_type.id,
            country_id=sample_country.id,
            scheduled_time=datetime.now(timezone.utc) + timedelta(days=1),
            importance_level=3,
            event_description="重要的经济指标",
        )
        financial_calendar_service.create_financial_event(event_data)

        # 搜索事件
        result = financial_calendar_service.search_events("非农")

        assert len(result) >= 1
        assert any("非农" in event.event_title for event in result)

    def test_get_calendar_summary(
        self,
        financial_calendar_service: FinancialCalendarService,
        sample_country,
        sample_event_type,
    ):
        """测试获取日历摘要"""
        # 创建不同重要性级别的事件
        base_time = datetime.now(timezone.utc)
        start_date = base_time
        end_date = base_time + timedelta(days=7)

        for i in range(3):
            event_data = FinancialEventCreate(
                event_title=f"测试事件 {i+1}",
                event_type_id=sample_event_type.id,
                country_id=sample_country.id,
                scheduled_time=base_time + timedelta(days=i + 1),
                importance_level=(i % 3) + 1,
            )
            financial_calendar_service.create_financial_event(event_data)

        # 获取摘要
        result = financial_calendar_service.get_calendar_summary(start_date, end_date)

        assert "total_events" in result
        assert "importance_distribution" in result
        assert "top_countries" in result
        assert "top_event_types" in result
        assert result["total_events"] == 3
