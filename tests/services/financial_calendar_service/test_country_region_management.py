"""
财经日历服务国家地区管理测试
测试国家地区相关的CRUD操作
"""

from datetime import time
from decimal import Decimal

import pytest
from sqlalchemy.orm import Session

from src.services.financial_calendar_service.models import (CountryRegion,
                                                            EconomicIndicator,
                                                            EventType,
                                                            FinancialEvent)
from src.services.financial_calendar_service.schemas import (
    CountryRegionCreate, CountryRegionUpdate)
from src.services.financial_calendar_service.service import \
    FinancialCalendarService


@pytest.fixture
def financial_calendar_service(db_session: Session):
    """创建财经日历服务实例"""
    return FinancialCalendarService(db_session)


@pytest.fixture
def sample_country_data():
    """示例国家地区数据"""
    return CountryRegionCreate(
        country_code="US",
        country_name="美国",
        country_name_en="United States",
        region="North America",
        flag_icon="us.png",
        time_zone="America/New_York",
        dst_support=True,
        primary_currency="USD",
        currency_symbol="$",
        exchange_rate_to_usd=Decimal("1.0"),
        global_importance_score=Decimal("1.0"),
        financial_market_weight=Decimal("1.0"),
        typical_data_release_time=time(8, 30),
        market_open_time=time(9, 30),
        market_close_time=time(16, 0),
        data_quality_score=Decimal("0.95"),
    )


class TestCountryRegionManagement:
    """国家地区管理测试类"""

    def test_create_country_region_success(
        self,
        financial_calendar_service: FinancialCalendarService,
        sample_country_data: CountryRegionCreate,
    ):
        """测试成功创建国家地区"""
        # 创建国家地区
        result = financial_calendar_service.create_country_region(sample_country_data)

        # 验证结果
        assert result.id is not None
        assert result.country_code == "US"
        assert result.country_name == "美国"
        assert result.country_name_en == "United States"
        assert result.region == "North America"
        assert result.time_zone == "America/New_York"
        assert result.dst_support is True
        assert result.primary_currency == "USD"
        assert result.currency_symbol == "$"
        assert result.global_importance_score == Decimal("1.0")
        assert result.is_active is True
        assert result.created_at is not None

    def test_create_country_region_duplicate_code(
        self,
        financial_calendar_service: FinancialCalendarService,
        sample_country_data: CountryRegionCreate,
    ):
        """测试创建重复国家代码的国家地区"""
        # 创建第一个国家
        financial_calendar_service.create_country_region(sample_country_data)

        # 尝试创建相同代码的国家，应该失败
        with pytest.raises(ValueError, match="Country with code US already exists"):
            financial_calendar_service.create_country_region(sample_country_data)

    def test_get_country_region_success(
        self,
        financial_calendar_service: FinancialCalendarService,
        sample_country_data: CountryRegionCreate,
    ):
        """测试成功获取国家地区"""
        # 创建国家地区
        created_country = financial_calendar_service.create_country_region(
            sample_country_data
        )

        # 获取国家地区
        result = financial_calendar_service.get_country_region(created_country.id)

        # 验证结果
        assert result is not None
        assert result.id == created_country.id
        assert result.country_code == "US"
        assert result.country_name == "美国"

    def test_get_country_region_not_found(
        self, financial_calendar_service: FinancialCalendarService
    ):
        """测试获取不存在的国家地区"""
        result = financial_calendar_service.get_country_region(99999)
        assert result is None

    def test_update_country_region_success(
        self,
        financial_calendar_service: FinancialCalendarService,
        sample_country_data: CountryRegionCreate,
    ):
        """测试成功更新国家地区"""
        # 创建国家地区
        created_country = financial_calendar_service.create_country_region(
            sample_country_data
        )

        # 更新数据
        update_data = CountryRegionUpdate(
            country_name="美利坚合众国",
            region="Americas",
            global_importance_score=Decimal("0.95"),
            data_quality_score=Decimal("0.98"),
        )

        # 执行更新
        result = financial_calendar_service.update_country_region(
            created_country.id, update_data
        )

        # 验证结果
        assert result is not None
        assert result.id == created_country.id
        assert result.country_name == "美利坚合众国"
        assert result.region == "Americas"
        assert result.global_importance_score == Decimal("0.95")
        assert result.data_quality_score == Decimal("0.98")
        # 未更新的字段应保持不变
        assert result.country_code == "US"
        assert result.primary_currency == "USD"

    def test_update_country_region_not_found(
        self, financial_calendar_service: FinancialCalendarService
    ):
        """测试更新不存在的国家地区"""
        update_data = CountryRegionUpdate(country_name="不存在的国家")
        result = financial_calendar_service.update_country_region(99999, update_data)
        assert result is None

    def test_delete_country_region_success(
        self,
        financial_calendar_service: FinancialCalendarService,
        sample_country_data: CountryRegionCreate,
    ):
        """测试成功删除国家地区"""
        # 创建国家地区
        created_country = financial_calendar_service.create_country_region(
            sample_country_data
        )

        # 删除国家地区
        result = financial_calendar_service.delete_country_region(created_country.id)

        # 验证删除成功
        assert result is True

        # 验证国家地区已不存在
        deleted_country = financial_calendar_service.get_country_region(
            created_country.id
        )
        assert deleted_country is None

    def test_delete_country_region_not_found(
        self, financial_calendar_service: FinancialCalendarService
    ):
        """测试删除不存在的国家地区"""
        result = financial_calendar_service.delete_country_region(99999)
        assert result is False

    def test_delete_country_region_with_associated_events(
        self,
        db_session: Session,
        financial_calendar_service: FinancialCalendarService,
        sample_country_data: CountryRegionCreate,
    ):
        """测试删除有关联财经事件的国家地区"""
        # 创建国家地区
        created_country = financial_calendar_service.create_country_region(
            sample_country_data
        )

        # 创建事件类型
        event_type = EventType(
            type_code="employment", type_name="就业数据", category="economic_indicator"
        )
        db_session.add(event_type)
        db_session.commit()
        db_session.refresh(event_type)

        # 创建财经事件
        from datetime import datetime, timezone

        financial_event = FinancialEvent(
            event_title="测试事件",
            event_type_id=event_type.id,
            country_id=created_country.id,
            scheduled_time=datetime.now(timezone.utc),
            importance_level=2,
        )
        db_session.add(financial_event)
        db_session.commit()

        # 尝试删除国家地区，应该失败
        with pytest.raises(
            ValueError, match="Cannot delete country with associated financial events"
        ):
            financial_calendar_service.delete_country_region(created_country.id)

    def test_delete_country_region_with_associated_indicators(
        self,
        db_session: Session,
        financial_calendar_service: FinancialCalendarService,
        sample_country_data: CountryRegionCreate,
    ):
        """测试删除有关联经济指标的国家地区"""
        # 创建国家地区
        created_country = financial_calendar_service.create_country_region(
            sample_country_data
        )

        # 创建事件类型
        event_type = EventType(
            type_code="employment", type_name="就业数据", category="economic_indicator"
        )
        db_session.add(event_type)
        db_session.commit()
        db_session.refresh(event_type)

        # 创建经济指标
        economic_indicator = EconomicIndicator(
            indicator_code="NFP",
            indicator_name="非农就业人数",
            country_id=created_country.id,
            event_type_id=event_type.id,
        )
        db_session.add(economic_indicator)
        db_session.commit()

        # 尝试删除国家地区，应该失败
        with pytest.raises(
            ValueError,
            match="Cannot delete country with associated economic indicators",
        ):
            financial_calendar_service.delete_country_region(created_country.id)

    def test_get_countries_active_only(
        self, db_session: Session, financial_calendar_service: FinancialCalendarService
    ):
        """测试只获取活跃的国家地区"""
        # 创建活跃国家
        active_country = CountryRegion(
            country_code="US",
            country_name="美国",
            time_zone="America/New_York",
            primary_currency="USD",
            is_active=True,
        )
        db_session.add(active_country)

        # 创建非活跃国家
        inactive_country = CountryRegion(
            country_code="XX",
            country_name="非活跃国家",
            time_zone="UTC",
            primary_currency="XXX",
            is_active=False,
        )
        db_session.add(inactive_country)
        db_session.commit()

        # 只获取活跃国家
        active_countries = financial_calendar_service.get_countries(active_only=True)
        assert len(active_countries) == 1
        assert active_countries[0].country_code == "US"

        # 获取所有国家
        all_countries = financial_calendar_service.get_countries(active_only=False)
        assert len(all_countries) == 2

    def test_get_countries_ordered_by_name(
        self, db_session: Session, financial_calendar_service: FinancialCalendarService
    ):
        """测试国家地区按名称排序"""
        # 创建多个国家
        countries_data = [
            ("CN", "中国", "Asia/Shanghai", "CNY"),
            ("US", "美国", "America/New_York", "USD"),
            ("JP", "日本", "Asia/Tokyo", "JPY"),
        ]

        for code, name, tz, currency in countries_data:
            country = CountryRegion(
                country_code=code,
                country_name=name,
                time_zone=tz,
                primary_currency=currency,
            )
            db_session.add(country)

        db_session.commit()

        # 获取国家列表
        countries = financial_calendar_service.get_countries()

        # 验证排序
        country_names = [country.country_name for country in countries]
        assert country_names == ["中国", "日本", "美国"]  # 按中文名称排序

    def test_create_country_region_minimal_data(
        self, financial_calendar_service: FinancialCalendarService
    ):
        """测试使用最少必需数据创建国家地区"""
        minimal_data = CountryRegionCreate(
            country_code="GB",
            country_name="英国",
            time_zone="Europe/London",
            primary_currency="GBP",
        )

        result = financial_calendar_service.create_country_region(minimal_data)

        # 验证结果
        assert result.id is not None
        assert result.country_code == "GB"
        assert result.country_name == "英国"
        assert result.time_zone == "Europe/London"
        assert result.primary_currency == "GBP"
        # 验证默认值
        assert result.dst_support is False
        assert result.global_importance_score == Decimal("1.0")
        assert result.financial_market_weight == Decimal("1.0")
        assert result.data_quality_score == Decimal("0.8")
        assert result.is_active is True

    def test_update_country_region_partial_update(
        self, financial_calendar_service: FinancialCalendarService
    ):
        """测试部分更新国家地区"""
        # 创建国家地区
        create_data = CountryRegionCreate(
            country_code="CA",
            country_name="加拿大",
            time_zone="America/Toronto",
            primary_currency="CAD",
            global_importance_score=Decimal("0.8"),
        )
        created_country = financial_calendar_service.create_country_region(create_data)

        # 只更新一个字段
        update_data = CountryRegionUpdate(global_importance_score=Decimal("0.9"))

        result = financial_calendar_service.update_country_region(
            created_country.id, update_data
        )

        # 验证结果
        assert result is not None
        assert result.global_importance_score == Decimal("0.9")
        # 其他字段保持不变
        assert result.country_code == "CA"
        assert result.country_name == "加拿大"
        assert result.time_zone == "America/Toronto"
        assert result.primary_currency == "CAD"
