"""
主应用测试模块
测试FastAPI应用的核心功能
"""

import pytest
from fastapi.testclient import TestClient


class TestHealthCheck:
    """健康检查测试"""

    def test_health_check(self, test_client: TestClient):
        """测试健康检查接口"""
        response = test_client.get("/health")
        assert response.status_code == 200

        data = response.json()
        assert "status" in data
        assert data["status"] == "ok"
        assert "environment" in data
        assert "version" in data

    def test_invalid_endpoint(self, test_client: TestClient):
        """测试无效端点"""
        response = test_client.get("/nonexistent")
        assert response.status_code == 404


class TestAPIVersioning:
    """API版本测试"""

    def test_api_prefix(self, test_client: TestClient):
        """测试API前缀"""
        # 测试不带前缀的请求应该返回404
        response = test_client.get("/users/")
        assert response.status_code == 404


class TestAPIDocumentation:
    """API文档测试"""

    def test_api_docs_access(self, test_client: TestClient):
        """测试API文档访问"""
        response = test_client.get("/docs")
        # 在DEBUG模式下应该能访问文档
        assert response.status_code in [200, 404]  # 404表示生产环境不可访问


class TestCORS:
    """CORS跨域测试"""

    def test_cors_headers(self, test_client: TestClient):
        """测试CORS头部"""
        response = test_client.options("/health")
        # 检查是否有CORS相关头部
        assert response.status_code in [200, 405]  # 405是方法不允许
