"""
统一的测试配置文件
包含所有测试需要的fixture和配置
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = str(Path(__file__).parent.parent)
sys.path.insert(0, project_root)

# 必须在导入任何其他模块之前设置测试环境
os.environ["ENVIRONMENT"] = "testing"
os.environ["DATABASE_URL"] = "sqlite:///:memory:"

import json
import uuid
from datetime import datetime, timezone
from typing import Generator
from unittest.mock import Mock

import pytest
from fastapi.testclient import TestClient
from sqlalchemy import (BIGINT, DECIMAL, Boolean, DateTime, Integer, String,
                        Text, Time, TypeDecorator, create_engine)
from sqlalchemy.dialects import sqlite
from sqlalchemy.dialects.postgresql import ARRAY, JSONB
from sqlalchemy.orm import Session, sessionmaker
from sqlalchemy.pool import StaticPool
from sqlalchemy.types import UserDefinedType


# 重要：修补SQLite对PostgreSQL类型的支持
# 必须在导入模型之前完成
class SQLiteJSONB(TypeDecorator):
    """SQLite环境下的JSONB类型适配器"""

    impl = Text
    cache_ok = True

    def process_bind_param(self, value, dialect):
        if value is not None:
            return json.dumps(value, ensure_ascii=False, default=str)
        return value

    def process_result_value(self, value, dialect):
        if value is not None:
            try:
                return json.loads(value)
            except (json.JSONDecodeError, TypeError):
                return value
        return value


# 在SQLite方言中添加PostgreSQL类型支持
sqlite.base.SQLiteTypeCompiler.visit_JSONB = lambda self, type_, **kw: "TEXT"
sqlite.base.SQLiteTypeCompiler.visit_ARRAY = lambda self, type_, **kw: "TEXT"
sqlite.base.SQLiteTypeCompiler.visit_BIGINT = lambda self, type_, **kw: "INTEGER"

# 替换JSONB类型以便在SQLite中工作
import sys

from sqlalchemy.dialects import postgresql

# 备份原始JSONB
_original_jsonb = postgresql.JSONB

# 在测试环境中替换JSONB
postgresql.JSONB = SQLiteJSONB

# 确保所有导入都使用替换后的类型
sys.modules["sqlalchemy.dialects.postgresql"].JSONB = SQLiteJSONB

from src.core.database import Base
# 导入所有模型以确保测试能够创建所有表
from src.core.models import *
from src.main import app
from src.services.sms_service.service import SmsService
from src.services.user_service.service import RedisService, UserService


class JSONBText(TypeDecorator):
    """
    JSONB类型适配器，在SQLite中使用Text存储JSON数据
    这样可以保持代码中的JSONB类型不变，只在测试环境中进行适配
    """

    impl = Text
    cache_ok = True

    def process_bind_param(self, value, dialect):
        if value is not None:
            return json.dumps(value)
        return value

    def process_result_value(self, value, dialect):
        if value is not None:
            try:
                return json.loads(value)
            except (json.JSONDecodeError, TypeError):
                return value
        return value


# 使用内存数据库进行测试
SQLALCHEMY_DATABASE_URL = "sqlite:///:memory:"
engine = create_engine(
    SQLALCHEMY_DATABASE_URL,
    connect_args={"check_same_thread": False, "timeout": 20},  # 允许多线程访问
    echo=False,  # 在测试中关闭SQL日志
    poolclass=StaticPool,  # 使用StaticPool确保同一个连接
    pool_recycle=-1,  # 禁用连接回收
    pool_pre_ping=True,  # 连接前检查
)

TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


@pytest.fixture(scope="function")
def db_session() -> Generator[Session, None, None]:
    """创建数据库会话，每个测试函数都会创建新的数据库"""
    # 为每个测试创建全新的表
    Base.metadata.create_all(bind=engine)

    session = TestingSessionLocal()
    try:
        yield session
    finally:
        session.close()
        # 测试完成后删除所有表
        Base.metadata.drop_all(bind=engine)


@pytest.fixture
def test_client() -> TestClient:
    """创建测试客户端"""
    return TestClient(app)


@pytest.fixture
def client(db_session: Session) -> TestClient:
    """
    创建测试客户端，并覆盖数据库依赖
    """
    from src.core.database import get_db

    def override_get_db():
        try:
            # 使用同一个session实例，避免多线程问题
            yield db_session
        finally:
            pass

    # 创建一个跳过lifespan的测试客户端，避免在测试时连接production数据库
    from fastapi import FastAPI

    from src.services.financial_calendar_service.router import \
        router as financial_calendar_router
    from src.services.tag_classification_service.router import \
        router as tag_classification_router
    from src.services.user_service.router import router as user_router

    # 创建测试专用的应用，不带lifespan
    test_app = FastAPI(title="Test App", version="1.0.0", description="Test API")

    # 注册路由
    test_app.include_router(user_router, tags=["用户管理"])
    test_app.include_router(tag_classification_router, tags=["标签分类"])
    test_app.include_router(financial_calendar_router, tags=["财经日历"])

    # 覆盖数据库依赖
    test_app.dependency_overrides[get_db] = override_get_db

    with TestClient(test_app) as test_client:
        yield test_client

    # 清理依赖覆盖
    test_app.dependency_overrides.clear()


@pytest.fixture
def authenticated_client(client: TestClient, db_session: Session) -> TestClient:
    """
    创建已认证的测试客户端
    跳过实际的认证逻辑，直接设置依赖
    """
    from src.services.user_service.dependencies import get_current_user
    from src.services.user_service.models import User

    # 创建测试用户
    test_user = User(
        id=1,
        username="testuser",
        email="<EMAIL>",
        phone="13800138000",
        password_hash="hashed_password",
        is_active=True,
        is_verified=True,
    )
    db_session.add(test_user)
    db_session.commit()
    db_session.refresh(test_user)

    # 覆盖认证依赖
    def override_get_current_user():
        return test_user

    # 在客户端的应用上设置依赖覆盖
    client.app.dependency_overrides[get_current_user] = override_get_current_user

    return client


@pytest.fixture
def mock_redis_service() -> Mock:
    """创建模拟Redis服务"""
    mock_redis = Mock(spec=RedisService)
    mock_redis.set_value.return_value = True
    mock_redis.get_value.return_value = "123456"
    mock_redis.delete_key.return_value = True
    mock_redis.increment_value.return_value = 1
    mock_redis.get_or_set.return_value = "cached_value"
    mock_redis.get_all_keys.return_value = ["key1", "key2", "key3"]
    mock_redis.set_hash.return_value = True
    mock_redis.get_hash.return_value = "hash_value"
    mock_redis.get_all_hash_fields.return_value = {
        "field1": "value1",
        "field2": "value2",
    }
    return mock_redis


@pytest.fixture
def mock_sms_service() -> Mock:
    """创建模拟SMS服务"""
    mock_sms = Mock(spec=SmsService)
    mock_sms.send_verification_code.return_value = Mock(
        success=True, message="验证码发送成功"
    )
    return mock_sms


@pytest.fixture
def unique_phone() -> str:
    """生成唯一的手机号"""
    import random
    import time

    # 生成以138开头的11位手机号，后8位随机数字
    suffix = str(int(time.time() * 1000))[-8:]
    return f"138{suffix}"


@pytest.fixture
def unique_email() -> str:
    """生成唯一的邮箱地址"""
    return f"test_{uuid.uuid4().hex[:8]}@example.com"


@pytest.fixture
def unique_username() -> str:
    """生成唯一的用户名"""
    return f"user_{uuid.uuid4().hex[:8]}"


# 测试环境配置
@pytest.fixture(autouse=True)
def setup_test_env():
    """自动设置测试环境变量"""
    # 环境变量已在文件开头设置
    yield
    # 测试运行完成后的清理


# AI服务测试fixtures
@pytest.fixture
def sample_ai_model(db_session):
    """创建示例AI模型"""
    from src.services.ai_service.models import AIModel, ModelType, ModelProvider, ModelStatus
    
    model = AIModel(
        model_name="test-deepseek-chat",
        display_name="测试DeepSeek模型",
        description="用于测试的DeepSeek聊天模型",
        model_type=ModelType.LLM,
        provider=ModelProvider.DEEPSEEK,
        model_version="v1.0",
        api_endpoint="https://api.deepseek.com/v1/chat/completions",
        api_key_name="DEEPSEEK_API_KEY",
        model_params={"context_length": 32768, "max_tokens": 4096},
        status=ModelStatus.ACTIVE
    )
    
    db_session.add(model)
    db_session.commit()
    db_session.refresh(model)
    return model

@pytest.fixture
def sample_model_metrics(db_session, sample_ai_model):
    """创建示例模型指标"""
    from src.services.ai_service.models import ModelMetrics
    from decimal import Decimal
    
    metrics = ModelMetrics(
        model_id=sample_ai_model.id,
        total_requests=100,
        successful_requests=95,
        failed_requests=5,
        avg_response_time=Decimal("1.25"),
        avg_cost_per_request=Decimal("0.001"),
        date=datetime.utcnow().date()
    )
    
    db_session.add(metrics)
    db_session.commit()
    db_session.refresh(metrics)
    return metrics

# 信息处理服务测试fixtures
@pytest.fixture
def sample_analysis_request(db_session):
    """创建示例分析请求"""
    from src.services.information_processing_service.models import (
        AnalysisRequest, AnalysisType, AnalysisStatus
    )
    
    request = AnalysisRequest(
        request_id="test-request-001",
        content="苹果公司发布了新的人工智能产品，这是一个重大突破。",
        analysis_types=[AnalysisType.ENTITY_EXTRACTION, AnalysisType.SENTIMENT_ANALYSIS],
        source_type="web",
        source_url="https://example.com/article/123",
        status=AnalysisStatus.PENDING,
        extra_metadata={"title": "AI产品发布", "author": "测试作者"}
    )
    
    db_session.add(request)
    db_session.commit()
    db_session.refresh(request)
    return request

@pytest.fixture
def sample_analysis_result(db_session, sample_analysis_request):
    """创建示例分析结果"""
    from src.services.information_processing_service.models import (
        AnalysisResult, AnalysisType
    )
    
    result_data = {
        "entities": [
            {"text": "苹果公司", "type": "ORGANIZATION", "confidence": 0.95},
            {"text": "人工智能", "type": "TECHNOLOGY", "confidence": 0.88}
        ],
        "total_entities": 2
    }
    
    result = AnalysisResult(
        request_id=sample_analysis_request.id,
        analysis_type=AnalysisType.ENTITY_EXTRACTION,
        result_data=result_data,
        confidence_score=0.91,
        processing_time_ms=1200,
        model_version="deepseek-v1.0"
    )
    
    db_session.add(result)
    db_session.commit()
    db_session.refresh(result)
    return result
