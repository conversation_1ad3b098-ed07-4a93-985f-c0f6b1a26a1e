#!/usr/bin/env python3
"""
AI处理功能测试用例
"""

import pytest
import asyncio
import os
import sys
from unittest.mock import AsyncMock, MagicMock

# 添加项目路径
sys.path.insert(0, '/home/<USER>/workspace/app_finsight_backend')
sys.path.insert(0, '/home/<USER>/workspace/app_finsight_backend/src')

# 设置环境变量
os.environ.setdefault('ENVIRONMENT', 'development')

from src.services.data_processing_service.ai_services import AIContentAnalyzer
from src.core.database import SessionLocal
from src.services.data_processing_service.models import UnifiedContentTags, UnifiedContentClassifications


class TestAIContentAnalyzer:
    """AI内容分析器测试类"""

    @pytest.fixture
    def analyzer(self):
        """创建AI内容分析器实例"""
        return AIContentAnalyzer()

    @pytest.fixture
    def sample_content(self):
        """测试用的内容数据"""
        return {
            "title": "央行宣布降准政策，释放流动性支持实体经济",
            "content": "中国人民银行今日宣布，决定于2024年1月15日下调金融机构存款准备金率0.5个百分点。此次降准将释放长期资金约1万亿元，主要用于支持小微企业和民营企业发展，促进实体经济健康发展。",
            "content_type": "flash_news",
            "content_id": 8888  # 测试用的ID
        }

    @pytest.fixture
    def ai_config(self):
        """AI分析配置"""
        return {
            "generate_summary": True,
            "extract_tags": True,
            "classify_content": True,
            "extract_entities": True,
            "sentiment_analysis": True,
            "importance_scoring": True,
            "tag_confidence_threshold": 0.6,
            "classification_confidence_threshold": 0.7
        }

    @pytest.mark.asyncio
    async def test_tag_extraction(self, analyzer, sample_content):
        """测试标签提取功能"""
        # 使用AI客户端提取标签
        async with analyzer.ai_client as client:
            tags = await analyzer._extract_tags_with_client(client, sample_content)
            
            # 验证标签提取结果
            assert isinstance(tags, list)
            assert len(tags) > 0
            
            # 验证标签内容
            expected_tags = ["中国人民银行", "降准", "存款准备金率", "流动性", "实体经济"]
            for expected_tag in expected_tags:
                assert any(expected_tag in tag for tag in tags), f"未找到预期标签: {expected_tag}"

    @pytest.mark.asyncio
    async def test_classification(self, analyzer, sample_content):
        """测试分类功能"""
        # 使用AI客户端进行分类
        async with analyzer.ai_client as client:
            classifications = await analyzer._classify_content_with_client(client, sample_content)
            
            # 验证分类结果
            assert isinstance(classifications, dict)
            assert len(classifications) > 0
            
            # 验证分类内容
            expected_classifications = {
                "primary_category": "monetary_policy",
                "impact_scope": "domestic",
                "urgency_level": "high"
            }
            
            for key, expected_value in expected_classifications.items():
                assert key in classifications, f"未找到分类维度: {key}"

    @pytest.mark.asyncio
    async def test_tag_matching(self, analyzer):
        """测试标签匹配功能"""
        test_tags = ["中国人民银行", "降准政策", "流动性", "货币政策", "实体经济"]
        
        matched_tags = await analyzer.match_tags_only(test_tags, confidence_threshold=0.6)
        
        # 验证匹配结果
        assert isinstance(matched_tags, list)
        assert len(matched_tags) > 0
        
        # 验证匹配结果结构
        for tag_match in matched_tags:
            assert "ai_tag" in tag_match
            assert "standard_tag_id" in tag_match
            assert "confidence" in tag_match
            assert "match_method" in tag_match
            assert tag_match["confidence"] >= 0.6

    @pytest.mark.asyncio
    async def test_classification_matching(self, analyzer):
        """测试分类匹配功能"""
        test_classifications = {
            "primary_category": "monetary_policy",
            "impact_scope": "domestic",
            "urgency_level": "high",
            "industry": "banking"
        }
        
        matched_classifications = await analyzer.match_classifications_only(
            test_classifications, confidence_threshold=0.7
        )
        
        # 验证匹配结果
        assert isinstance(matched_classifications, list)
        assert len(matched_classifications) > 0
        
        # 验证匹配结果结构
        for classification_match in matched_classifications:
            assert "dimension_key" in classification_match
            assert "ai_classification_value" in classification_match
            assert "dimension_id" in classification_match
            assert "value_id" in classification_match
            assert "confidence" in classification_match
            assert "match_method" in classification_match
            assert classification_match["confidence"] >= 0.7

    @pytest.mark.asyncio
    async def test_full_ai_processing(self, analyzer, sample_content, ai_config):
        """测试完整AI处理流程"""
        # 执行完整AI分析
        result = await analyzer.analyze_content(sample_content, ai_config)
        
        # 验证分析结果
        assert isinstance(result, dict)
        
        # 验证必要字段
        required_fields = [
            "summary", "ai_extracted_tags", "matched_tag_count",
            "ai_classifications", "matched_classification_count",
            "sentiment_analysis", "importance_score"
        ]
        
        for field in required_fields:
            assert field in result, f"缺少必要字段: {field}"
        
        # 验证标签处理结果
        assert isinstance(result["ai_extracted_tags"], list)
        assert result["matched_tag_count"] > 0
        
        # 验证分类处理结果
        assert isinstance(result["ai_classifications"], dict)
        assert result["matched_classification_count"] > 0
        
        # 验证情感分析结果
        assert isinstance(result["sentiment_analysis"], dict)
        assert "sentiment" in result["sentiment_analysis"]
        
        # 验证重要性评分
        assert isinstance(result["importance_score"], (int, float))
        assert 0 <= result["importance_score"] <= 1

    @pytest.mark.asyncio
    async def test_database_persistence(self, analyzer, sample_content, ai_config):
        """测试数据库持久化功能"""
        # 清理之前的测试数据
        await self._cleanup_test_data(sample_content["content_type"], sample_content["content_id"])
        
        # 执行完整AI分析
        result = await analyzer.analyze_content(sample_content, ai_config)
        
        # 验证数据库中的记录
        db = SessionLocal()
        try:
            # 检查标签关联
            tag_records = db.query(UnifiedContentTags).filter(
                UnifiedContentTags.content_type == sample_content["content_type"],
                UnifiedContentTags.content_id == sample_content["content_id"]
            ).all()
            
            assert len(tag_records) > 0, "未找到标签关联记录"
            assert len(tag_records) == result["matched_tag_count"], "标签关联记录数量不匹配"
            
            # 检查分类关联
            classification_records = db.query(UnifiedContentClassifications).filter(
                UnifiedContentClassifications.content_type == sample_content["content_type"],
                UnifiedContentClassifications.content_id == sample_content["content_id"]
            ).all()
            
            assert len(classification_records) > 0, "未找到分类关联记录"
            assert len(classification_records) == result["matched_classification_count"], "分类关联记录数量不匹配"
            
            # 验证记录字段
            for record in tag_records:
                assert record.source == "ai"
                assert record.confidence_score > 0
                
            for record in classification_records:
                assert record.source == "ai"
                assert record.confidence_score > 0
                
        finally:
            db.close()
            # 清理测试数据
            await self._cleanup_test_data(sample_content["content_type"], sample_content["content_id"])

    async def _cleanup_test_data(self, content_type: str, content_id: int):
        """清理测试数据"""
        db = SessionLocal()
        try:
            # 删除标签关联
            db.query(UnifiedContentTags).filter(
                UnifiedContentTags.content_type == content_type,
                UnifiedContentTags.content_id == content_id
            ).delete()
            
            # 删除分类关联
            db.query(UnifiedContentClassifications).filter(
                UnifiedContentClassifications.content_type == content_type,
                UnifiedContentClassifications.content_id == content_id
            ).delete()
            
            db.commit()
        except Exception:
            db.rollback()
        finally:
            db.close()


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])
