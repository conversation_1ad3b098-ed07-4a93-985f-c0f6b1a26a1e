# FinSight Backend Dependencies

# FastAPI 框架
fastapi==0.111.0
uvicorn[standard]==0.29.0

# 数据库相关
SQLAlchemy==2.0.30
alembic==1.13.1
psycopg2-binary==2.9.9
asyncpg==0.29.0

# Redis
redis==5.0.4
hiredis==2.2.3

# 认证和安全
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart>=0.0.7
cryptography==41.0.7

# 腾讯云服务
tencentcloud-sdk-python==3.0.1240

# 数据验证
pydantic==2.7.1
pydantic-settings==2.2.1

# 环境配置
python-dotenv==1.0.0

# HTTP请求
httpx==0.25.2
aiohttp==3.9.1

# 任务队列
celery==5.3.4
kombu==5.3.4

# 消息队列
kafka-python==2.0.2
aiokafka==0.12.0

# 搜索引擎
elasticsearch==8.11.0

# 文件存储
minio==7.2.0

# 数据爬取
scrapy==2.11.0
scrapy-playwright==0.0.36
playwright==1.40.0
beautifulsoup4==4.12.2
requests==2.31.0
tweepy==4.14.0
feedparser==6.0.10
pymongo==4.6.1
psutil==5.9.8

# 机器学习/NLP
openai==1.3.8
transformers==4.36.0
jieba==0.42.1

# 监控和日志
prometheus-client==0.19.0
prometheus-fastapi-instrumentator==6.1.0
loguru==0.7.2
structlog==23.2.0

# 开发工具
pytest==8.2.1
pytest-asyncio==0.21.1
pytest-mock==3.12.0
pytest-cov==5.0.0
coverage==7.5.0
black==24.4.2
isort==5.13.2
flake8==6.1.0
pylint==3.2.2
safety==3.2.0
chardet==5.2.0

# 日期时间处理
python-dateutil==2.8.2

# 数据处理
pandas==2.1.4
numpy==1.26.2

# 调度任务
apscheduler==3.10.4