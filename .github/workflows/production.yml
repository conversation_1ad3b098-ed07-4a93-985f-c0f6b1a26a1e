name: FinSight Backend Production Pipeline

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:17.3
        env:
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: finsight_test
        ports:
          - 5432:5432
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
      redis:
        image: redis:6
        ports:
          - 6379:6379
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    steps:
    - uses: actions/checkout@v3
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.12.3'
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install flake8 pytest pytest-cov
        if [ -f requirements.txt ]; then pip install -r requirements.txt; fi
    - name: Lint with flake8
      run: |
        # stop the build if there are Python syntax errors or undefined names
        flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
        # exit-zero treats all errors as warnings. The GitHub editor is 127 chars wide
        flake8 . --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics
    - name: Test with pytest
      env:
        DATABASE_URL: postgresql://postgres:postgres@localhost:5432/finsight_test
        REDIS_URL: redis://localhost:6379/0
        JWT_SECRET_KEY: test_secret_key
        JWT_ACCESS_TOKEN_EXPIRE_MINUTES: 30
      run: |
        pytest --cov=./

  deploy_production:
    runs-on: ubuntu-latest
    needs: test
    if: github.ref == 'refs/heads/main'
    environment: production
    steps:
    - uses: actions/checkout@v3
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.12.3'
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
    - name: Deploy to production server
      uses: appleboy/ssh-action@v0.1.4
      with:
        host: ${{ secrets.SERVER_HOST }}
        username: ${{ secrets.SERVER_USERNAME }}
        key: ${{ secrets.SERVER_SSH_KEY }}
        verbose: true
        script: |
          cd /home/<USER>/workspace/app_finsight_backend
          # 备份当前版本
          if [ -d "current" ]; then
            tar -czf backups/backup-prod-$(date +%Y%m%d_%H%M%S).tar.gz current/
          fi
          # 拉取最新代码
          git fetch --all
          git reset --hard origin/main
          # 创建新的虚拟环境
          python3 -m venv venv/finsight
          source venv/finsight/bin/activate
          # 安装依赖
          pip install -r requirements.txt
          # 创建或更新环境变量文件
          if [ ! -f .env.production ]; then
            cp env_example.txt .env.production
          fi
          # 重启服务
          sudo systemctl restart finsight.service
          # 等待服务启动
          sleep 10
          # 检查服务状态
          sudo systemctl status finsight.service

  health_check_production:
    runs-on: ubuntu-latest
    needs: deploy_production
    if: github.ref == 'refs/heads/main'
    steps:
    - name: Wait for application to start
      run: sleep 30
    - name: Check application health
      uses: jtalk/url-health-check-action@v2
      with:
        url:  http://api.lightrain.vip/health
        max-attempts: 3
        retry-delay: 5s

  rollback_production:
    runs-on: ubuntu-latest
    needs: [deploy_production, health_check_production]
    if: failure() && needs.deploy_production.result == 'success' && github.ref == 'refs/heads/main'
    steps:
    - name: Rollback production deployment
      uses: appleboy/ssh-action@v0.1.4
      with:
        host: ${{ secrets.SERVER_HOST }}
        username: ${{ secrets.SERVER_USERNAME }}
        key: ${{ secrets.SERVER_SSH_KEY }}
        script: |
          cd /home/<USER>/workspace/app_finsight_backend
          # 获取最新的生产环境备份
          LATEST_BACKUP=$(ls -t backups/backup-prod-*.tar.gz | head -1)
          if [ -n "$LATEST_BACKUP" ]; then
            # 停止当前服务
            sudo systemctl stop finsight.service
            # 恢复备份
            rm -rf current
            tar -xzf $LATEST_BACKUP
            # 重启服务
            sudo systemctl restart finsight.service
            echo "Production environment rolled back to backup: $LATEST_BACKUP"
          else
            echo "No production backup found for rollback"
            exit 1
          fi
      

  