global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "finsight_rules.yml"

scrape_configs:
  - job_name: 'finsight-api'
    static_configs:
      - targets: ['api:8000']
    metrics_path: '/metrics'
    scrape_interval: 10s
    
  - job_name: 'postgres'
    static_configs:
      - targets: ['db:5432']
    
  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']
      
  - job_name: 'elasticsearch'
    static_configs:
      - targets: ['elasticsearch:9200']

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093 