"""
自定义JSON响应类
确保中文字符在API响应中正确显示，不被转义为Unicode序列
"""

import json
from typing import Any
from fastapi.responses import JSONResponse
from fastapi.encoders import jsonable_encoder


class UTF8JSONResponse(JSONResponse):
    """
    确保中文字符正确显示的JSON响应类
    使用ensure_ascii=False避免Unicode转义
    """
    
    def render(self, content: Any) -> bytes:
        """
        渲染JSON响应内容
        
        Args:
            content: 要序列化的内容
            
        Returns:
            bytes: UTF-8编码的JSON字节串
        """
        # 确保内容可以被JSON序列化
        json_compatible_content = jsonable_encoder(content)
        
        # 使用ensure_ascii=False确保中文字符不被转义
        json_str = json.dumps(
            json_compatible_content,
            ensure_ascii=False,
            allow_nan=False,
            indent=None,
            separators=(",", ":"),
        )
        
        return json_str.encode("utf-8")


class UnicodeJSONResponse(UTF8JSONResponse):
    """
    Unicode友好的JSON响应类
    别名，提供更明确的命名
    """
    pass


def create_utf8_json_response(content: Any, status_code: int = 200, headers: dict = None) -> UTF8JSONResponse:
    """
    创建UTF-8 JSON响应的便捷函数
    
    Args:
        content: 响应内容
        status_code: HTTP状态码
        headers: 响应头
        
    Returns:
        UTF8JSONResponse: 配置好的JSON响应
    """
    return UTF8JSONResponse(
        content=content,
        status_code=status_code,
        headers=headers,
        media_type="application/json; charset=utf-8"
    ) 