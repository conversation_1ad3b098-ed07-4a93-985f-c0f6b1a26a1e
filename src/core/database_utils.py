"""
数据库工具类
提供数据库操作的工具函数，包含JSON序列化配置
"""

import json
from typing import Any, Dict, List, Optional
from sqlalchemy.types import TypeDecorator, Text
from sqlalchemy.dialects.postgresql import JSONB
import logging

logger = logging.getLogger(__name__)


class UTF8JSONB(TypeDecorator):
    """
    确保中文字符正确存储的JSONB类型
    自动处理JSON序列化时的中文编码问题
    """
    
    impl = JSONB
    cache_ok = True
    
    def process_bind_param(self, value, dialect):
        """
        将Python对象转换为数据库存储格式
        确保中文字符不被转义
        """
        if value is not None:
            # 使用ensure_ascii=False确保中文字符不被转义
            # 使用separators减少不必要的空格
            return json.dumps(
                value, 
                ensure_ascii=False, 
                separators=(',', ':'),
                default=str
            )
        return value
    
    def process_result_value(self, value, dialect):
        """
        将数据库存储格式转换为Python对象
        """
        if value is not None:
            if isinstance(value, str):
                try:
                    return json.loads(value)
                except (json.JSONDecodeError, TypeError) as e:
                    logger.warning(f"JSON解析失败: {e}, 原始值: {value}")
                    return value
            else:
                # 如果已经是Python对象，直接返回
                return value
        return value


def ensure_utf8_json_serialization(data: Any) -> str:
    """
    确保JSON序列化时中文字符不被转义
    
    Args:
        data: 要序列化的数据
        
    Returns:
        str: JSON字符串
    """
    return json.dumps(
        data,
        ensure_ascii=False,
        separators=(',', ':'),
        default=str
    )


def safe_json_loads(json_str: str) -> Any:
    """
    安全的JSON反序列化
    
    Args:
        json_str: JSON字符串
        
    Returns:
        反序列化的对象，失败返回原字符串
    """
    if not json_str:
        return None
        
    if not isinstance(json_str, str):
        return json_str
        
    try:
        return json.loads(json_str)
    except (json.JSONDecodeError, TypeError) as e:
        logger.warning(f"JSON解析失败: {e}, 原始值: {json_str}")
        return json_str


def fix_unicode_json_fields(data: Dict[str, Any], fields: List[str]) -> Dict[str, Any]:
    """
    修复JSON字段中的Unicode编码问题
    
    Args:
        data: 包含JSON字段的数据字典
        fields: 需要修复的字段名列表
        
    Returns:
        修复后的数据字典
    """
    if not data:
        return data
        
    result = data.copy()
    
    for field in fields:
        if field in result and result[field]:
            value = result[field]
            
            # 如果是字符串，尝试解析并重新序列化
            if isinstance(value, str):
                try:
                    # 先解析为Python对象
                    parsed = json.loads(value)
                    # 重新序列化，确保中文字符不被转义
                    result[field] = ensure_utf8_json_serialization(parsed)
                except (json.JSONDecodeError, TypeError):
                    # 解析失败，保持原值
                    pass
            elif isinstance(value, (dict, list)):
                # 如果已经是Python对象，重新序列化
                result[field] = ensure_utf8_json_serialization(value)
    
    return result


def decode_unicode_escapes(text: str) -> str:
    """
    解码Unicode转义序列
    
    Args:
        text: 包含Unicode转义序列的文本
        
    Returns:
        解码后的文本
    """
    if not text or not isinstance(text, str):
        return text
        
    try:
        # 解码Unicode转义序列
        return text.encode().decode('unicode_escape')
    except (UnicodeDecodeError, UnicodeEncodeError) as e:
        logger.warning(f"Unicode解码失败: {e}, 原始文本: {text}")
        return text


def fix_json_unicode_in_database():
    """
    修复数据库中已存在的Unicode编码问题
    这是一个一次性的修复脚本
    """
    from sqlalchemy import create_engine, text
    from ..core.config import settings
    
    logger.info("开始修复数据库中的Unicode编码问题")
    
    engine = create_engine(settings.DATABASE_URL)
    
    try:
        with engine.connect() as conn:
            # 修复processed_contents表中的JSON字段
            logger.info("修复processed_contents表...")
            
            # 获取所有需要修复的记录
            result = conn.execute(text("""
                SELECT id, ai_tags, ai_categories, entities, keywords 
                FROM processed_contents 
                WHERE ai_tags IS NOT NULL 
                   OR ai_categories IS NOT NULL 
                   OR entities IS NOT NULL 
                   OR keywords IS NOT NULL
            """))
            
            records = result.fetchall()
            logger.info(f"找到 {len(records)} 条需要检查的记录")
            
            fixed_count = 0
            
            for record in records:
                record_id = record[0]
                updates = []
                params = {'id': record_id}
                
                # 检查并修复每个JSON字段
                for i, field_name in enumerate(['ai_tags', 'ai_categories', 'entities', 'keywords'], 1):
                    field_value = record[i]
                    
                    if field_value and isinstance(field_value, str):
                        # 检查是否包含Unicode转义序列
                        if '\\u' in field_value:
                            try:
                                # 解析并重新序列化
                                parsed = json.loads(field_value)
                                fixed_value = ensure_utf8_json_serialization(parsed)
                                
                                updates.append(f"{field_name} = :{field_name}")
                                params[field_name] = fixed_value
                                
                                logger.debug(f"修复记录 {record_id} 的 {field_name} 字段")
                                
                            except (json.JSONDecodeError, TypeError) as e:
                                logger.warning(f"记录 {record_id} 的 {field_name} 字段解析失败: {e}")
                
                # 如果有需要更新的字段，执行更新
                if updates:
                    update_sql = f"""
                        UPDATE processed_contents 
                        SET {', '.join(updates)}
                        WHERE id = :id
                    """
                    
                    conn.execute(text(update_sql), params)
                    fixed_count += 1
            
            # 提交事务
            conn.commit()
            
            logger.info(f"修复完成，共修复 {fixed_count} 条记录")
            
    except Exception as e:
        logger.error(f"修复数据库Unicode编码问题失败: {e}")
        raise 