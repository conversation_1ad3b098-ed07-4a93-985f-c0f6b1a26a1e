"""
统一任务执行框架
提供统一的任务接口、执行模式和管理功能
"""

import asyncio
import logging
from abc import ABC, abstractmethod
from datetime import datetime, timezone
from enum import Enum
from typing import Any, Dict, List, Optional, Type, Union

from celery import current_task
from sqlalchemy.orm import Session

from .celery_app import TaskPriority, celery_app
from .database import SessionLocal

logger = logging.getLogger(__name__)


class TaskExecutionMode(str, Enum):
    """任务执行模式"""
    DIRECT = "direct"  # 直接执行
    ASYNC_QUEUE = "async_queue"  # 异步队列执行
    MIXED = "mixed"  # 混合模式（根据任务特性自动选择）


class TaskType(str, Enum):
    """任务类型"""
    CRAWLER = "crawler"
    DATA_PROCESSING = "data_processing"
    BATCH_PROCESSING = "batch_processing"
    CLEANUP = "cleanup"
    MONITORING = "monitoring"


class TaskStatus(str, Enum):
    """任务状态"""
    PENDING = "pending"
    RUNNING = "running"
    SUCCESS = "success"
    FAILED = "failed"
    RETRY = "retry"
    CANCELLED = "cancelled"


class TaskContext:
    """任务执行上下文"""
    
    def __init__(
        self,
        task_id: str,
        task_type: TaskType,
        execution_mode: TaskExecutionMode,
        parameters: Dict[str, Any],
        priority: int = TaskPriority.NORMAL,
        metadata: Optional[Dict[str, Any]] = None
    ):
        self.task_id = task_id
        self.task_type = task_type
        self.execution_mode = execution_mode
        self.parameters = parameters
        self.priority = priority
        self.metadata = metadata or {}
        self.start_time = None
        self.end_time = None
        self.status = TaskStatus.PENDING
        self.result = None
        self.error = None


class BaseTaskExecutor(ABC):
    """
    基础任务执行器抽象类
    定义所有任务执行器的通用接口
    """
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
    
    @abstractmethod
    async def execute_task(self, context: TaskContext) -> Dict[str, Any]:
        """
        执行任务的抽象方法
        
        Args:
            context: 任务执行上下文
            
        Returns:
            Dict: 任务执行结果
        """
        pass
    
    @abstractmethod
    def validate_parameters(self, parameters: Dict[str, Any]) -> bool:
        """
        验证任务参数的抽象方法
        
        Args:
            parameters: 任务参数
            
        Returns:
            bool: 参数是否有效
        """
        pass
    
    def get_execution_mode(self, parameters: Dict[str, Any]) -> TaskExecutionMode:
        """
        根据参数确定执行模式（可被子类重写）
        
        Args:
            parameters: 任务参数
            
        Returns:
            TaskExecutionMode: 推荐的执行模式
        """
        return TaskExecutionMode.DIRECT
    
    async def setup(self, context: TaskContext):
        """任务执行前的设置工作"""
        context.start_time = datetime.now(timezone.utc)
        context.status = TaskStatus.RUNNING
        self.logger.info(f"开始执行任务 {context.task_id} ({context.task_type})")
    
    async def cleanup(self, context: TaskContext):
        """任务执行后的清理工作"""
        context.end_time = datetime.now(timezone.utc)
        duration = context.end_time - context.start_time if context.start_time else None
        self.logger.info(f"任务 {context.task_id} 执行完成，状态: {context.status}, 耗时: {duration}")


class UnifiedTaskManager:
    """
    统一任务管理器
    负责任务的调度、执行和监控
    """
    
    def __init__(self):
        self.executors: Dict[TaskType, BaseTaskExecutor] = {}
        self.logger = logging.getLogger(__name__)
    
    def register_executor(self, task_type: TaskType, executor: BaseTaskExecutor):
        """
        注册任务执行器
        
        Args:
            task_type: 任务类型
            executor: 任务执行器实例
        """
        self.executors[task_type] = executor
        self.logger.info(f"注册任务执行器: {task_type} -> {executor.__class__.__name__}")
    
    def get_executor(self, task_type: TaskType) -> Optional[BaseTaskExecutor]:
        """
        获取任务执行器
        
        Args:
            task_type: 任务类型
            
        Returns:
            Optional[BaseTaskExecutor]: 执行器实例
        """
        return self.executors.get(task_type)
    
    async def submit_task(
        self,
        task_type: TaskType,
        parameters: Dict[str, Any],
        execution_mode: Optional[TaskExecutionMode] = None,
        priority: int = TaskPriority.NORMAL,
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        提交任务
        
        Args:
            task_type: 任务类型
            parameters: 任务参数
            execution_mode: 执行模式（如果不指定则由执行器决定）
            priority: 任务优先级
            metadata: 任务元数据
            
        Returns:
            str: 任务ID
        """
        executor = self.get_executor(task_type)
        if not executor:
            raise ValueError(f"未找到任务类型 {task_type} 的执行器")
        
        # 验证参数
        if not executor.validate_parameters(parameters):
            raise ValueError(f"任务参数验证失败: {parameters}")
        
        # 确定执行模式
        if execution_mode is None:
            execution_mode = executor.get_execution_mode(parameters)
        
        # 生成任务ID
        task_id = f"{task_type}_{datetime.now(timezone.utc).strftime('%Y%m%d_%H%M%S_%f')}"
        
        # 创建任务上下文
        context = TaskContext(
            task_id=task_id,
            task_type=task_type,
            execution_mode=execution_mode,
            parameters=parameters,
            priority=priority,
            metadata=metadata
        )
        
        # 根据执行模式提交任务
        if execution_mode == TaskExecutionMode.DIRECT:
            # 直接执行（非阻塞）
            asyncio.create_task(self._execute_direct(context))
        elif execution_mode == TaskExecutionMode.ASYNC_QUEUE:
            # 异步队列执行
            await self._execute_async_queue(context)
        else:
            raise ValueError(f"不支持的执行模式: {execution_mode}")
        
        self.logger.info(f"任务已提交: {task_id} (模式: {execution_mode})")
        return task_id
    
    async def _execute_direct(self, context: TaskContext):
        """直接执行任务"""
        executor = self.get_executor(context.task_type)
        if not executor:
            self.logger.error(f"任务执行器不存在: {context.task_type}")
            return
        
        try:
            await executor.setup(context)
            result = await executor.execute_task(context)
            context.result = result
            context.status = TaskStatus.SUCCESS
        except Exception as e:
            context.error = str(e)
            context.status = TaskStatus.FAILED
            self.logger.error(f"任务 {context.task_id} 执行失败: {e}")
        finally:
            await executor.cleanup(context)
    
    async def _execute_async_queue(self, context: TaskContext):
        """通过异步队列执行任务"""
        # 这里将集成Celery任务
        from .task_queue import submit_unified_task
        task_id = submit_unified_task.delay(
            context.task_type.value,
            context.parameters,
            context.priority,
            context.metadata
        ).id
        context.task_id = task_id
        self.logger.info(f"任务已提交到队列: {task_id}")


# 全局任务管理器实例
task_manager = UnifiedTaskManager() 