"""
大模型配置管理
支持多个大模型的配置和管理
"""

from dataclasses import dataclass
from typing import Dict, List, Optional, Any
from enum import Enum
import os
from .config import settings


class ModelProvider(str, Enum):
    """模型提供商枚举"""
    DEEPSEEK = "deepseek"
    OPENAI = "openai"
    ANTHROPIC = "anthropic"
    ZHIPU = "zhipu"
    QWEN = "qwen"
    BAIDU = "baidu"
    LOCAL = "local"


class ModelType(str, Enum):
    """模型类型枚举"""
    CHAT = "chat"
    COMPLETION = "completion"
    EMBEDDING = "embedding"
    RERANK = "rerank"
    IMAGE = "image"
    AUDIO = "audio"
    LLM = "llm"


@dataclass
class ModelConfig:
    """单个模型配置"""
    name: str
    provider: ModelProvider
    model_type: ModelType
    model_id: str
    api_key: Optional[str] = None
    api_base: Optional[str] = None
    max_tokens: int = 4096
    temperature: float = 0.7
    top_p: float = 0.9
    frequency_penalty: float = 0.0
    presence_penalty: float = 0.0
    timeout: int = 30
    max_retries: int = 3
    rate_limit_rpm: int = 60  # 每分钟请求数
    rate_limit_tpm: int = 100000  # 每分钟token数
    cost_per_1k_tokens: float = 0.0
    is_active: bool = True
    priority: int = 5  # 优先级，数字越小优先级越高
    extra_config: Dict[str, Any] = None

    def __post_init__(self):
        if self.extra_config is None:
            self.extra_config = {}


class AIModelManager:
    """AI模型管理器"""
    
    def __init__(self):
        self._models: Dict[str, ModelConfig] = {}
        self._load_default_configs()
    
    def _load_default_configs(self):
        """加载默认模型配置"""
        # DeepSeek 模型配置
        deepseek_config = ModelConfig(
            name="deepseek-chat",
            provider=ModelProvider.DEEPSEEK,
            model_type=ModelType.CHAT,
            model_id="deepseek-chat",
            api_key=os.getenv("DEEPSEEK_API_KEY", ""),
            api_base=os.getenv("DEEPSEEK_API_BASE", "https://api.deepseek.com/v1"),
            max_tokens=4096,
            temperature=0.7,
            rate_limit_rpm=60,
            rate_limit_tpm=100000,
            cost_per_1k_tokens=0.001,
            priority=1,
            extra_config={
                "supports_functions": True,
                "supports_vision": False,
                "context_length": 32768
            }
        )
        
        # OpenAI 模型配置
        openai_config = ModelConfig(
            name="gpt-3.5-turbo",
            provider=ModelProvider.OPENAI,
            model_type=ModelType.CHAT,
            model_id="gpt-3.5-turbo",
            api_key=os.getenv("OPENAI_API_KEY", ""),
            api_base=os.getenv("OPENAI_API_BASE", "https://api.openai.com/v1"),
            max_tokens=4096,
            temperature=0.7,
            rate_limit_rpm=60,
            rate_limit_tpm=90000,
            cost_per_1k_tokens=0.002,
            priority=2,
            extra_config={
                "supports_functions": True,
                "supports_vision": False,
                "context_length": 16384
            }
        )
        
        # 智谱 AI 模型配置
        zhipu_config = ModelConfig(
            name="glm-4",
            provider=ModelProvider.ZHIPU,
            model_type=ModelType.CHAT,
            model_id="glm-4",
            api_key=os.getenv("ZHIPU_API_KEY", ""),
            api_base=os.getenv("ZHIPU_API_BASE", "https://open.bigmodel.cn/api/paas/v4"),
            max_tokens=4096,
            temperature=0.7,
            rate_limit_rpm=60,
            rate_limit_tpm=100000,
            cost_per_1k_tokens=0.001,
            priority=3,
            extra_config={
                "supports_functions": True,
                "supports_vision": False,
                "context_length": 32768
            }
        )
        
        # 本地模型配置
        local_config = ModelConfig(
            name="local-llm",
            provider=ModelProvider.LOCAL,
            model_type=ModelType.CHAT,
            model_id="local-model",
            api_base=os.getenv("LOCAL_LLM_API_BASE", "http://localhost:8000/v1"),
            max_tokens=4096,
            temperature=0.7,
            rate_limit_rpm=1000,
            rate_limit_tpm=1000000,
            cost_per_1k_tokens=0.0,
            priority=10,
            is_active=False,  # 默认关闭
            extra_config={
                "supports_functions": False,
                "supports_vision": False,
                "context_length": 8192
            }
        )
        
        # 添加到模型字典
        self._models[deepseek_config.name] = deepseek_config
        self._models[openai_config.name] = openai_config
        self._models[zhipu_config.name] = zhipu_config
        self._models[local_config.name] = local_config
    
    def get_model(self, name: str) -> Optional[ModelConfig]:
        """获取指定名称的模型配置"""
        return self._models.get(name)
    
    def get_models_by_provider(self, provider: ModelProvider) -> List[ModelConfig]:
        """根据提供商获取模型配置列表"""
        return [model for model in self._models.values() if model.provider == provider]
    
    def get_models_by_type(self, model_type: ModelType) -> List[ModelConfig]:
        """根据类型获取模型配置列表"""
        return [model for model in self._models.values() if model.model_type == model_type]
    
    def get_active_models(self) -> List[ModelConfig]:
        """获取所有活跃的模型配置"""
        return [model for model in self._models.values() if model.is_active]
    
    def get_default_model(self, model_type: ModelType = ModelType.CHAT) -> Optional[ModelConfig]:
        """获取默认模型（优先级最高的活跃模型）"""
        active_models = [
            model for model in self._models.values() 
            if model.is_active and model.model_type == model_type
        ]
        if not active_models:
            return None
        return min(active_models, key=lambda x: x.priority)
    
    def add_model(self, config: ModelConfig):
        """添加新的模型配置"""
        self._models[config.name] = config
    
    def update_model(self, name: str, **kwargs):
        """更新模型配置"""
        if name in self._models:
            model = self._models[name]
            for key, value in kwargs.items():
                if hasattr(model, key):
                    setattr(model, key, value)
    
    def remove_model(self, name: str):
        """移除模型配置"""
        if name in self._models:
            del self._models[name]
    
    def list_models(self) -> List[str]:
        """列出所有模型名称"""
        return list(self._models.keys())
    
    def get_model_info(self, name: str) -> Optional[Dict[str, Any]]:
        """获取模型信息"""
        model = self.get_model(name)
        if not model:
            return None
        
        return {
            "name": model.name,
            "provider": model.provider.value,
            "model_type": model.model_type.value,
            "model_id": model.model_id,
            "max_tokens": model.max_tokens,
            "temperature": model.temperature,
            "rate_limit_rpm": model.rate_limit_rpm,
            "rate_limit_tpm": model.rate_limit_tpm,
            "cost_per_1k_tokens": model.cost_per_1k_tokens,
            "is_active": model.is_active,
            "priority": model.priority,
            "extra_config": model.extra_config
        }


# 全局模型管理器实例
model_manager = AIModelManager()


def get_model_config(name: str) -> Optional[ModelConfig]:
    """获取模型配置的便捷函数"""
    return model_manager.get_model(name)


def get_default_model_config(model_type: ModelType = ModelType.CHAT) -> Optional[ModelConfig]:
    """获取默认模型配置的便捷函数"""
    return model_manager.get_default_model(model_type)


def list_available_models() -> List[str]:
    """列出所有可用模型的便捷函数"""
    return model_manager.list_models()


def get_active_models() -> List[ModelConfig]:
    """获取所有活跃模型的便捷函数"""
    return model_manager.get_active_models()


# 模型配置常量
DEFAULT_MODEL_NAMES = {
    ModelType.CHAT: "deepseek-chat",
    ModelType.COMPLETION: "deepseek-chat",
    ModelType.EMBEDDING: "text-embedding-3-small",
    ModelType.RERANK: "bge-reranker-large"
}


# 任务类型到模型的映射
TASK_MODEL_MAPPING = {
    "summary": ModelType.LLM,
    "classification": ModelType.LLM,
    "extraction": ModelType.LLM,
    "generation": ModelType.LLM,
    "analysis": ModelType.LLM,
    "embedding": ModelType.EMBEDDING,
    "rerank": ModelType.RERANK
} 