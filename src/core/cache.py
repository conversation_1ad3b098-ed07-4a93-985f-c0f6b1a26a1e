"""
缓存服务配置
管理Redis连接和缓存操作
"""

import logging
from typing import Optional
import redis
from redis import Redis
from .config import settings

logger = logging.getLogger(__name__)

# Redis连接池
_redis_pool: Optional[redis.ConnectionPool] = None
_redis_client: Optional[Redis] = None


def get_redis_client() -> Redis:
    """
    获取Redis客户端实例
    
    Returns:
        Redis客户端实例
    """
    global _redis_pool, _redis_client
    
    if _redis_client is None:
        try:
            # 创建连接池
            _redis_pool = redis.ConnectionPool.from_url(
                settings.REDIS_URL,
                max_connections=20,
                socket_connect_timeout=5,
                socket_timeout=5,
                retry_on_timeout=True,
                health_check_interval=30
            )
            
            # 创建Redis客户端
            _redis_client = redis.Redis(
                connection_pool=_redis_pool,
                decode_responses=False  # 不自动解码，由具体服务控制
            )
            
            # 测试连接
            _redis_client.ping()
            logger.info("Redis connection established successfully")
            
        except Exception as e:
            logger.error(f"Failed to connect to Redis: {e}")
            # 如果Redis连接失败，返回一个Mock客户端以避免应用崩溃
            _redis_client = MockRedisClient()
    
    return _redis_client


class MockRedisClient:
    """Redis模拟客户端，当Redis不可用时使用"""
    
    def __init__(self):
        logger.warning("Using Mock Redis client - caching will be disabled")
        self._storage = {}
    
    def ping(self):
        return True
    
    def exists(self, key):
        return key in self._storage
    
    def get(self, key):
        return self._storage.get(key)
    
    def set(self, key, value, ex=None):
        self._storage[key] = value
        return True
    
    def delete(self, *keys):
        count = 0
        for key in keys:
            if key in self._storage:
                del self._storage[key]
                count += 1
        return count
    
    def sadd(self, key, *values):
        if key not in self._storage:
            self._storage[key] = set()
        if not isinstance(self._storage[key], set):
            self._storage[key] = set()
        
        count = 0
        for value in values:
            if value not in self._storage[key]:
                self._storage[key].add(value)
                count += 1
        return count
    
    def smembers(self, key):
        return self._storage.get(key, set())
    
    def sismember(self, key, value):
        storage_set = self._storage.get(key, set())
        return value in storage_set if isinstance(storage_set, set) else False
    
    def keys(self, pattern):
        import fnmatch
        return [k for k in self._storage.keys() if fnmatch.fnmatch(str(k), pattern)]
    
    def expire(self, key, time):
        # Mock实现不处理过期
        return True
    
    def pipeline(self):
        return MockRedisPipeline(self)


class MockRedisPipeline:
    """Redis Pipeline模拟实现"""
    
    def __init__(self, client):
        self.client = client
        self.commands = []
    
    def delete(self, *keys):
        self.commands.append(('delete', keys))
        return self
    
    def sadd(self, key, *values):
        self.commands.append(('sadd', key, values))
        return self
    
    def expire(self, key, time):
        self.commands.append(('expire', key, time))
        return self
    
    def execute(self):
        results = []
        for command in self.commands:
            if command[0] == 'delete':
                result = self.client.delete(*command[1])
                results.append(result)
            elif command[0] == 'sadd':
                result = self.client.sadd(command[1], *command[2])
                results.append(result)
            elif command[0] == 'expire':
                result = self.client.expire(command[1], command[2])
                results.append(result)
        
        self.commands = []
        return results


def get_cache_service():
    """
    获取缓存服务（用于依赖注入）
    
    Returns:
        缓存服务实例
    """
    from ..services.permission_service.cache import PermissionCacheService
    return PermissionCacheService(get_redis_client())


def close_redis_connection():
    """
    关闭Redis连接
    """
    global _redis_pool, _redis_client
    
    if _redis_client:
        try:
            _redis_client.close()
            logger.info("Redis connection closed")
        except Exception as e:
            logger.error(f"Error closing Redis connection: {e}")
        finally:
            _redis_client = None
    
    if _redis_pool:
        try:
            _redis_pool.disconnect()
            logger.info("Redis connection pool closed")
        except Exception as e:
            logger.error(f"Error closing Redis connection pool: {e}")
        finally:
            _redis_pool = None 