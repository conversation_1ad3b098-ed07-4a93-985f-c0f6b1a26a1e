"""
MongoDB 连接管理器
提供单例的 MongoDB 连接池和数据库操作接口
"""

import logging
import threading
from contextlib import contextmanager
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional

from pymongo import MongoClient
from pymongo.collection import Collection
from pymongo.database import Database
from pymongo.errors import ConnectionFailure

from .config import get_settings

logger = logging.getLogger(__name__)


class MongoDBManager:
    """
    MongoDB 连接管理器
    使用单例模式管理 MongoDB 连接池
    """

    _instance = None
    _lock = threading.Lock()

    def __new__(cls):
        """单例模式实现"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(MongoDBManager, cls).__new__(cls)
        return cls._instance

    def __init__(self):
        """初始化 MongoDB 管理器"""
        if hasattr(self, "_initialized"):
            return

        self._initialized = True
        self.settings = get_settings()
        self._client: Optional[MongoClient] = None
        self._database: Optional[Database] = None
        self._connection_count = 0
        self._last_ping_time: Optional[datetime] = None

        logger.info("MongoDB 管理器已初始化")

    def connect(self) -> bool:
        """
        建立 MongoDB 连接

        Returns:
            bool: 连接成功返回 True
        """
        try:
            if self._client is not None:
                # 检查现有连接是否可用
                if self._ping_database():
                    return True
                else:
                    # 连接不可用，关闭并重新创建
                    self._close_connection()

            logger.info(f"正在连接 MongoDB: {self.settings.MONGODB_URL}")

            # 创建 MongoDB 客户端
            self._client = MongoClient(
                self.settings.MONGODB_URL,
                serverSelectionTimeoutMS=5000,  # 5秒超时
                connectTimeoutMS=3000,  # 3秒连接超时
                maxPoolSize=20,  # 最大连接池大小
                minPoolSize=5,  # 最小连接池大小
                maxIdleTimeMS=30000,  # 30秒最大空闲时间
                retryWrites=True,  # 启用重试写入
            )

            # 获取数据库
            self._database = self._client[self.settings.MONGODB_DB_NAME]

            # 测试连接
            if self._ping_database():
                self._connection_count += 1
                logger.info(
                    f"MongoDB 连接成功，数据库: {self.settings.MONGODB_DB_NAME}"
                )
                return True
            else:
                raise ConnectionFailure("无法连接到 MongoDB")

        except Exception as e:
            logger.error(f"MongoDB 连接失败: {e}")
            self._close_connection()
            return False

    def _ping_database(self) -> bool:
        """
        测试数据库连接

        Returns:
            bool: 连接正常返回 True
        """
        try:
            if self._client is None:
                return False

            # 执行 ping 命令
            self._client.admin.command("ping")
            self._last_ping_time = datetime.now(timezone.utc)
            return True

        except Exception as e:
            logger.warning(f"MongoDB ping 失败: {e}")
            return False

    def _close_connection(self):
        """关闭 MongoDB 连接"""
        if self._client is not None:
            try:
                self._client.close()
                logger.info("MongoDB 连接已关闭")
            except Exception as e:
                logger.error(f"关闭 MongoDB 连接时出错: {e}")
            finally:
                self._client = None
                self._database = None

    def get_database(self) -> Optional[Database]:
        """
        获取数据库实例

        Returns:
            Database: MongoDB 数据库实例
        """
        if self._database is None:
            if not self.connect():
                return None
        return self._database

    def get_collection(self, collection_name: str) -> Optional[Collection]:
        """
        获取集合实例

        Args:
            collection_name: 集合名称

        Returns:
            Collection: MongoDB 集合实例
        """
        database = self.get_database()
        if database is None:
            return None
        return database[collection_name]

    def get_raw_content_collection(self) -> Optional[Collection]:
        """
        获取原始内容集合

        Returns:
            Collection: 原始内容集合实例
        """
        return self.get_collection("raw_content")

    def get_logs_collection(self) -> Optional[Collection]:
        """
        获取日志集合

        Returns:
            Collection: 日志集合实例
        """
        return self.get_collection("logs")

    async def save_raw_content(self, data: Dict[str, Any]) -> Optional[str]:
        """
        保存原始内容数据

        Args:
            data: 要保存的数据

        Returns:
            str: 文档ID，失败抛出异常
        """
        try:
            collection = self.get_raw_content_collection()
            if collection is None:
                logger.error("无法获取原始内容集合")
                raise RuntimeError("无法获取原始内容集合")

            # 检查内容哈希是否已存在
            if "content_hash" in data:
                existing = collection.find_one({"content_hash": data["content_hash"]})
                if existing:
                    logger.info(
                        f'内容哈希已存在，跳过插入: content_hash={data["content_hash"]}'
                    )
                    logger.debug(
                        f'内容哈希已存在，跳过插入: content_hash={data["content_hash"]}'
                    )
                    return str(existing["_id"])

            # 添加保存时间戳
            data["saved_at"] = datetime.now(timezone.utc)

            # 插入文档
            result = collection.insert_one(data)
            logger.debug(f"原始内容已保存: {result.inserted_id}")
            return str(result.inserted_id)

        except Exception as e:
            logger.error(f"保存原始内容失败: {e}")
            raise  # 关键：抛出异常而不是返回None

    async def find_content_by_hash(self, content_hash: str) -> Optional[Dict[str, Any]]:
        """
        根据内容哈希查找数据

        Args:
            content_hash: 内容哈希值

        Returns:
            Dict: 找到的文档，未找到返回 None
        """
        try:
            collection = self.get_raw_content_collection()
            if collection is None:
                return None

            return collection.find_one({"content_hash": content_hash})

        except Exception as e:
            logger.error(f"根据哈希查找内容失败: {e}")
            return None

    async def get_raw_content(self, document_id: str) -> Optional[Dict[str, Any]]:
        """
        根据文档ID获取原始内容

        Args:
            document_id: 文档ID

        Returns:
            Dict: 文档数据，不存在返回None
        """
        try:
            from bson import ObjectId

            collection = self.get_raw_content_collection()
            if collection is None:
                logger.error("无法获取原始内容集合")
                return None

            # 将字符串转换为ObjectId
            object_id = ObjectId(document_id)
            document = collection.find_one({"_id": object_id})

            if document:
                logger.debug(f"成功获取文档: {document_id}")
                return document
            else:
                logger.warning(f"文档不存在: {document_id}")
                return None

        except Exception as e:
            logger.error(f"获取原始内容失败: {e}")
            return None

    async def get_recent_raw_content(self, limit: int = 10) -> List[Dict[str, Any]]:
        """
        获取最近的原始内容记录

        Args:
            limit: 返回记录数量限制

        Returns:
            List[Dict]: 最近的原始内容记录列表
        """
        try:
            collection = self.get_raw_content_collection()
            if collection is None:
                logger.error("无法获取原始内容集合")
                return []

            # 按创建时间降序排列，获取最近的记录
            cursor = collection.find().sort("crawl_time", -1).limit(limit)
            documents = list(cursor)

            logger.debug(f"成功获取 {len(documents)} 条最近的原始内容记录")
            return documents

        except Exception as e:
            logger.error(f"获取最近原始内容失败: {e}")
            return []

    async def delete_raw_content(self, document_id: str) -> bool:
        """
        删除原始内容

        Args:
            document_id: 文档ID

        Returns:
            bool: 删除是否成功
        """
        try:
            if not self.is_connected():
                logger.error("MongoDB 未连接")
                return False

            collection = self.get_raw_content_collection()
            if collection is None:
                logger.error("无法获取原始内容集合")
                return False

            from bson import ObjectId

            # 删除文档
            result = collection.delete_one({"_id": ObjectId(document_id)})

            if result.deleted_count > 0:
                logger.info(f"成功删除原始内容: {document_id}")
                return True
            else:
                logger.warning(f"未找到要删除的内容: {document_id}")
                return False

        except Exception as e:
            logger.error(f"删除原始内容失败: {e}")
            return False

    async def delete_raw_content_by_hash(self, content_hash: str) -> bool:
        """
        根据内容哈希删除原始内容

        Args:
            content_hash: 内容哈希

        Returns:
            bool: 删除是否成功
        """
        try:
            if not self.is_connected():
                logger.error("MongoDB 未连接")
                return False

            collection = self.get_raw_content_collection()
            if collection is None:
                logger.error("无法获取原始内容集合")
                return False

            # 删除文档
            result = collection.delete_many({"content_hash": content_hash})

            if result.deleted_count > 0:
                logger.info(f"成功删除 {result.deleted_count} 条原始内容: {content_hash}")
                return True
            else:
                logger.warning(f"未找到要删除的内容: {content_hash}")
                return False

        except Exception as e:
            logger.error(f"根据哈希删除原始内容失败: {e}")
            return False

    async def get_content_by_task(self, task_id: int) -> List[Dict[str, Any]]:
        """
        获取指定任务的所有内容

        Args:
            task_id: 任务ID

        Returns:
            List: 内容列表
        """
        try:
            collection = self.get_raw_content_collection()
            if collection is None:
                return []

            cursor = collection.find({"task_id": task_id})
            return list(cursor)

        except Exception as e:
            logger.error(f"获取任务内容失败: {e}")
            return []

    async def log_crawl_activity(self, activity_data: Dict[str, Any]) -> Optional[str]:
        """
        记录爬虫活动日志

        Args:
            activity_data: 活动数据

        Returns:
            str: 日志ID，失败返回 None
        """
        try:
            collection = self.get_logs_collection()
            if collection is None:
                return None

            # 添加时间戳和类型
            activity_data.update(
                {"timestamp": datetime.now(timezone.utc), "log_type": "crawl_activity"}
            )

            result = collection.insert_one(activity_data)
            return str(result.inserted_id)

        except Exception as e:
            logger.error(f"记录爬虫活动日志失败: {e}")
            return None

    def get_connection_info(self) -> Dict[str, Any]:
        """
        获取连接信息

        Returns:
            Dict: 连接状态信息
        """
        return {
            "connected": self._client is not None,
            "database_name": self.settings.MONGODB_DB_NAME,
            "host": self.settings.MONGODB_HOST,
            "port": self.settings.MONGODB_PORT,
            "connection_count": self._connection_count,
            "last_ping_time": self._last_ping_time,
        }

    @contextmanager
    def get_client_context(self):
        """
        获取客户端上下文管理器
        用于需要直接操作客户端的场景
        """
        if self._client is None:
            if not self.connect():
                raise ConnectionFailure("无法建立 MongoDB 连接")

        try:
            yield self._client
        except Exception as e:
            logger.error(f"MongoDB 操作出错: {e}")
            raise

    def create_indexes(self):
        """创建数据库索引"""
        try:
            # 原始内容集合索引
            raw_content = self.get_raw_content_collection()
            if raw_content is not None:
                indexes = [
                    ("content_hash", 1),  # 用于去重
                    ("data_source_id", 1),  # 按数据源查询
                    ("crawl_time", -1),  # 按时间排序
                    ("task_id", 1),  # 按任务查询
                    ([("data_source_id", 1), ("crawl_time", -1)]),  # 复合索引
                ]

                for index in indexes:
                    if isinstance(index, tuple):
                        raw_content.create_index(index[0], direction=index[1])
                    elif isinstance(index, list):
                        raw_content.create_index(index)

                logger.info("原始内容集合索引创建完成")

            # 日志集合索引
            logs = self.get_logs_collection()
            if logs is not None:
                log_indexes = [
                    ("timestamp", -1),  # 按时间查询
                    ("log_type", 1),  # 按日志类型查询
                    ("level", 1),  # 按日志级别查询
                ]

                for index in log_indexes:
                    logs.create_index(index[0], direction=index[1])

                logger.info("日志集合索引创建完成")

        except Exception as e:
            logger.error(f"创建索引失败: {e}")

    def cleanup(self):
        """清理资源"""
        self._close_connection()
        logger.info("MongoDB 管理器已清理")


# 全局 MongoDB 管理器实例
mongodb_manager = MongoDBManager()
