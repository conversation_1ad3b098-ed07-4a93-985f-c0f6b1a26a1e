"""
所有数据模型的统一导入点
确保SQLAlchemy能够发现所有模型并正确创建数据库表
"""

# 导入数据采集服务模型
from ..services.data_collection_service.models import (
    DataSource, DataSourceConfig, DataSourceCredential, CrawlTask,
    EventDrivenCrawlRule, RawDataRecord
)

# 导入数据处理服务模型
from ..services.data_processing_service.models import (
    DataProcessingPipeline, DataProcessingStatus,
    FlashNews, NewsArticle, ResearchReport,
    EconomicIndicatorBase, EconomicIndicatorData,
    AITagMatches, UnifiedContentTags, UnifiedContentClassifications
)

# 导入财经日历服务模型
from ..services.financial_calendar_service.models import (CountryRegion,
                                                          EconomicIndicator,
                                                          EventAffectedMarket,
                                                          EventRelatedEvent,
                                                          EventType,
                                                          FinancialEvent)
# 导入短信服务模型
from ..services.sms_service.models import (SmsBusiness, SmsFrequencyLimit,
                                           SmsSignature, SmsTemplate)
# 导入标签分类服务模型
from ..services.tag_classification_service.models import (
    ClassificationDimension, ClassificationValue, Tag, TagClassification,
    TagRelationship, UserClassificationPreference, UserInterestTag,
    UserProfileSnapshot)
# 导入用户服务模型
from ..services.user_service.models import (SmsVerificationCode, User,
                                            UserBehavior, UserSession, UserTag)

__all__ = [
    # 数据采集服务模型
    "DataSource",
    "DataSourceConfig",
    "DataSourceCredential",
    "CrawlTask",
    "EventDrivenCrawlRule",
    "RawDataRecord",
    # 数据处理服务模型
    "DataProcessingPipeline",
    "DataProcessingStatus",
    "FlashNews",
    "NewsArticle",
    "ResearchReport",
    "EconomicIndicatorBase",
    "EconomicIndicatorData",
    "AITagMatches",
    "UnifiedContentTags",
    "UnifiedContentClassifications",
    # 用户服务模型
    "User",
    "UserBehavior",
    "UserSession",
    "UserTag",
    "SmsVerificationCode",
    # 短信服务模型
    "SmsBusiness",
    "SmsFrequencyLimit",
    "SmsSignature",
    "SmsTemplate",
    # 标签分类服务模型
    "Tag",
    "TagClassification",
    "TagRelationship",
    "ClassificationDimension",
    "ClassificationValue",
    "UserClassificationPreference",
    "UserInterestTag",
    "UserProfileSnapshot",
    # 财经日历服务模型
    "CountryRegion",
    "EconomicIndicator",
    "EventAffectedMarket",
    "EventRelatedEvent",
    "EventType",
    "FinancialEvent",
]
