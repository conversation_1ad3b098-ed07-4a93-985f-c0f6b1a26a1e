"""
应用配置模块
管理不同环境的配置参数
"""

import os
from typing import List, Optional

from pydantic import ConfigDict, field_validator
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """
    应用配置类
    支持开发、测试、生产环境的配置覆盖
    """

    model_config = ConfigDict(
        env_file=".env", case_sensitive=True, extra="ignore"  # 忽略额外的环境变量
    )

    # 应用基础配置
    APP_NAME: str = "FinSight Backend"
    APP_VERSION: str = "0.1.0"
    DEBUG: bool = False
    ENVIRONMENT: str = "development"

    # API配置
    API_PREFIX: str = "/api/v1"
    HOST: str = "0.0.0.0"
    PORT: int = 8000

    # 数据库配置
    DATABASE_URL: Optional[str] = None
    DB_HOST: str = "localhost"
    DB_PORT: int = 5432
    DB_NAME: str = "finsight"
    DB_USER: str = "postgres"
    DB_PASSWORD: str = ""

    # MongoDB配置
    MONGODB_URL: Optional[str] = None
    MONGODB_HOST: str = "localhost"
    MONGODB_PORT: int = 27017
    MONGODB_DB_NAME: str = "finsight_content"
    MONGODB_USERNAME: Optional[str] = None
    MONGODB_PASSWORD: Optional[str] = None

    # Redis配置
    REDIS_URL: Optional[str] = None
    REDIS_HOST: str = "localhost"
    REDIS_PORT: int = 6379
    REDIS_DB: int = 0
    REDIS_PASSWORD: Optional[str] = None

    # JWT配置
    JWT_SECRET_KEY: str = "your-secret-key-here"
    JWT_ALGORITHM: str = "HS256"
    JWT_ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    JWT_REFRESH_TOKEN_EXPIRE_DAYS: int = 7

    # 加密配置
    ENCRYPTION_KEY: Optional[str] = None

    # Elasticsearch配置
    ELASTICSEARCH_URL: Optional[str] = None
    ELASTICSEARCH_HOST: str = "localhost"
    ELASTICSEARCH_PORT: int = 9200

    # Kafka配置
    KAFKA_BOOTSTRAP_SERVERS: List[str] = ["localhost:9092"]
    KAFKA_GROUP_ID: str = "finsight-group"

    # DeepSeek API配置
    DEEPSEEK_API_KEY: Optional[str] = None
    DEEPSEEK_API_URL: str = "https://api.deepseek.com"

    # 文件存储配置
    MINIO_ENDPOINT: str = "localhost:9000"
    MINIO_ACCESS_KEY: Optional[str] = None
    MINIO_SECRET_KEY: Optional[str] = None
    MINIO_BUCKET_NAME: str = "finsight-files"

    # 监控配置
    PROMETHEUS_METRICS_PATH: str = "/metrics"
    LOG_LEVEL: str = "INFO"

    # 腾讯云短信配置
    TENCENT_CLOUD_SECRET_ID: Optional[str] = None
    TENCENT_CLOUD_SECRET_KEY: Optional[str] = None
    TENCENT_CLOUD_SMS_SDK_APP_ID: Optional[str] = None
    TENCENT_CLOUD_SMS_REGION: str = "ap-guangzhou"

    # 短信业务配置（基于业务名称）
    TENCENT_CLOUD_SMS_LOGIN: str = "登录验证"  # 登录验证码业务名称
    TENCENT_CLOUD_SMS_REGISTER: str = "注册验证"  # 注册验证码业务名称
    TENCENT_CLOUD_SMS_RESET_PASSWORD: str = "密码重置"  # 密码重置验证码业务名称

    # 安全配置
    CORS_ORIGINS: List[str] = ["*"]
    RATE_LIMIT_PER_MINUTE: int = 100

    # 数据库管理配置
    AUTO_CREATE_TABLES: bool = True  # 启动时自动创建不存在的表
    PRESERVE_DATA_ON_RESTART: bool = True  # 重启时保留数据（开发环境默认保留）

    @field_validator("DATABASE_URL", mode="before")
    @classmethod
    def assemble_db_connection(cls, v: Optional[str], info) -> str:
        """
        构建数据库连接URL
        """
        if isinstance(v, str) and v:
            return v

        # 从其他字段获取数据库连接参数
        values = info.data if hasattr(info, "data") else {}
        db_user = values.get("DB_USER", "postgres")
        db_password = values.get("DB_PASSWORD", "")
        db_host = values.get("DB_HOST", "localhost")
        db_port = values.get("DB_PORT", 5432)
        db_name = values.get("DB_NAME", "finsight")
        return f"postgresql://{db_user}:{db_password}@{db_host}:{db_port}/{db_name}"

    @field_validator("MONGODB_URL", mode="before")
    @classmethod
    def assemble_mongodb_connection(cls, v: Optional[str], info) -> str:
        """
        构建MongoDB连接URL
        """
        if isinstance(v, str) and v:
            return v

        # 从其他字段获取MongoDB连接参数
        values = info.data if hasattr(info, "data") else {}
        username = values.get("MONGODB_USERNAME")
        password = values.get("MONGODB_PASSWORD")
        auth_part = f"{username}:{password}@" if username and password else ""
        mongodb_host = values.get("MONGODB_HOST", "localhost")
        mongodb_port = values.get("MONGODB_PORT", 27017)
        mongodb_db = values.get("MONGODB_DB_NAME", "finsight_content")
        return f"mongodb://{auth_part}{mongodb_host}:{mongodb_port}/{mongodb_db}"

    @field_validator("REDIS_URL", mode="before")
    @classmethod
    def assemble_redis_connection(cls, v: Optional[str], info) -> str:
        """
        构建Redis连接URL
        """
        if isinstance(v, str) and v:
            return v

        # 从其他字段获取Redis连接参数
        values = info.data if hasattr(info, "data") else {}
        password_part = (
            f":{values.get('REDIS_PASSWORD')}@" if values.get("REDIS_PASSWORD") else ""
        )
        redis_host = values.get("REDIS_HOST", "localhost")
        redis_port = values.get("REDIS_PORT", 6379)
        redis_db = values.get("REDIS_DB", 0)
        return f"redis://{password_part}{redis_host}:{redis_port}/{redis_db}"


class DevelopmentSettings(Settings):
    """
    开发环境配置
    """

    model_config = ConfigDict(
        env_file=".env.develop", case_sensitive=True, extra="ignore"
    )

    DEBUG: bool = True
    ENVIRONMENT: str = "development"
    LOG_LEVEL: str = "DEBUG"

    # 开发环境数据库配置
    AUTO_CREATE_TABLES: bool = True  # 启动时自动创建不存在的表
    PRESERVE_DATA_ON_RESTART: bool = True  # 重启时保留数据

    # 开发环境MongoDB配置
    MONGODB_DB_NAME: str = "finsight"


class TestingSettings(Settings):
    """
    测试环境配置
    """

    model_config = ConfigDict(env_file=".env.test", case_sensitive=True, extra="ignore")

    ENVIRONMENT: str = "testing"
    DB_NAME: str = "finsight_test"

    # 测试环境数据库配置
    AUTO_CREATE_TABLES: bool = True  # 启动时自动创建表
    PRESERVE_DATA_ON_RESTART: bool = False  # 测试环境可以重置数据

    # 测试环境MongoDB配置
    MONGODB_DB_NAME: str = "finsight_content_test"


class ProductionSettings(Settings):
    """
    生产环境配置
    """

    model_config = ConfigDict(
        env_file=".env.production", case_sensitive=True, extra="ignore"
    )

    ENVIRONMENT: str = "production"
    DEBUG: bool = False

    # 生产环境数据库配置
    AUTO_CREATE_TABLES: bool = True  # 启动时自动创建不存在的表
    PRESERVE_DATA_ON_RESTART: bool = True  # 生产环境必须保留数据

    # 生产环境MongoDB配置
    MONGODB_DB_NAME: str = "finsight"


def get_settings() -> Settings:
    """
    获取当前环境配置
    """
    env = os.getenv("ENVIRONMENT", "development")

    if env == "development":
        return DevelopmentSettings()
    elif env == "testing":
        return TestingSettings()
    elif env == "production":
        return ProductionSettings()
    else:
        return Settings()


# 全局配置实例
settings = get_settings()
