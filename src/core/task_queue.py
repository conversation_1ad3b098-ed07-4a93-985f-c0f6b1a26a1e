"""
统一任务队列
提供基于Celery的异步任务执行功能
"""

import asyncio
import logging
from typing import Any, Dict, Optional

from celery import current_task

from .celery_app import TaskPriority, celery_app
from .database import SessionLocal
from .task_framework import TaskContext, TaskStatus, TaskType, task_manager

logger = logging.getLogger(__name__)


@celery_app.task(
    bind=True,
    autoretry_for=(Exception,),
    retry_kwargs={'max_retries': 3, 'countdown': 60},
    name="unified_task"
)
def submit_unified_task(
    self,
    task_type_str: str,
    parameters: Dict[str, Any],
    priority: int = TaskPriority.NORMAL,
    metadata: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    统一的Celery任务执行器
    
    Args:
        task_type_str: 任务类型字符串
        parameters: 任务参数
        priority: 任务优先级
        metadata: 任务元数据
        
    Returns:
        Dict: 任务执行结果
    """
    
    try:
        # 转换任务类型
        task_type = TaskType(task_type_str)
        
        # 获取任务执行器
        executor = task_manager.get_executor(task_type)
        if not executor:
            raise ValueError(f"未找到任务类型 {task_type} 的执行器")
        
        # 创建任务上下文
        context = TaskContext(
            task_id=self.request.id,
            task_type=task_type,
            execution_mode="async_queue",
            parameters=parameters,
            priority=priority,
            metadata=metadata or {}
        )
        
        # 执行任务
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            # 异步执行任务
            result = loop.run_until_complete(_execute_task_async(executor, context))
            return result
            
        finally:
            loop.close()
            
    except Exception as e:
        logger.error(f"统一任务执行失败 {self.request.id}: {e}")
        raise


async def _execute_task_async(executor, context: TaskContext) -> Dict[str, Any]:
    """
    异步执行任务
    
    Args:
        executor: 任务执行器
        context: 任务上下文
        
    Returns:
        Dict: 执行结果
    """
    try:
        await executor.setup(context)
        result = await executor.execute_task(context)
        context.result = result
        context.status = TaskStatus.SUCCESS
        
        return {
            "task_id": context.task_id,
            "status": context.status.value,
            "result": result,
            "start_time": context.start_time.isoformat() if context.start_time else None,
            "end_time": context.end_time.isoformat() if context.end_time else None,
        }
        
    except Exception as e:
        context.error = str(e)
        context.status = TaskStatus.FAILED
        logger.error(f"任务 {context.task_id} 执行失败: {e}")
        
        return {
            "task_id": context.task_id,
            "status": context.status.value,
            "error": str(e),
            "start_time": context.start_time.isoformat() if context.start_time else None,
            "end_time": context.end_time.isoformat() if context.end_time else None,
        }
        
    finally:
        await executor.cleanup(context)


# 数据采集任务（为向后兼容保留原有的装饰器）
@celery_app.task(
    bind=True,
    autoretry_for=(Exception,),
    retry_kwargs={'max_retries': 3, 'countdown': 60},
    name="crawler_task"
)
def crawler_task(
    self,
    data_source_id: int,
    task_id: int,
    config: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    爬虫任务（兼容性接口）
    
    Args:
        data_source_id: 数据源ID
        task_id: 任务ID
        config: 配置参数
        
    Returns:
        Dict: 执行结果
    """
    
    # 转换为统一任务格式
    parameters = {
        "data_source_id": data_source_id,
        "task_id": task_id,
        "config": config or {}
    }
    
    return submit_unified_task(
        TaskType.CRAWLER.value,
        parameters,
        TaskPriority.NORMAL
    )


# 便捷的任务提交函数
def submit_crawler_task(
    data_source_id: int,
    task_id: int,
    config: Optional[Dict[str, Any]] = None,
    priority: int = TaskPriority.NORMAL
) -> str:
    """
    提交爬虫任务到队列
    
    Args:
        data_source_id: 数据源ID
        task_id: 任务ID
        config: 配置参数
        priority: 任务优先级
        
    Returns:
        str: Celery任务ID
    """
    
    parameters = {
        "data_source_id": data_source_id,
        "task_id": task_id,
        "config": config or {}
    }
    
    result = submit_unified_task.delay(
        TaskType.CRAWLER.value,
        parameters,
        priority
    )
    
    logger.info(f"爬虫任务已提交: data_source_id={data_source_id}, task_id={task_id}, celery_task_id={result.id}")
    return result.id


def submit_batch_crawler_task(
    data_source_ids: list,
    config: Optional[Dict[str, Any]] = None,
    priority: int = TaskPriority.NORMAL
) -> list:
    """
    批量提交爬虫任务
    
    Args:
        data_source_ids: 数据源ID列表
        config: 配置参数
        priority: 任务优先级
        
    Returns:
        list: Celery任务ID列表
    """
    
    task_ids = []
    for data_source_id in data_source_ids:
        parameters = {
            "data_source_id": data_source_id,
            "task_id": None,  # 批量任务不指定特定任务ID
            "config": config or {},
            "batch_mode": True
        }
        
        result = submit_unified_task.delay(
            TaskType.CRAWLER.value,
            parameters,
            priority
        )
        task_ids.append(result.id)
    
    logger.info(f"批量爬虫任务已提交: {len(task_ids)} 个任务")
    return task_ids 