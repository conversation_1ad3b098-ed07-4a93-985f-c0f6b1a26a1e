"""
日志配置模块
提供统一的日志配置和管理
"""

import os
import logging
import logging.handlers
from datetime import datetime
from typing import Dict, Any
from pathlib import Path

from .config import settings


class ColoredFormatter(logging.Formatter):
    """彩色日志格式化器"""
    
    # 颜色代码
    COLORS = {
        'DEBUG': '\033[36m',    # 青色
        'INFO': '\033[32m',     # 绿色
        'WARNING': '\033[33m',  # 黄色
        'ERROR': '\033[31m',    # 红色
        'CRITICAL': '\033[35m', # 紫色
        'RESET': '\033[0m'      # 重置
    }
    
    def format(self, record):
        # 添加颜色
        if record.levelname in self.COLORS:
            record.levelname = f"{self.COLORS[record.levelname]}{record.levelname}{self.COLORS['RESET']}"
        
        return super().format(record)


class DataProcessingFilter(logging.Filter):
    """数据处理日志过滤器"""
    
    def filter(self, record):
        # 添加额外的上下文信息
        if not hasattr(record, 'component'):
            record.component = 'unknown'
        if not hasattr(record, 'record_id'):
            record.record_id = 'N/A'
        if not hasattr(record, 'task_id'):
            record.task_id = 'N/A'
        
        return True


def setup_logging():
    """设置日志配置"""
    
    # 创建日志目录
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    # 根日志器配置
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.DEBUG if settings.DEBUG else logging.INFO)
    
    # 清除现有处理器
    root_logger.handlers.clear()
    
    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.DEBUG if settings.DEBUG else logging.INFO)
    
    # 控制台格式（带颜色）
    console_formatter = ColoredFormatter(
        fmt='%(asctime)s | %(levelname)-8s | %(name)-20s | %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    console_handler.setFormatter(console_formatter)
    console_handler.addFilter(DataProcessingFilter())
    
    # 文件处理器 - 应用日志
    app_file_handler = logging.handlers.RotatingFileHandler(
        filename=log_dir / "finsight_app.log",
        maxBytes=10 * 1024 * 1024,  # 10MB
        backupCount=5,
        encoding='utf-8'
    )
    app_file_handler.setLevel(logging.INFO)
    
    # 文件格式
    file_formatter = logging.Formatter(
        fmt='%(asctime)s | %(levelname)-8s | %(name)-30s | %(component)-15s | %(record_id)-10s | %(task_id)-15s | %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    app_file_handler.setFormatter(file_formatter)
    app_file_handler.addFilter(DataProcessingFilter())
    
    # 错误日志处理器
    error_file_handler = logging.handlers.RotatingFileHandler(
        filename=log_dir / "finsight_error.log",
        maxBytes=10 * 1024 * 1024,  # 10MB
        backupCount=10,
        encoding='utf-8'
    )
    error_file_handler.setLevel(logging.ERROR)
    error_file_handler.setFormatter(file_formatter)
    error_file_handler.addFilter(DataProcessingFilter())
    
    # 数据处理专用日志处理器
    processing_file_handler = logging.handlers.RotatingFileHandler(
        filename=log_dir / "data_processing.log",
        maxBytes=20 * 1024 * 1024,  # 20MB
        backupCount=10,
        encoding='utf-8'
    )
    processing_file_handler.setLevel(logging.DEBUG)
    processing_file_handler.setFormatter(file_formatter)
    processing_file_handler.addFilter(DataProcessingFilter())
    
    # 添加处理器到根日志器
    root_logger.addHandler(console_handler)
    root_logger.addHandler(app_file_handler)
    root_logger.addHandler(error_file_handler)
    
    # 为数据处理模块添加专用处理器
    processing_logger = logging.getLogger('src.services.data_processing_service')
    processing_logger.addHandler(processing_file_handler)
    processing_logger.setLevel(logging.DEBUG)
    
    # Celery日志配置
    celery_logger = logging.getLogger('celery')
    celery_logger.setLevel(logging.INFO)
    
    celery_file_handler = logging.handlers.RotatingFileHandler(
        filename=log_dir / "celery.log",
        maxBytes=10 * 1024 * 1024,  # 10MB
        backupCount=5,
        encoding='utf-8'
    )
    celery_file_handler.setLevel(logging.INFO)
    celery_file_handler.setFormatter(file_formatter)
    celery_logger.addHandler(celery_file_handler)
    
    # 数据库日志配置
    db_logger = logging.getLogger('sqlalchemy.engine')
    if settings.DEBUG:
        db_logger.setLevel(logging.INFO)
    else:
        db_logger.setLevel(logging.WARNING)
    
    # MongoDB日志配置
    mongo_logger = logging.getLogger('pymongo')
    mongo_logger.setLevel(logging.WARNING)
    
    # 第三方库日志级别
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    logging.getLogger('requests').setLevel(logging.WARNING)
    logging.getLogger('httpx').setLevel(logging.WARNING)
    
    logging.info("日志系统初始化完成")


def get_logger(name: str, **context) -> logging.LoggerAdapter:
    """
    获取带上下文的日志器
    
    Args:
        name: 日志器名称
        **context: 上下文信息
        
    Returns:
        LoggerAdapter: 带上下文的日志器
    """
    
    logger = logging.getLogger(name)
    return logging.LoggerAdapter(logger, context)


def get_processing_logger(component: str, record_id: str = None, task_id: str = None) -> logging.LoggerAdapter:
    """
    获取数据处理专用日志器
    
    Args:
        component: 组件名称
        record_id: 记录ID
        task_id: 任务ID
        
    Returns:
        LoggerAdapter: 数据处理日志器
    """
    
    context = {
        'component': component,
        'record_id': record_id or 'N/A',
        'task_id': task_id or 'N/A'
    }
    
    return get_logger('src.services.data_processing_service', **context)


class LoggingContext:
    """日志上下文管理器"""
    
    def __init__(self, logger: logging.LoggerAdapter, **context):
        self.logger = logger
        self.context = context
        self.original_extra = getattr(logger, 'extra', {})
    
    def __enter__(self):
        # 更新上下文
        if hasattr(self.logger, 'extra'):
            self.logger.extra.update(self.context)
        else:
            self.logger.extra = self.context
        return self.logger
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        # 恢复原始上下文
        if hasattr(self.logger, 'extra'):
            self.logger.extra = self.original_extra


def log_processing_stage(logger: logging.LoggerAdapter, stage: str, record_id: str = None, **kwargs):
    """
    记录处理阶段日志
    
    Args:
        logger: 日志器
        stage: 处理阶段
        record_id: 记录ID
        **kwargs: 额外信息
    """
    
    extra_info = f" | {' | '.join(f'{k}={v}' for k, v in kwargs.items())}" if kwargs else ""
    message = f"处理阶段: {stage}"
    
    if record_id:
        message += f" | 记录ID: {record_id}"
    
    message += extra_info
    
    logger.info(message)


def log_processing_error(logger: logging.LoggerAdapter, error: Exception, record_id: str = None, **kwargs):
    """
    记录处理错误日志
    
    Args:
        logger: 日志器
        error: 错误对象
        record_id: 记录ID
        **kwargs: 额外信息
    """
    
    extra_info = f" | {' | '.join(f'{k}={v}' for k, v in kwargs.items())}" if kwargs else ""
    message = f"处理错误: {str(error)}"
    
    if record_id:
        message += f" | 记录ID: {record_id}"
    
    message += extra_info
    
    logger.error(message, exc_info=True)


def log_processing_success(logger: logging.LoggerAdapter, result: Dict[str, Any], record_id: str = None, **kwargs):
    """
    记录处理成功日志
    
    Args:
        logger: 日志器
        result: 处理结果
        record_id: 记录ID
        **kwargs: 额外信息
    """
    
    extra_info = f" | {' | '.join(f'{k}={v}' for k, v in kwargs.items())}" if kwargs else ""
    message = f"处理成功"
    
    if record_id:
        message += f" | 记录ID: {record_id}"
    
    if result:
        message += f" | 结果: {result}"
    
    message += extra_info
    
    logger.info(message)


def log_performance_metrics(logger: logging.LoggerAdapter, metrics: Dict[str, Any], **kwargs):
    """
    记录性能指标日志
    
    Args:
        logger: 日志器
        metrics: 性能指标
        **kwargs: 额外信息
    """
    
    extra_info = f" | {' | '.join(f'{k}={v}' for k, v in kwargs.items())}" if kwargs else ""
    message = f"性能指标: {metrics}"
    message += extra_info
    
    logger.info(message)


# 初始化日志系统
if not os.getenv('SKIP_LOGGING_INIT'):
    setup_logging()
