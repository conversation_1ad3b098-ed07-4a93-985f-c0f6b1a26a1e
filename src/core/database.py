"""
数据库连接配置
管理SQLAlchemy数据库连接和会话
"""

from typing import Generator
import time
from sqlalchemy import create_engine, event
from sqlalchemy.exc import DBAPIError, SQLAlchemyError
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import Session, sessionmaker
from sqlalchemy.pool import QueuePool

from .config import settings

# 创建数据库引擎
engine = create_engine(
    settings.DATABASE_URL,
    # 连接池配置
    poolclass=QueuePool,
    pool_size=20,  # 连接池大小
    max_overflow=10,  # 超出pool_size后的最大连接数
    pool_timeout=30,  # 连接池获取连接的超时时间
    pool_recycle=1800,  # 连接回收时间（30分钟）
    pool_pre_ping=True,  # 连接前验证
    # 重试配置
    pool_reset_on_return='commit',  # 在连接返回池时重置连接状态
    echo=settings.DEBUG,  # 调试模式下打印SQL
)

# 配置连接池事件
@event.listens_for(engine, "connect")
def connect(dbapi_connection, connection_record):
    """连接创建时的回调"""
    connection_record.info['pid'] = time.time()

@event.listens_for(engine, "checkout")
def checkout(dbapi_connection, connection_record, connection_proxy):
    """连接取出时的回调"""
    pid = connection_record.info['pid']
    if time.time() - pid > 1800:  # 如果连接存在超过30分钟
        try:
            # 尝试刷新连接
            connection_proxy.connection.ping()
        except Exception:
            # 如果刷新失败，标记连接为无效
            connection_record.invalidate()

# 创建会话工厂
SessionLocal = sessionmaker(
    autocommit=False,
    autoflush=False,
    bind=engine,
    # 配置会话
    expire_on_commit=False  # 避免在提交后重新加载对象
)

# 创建基类
Base = declarative_base()

def get_db() -> Generator[Session, None, None]:
    """
    获取数据库会话，包含重试机制

    Yields:
        数据库会话对象
    """
    retries = 3
    retry_delay = 1  # 初始重试延迟（秒）

    for attempt in range(retries):
        db = SessionLocal()
        try:
            yield db
            break  # 如果成功，跳出重试循环
        except (DBAPIError, SQLAlchemyError) as e:
            if attempt < retries - 1:  # 如果还有重试机会
                time.sleep(retry_delay)
                retry_delay *= 2  # 指数退避
                continue
            raise  # 如果重试次数用完，重新抛出异常
        finally:
            db.close()

def create_tables():
    """
    创建所有数据库表并初始化权限系统

    注意：此函数使用 create_all，只会创建不存在的表，
    不会删除或修改现有表结构和数据。这确保了数据安全。
    """
    print("🔧 创建数据库表...")
    print(f"🔒 环境: {settings.ENVIRONMENT}")
    print(f"🗄️ 数据库: {settings.DATABASE_URL}")

    Base.metadata.create_all(bind=engine)
    print("✅ 数据库表创建完成（仅创建不存在的表，不会删除现有数据）")
    
    # 运行权限系统迁移
    try:
        from ..services.permission_service.migrations import run_migration
        run_migration()
        print("✅ 权限系统初始化完成")
    except Exception as e:
        print(f"⚠️ 权限系统初始化失败: {e}")
        print("   权限功能可能不可用，请检查相关配置")

def drop_tables():
    """
    删除所有数据库表

    ⚠️ 安全保护：此函数在开发环境中被禁用，防止意外删除数据。
    只允许在测试环境中使用，且需要明确的环境变量授权。
    """
    import os

    current_env = os.getenv("ENVIRONMENT", "development")
    allow_drop = os.getenv("ALLOW_DROP_TABLES", "false").lower() == "true"

    if current_env == "testing" and allow_drop:
        print("⚠️ 测试环境：删除所有数据库表")
        Base.metadata.drop_all(bind=engine)
        print("✅ 数据库表已删除")
    else:
        print(f"🔒 安全保护：在 {current_env} 环境中禁止删除数据库表")
        print("   如需删除表，请设置环境变量 ALLOW_DROP_TABLES=true 且在测试环境中执行")

def reset_tables():
    """
    重置所有数据库表（删除后重新创建）

    ⚠️ 危险操作：此函数会删除所有数据！
    只允许在测试环境中使用，且需要明确的环境变量授权。
    """
    import os

    current_env = os.getenv("ENVIRONMENT", "development")
    allow_reset = os.getenv("ALLOW_RESET_TABLES", "false").lower() == "true"

    if current_env == "testing" and allow_reset:
        print("⚠️ 测试环境：重置所有数据库表")
        Base.metadata.drop_all(bind=engine)
        Base.metadata.create_all(bind=engine)
        print("✅ 数据库表已重置")
    else:
        print(f"🔒 安全保护：在 {current_env} 环境中禁止重置数据库表")
        print(
            "   如需重置表，请设置环境变量 ALLOW_RESET_TABLES=true 且在测试环境中执行"
        )
