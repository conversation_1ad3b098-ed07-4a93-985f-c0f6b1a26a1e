"""
SMS服务数据验证模型

此模块定义了SMS服务相关的Pydantic数据模型，用于：
- 请求数据验证
- 响应数据序列化
- 业务配置管理
"""

import re
from datetime import datetime, timezone
from enum import IntEnum
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, ConfigDict, Field, field_validator


class SmsBusinessType(IntEnum):
    """短信业务类型枚举"""

    VERIFICATION = 1  # 验证码
    NOTIFICATION = 2  # 通知
    MARKETING = 3  # 营销


class SmsSignatureCreate(BaseModel):
    """创建短信签名请求"""

    signature_name: str = Field(..., max_length=100, description="签名名称")
    sign_id: str = Field(..., max_length=50, description="腾讯云签名ID")


class SmsSignatureResponse(BaseModel):
    """短信签名响应"""

    id: int = Field(..., description="签名ID")
    signature_name: str = Field(..., description="签名名称")
    sign_id: str = Field(..., description="腾讯云签名ID")
    created_at: datetime = Field(..., description="创建时间")

    model_config = ConfigDict(from_attributes=True)


class SmsTemplateCreate(BaseModel):
    """创建短信模板请求"""

    template_name: str = Field(..., max_length=100, description="模板名称")
    template_content: str = Field(..., description="模板内容")
    template_params: List[str] = Field(..., description="模板参数定义")
    template_type: SmsBusinessType = Field(..., description="模板类型")
    template_id: str = Field(..., max_length=50, description="腾讯云模板ID")


class SmsTemplateResponse(BaseModel):
    """短信模板响应"""

    id: int = Field(..., description="模板ID")
    template_name: str = Field(..., description="模板名称")
    template_content: str = Field(..., description="模板内容")
    template_params: List[str] = Field(..., description="模板参数定义")
    template_type: SmsBusinessType = Field(..., description="模板类型")
    template_id: str = Field(..., description="腾讯云模板ID")
    created_at: datetime = Field(..., description="创建时间")

    model_config = ConfigDict(from_attributes=True)


class SmsBusinessCreate(BaseModel):
    """创建短信业务请求"""

    signature_id: int = Field(..., description="签名ID")
    template_id: int = Field(..., description="模板ID")
    business_name: str = Field(..., max_length=100, description="业务名称")
    sms_type: SmsBusinessType = Field(..., description="短信类型")
    daily_limit: Optional[int] = Field(
        10, ge=1, le=1000, description="单用户每日发送限制"
    )
    minute_limit: Optional[int] = Field(
        3, ge=1, le=60, description="单用户每分钟发送限制"
    )


class SmsBusinessConfig(BaseModel):
    """短信业务配置"""

    signature_name: str = Field(..., description="签名名称")
    template_id: str = Field(..., description="腾讯云模板ID")
    template_params: List[str] = Field(..., description="模板参数定义")
    sms_type: SmsBusinessType = Field(..., description="短信类型")
    daily_limit: int = Field(..., description="单用户每日发送限制")
    minute_limit: int = Field(..., description="单用户每分钟发送限制")


class SmsBusinessResponse(BaseModel):
    """短信业务响应"""

    id: int = Field(..., description="业务ID")
    business_name: str = Field(..., description="业务名称")
    sms_type: SmsBusinessType = Field(..., description="短信类型")
    daily_limit: int = Field(..., description="单用户每日发送限制")
    minute_limit: int = Field(..., description="单用户每分钟发送限制")
    signature: SmsSignatureResponse = Field(..., description="签名信息")
    template: SmsTemplateResponse = Field(..., description="模板信息")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

    model_config = ConfigDict(from_attributes=True)


class SmsSendRequest(BaseModel):
    """短信发送请求"""

    business_name: str = Field(..., description="业务名称")
    phone_numbers: List[str] = Field(
        ..., min_length=1, max_length=200, description="手机号码列表"
    )
    template_params: List[str] = Field(default_factory=list, description="模板参数")

    @field_validator("phone_numbers")
    @classmethod
    def validate_phone_numbers(cls, v):
        """验证手机号码格式"""
        phone_pattern = re.compile(r"^(\+86)?1[3-9]\d{9}$")
        for phone in v:
            # 移除空格和特殊字符
            clean_phone = "".join(
                char for char in phone if char.isdigit() or char == "+"
            )
            if not phone_pattern.match(clean_phone.replace("+86", "")):
                raise ValueError(f"无效的手机号码格式: {phone}")
        return v


class SmsSendStatusInfo(BaseModel):
    """短信发送状态信息"""

    serial_no: str = Field(..., description="短信序列号")
    phone_number: str = Field(..., description="手机号码")
    fee: int = Field(..., description="费用")
    session_context: str = Field(..., description="会话上下文")
    code: str = Field(..., description="状态码")
    message: str = Field(..., description="状态消息")
    iso_code: str = Field(..., description="国家代码")


class SmsSendResponse(BaseModel):
    """短信发送响应"""

    success: bool = Field(..., description="发送是否成功")
    message: str = Field(..., description="响应消息")
    request_id: Optional[str] = Field(None, description="请求ID")
    send_status_set: List[SmsSendStatusInfo] = Field(
        default_factory=list, description="发送状态列表"
    )
    error_code: Optional[str] = Field(None, description="错误代码")
    error_message: Optional[str] = Field(None, description="详细错误消息")


class SmsFrequencyLimitResponse(BaseModel):
    """短信频率限制响应"""

    id: int = Field(..., description="记录ID")
    business_id: int = Field(..., description="业务ID")
    phone_number: str = Field(..., description="手机号")
    daily_count: int = Field(..., description="当日发送次数")
    minute_count: int = Field(..., description="当前分钟发送次数")
    last_send_time: Optional[datetime] = Field(None, description="最后发送时间")
    date_key: str = Field(..., description="日期键")

    model_config = ConfigDict(from_attributes=True)


class SmsServiceStatus(BaseModel):
    """短信服务状态响应"""

    enabled: bool = Field(..., description="服务是否启用")
    provider: str = Field(..., description="服务提供商")
    configuration_status: Dict[str, Any] = Field(..., description="配置状态")
    last_check_time: datetime = Field(..., description="最后检查时间")
