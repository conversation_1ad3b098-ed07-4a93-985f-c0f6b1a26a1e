"""
SMS服务数据库迁移脚本

此脚本用于：
- 创建SMS相关数据库表
- 初始化默认数据
- 设置初始业务配置
"""

from enum import IntEnum

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from ...core.config import settings
from .models import Base, SmsBusiness, SmsSignature, SmsTemplate


class SmsBusinessType(IntEnum):
    """短信业务类型枚举"""

    VERIFICATION = 1  # 验证码
    NOTIFICATION = 2  # 通知
    MARKETING = 3  # 营销


def create_sms_tables(engine):
    """创建SMS相关表"""
    # 创建所有表
    Base.metadata.create_all(bind=engine)
    print("✅ SMS数据表创建完成")
    print("  - sms_signature: 短信签名表")
    print("  - sms_template: 短信模板表")
    print("  - sms_business: 短信业务表")
    print("  - sms_frequency_limit: 短信频率限制表")


def initialize_sms_data(session):
    """初始化SMS基础数据"""
    # 检查是否已有数据
    existing_signature = session.query(SmsSignature).first()
    if existing_signature:
        print("⚠️  检测到已有SMS数据，跳过初始化")
        return

    # 创建默认签名
    default_signature = SmsSignature(
        signature_name="FinSight",
        sign_id="your-default-sign-id-here",  # 需要替换为实际的腾讯云签名ID
    )
    session.add(default_signature)
    session.commit()  # 提交以获取ID

    # 创建默认模板
    templates = [
        SmsTemplate(
            template_name="登录验证码模板",
            template_content="您的登录验证码是：{1}，有效期{2}分钟。请勿泄露给他人。",
            template_params=["code", "expire_minutes"],
            template_type=SmsBusinessType.VERIFICATION,
            template_id="your-login-template-id-here",
        ),
        SmsTemplate(
            template_name="注册验证码模板",
            template_content="您的注册验证码是：{1}，有效期{2}分钟。请勿泄露给他人。",
            template_params=["code", "expire_minutes"],
            template_type=SmsBusinessType.VERIFICATION,
            template_id="your-register-template-id-here",
        ),
        SmsTemplate(
            template_name="密码重置验证码模板",
            template_content="您的密码重置验证码是：{1}，有效期{2}分钟。请勿泄露给他人。",
            template_params=["code", "expire_minutes"],
            template_type=SmsBusinessType.VERIFICATION,
            template_id="your-reset-template-id-here",
        ),
    ]

    for template in templates:
        session.add(template)

    session.commit()

    # 创建默认业务配置
    businesses = [
        SmsBusiness(
            signature_id=default_signature.id,
            template_id=templates[0].id,  # 登录验证码模板
            business_name="登录验证",
            sms_type=SmsBusinessType.VERIFICATION,
            daily_limit=10,
            minute_limit=3,
        ),
        SmsBusiness(
            signature_id=default_signature.id,
            template_id=templates[1].id,  # 注册验证码模板
            business_name="注册验证",
            sms_type=SmsBusinessType.VERIFICATION,
            daily_limit=5,
            minute_limit=2,
        ),
        SmsBusiness(
            signature_id=default_signature.id,
            template_id=templates[2].id,  # 密码重置验证码模板
            business_name="密码重置",
            sms_type=SmsBusinessType.VERIFICATION,
            daily_limit=5,
            minute_limit=2,
        ),
    ]

    for business in businesses:
        session.add(business)

    session.commit()

    print("✅ SMS基础数据初始化完成")
    print(f"  - 创建签名: {default_signature.signature_name}")
    print(f"  - 创建模板: {len(templates)} 个")
    print(f"  - 创建业务: {len(businesses)} 个")


def run_migration():
    """运行迁移"""
    engine = create_engine(settings.DATABASE_URL)

    # 创建表结构
    create_sms_tables(engine)
    print("✅ SMS表结构创建完成")

    # 初始化数据
    SessionLocal = sessionmaker(bind=engine)
    session = SessionLocal()

    try:
        initialize_sms_data(session)
    except Exception as e:
        print(f"❌ 初始化SMS数据失败: {e}")
        session.rollback()
    finally:
        session.close()


if __name__ == "__main__":
    run_migration()
