"""
腾讯云短信客户端

基于腾讯云短信SDK实现短信发送功能，支持验证码、通知和营销短信
"""

import logging
import re
from typing import Any, Dict, List, Optional

from tencentcloud.common import credential
from tencentcloud.common.exception.tencent_cloud_sdk_exception import \
    TencentCloudSDKException
from tencentcloud.common.profile.client_profile import ClientProfile
from tencentcloud.common.profile.http_profile import HttpProfile
from tencentcloud.sms.v20210111 import models, sms_client

from ...core.config import settings
from .schemas import SmsSendResponse, SmsSendStatusInfo


class TencentSmsClient:
    """
    腾讯云短信客户端

    提供基于腾讯云短信API的短信发送功能，支持：
    - 验证码短信发送
    - 通知短信发送
    - 营销短信发送
    - 错误处理和重试机制
    """

    def __init__(self):
        """初始化腾讯云短信客户端"""
        self.secret_id = settings.TENCENT_CLOUD_SECRET_ID
        self.secret_key = settings.TENCENT_CLOUD_SECRET_KEY
        self.sdk_app_id = settings.TENCENT_CLOUD_SMS_SDK_APP_ID
        self.region = settings.TENCENT_CLOUD_SMS_REGION

        # 验证必要配置
        if not all([self.secret_id, self.secret_key, self.sdk_app_id]):
            logging.warning("腾讯云短信配置不完整，将使用模拟模式")
            self.enabled = False
        else:
            self.enabled = True
            self._init_client()

    def _init_client(self) -> None:
        """初始化腾讯云短信客户端"""
        try:
            # 实例化认证对象
            cred = credential.Credential(self.secret_id, self.secret_key)

            # 实例化HTTP选项
            http_profile = HttpProfile()
            http_profile.reqMethod = "POST"
            http_profile.reqTimeout = 30
            http_profile.endpoint = "sms.tencentcloudapi.com"

            # 实例化客户端配置
            client_profile = ClientProfile()
            client_profile.signMethod = "TC3-HMAC-SHA256"
            client_profile.httpProfile = http_profile

            # 实例化短信客户端
            self.client = sms_client.SmsClient(cred, self.region, client_profile)

            logging.info("✅ 腾讯云短信客户端初始化成功")

        except (TencentCloudSDKException, ValueError) as e:
            logging.error("❌ 腾讯云短信客户端初始化失败: %s", e)
            self.enabled = False

    def _format_phone_number(self, phone: str) -> str:
        """
        格式化手机号码为E.164标准格式

        Args:
            phone: 原始手机号码

        Returns:
            格式化后的手机号码
        """
        # 移除所有空格和特殊字符
        phone = "".join(char for char in phone if char.isdigit())

        # 如果不是以+开头，默认添加+86（中国大陆）
        if not phone.startswith("+"):
            if phone.startswith("86"):
                phone = "+" + phone
            elif len(phone) == 11 and phone.startswith("1"):
                phone = "+86" + phone
            else:
                phone = "+86" + phone

        return phone

    def send_sms(
        self,
        phone_numbers: List[str],
        template_id: str,
        sign_name: str,
        template_params: Optional[List[str]] = None,
    ) -> SmsSendResponse:
        """
        发送短信

        Args:
            phone_numbers: 接收短信的手机号码列表
            template_id: 腾讯云短信模板ID
            sign_name: 短信签名
            template_params: 短信模板参数列表

        Returns:
            短信发送响应
        """
        if not self.enabled:
            logging.warning("腾讯云短信服务未启用，使用模拟发送")
            return self._create_mock_response(phone_numbers)

        try:
            # 格式化手机号码
            formatted_phones = [
                self._format_phone_number(phone) for phone in phone_numbers
            ]

            # 创建请求对象
            req = models.SendSmsRequest()

            # 设置短信应用ID
            req.SmsSdkAppId = self.sdk_app_id

            # 设置短信签名
            req.SignName = sign_name

            # 设置模板ID
            req.TemplateId = template_id

            # 设置模板参数
            if template_params:
                req.TemplateParamSet = template_params

            # 设置手机号码
            req.PhoneNumberSet = formatted_phones

            # 发送短信
            resp = self.client.SendSms(req)

            # 处理响应
            send_status_list = []
            success_count = 0
            total_count = 0
            failed_messages = []

            if hasattr(resp, "SendStatusSet") and resp.SendStatusSet:
                total_count = len(resp.SendStatusSet)
                for status in resp.SendStatusSet:
                    status_info = SmsSendStatusInfo(
                        serial_no=status.SerialNo,
                        phone_number=status.PhoneNumber,
                        fee=status.Fee,
                        session_context=status.SessionContext,
                        code=status.Code,
                        message=status.Message,
                        iso_code=status.IsoCode,
                    )
                    send_status_list.append(status_info)

                    # 判断单个号码发送是否成功
                    if status.Code == "Ok":
                        success_count += 1
                    else:
                        failed_messages.append(
                            f"{status.PhoneNumber}: {status.Message}"
                        )

            # 判断整体发送是否成功
            if total_count > 0 and success_count == total_count:
                logging.info(
                    "✅ 短信发送成功，RequestId: %s，成功数量: %s/%s",
                    resp.RequestId,
                    success_count,
                    total_count,
                )
                return SmsSendResponse(
                    success=True,
                    message=f"短信发送成功，共{success_count}条",
                    request_id=resp.RequestId,
                    send_status_set=send_status_list,
                )
            elif success_count > 0:
                logging.warning(
                    "⚠️ 短信部分发送成功，RequestId: %s，成功数量: %s/%s，失败信息: %s",
                    resp.RequestId,
                    success_count,
                    total_count,
                    "; ".join(failed_messages),
                )
                return SmsSendResponse(
                    success=False,
                    message=f"短信部分发送成功，成功{success_count}条，失败{total_count - success_count}条",
                    request_id=resp.RequestId,
                    send_status_set=send_status_list,
                    error_message="; ".join(failed_messages),
                )
            else:
                logging.error(
                    "❌ 短信发送全部失败，RequestId: %s，失败信息: %s",
                    resp.RequestId,
                    "; ".join(failed_messages),
                )
                return SmsSendResponse(
                    success=False,
                    message="短信发送全部失败",
                    request_id=resp.RequestId,
                    send_status_set=send_status_list,
                    error_message="; ".join(failed_messages),
                )

        except TencentCloudSDKException as e:
            error_msg = f"腾讯云SDK异常: {e.get_code()} - {e.get_message()}"
            logging.error("❌ 短信发送失败: %s", error_msg)
            return SmsSendResponse(
                success=False,
                message="短信发送失败",
                error_code=e.get_code(),
                error_message=error_msg,
            )

        except Exception as e:
            error_msg = f"未知异常: {str(e)}"
            logging.error("❌ 短信发送失败: %s", error_msg)
            return SmsSendResponse(
                success=False,
                message="短信发送失败",
                error_message=error_msg,
            )

    def _create_mock_response(self, phone_numbers: List[str]) -> SmsSendResponse:
        """
        创建模拟响应

        Args:
            phone_numbers: 手机号码列表

        Returns:
            模拟的短信发送响应
        """
        mock_status_list = []
        for i, phone in enumerate(phone_numbers):
            status_info = SmsSendStatusInfo(
                serial_no=f"mock_{i}",
                phone_number=self._format_phone_number(phone),
                fee=1,
                session_context="",
                code="Ok",
                message="send success",
                iso_code="CN",
            )
            mock_status_list.append(status_info)

        return SmsSendResponse(
            success=True,
            message="模拟发送成功",
            request_id="mock_request_id",
            send_status_set=mock_status_list,
        )

    def validate_phone_number(self, phone: str) -> bool:
        """
        验证手机号码格式

        Args:
            phone: 手机号码

        Returns:
            格式是否有效
        """
        # 中国大陆手机号码正则表达式
        pattern = re.compile(r"^(\+86)?1[3-9]\d{9}$")

        # 移除空格和特殊字符
        clean_phone = "".join(char for char in phone if char.isdigit() or char == "+")

        # 如果以+86开头，移除+86再验证
        if clean_phone.startswith("+86"):
            clean_phone = clean_phone[3:]

        return bool(pattern.match(clean_phone))

    def get_service_status(self) -> Dict[str, Any]:
        """
        获取服务状态

        Returns:
            服务状态信息
        """
        return {
            "enabled": self.enabled,
            "provider": "腾讯云短信",
            "configuration_status": {
                "secret_id": bool(self.secret_id),
                "secret_key": bool(self.secret_key),
                "sdk_app_id": bool(self.sdk_app_id),
                "region": self.region,
            },
            "last_check_time": None,  # 可以在这里添加健康检查
        }
