"""
SMS服务核心业务逻辑

此模块实现了短信发送服务的核心业务功能，包括：
- 业务配置管理
- 发送频率限制
- 短信发送逻辑
- 验证码发送
"""

import logging
from datetime import datetime, timezone
from typing import Dict, List, Optional

from sqlalchemy.orm import Session

from ...core.config import settings
from .models import SmsBusiness, SmsFrequencyLimit, SmsSignature, SmsTemplate
from .schemas import (SmsBusinessConfig, SmsBusinessResponse, SmsSendRequest,
                      SmsSendResponse, SmsServiceStatus)
from .tencent_client import TencentSmsClient


class SmsService:
    """
    短信服务类

    提供统一的短信发送和管理功能，支持：
    - 基于业务名称的短信配置管理
    - 发送频率限制
    - 腾讯云短信集成
    - 自动故障转移
    """

    def __init__(self, db_session: Session):
        """
        初始化短信服务

        Args:
            db_session: 数据库会话
        """
        self.db = db_session
        self.tencent_client = TencentSmsClient()
        self._business_cache: Dict[str, SmsBusinessConfig] = {}
        self._load_business_configs()

    def _normalize_template_params(self, template_params) -> List[str]:
        """
        标准化模板参数格式

        Args:
            template_params: 原始模板参数，可能是字典列表或字符串列表

        Returns:
            标准化后的字符串列表
        """
        if not template_params or not isinstance(template_params, list):
            return []

        if not template_params:  # 空列表
            return []

        # 如果是字典列表格式，提取参数名称
        if isinstance(template_params[0], dict):
            return [
                param.get("name", "")
                for param in template_params
                if isinstance(param, dict)
            ]
        # 如果已经是字符串列表，直接使用
        if isinstance(template_params[0], str):
            return template_params
        return []

    def _load_business_configs(self) -> None:
        """从数据库加载业务配置到缓存"""
        try:
            businesses = (
                self.db.query(SmsBusiness).join(SmsSignature).join(SmsTemplate).all()
            )

            for business in businesses:
                config = SmsBusinessConfig(
                    signature_name=business.signature.signature_name,
                    template_id=business.template.template_id,
                    template_params=self._normalize_template_params(
                        business.template.template_params
                    ),
                    sms_type=business.sms_type,
                    daily_limit=business.daily_limit,
                    minute_limit=business.minute_limit,
                )
                self._business_cache[business.business_name] = config

            logging.info("✅ 成功加载 %d 个短信业务配置", len(self._business_cache))

        except Exception as e:
            logging.error("❌ 加载短信业务配置失败: %s", e)
            self._business_cache = {}

    def get_business_config(self, business_name: str) -> Optional[SmsBusinessConfig]:
        """
        获取业务配置

        Args:
            business_name: 业务名称

        Returns:
            业务配置或None
        """
        # 先从缓存获取
        config = self._business_cache.get(business_name)
        if config:
            return config

        # 缓存未命中，从数据库查询
        try:
            business = (
                self.db.query(SmsBusiness)
                .join(SmsSignature)
                .join(SmsTemplate)
                .filter(SmsBusiness.business_name == business_name)
                .first()
            )

            if business:
                config = SmsBusinessConfig(
                    signature_name=business.signature.signature_name,
                    template_id=business.template.template_id,
                    template_params=self._normalize_template_params(
                        business.template.template_params
                    ),
                    sms_type=business.sms_type,
                    daily_limit=business.daily_limit,
                    minute_limit=business.minute_limit,
                )
                # 更新缓存
                self._business_cache[business_name] = config
                logging.info("✅ 成功加载业务配置: %s", business_name)
                return config

        except Exception as e:
            logging.error("❌ 查询业务配置失败: %s", e)

        return None

    def _check_frequency_limit(
        self, business_name: str, phone_number: str
    ) -> Dict[str, any]:
        """
        检查发送频率限制

        Args:
            business_name: 业务名称
            phone_number: 手机号码

        Returns:
            检查结果字典
        """
        try:
            # 获取业务配置
            config = self.get_business_config(business_name)
            if not config:
                return {"allowed": False, "reason": f"业务配置不存在: {business_name}"}

            # 获取业务ID
            business = (
                self.db.query(SmsBusiness)
                .filter(SmsBusiness.business_name == business_name)
                .first()
            )
            if not business:
                return {"allowed": False, "reason": f"业务不存在: {business_name}"}

            current_time = datetime.now(timezone.utc)
            date_key = current_time.strftime("%Y%m%d")
            current_minute = current_time.replace(second=0, microsecond=0)

            # 查询或创建频率限制记录
            freq_limit = (
                self.db.query(SmsFrequencyLimit)
                .filter(
                    SmsFrequencyLimit.business_id == business.id,
                    SmsFrequencyLimit.phone_number == phone_number,
                    SmsFrequencyLimit.date_key == date_key,
                )
                .first()
            )

            if not freq_limit:
                # 创建新记录
                freq_limit = SmsFrequencyLimit(
                    business_id=business.id,
                    phone_number=phone_number,
                    daily_count=0,
                    minute_count=0,
                    date_key=date_key,
                )
                self.db.add(freq_limit)

            # 检查每日限制
            if freq_limit.daily_count >= config.daily_limit:
                return {
                    "allowed": False,
                    "reason": f"超过每日发送限制 {config.daily_limit} 条",
                }

            # 检查每分钟限制
            if (
                freq_limit.last_send_time
                and freq_limit.last_send_time >= current_minute
                and freq_limit.minute_count >= config.minute_limit
            ):
                return {
                    "allowed": False,
                    "reason": f"超过每分钟发送限制 {config.minute_limit} 条",
                }

            return {
                "allowed": True,
                "freq_limit": freq_limit,
                "current_time": current_time,
            }

        except Exception as e:
            logging.error("❌ 检查发送频率限制失败: %s", e)
            return {"allowed": False, "reason": f"检查频率限制失败: {str(e)}"}

    def _update_frequency_limit(
        self, freq_limit: SmsFrequencyLimit, current_time: datetime
    ) -> None:
        """
        更新发送频率限制记录

        Args:
            freq_limit: 频率限制记录
            current_time: 当前时间
        """
        try:
            current_minute = current_time.replace(second=0, microsecond=0)

            # 更新每日计数
            freq_limit.daily_count += 1

            # 更新每分钟计数
            if (
                freq_limit.last_send_time is None
                or freq_limit.last_send_time < current_minute
            ):
                freq_limit.minute_count = 1
            else:
                freq_limit.minute_count += 1

            # 更新最后发送时间
            freq_limit.last_send_time = current_time

            self.db.commit()

        except Exception as e:
            logging.error("❌ 更新发送频率限制失败: %s", e)
            self.db.rollback()

    def send_sms(self, request: SmsSendRequest) -> SmsSendResponse:
        """
        发送短信

        Args:
            request: 短信发送请求

        Returns:
            短信发送响应
        """
        # 获取业务配置
        config = self.get_business_config(request.business_name)
        if not config:
            return SmsSendResponse(
                success=False,
                message=f"业务配置不存在: {request.business_name}",
                error_message=f"未找到业务 '{request.business_name}' 的配置",
            )

        # 验证手机号码格式
        invalid_phones = []
        for phone in request.phone_numbers:
            if not self.tencent_client.validate_phone_number(phone):
                invalid_phones.append(phone)

        if invalid_phones:
            return SmsSendResponse(
                success=False,
                message="手机号码格式无效",
                error_message=f"无效的手机号码: {', '.join(invalid_phones)}",
            )

        # 检查发送频率限制（对每个手机号分别检查）
        frequency_errors = []
        for phone in request.phone_numbers:
            limit_check = self._check_frequency_limit(request.business_name, phone)
            if not limit_check["allowed"]:
                frequency_errors.append(f"{phone}: {limit_check['reason']}")

        if frequency_errors:
            return SmsSendResponse(
                success=False,
                message="发送频率限制",
                error_message="; ".join(frequency_errors),
            )

        # 发送短信
        try:
            response = self.tencent_client.send_sms(
                phone_numbers=request.phone_numbers,
                template_id=config.template_id,
                sign_name=config.signature_name,
                template_params=request.template_params,
            )

            # 如果发送成功，更新频率限制记录
            if response.success:
                for phone in request.phone_numbers:
                    limit_check = self._check_frequency_limit(
                        request.business_name, phone
                    )
                    if limit_check["allowed"]:
                        self._update_frequency_limit(
                            limit_check["freq_limit"], limit_check["current_time"]
                        )

            return response

        except Exception as e:
            logging.error("❌ 发送短信失败: %s", e)
            return SmsSendResponse(
                success=False,
                message="发送短信失败",
                error_message=str(e),
            )

    def send_verification_code(
        self, phone: str, code: str, business_name: str = None
    ) -> SmsSendResponse:
        """
        发送验证码短信

        Args:
            phone: 手机号码
            code: 验证码
            business_name: 业务名称，默认使用登录验证

        Returns:
            短信发送响应
        """
        # 使用默认的登录验证业务
        if not business_name:
            # 从环境变量获取默认业务名称
            business_name = getattr(settings, "TENCENT_CLOUD_SMS_LOGIN", "登录验证")

        request = SmsSendRequest(
            business_name=business_name,
            phone_numbers=[phone],
            template_params=[code],
        )

        return self.send_sms(request)

    def get_service_status(self) -> SmsServiceStatus:
        """
        获取服务状态

        Returns:
            服务状态信息
        """
        tencent_status = self.tencent_client.get_service_status()

        return SmsServiceStatus(
            enabled=tencent_status["enabled"],
            provider=tencent_status["provider"],
            configuration_status={
                **tencent_status["configuration_status"],
                "business_configs_loaded": len(self._business_cache),
                "available_businesses": list(self._business_cache.keys()),
            },
            last_check_time=datetime.now(timezone.utc),
        )

    def reload_business_configs(self) -> bool:
        """
        重新加载业务配置

        Returns:
            是否成功重新加载
        """
        try:
            self._business_cache.clear()
            self._load_business_configs()
            return True
        except Exception as e:
            logging.error("❌ 重新加载业务配置失败: %s", e)
            return False

    def list_businesses(self) -> List[SmsBusinessResponse]:
        """
        列出所有短信业务

        Returns:
            业务列表
        """
        try:
            businesses = (
                self.db.query(SmsBusiness).join(SmsSignature).join(SmsTemplate).all()
            )

            return [
                SmsBusinessResponse(
                    id=business.id,
                    business_name=business.business_name,
                    sms_type=business.sms_type,
                    daily_limit=business.daily_limit,
                    minute_limit=business.minute_limit,
                    signature=business.signature,
                    template=business.template,
                    created_at=business.created_at,
                    updated_at=business.updated_at,
                )
                for business in businesses
            ]

        except Exception as e:
            logging.error("❌ 获取业务列表失败: %s", e)
            return []
