"""
SMS服务数据模型定义

此模块定义了SMS服务相关的SQLAlchemy数据模型，包括：
- SmsSignature: 短信签名表
- SmsTemplate: 短信模板表
- SmsBusiness: 短信业务表
- SmsFrequencyLimit: 短信发送频率限制表
"""

from datetime import datetime, timezone
from typing import Dict, List, Optional

from sqlalchemy import (JSON, BigInteger, Column, DateTime, ForeignKey, Index,
                        Integer, SmallInteger, String, Text, TIMESTAMP, UniqueConstraint)
from sqlalchemy.sql import func
from sqlalchemy.orm import declarative_base, relationship

from ...core.database import Base


class SmsSignature(Base):
    """
    短信签名表（仅存储审核通过记录）
    """

    __tablename__ = "sms_signature"

    id = Column(BigInteger, primary_key=True, autoincrement=True, comment="签名ID")
    signature_name = Column(
        String(100), nullable=False, unique=True, comment="签名名称"
    )
    sign_id = Column(String(50), nullable=False, unique=True, comment="腾讯云签名ID")
    created_at = Column(
        TIMESTAMP, nullable=False, server_default=func.now(), comment="创建时间"
    )

    # 关联关系
    businesses = relationship("SmsBusiness", back_populates="signature")

    __table_args__ = (
        UniqueConstraint("signature_name", name="uk_signature_name"),
        UniqueConstraint("sign_id", name="uk_tencent_sign_id"),
        {"comment": "短信签名表（仅存储审核通过记录）"},
    )


class SmsTemplate(Base):
    """
    短信模板表（仅存储审核通过记录）
    """

    __tablename__ = "sms_template"

    id = Column(BigInteger, primary_key=True, autoincrement=True, comment="模板ID")
    template_name = Column(String(100), nullable=False, unique=True, comment="模板名称")
    template_content = Column(Text, nullable=False, comment="模板内容")
    template_params = Column(JSON, nullable=False, comment="模板参数定义(JSON数组)")
    template_type = Column(
        SmallInteger, nullable=False, comment="模板类型(1-验证码，2-通知，3-营销)"
    )
    template_id = Column(
        String(50), nullable=False, unique=True, comment="腾讯云模板ID"
    )
    created_at = Column(
        TIMESTAMP, nullable=False, server_default=func.now(), comment="创建时间"
    )

    # 关联关系
    businesses = relationship("SmsBusiness", back_populates="template")

    __table_args__ = (
        UniqueConstraint("template_name", name="uk_template_name"),
        UniqueConstraint("template_id", name="uk_tencent_template_id"),
        {"comment": "短信模板表（仅存储审核通过记录）"},
    )


class SmsBusiness(Base):
    """
    短信业务表
    """

    __tablename__ = "sms_business"

    id = Column(BigInteger, primary_key=True, autoincrement=True, comment="业务ID")
    signature_id = Column(
        BigInteger, ForeignKey("sms_signature.id"), nullable=False, comment="签名ID"
    )
    template_id = Column(
        BigInteger, ForeignKey("sms_template.id"), nullable=False, comment="模板ID"
    )
    business_name = Column(String(100), nullable=False, unique=True, comment="业务名称")
    sms_type = Column(
        SmallInteger, nullable=False, comment="短信类型(1-验证码，2-通知，3-营销)"
    )
    daily_limit = Column(
        Integer, nullable=True, default=10, comment="单用户每日发送限制"
    )
    minute_limit = Column(
        Integer, nullable=True, default=3, comment="单用户每分钟发送限制"
    )
    created_at = Column(
        TIMESTAMP, nullable=False, server_default=func.now(), comment="创建时间"
    )
    updated_at = Column(
        TIMESTAMP,
        nullable=False,
        server_default=func.now(),
        onupdate=func.now(),
        comment="更新时间",
    )

    # 关联关系
    signature = relationship("SmsSignature", back_populates="businesses")
    template = relationship("SmsTemplate", back_populates="businesses")
    frequency_limits = relationship("SmsFrequencyLimit", back_populates="business")

    __table_args__ = (
        UniqueConstraint("business_name", name="uk_business_name"),
        Index("idx_signature_id", "signature_id"),
        Index("idx_template_id", "template_id"),
        {"comment": "短信业务表"},
    )


class SmsFrequencyLimit(Base):
    """
    短信发送频率限制表
    """

    __tablename__ = "sms_frequency_limit"

    id = Column(BigInteger, primary_key=True, autoincrement=True, comment="ID")
    business_id = Column(
        BigInteger, ForeignKey("sms_business.id"), nullable=False, comment="业务ID"
    )
    phone_number = Column(String(20), nullable=False, comment="手机号")
    daily_count = Column(Integer, nullable=False, default=0, comment="当日发送次数")
    minute_count = Column(
        Integer, nullable=False, default=0, comment="当前分钟发送次数"
    )
    last_send_time = Column(DateTime, nullable=True, comment="最后发送时间")
    date_key = Column(String(10), nullable=False, comment="日期键(YYYYMMDD)")
    created_at = Column(
        DateTime, nullable=False, default=datetime.now(timezone.utc), comment="创建时间"
    )
    updated_at = Column(
        DateTime,
        nullable=False,
        default=datetime.now(timezone.utc),
        onupdate=datetime.now(timezone.utc),
        comment="更新时间",
    )

    # 关联关系
    business = relationship("SmsBusiness", back_populates="frequency_limits")

    __table_args__ = (
        UniqueConstraint(
            "business_id", "phone_number", "date_key", name="uk_business_phone_date"
        ),
        Index("idx_last_send_time", "last_send_time"),
        {"comment": "短信发送频率限制表"},
    )
