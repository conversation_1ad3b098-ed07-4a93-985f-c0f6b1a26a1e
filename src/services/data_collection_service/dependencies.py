"""
数据采集服务依赖注入模块
提供服务实例的依赖注入功能
专注于数据采集功能
"""

from functools import lru_cache
from typing import Generator, Tuple

from fastapi import Depends
from sqlalchemy.orm import Session

from src.core.database import get_db

from .service import (CrawlTaskService, DataSourceConfigService,
                      DataSourceCredentialService, DataSourceService,
                      EventDrivenCrawlRuleService, RawDataRecordService)

# ==================== 服务实例缓存 ====================


@lru_cache()
def get_data_source_service() -> DataSourceService:
    """
    获取数据源服务实例

    Returns:
        DataSourceService: 数据源服务实例
    """
    return DataSourceService()


@lru_cache()
def get_event_driven_crawl_rule_service() -> EventDrivenCrawlRuleService:
    """
    获取事件驱动采集规则服务实例

    Returns:
        EventDrivenCrawlRuleService: 事件驱动规则服务实例
    """
    return EventDrivenCrawlRuleService()


@lru_cache()
def get_crawl_task_service() -> CrawlTaskService:
    """
    获取采集任务服务实例

    Returns:
        CrawlTaskService: 采集任务服务实例
    """
    return CrawlTaskService()


@lru_cache()
def get_data_source_config_service() -> DataSourceConfigService:
    """
    获取数据源配置服务实例

    Returns:
        DataSourceConfigService: 数据源配置服务实例
    """
    return DataSourceConfigService()


@lru_cache()
def get_data_source_credential_service() -> DataSourceCredentialService:
    """
    获取数据源凭证服务实例

    Returns:
        DataSourceCredentialService: 数据源凭证服务实例
    """
    return DataSourceCredentialService()


@lru_cache()
def get_raw_data_record_service() -> RawDataRecordService:
    """
    获取原始数据记录服务实例

    Returns:
        RawDataRecordService: 原始数据记录服务实例
    """
    return RawDataRecordService()


# ==================== 组合服务依赖 ====================


def get_data_collection_services(
    db: Session = Depends(get_db),
    data_source_service: DataSourceService = Depends(get_data_source_service),
    rule_service: EventDrivenCrawlRuleService = Depends(
        get_event_driven_crawl_rule_service
    ),
    task_service: CrawlTaskService = Depends(get_crawl_task_service),
    config_service: DataSourceConfigService = Depends(get_data_source_config_service),
    credential_service: DataSourceCredentialService = Depends(
        get_data_source_credential_service
    ),
    record_service: RawDataRecordService = Depends(get_raw_data_record_service),
) -> dict:
    """
    获取所有数据采集相关服务

    Args:
        db: 数据库会话
        data_source_service: 数据源服务
        rule_service: 事件驱动规则服务
        task_service: 采集任务服务
        config_service: 数据源配置服务
        credential_service: 数据源凭证服务
        record_service: 原始数据记录服务

    Returns:
        dict: 包含所有服务的字典
    """
    return {
        "db": db,
        "data_source_service": data_source_service,
        "rule_service": rule_service,
        "task_service": task_service,
        "config_service": config_service,
        "credential_service": credential_service,
        "record_service": record_service,
    }


def get_crawl_orchestration_services(
    db: Session = Depends(get_db),
    data_source_service: DataSourceService = Depends(get_data_source_service),
    task_service: CrawlTaskService = Depends(get_crawl_task_service),
    config_service: DataSourceConfigService = Depends(get_data_source_config_service),
    credential_service: DataSourceCredentialService = Depends(
        get_data_source_credential_service
    ),
) -> dict:
    """
    获取采集编排相关服务

    Args:
        db: 数据库会话
        data_source_service: 数据源服务
        task_service: 采集任务服务
        config_service: 数据源配置服务
        credential_service: 数据源凭证服务

    Returns:
        dict: 包含编排相关服务的字典
    """
    return {
        "db": db,
        "data_source_service": data_source_service,
        "task_service": task_service,
        "config_service": config_service,
        "credential_service": credential_service,
    }


def get_event_driven_services(
    db: Session = Depends(get_db),
    rule_service: EventDrivenCrawlRuleService = Depends(
        get_event_driven_crawl_rule_service
    ),
    task_service: CrawlTaskService = Depends(get_crawl_task_service),
) -> dict:
    """
    获取事件驱动相关服务

    Args:
        db: 数据库会话
        rule_service: 事件驱动规则服务
        task_service: 采集任务服务

    Returns:
        dict: 包含事件驱动相关服务的字典
    """
    return {
        "db": db,
        "rule_service": rule_service,
        "task_service": task_service,
    }


def get_data_source_management_services(
    db: Session = Depends(get_db),
    data_source_service: DataSourceService = Depends(get_data_source_service),
    config_service: DataSourceConfigService = Depends(get_data_source_config_service),
    credential_service: DataSourceCredentialService = Depends(
        get_data_source_credential_service
    ),
) -> dict:
    """
    获取数据源管理相关服务

    Args:
        db: 数据库会话
        data_source_service: 数据源服务
        config_service: 数据源配置服务
        credential_service: 数据源凭证服务

    Returns:
        dict: 包含数据源管理相关服务的字典
    """
    return {
        "db": db,
        "data_source_service": data_source_service,
        "config_service": config_service,
        "credential_service": credential_service,
    }


def get_health_check_services(
    data_source_service: DataSourceService = Depends(get_data_source_service),
    task_service: CrawlTaskService = Depends(get_crawl_task_service),
) -> dict:
    """
    获取健康检查相关服务

    Args:
        data_source_service: 数据源服务
        task_service: 采集任务服务

    Returns:
        dict: 包含健康检查相关服务的字典
    """
    return {
        "data_source_service": data_source_service,
        "task_service": task_service,
    }


def get_admin_services(
    services: dict = Depends(get_data_collection_services),
) -> dict:
    """
    获取管理员相关的所有服务

    Args:
        services: 所有数据采集服务

    Returns:
        dict: 管理员服务集合
    """
    return services


def get_operator_services(
    db: Session = Depends(get_db),
    data_source_service: DataSourceService = Depends(get_data_source_service),
    task_service: CrawlTaskService = Depends(get_crawl_task_service),
    rule_service: EventDrivenCrawlRuleService = Depends(
        get_event_driven_crawl_rule_service
    ),
) -> dict:
    """
    获取操作员相关服务（只读）

    Args:
        db: 数据库会话
        data_source_service: 数据源服务
        task_service: 采集任务服务
        rule_service: 事件驱动规则服务

    Returns:
        dict: 包含操作员相关服务的字典
    """
    return {
        "db": db,
        "data_source_service": data_source_service,
        "task_service": task_service,
        "rule_service": rule_service,
    }


def get_readonly_services(
    db: Session = Depends(get_db),
    data_source_service: DataSourceService = Depends(get_data_source_service),
    task_service: CrawlTaskService = Depends(get_crawl_task_service),
    record_service: RawDataRecordService = Depends(get_raw_data_record_service),
) -> dict:
    """
    获取只读服务集合

    Args:
        db: 数据库会话
        data_source_service: 数据源服务
        task_service: 采集任务服务
        record_service: 原始数据记录服务

    Returns:
        dict: 包含只读服务的字典
    """
    return {
        "db": db,
        "data_source_service": data_source_service,
        "task_service": task_service,
        "record_service": record_service,
    }


def get_data_collection_management_services(
    db: Session = Depends(get_db),
) -> Generator[Tuple[
    "DataSourceService",
    "DataSourceConfigService", 
    "DataSourceCredentialService",
    "RawDataRecordService"
], None, None]:
    """
    获取数据采集管理服务生成器

    Args:
        db: 数据库会话

    Yields:
        Tuple: 包含数据采集管理相关服务的元组
    """
    data_source_service = DataSourceService()
    config_service = DataSourceConfigService()
    credential_service = DataSourceCredentialService()
    record_service = RawDataRecordService()
    
    yield data_source_service, config_service, credential_service, record_service
