"""
数据采集服务模块
提供数据源管理、爬虫任务执行等功能
"""

from .dependencies import (
    get_data_source_service,
    get_crawl_task_service,
    get_event_driven_crawl_rule_service,
    get_raw_data_record_service,
)
from .models import (
    DataSource,
    DataSourceConfig,
    DataSourceCredential,
    CrawlTask,
    EventDrivenCrawlRule,
    RawDataRecord,
    BusinessDataType,
)
from .schemas import (
    DataSourceCreate,
    DataSourceUpdate,
    DataSourceResponse,
    DataSourceConfigCreate,
    DataSourceConfigUpdate,
    DataSourceConfigResponse,
    DataSourceCredentialCreate,
    DataSourceCredentialUpdate,
    DataSourceCredentialResponse,
    CrawlTaskCreate,
    CrawlTaskUpdate,
    CrawlTaskResponse,
    EventDrivenCrawlRuleCreate,
    EventDrivenCrawlRuleUpdate,
    EventDrivenCrawlRuleResponse,
    RawDataRecordCreate,
    RawDataRecordUpdate,
    RawDataRecordResponse,
    CrawlResult,
    CollectionMethod,
    ContentCategory,
    CrawlMode,
)
from .service import (
    DataSourceService,
    EventDrivenCrawlRuleService,
    CrawlTaskService,
    RawDataRecordService,
)
from .task_manager import CrawlerTaskExecutor, DataCollectionTaskManager, data_collection_task_manager

# 注册爬虫任务执行器到统一任务框架
def _register_task_executor():
    """注册爬虫任务执行器"""
    try:
        from ...core.task_framework import task_manager, TaskType
        
        # 创建爬虫任务执行器实例
        crawler_executor = CrawlerTaskExecutor()
        
        # 注册到统一任务管理器
        task_manager.register_executor(TaskType.CRAWLER, crawler_executor)
        
        print("✅ 爬虫任务执行器已注册到统一任务框架")
        
    except Exception as e:
        print(f"❌ 注册爬虫任务执行器失败: {e}")


# 在模块加载时自动注册
_register_task_executor()

__all__ = [
    # Dependencies
    "get_data_source_service",
    "get_crawl_task_service",
    "get_event_driven_crawl_rule_service",
    "get_raw_data_record_service",
    
    # Models
    "DataSource",
    "DataSourceConfig",
    "DataSourceCredential",
    "CrawlTask",
    "EventDrivenCrawlRule",
    "RawDataRecord",
    "BusinessDataType",
    "CollectionMethod",
    "ContentCategory",
    "CrawlMode",
    
    # Schemas
    "DataSourceCreate",
    "DataSourceUpdate",
    "DataSourceResponse",
    "DataSourceConfigCreate",
    "DataSourceConfigUpdate",
    "DataSourceConfigResponse",
    "DataSourceCredentialCreate",
    "DataSourceCredentialUpdate",
    "DataSourceCredentialResponse",
    "CrawlTaskCreate",
    "CrawlTaskUpdate",
    "CrawlTaskResponse",
    "EventDrivenCrawlRuleCreate",
    "EventDrivenCrawlRuleUpdate",
    "EventDrivenCrawlRuleResponse",
    "RawDataRecordCreate",
    "RawDataRecordUpdate",
    "RawDataRecordResponse",
    "CrawlResult",
    
    # Services
    "DataSourceService",
    "EventDrivenCrawlRuleService",
    "CrawlTaskService",
    "RawDataRecordService",
    
    # Task Management
    "CrawlerTaskExecutor",
    "DataCollectionTaskManager",
    "data_collection_task_manager",
]
