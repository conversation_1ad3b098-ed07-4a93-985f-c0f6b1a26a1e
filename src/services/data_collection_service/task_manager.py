"""
数据采集任务管理器
基于统一任务框架的爬虫任务执行器
"""

import asyncio
import logging
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional

from sqlalchemy.orm import Session

from ...core.database import SessionLocal
from ...core.task_framework import BaseTaskExecutor, TaskContext, TaskExecutionMode, TaskType
from ...core.task_queue import submit_crawler_task, submit_batch_crawler_task
from .models import CrawlTask, DataSource, RawDataRecord
from .schemas import CrawlTaskCreate, CrawlTaskUpdate
from .service import CrawlTaskService, DataSourceService
from .crawlers.crawler_factory import CrawlerFactory

logger = logging.getLogger(__name__)


class CrawlerTaskExecutor(BaseTaskExecutor):
    """
    爬虫任务执行器
    实现统一任务框架的爬虫执行逻辑
    """
    
    def __init__(self):
        super().__init__()
        self.crawler_task_service = CrawlTaskService()
        self.data_source_service = DataSourceService()
        self.crawler_factory = CrawlerFactory()
    
    def validate_parameters(self, parameters: Dict[str, Any]) -> bool:
        """
        验证爬虫任务参数
        
        Args:
            parameters: 任务参数
            
        Returns:
            bool: 参数是否有效
        """
        required_fields = ["data_source_id"]
        
        # 检查必需字段
        for field in required_fields:
            if field not in parameters:
                self.logger.error(f"缺少必需参数: {field}")
                return False
        
        # 验证数据源ID
        data_source_id = parameters.get("data_source_id")
        if not isinstance(data_source_id, int) or data_source_id <= 0:
            self.logger.error(f"无效的数据源ID: {data_source_id}")
            return False
        
        return True
    
    def get_execution_mode(self, parameters: Dict[str, Any]) -> TaskExecutionMode:
        """
        根据任务参数确定执行模式
        
        Args:
            parameters: 任务参数
            
        Returns:
            TaskExecutionMode: 推荐的执行模式
        """
        db = SessionLocal()
        try:
            data_source_id = parameters.get("data_source_id")
            data_source = self.data_source_service.get_data_source_by_id(db, data_source_id)
            
            if not data_source:
                return TaskExecutionMode.DIRECT
            
            # 根据数据源配置决定执行模式
            config = parameters.get("config", {})
            
            # 批量模式优先使用异步队列
            if parameters.get("batch_mode", False):
                return TaskExecutionMode.ASYNC_QUEUE
            
            # 大型爬取任务使用异步队列
            max_pages = config.get("max_pages", 1)
            if max_pages > 10:
                return TaskExecutionMode.ASYNC_QUEUE
            
            # 长时间运行的任务使用异步队列
            estimated_duration = config.get("estimated_duration_minutes", 0)
            if estimated_duration > 5:
                return TaskExecutionMode.ASYNC_QUEUE
            
            # 实时采集使用异步队列
            if data_source.supports_realtime:
                return TaskExecutionMode.ASYNC_QUEUE
            
            # 默认使用直接执行（快速任务）
            return TaskExecutionMode.DIRECT
            
        finally:
            db.close()
    
    async def execute_task(self, context: TaskContext) -> Dict[str, Any]:
        """
        执行爬虫任务
        
        Args:
            context: 任务执行上下文
            
        Returns:
            Dict: 任务执行结果
        """
        db = SessionLocal()
        
        try:
            parameters = context.parameters
            data_source_id = parameters["data_source_id"]
            task_id = parameters.get("task_id")
            config = parameters.get("config", {})
            batch_mode = parameters.get("batch_mode", False)
            
            self.logger.info(f"开始执行爬虫任务: data_source_id={data_source_id}, task_id={task_id}")
            
            # 获取数据源
            data_source = self.data_source_service.get_data_source_by_id(db, data_source_id)
            if not data_source:
                raise ValueError(f"数据源不存在: {data_source_id}")
            
            # 获取或创建爬取任务
            if task_id:
                crawl_task = self.crawler_task_service.get_task_by_id(db, task_id)
                if not crawl_task:
                    raise ValueError(f"爬取任务不存在: {task_id}")
            else:
                # 创建新的爬取任务
                task_data = CrawlTaskCreate(
                    data_source_id=data_source_id,
                    task_type="scheduled" if batch_mode else "manual",
                    status="running",
                    config=config,
                    priority=context.priority
                )
                crawl_task = self.crawler_task_service.create_task(db, task_data)
                context.metadata["crawl_task_id"] = crawl_task.id
            
            # 更新任务状态
            self.crawler_task_service.update_task_status(
                db, crawl_task.id, "running", 
                f"开始执行 (Celery任务ID: {context.task_id})"
            )
            
            # 创建爬虫实例
            crawler_class = self.crawler_factory.get_crawler_class(data_source.collection_method)
            if not crawler_class:
                raise ValueError(f"不支持的采集方法: {data_source.collection_method}")
            
            # 执行爬取
            result = await self._execute_crawler(data_source, crawl_task, crawler_class, config)
            
            # 更新任务状态为成功
            self.crawler_task_service.update_task_status(
                db, crawl_task.id, "completed",
                f"爬取完成: {result.get('items_collected', 0)} 条数据"
            )
            
            return result
            
        except Exception as e:
            # 更新任务状态为失败
            if 'crawl_task' in locals():
                self.crawler_task_service.update_task_status(
                    db, crawl_task.id, "failed", str(e)
                )
            
            self.logger.error(f"爬虫任务执行失败: {e}")
            raise
            
        finally:
            db.close()
    
    async def _execute_crawler(
        self, 
        data_source: DataSource, 
        crawl_task: CrawlTask, 
        crawler_class, 
        config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        执行具体的爬虫逻辑
        
        Args:
            data_source: 数据源配置
            crawl_task: 爬取任务
            crawler_class: 爬虫类
            config: 配置参数
            
        Returns:
            Dict: 爬取结果
        """
        
        # 创建爬虫实例
        async with crawler_class(data_source, crawl_task, config) as crawler:
            # 执行爬取
            crawl_result = await crawler.crawl()
            
            # 处理爬取结果
            result = {
                "task_id": crawl_task.id,
                "data_source_id": data_source.id,
                "status": "success",
                "items_collected": crawl_result.items_collected,
                "items_failed": crawl_result.items_failed,
                "start_time": crawl_result.start_time.isoformat(),
                "end_time": crawl_result.end_time.isoformat(),
                "duration_seconds": (crawl_result.end_time - crawl_result.start_time).total_seconds(),
                "errors": crawl_result.errors[:10],  # 只保留前10个错误
                "metadata": crawl_result.metadata
            }
            
            self.logger.info(f"爬取完成: {result}")
            return result


class DataCollectionTaskManager:
    """
    数据采集任务管理器
    提供高级的任务管理和调度功能
    """
    
    def __init__(self):
        self.crawler_task_service = CrawlTaskService()
        self.data_source_service = DataSourceService()
        self.logger = logging.getLogger(__name__)
    
    async def submit_crawl_task(
        self,
        data_source_id: int,
        config: Optional[Dict[str, Any]] = None,
        execution_mode: Optional[str] = None,
        priority: int = 5
    ) -> Dict[str, Any]:
        """
        提交单个爬取任务
        
        Args:
            data_source_id: 数据源ID
            config: 配置参数
            execution_mode: 执行模式 ("direct" 或 "async")
            priority: 任务优先级
            
        Returns:
            Dict: 任务提交结果
        """
        
        db = SessionLocal()
        try:
            # 验证数据源
            data_source = self.data_source_service.get_data_source_by_id(db, data_source_id)
            if not data_source:
                raise ValueError(f"数据源不存在: {data_source_id}")
            
            # 创建爬取任务记录
            task_data = CrawlTaskCreate(
                data_source_id=data_source_id,
                task_type="manual",
                status="pending",
                config=config or {},
                priority=priority
            )
            crawl_task = self.crawler_task_service.create_task(db, task_data)
            
            # 根据执行模式提交任务
            if execution_mode == "direct":
                # 直接执行（非阻塞）
                from ...core.task_framework import task_manager, TaskType, TaskExecutionMode
                
                task_id = await task_manager.submit_task(
                    TaskType.CRAWLER,
                    {
                        "data_source_id": data_source_id,
                        "task_id": crawl_task.id,
                        "config": config or {}
                    },
                    TaskExecutionMode.DIRECT,
                    priority
                )
                
                return {
                    "crawl_task_id": crawl_task.id,
                    "execution_task_id": task_id,
                    "execution_mode": "direct",
                    "status": "submitted"
                }
                
            else:
                # 异步队列执行
                celery_task_id = submit_crawler_task(
                    data_source_id=data_source_id,
                    task_id=crawl_task.id,
                    config=config,
                    priority=priority
                )
                
                # 更新任务记录
                self.crawler_task_service.update_task_status(
                    db, crawl_task.id, "queued",
                    f"已提交到队列 (Celery任务ID: {celery_task_id})"
                )
                
                return {
                    "crawl_task_id": crawl_task.id,
                    "celery_task_id": celery_task_id,
                    "execution_mode": "async",
                    "status": "queued"
                }
                
        finally:
            db.close()
    
    async def submit_batch_crawl_tasks(
        self,
        data_source_ids: List[int],
        config: Optional[Dict[str, Any]] = None,
        priority: int = 5
    ) -> Dict[str, Any]:
        """
        批量提交爬取任务
        
        Args:
            data_source_ids: 数据源ID列表
            config: 配置参数
            priority: 任务优先级
            
        Returns:
            Dict: 批量任务提交结果
        """
        
        # 批量任务总是使用异步队列
        celery_task_ids = submit_batch_crawler_task(
            data_source_ids=data_source_ids,
            config=config,
            priority=priority
        )
        
        return {
            "batch_size": len(data_source_ids),
            "celery_task_ids": celery_task_ids,
            "execution_mode": "async",
            "status": "queued"
        }
    
    async def get_task_status(self, task_id: int) -> Optional[Dict[str, Any]]:
        """
        获取任务状态
        
        Args:
            task_id: 爬取任务ID
            
        Returns:
            Optional[Dict]: 任务状态信息
        """
        
        db = SessionLocal()
        try:
            task = self.crawler_task_service.get_task_by_id(db, task_id)
            if not task:
                return None
            
            return {
                "task_id": task.id,
                "data_source_id": task.data_source_id,
                "status": task.status,
                "task_type": task.task_type,
                "created_at": task.created_at.isoformat(),
                "started_at": task.started_at.isoformat() if task.started_at else None,
                "completed_at": task.completed_at.isoformat() if task.completed_at else None,
                "error_message": task.error_message,
                "items_collected": task.items_collected,
                "items_failed": task.items_failed,
                "progress": task.progress,
                "config": task.config
            }
            
        finally:
            db.close()
    
    async def cancel_task(self, task_id: int) -> bool:
        """
        取消任务
        
        Args:
            task_id: 爬取任务ID
            
        Returns:
            bool: 是否成功取消
        """
        
        db = SessionLocal()
        try:
            # 更新任务状态
            success = self.crawler_task_service.update_task_status(
                db, task_id, "cancelled", "用户取消"
            )
            
            if success:
                self.logger.info(f"任务已取消: {task_id}")
                
            return success
            
        finally:
            db.close()


# 全局实例
data_collection_task_manager = DataCollectionTaskManager() 