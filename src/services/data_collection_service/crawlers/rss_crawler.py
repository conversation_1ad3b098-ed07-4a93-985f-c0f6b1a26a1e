"""
RSS爬虫
专门处理RSS Feed的数据采集
"""

from typing import Any, Dict, List, Optional
from urllib.parse import urljoin

import aiohttp
import feedparser
from loguru import logger

from ..schemas import CrawlResult
from .base_crawler import BaseCrawler


class RssCrawler(BaseCrawler):
    """
    RSS Feed爬虫
    专门处理RSS订阅源的数据采集
    """

    async def crawl(self) -> CrawlResult:
        """
        执行RSS Feed采集

        Returns:
            CrawlResult: 采集结果
        """
        try:
            logger.info(f"开始RSS采集: {self.data_source.name}")

            # 获取RSS Feed
            items = await self._crawl_rss_feed()

            self.items_found = len(items)
            logger.info(f"发现 {self.items_found} 个RSS条目")

            # 处理每个数据项
            mongo_ids = []
            for item in items:
                mongo_id = await self.process_item(item)
                if mongo_id:
                    mongo_ids.append(mongo_id)

            # 构建结果
            result = CrawlResult(
                success=True,
                message=f"成功采集 {self.items_success}/{self.items_found} 个RSS条目",
                data={"mongo_ids": mongo_ids, "statistics": self.get_statistics()},
            )

            logger.info(f"RSS采集完成: {result.message}")
            return result

        except Exception as e:
            error_msg = f"RSS采集失败: {str(e)}"
            logger.error(error_msg)
            return CrawlResult(
                success=False,
                message=error_msg,
                data={"statistics": self.get_statistics()},
            )

    async def _crawl_rss_feed(self) -> List[Dict[str, Any]]:
        """
        采集RSS Feed数据

        Returns:
            List[Dict]: RSS条目列表
        """
        feed_url = self.task.target_url or self.data_source.base_url
        if not feed_url:
            raise ValueError("缺少RSS Feed URL")

        # 获取RSS内容
        rss_content = await self._fetch_rss_content(feed_url)
        self.network_requests += 1

        # 解析RSS Feed
        feed = feedparser.parse(rss_content)

        if feed.bozo:
            logger.warning(f"RSS解析警告: {feed.bozo_exception}")

        # 提取Feed信息
        feed_info = self._extract_feed_info(feed)
        logger.info(f"RSS Feed信息: {feed_info['title']} - {len(feed.entries)} 条目")

        # 转换条目为标准格式
        items = []
        for entry in feed.entries:
            item = await self._extract_rss_entry(entry, feed_info)
            if item:
                items.append(item)

        return items

    async def _fetch_rss_content(self, url: str) -> str:
        """
        获取RSS Feed内容

        Args:
            url: RSS Feed URL

        Returns:
            str: RSS内容
        """
        try:
            headers = {
                "Accept": "application/rss+xml, application/xml, text/xml, application/atom+xml",
                "User-Agent": self._get_default_headers()["User-Agent"],
            }

            async with self.session.get(url, headers=headers) as response:
                if response.status != 200:
                    raise Exception(f"HTTP {response.status}: {url}")

                content = await response.text()
                return content

        except Exception as e:
            raise Exception(f"获取RSS内容失败: {url}, 错误: {e}")

    def _extract_feed_info(self, feed) -> Dict[str, Any]:
        """
        提取Feed基本信息

        Args:
            feed: feedparser解析结果

        Returns:
            Dict: Feed信息
        """
        feed_data = feed.feed if hasattr(feed, "feed") else {}

        return {
            "title": getattr(feed_data, "title", ""),
            "description": getattr(feed_data, "description", ""),
            "link": getattr(feed_data, "link", ""),
            "language": getattr(feed_data, "language", ""),
            "copyright": getattr(feed_data, "rights", ""),
            "last_build_date": getattr(feed_data, "updated", ""),
            "generator": getattr(feed_data, "generator", ""),
            "image": self._extract_feed_image(feed_data),
            "category": getattr(feed_data, "category", ""),
            "ttl": getattr(feed_data, "ttl", ""),
        }

    def _extract_feed_image(self, feed_data) -> Dict[str, str]:
        """
        提取Feed图片信息

        Args:
            feed_data: Feed数据

        Returns:
            Dict: 图片信息
        """
        image = {}

        if hasattr(feed_data, "image"):
            image_data = feed_data.image
            image = {
                "url": getattr(image_data, "href", ""),
                "title": getattr(image_data, "title", ""),
                "link": getattr(image_data, "link", ""),
                "width": getattr(image_data, "width", ""),
                "height": getattr(image_data, "height", ""),
            }

        return image

    async def _extract_rss_entry(
        self, entry, feed_info: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """
        提取RSS条目信息

        Args:
            entry: RSS条目
            feed_info: Feed信息

        Returns:
            Dict: 条目数据，失败返回None
        """
        try:
            # 基本信息提取
            title = getattr(entry, "title", "").strip()
            content = self._extract_entry_content(entry)

            # 如果没有标题和内容，跳过
            if not title and not content:
                return None

            # 构建条目数据
            item = {
                "title": title,
                "content": content,
                "summary": getattr(entry, "summary", "").strip(),
                "author": self._extract_entry_author(entry),
                "publish_time": self._extract_entry_date(entry),
                "url": getattr(entry, "link", "").strip(),
                "guid": getattr(entry, "id", "").strip(),
                "category": self._extract_entry_category(entry),
                "tags": self._extract_entry_tags(entry),
                "enclosures": self._extract_entry_enclosures(entry),
                "comments": getattr(entry, "comments", "").strip(),
                "source": {
                    "feed_title": feed_info["title"],
                    "feed_url": feed_info["link"],
                    "feed_description": feed_info["description"],
                },
                "raw_entry": dict(entry),  # 保存原始条目数据
            }

            # 内容质量检查
            if len(item["content"]) < 10 and len(item["summary"]) < 10:
                logger.debug(f"RSS条目内容太短，跳过: {title}")
                return None

            return item

        except Exception as e:
            logger.warning(f"RSS条目提取失败: {e}")
            return None

    def _extract_entry_content(self, entry) -> str:
        """
        提取条目内容

        Args:
            entry: RSS条目

        Returns:
            str: 内容文本
        """
        content = ""

        # 尝试多个可能的内容字段
        if hasattr(entry, "content"):
            if isinstance(entry.content, list) and entry.content:
                # 选择最佳内容
                for content_item in entry.content:
                    if hasattr(content_item, "value"):
                        content = content_item.value
                        if (
                            hasattr(content_item, "type")
                            and content_item.type == "text/html"
                        ):
                            break  # 优先使用HTML内容
            else:
                content = str(entry.content)

        # 如果没有content字段，尝试其他字段
        if not content:
            if hasattr(entry, "description"):
                content = entry.description
            elif hasattr(entry, "summary"):
                content = entry.summary

        return self._clean_html_content(content)

    def _extract_entry_author(self, entry) -> str:
        """
        提取条目作者

        Args:
            entry: RSS条目

        Returns:
            str: 作者信息
        """
        if hasattr(entry, "author"):
            return entry.author.strip()
        elif hasattr(entry, "author_detail"):
            author = entry.author_detail
            if hasattr(author, "name"):
                return author.name.strip()
            elif hasattr(author, "email"):
                return author.email.strip()

        return ""

    def _extract_entry_date(self, entry) -> str:
        """
        提取条目日期

        Args:
            entry: RSS条目

        Returns:
            str: 日期字符串
        """
        # 尝试多个可能的日期字段
        date_fields = ["published", "updated", "created", "pubDate"]

        for field in date_fields:
            if hasattr(entry, field):
                date_str = getattr(entry, field)
                if date_str:
                    return str(date_str).strip()

        return ""

    def _extract_entry_category(self, entry) -> str:
        """
        提取条目分类

        Args:
            entry: RSS条目

        Returns:
            str: 分类信息
        """
        categories = []

        # 单个分类字段
        if hasattr(entry, "category"):
            categories.append(entry.category)

        # 多个分类标签
        if hasattr(entry, "tags"):
            for tag in entry.tags:
                if hasattr(tag, "term"):
                    categories.append(tag.term)

        return ", ".join(categories) if categories else ""

    def _extract_entry_tags(self, entry) -> List[str]:
        """
        提取条目标签

        Args:
            entry: RSS条目

        Returns:
            List[str]: 标签列表
        """
        tags = []

        if hasattr(entry, "tags"):
            for tag in entry.tags:
                if hasattr(tag, "term"):
                    tags.append(tag.term.strip())
                elif hasattr(tag, "label"):
                    tags.append(tag.label.strip())

        return tags

    def _extract_entry_enclosures(self, entry) -> List[Dict[str, str]]:
        """
        提取条目附件（如音频、视频）

        Args:
            entry: RSS条目

        Returns:
            List[Dict]: 附件信息列表
        """
        enclosures = []

        if hasattr(entry, "enclosures"):
            for enclosure in entry.enclosures:
                enclosure_info = {
                    "url": getattr(enclosure, "href", ""),
                    "type": getattr(enclosure, "type", ""),
                    "length": getattr(enclosure, "length", ""),
                }
                if enclosure_info["url"]:
                    enclosures.append(enclosure_info)

        return enclosures

    def _clean_html_content(self, content: str) -> str:
        """
        清理HTML内容

        Args:
            content: 原始内容

        Returns:
            str: 清理后的内容
        """
        if not content:
            return ""

        try:
            import re

            from bs4 import BeautifulSoup

            # 解析HTML
            soup = BeautifulSoup(content, "html.parser")

            # 移除脚本和样式标签
            for script in soup(["script", "style"]):
                script.decompose()

            # 获取纯文本
            text = soup.get_text()

            # 清理空白字符
            text = re.sub(r"\s+", " ", text)

            return text.strip()

        except Exception as e:
            logger.warning(f"HTML内容清理失败: {e}")
            return content.strip()

    async def _validate_rss_feed(self, url: str) -> bool:
        """
        验证RSS Feed的有效性

        Args:
            url: RSS Feed URL

        Returns:
            bool: 是否有效
        """
        try:
            content = await self._fetch_rss_content(url)
            feed = feedparser.parse(content)

            # 检查是否成功解析
            if feed.bozo and feed.bozo_exception:
                logger.warning(f"RSS Feed解析警告: {feed.bozo_exception}")

            # 检查是否有条目
            if not hasattr(feed, "entries") or len(feed.entries) == 0:
                logger.warning("RSS Feed没有条目")
                return False

            return True

        except Exception as e:
            logger.error(f"RSS Feed验证失败: {e}")
            return False

    def get_feed_metadata(self, feed) -> Dict[str, Any]:
        """
        获取Feed元数据，用于数据库存储

        Args:
            feed: feedparser解析结果

        Returns:
            Dict: Feed元数据
        """
        feed_info = self._extract_feed_info(feed)

        metadata = {
            "feed_title": feed_info["title"],
            "feed_description": feed_info["description"],
            "feed_url": self.task.target_url or self.data_source.base_url,
            "total_entries": len(feed.entries) if hasattr(feed, "entries") else 0,
            "language": feed_info["language"],
            "last_build_date": feed_info["last_build_date"],
            "generator": feed_info["generator"],
            "image": feed_info["image"],
            "crawl_timestamp": self.start_time,
        }

        return metadata
