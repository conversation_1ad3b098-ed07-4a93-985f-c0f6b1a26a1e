"""
API爬虫
处理各种API接口的数据采集，包括JSON、XML、RSS等格式
"""

import json
import xml.etree.ElementTree as ET
from typing import Any, Dict, List, Optional
from urllib.parse import urljoin

import aiohttp
import feedparser
from loguru import logger

from ..schemas import CrawlResult
from .base_crawler import BaseCrawler


class ApiCrawler(BaseCrawler):
    """
    API接口爬虫
    支持JSON、XML、RSS等格式的API数据采集
    """

    async def crawl(self) -> CrawlResult:
        """
        执行API数据采集

        Returns:
            CrawlResult: 采集结果
        """
        try:
            logger.info(f"开始API采集: {self.data_source.name}")

            # 根据采集方式选择处理函数
            if self.data_source.collection_method == "api_json":
                items = await self._crawl_json_api()
            elif self.data_source.collection_method == "api_xml":
                items = await self._crawl_xml_api()
            elif self.data_source.collection_method == "api_rss":
                items = await self._crawl_rss_feed()
            else:
                raise ValueError(
                    f"不支持的API采集方式: {self.data_source.collection_method}"
                )

            self.items_found = len(items)
            logger.info(f"发现 {self.items_found} 个数据项")

            # 处理每个数据项
            mongo_ids = []
            for item in items:
                mongo_id = await self.process_item(item)
                if mongo_id:
                    mongo_ids.append(mongo_id)

            # 构建结果
            result = CrawlResult(
                success=True,
                message=f"成功采集 {self.items_success}/{self.items_found} 个数据项",
                data={"mongo_ids": mongo_ids, "statistics": self.get_statistics()},
            )

            logger.info(f"API采集完成: {result.message}")
            return result

        except Exception as e:
            error_msg = f"API采集失败: {str(e)}"
            logger.error(error_msg)
            return CrawlResult(
                success=False,
                message=error_msg,
                data={"statistics": self.get_statistics()},
            )

    async def _crawl_json_api(self) -> List[Dict[str, Any]]:
        """
        采集JSON API数据

        Returns:
            List[Dict]: 数据项列表
        """
        target_url = self.task.target_url or self.data_source.base_url
        if not target_url:
            raise ValueError("缺少目标URL")

        # 获取配置
        config = self._get_extraction_config()

        # 发起HTTP请求
        response_data = await self._make_request(target_url)
        self.network_requests += 1

        # 解析JSON响应
        if isinstance(response_data, str):
            try:
                json_data = json.loads(response_data)
            except json.JSONDecodeError as e:
                raise ValueError(f"JSON解析失败: {e}")
        else:
            json_data = response_data

        # 提取数据项
        items = self._extract_json_items(json_data, config)
        return items

    async def _crawl_xml_api(self) -> List[Dict[str, Any]]:
        """
        采集XML API数据

        Returns:
            List[Dict]: 数据项列表
        """
        target_url = self.task.target_url or self.data_source.base_url
        if not target_url:
            raise ValueError("缺少目标URL")

        # 获取配置
        config = self._get_extraction_config()

        # 发起HTTP请求
        response_data = await self._make_request(target_url)
        self.network_requests += 1

        # 解析XML响应
        try:
            root = ET.fromstring(response_data)
        except ET.ParseError as e:
            raise ValueError(f"XML解析失败: {e}")

        # 提取数据项
        items = self._extract_xml_items(root, config)
        return items

    async def _crawl_rss_feed(self) -> List[Dict[str, Any]]:
        """
        采集RSS Feed数据

        Returns:
            List[Dict]: 数据项列表
        """
        target_url = self.task.target_url or self.data_source.base_url
        if not target_url:
            raise ValueError("缺少目标URL")

        # 发起HTTP请求
        response_data = await self._make_request(target_url)
        self.network_requests += 1

        # 使用feedparser解析RSS
        feed = feedparser.parse(response_data)

        if feed.bozo:
            logger.warning(f"RSS解析警告: {feed.bozo_exception}")

        # 转换为标准格式
        items = []
        for entry in feed.entries:
            item = {
                "title": getattr(entry, "title", ""),
                "content": self._extract_rss_content(entry),
                "author": getattr(entry, "author", ""),
                "publish_time": self._extract_rss_date(entry),
                "url": getattr(entry, "link", ""),
                "summary": getattr(entry, "summary", ""),
                "tags": [tag.term for tag in getattr(entry, "tags", [])],
                "category": getattr(entry, "category", ""),
                "guid": getattr(entry, "id", ""),
            }
            items.append(item)

        return items

    async def _make_request(
        self, url: str, method: str = "GET", params: Dict = None, data: Dict = None
    ) -> str:
        """
        发起HTTP请求，支持多种认证方式

        Args:
            url: 请求URL
            method: HTTP方法 (GET, POST等)
            params: URL参数
            data: 请求体数据

        Returns:
            str: 响应内容
        """
        try:
            # 准备请求参数
            request_params = {}

            # 处理OAuth 1.0a认证 (如Twitter)
            if hasattr(self, "_oauth_1_0a_credentials"):
                # 为OAuth 1.0a动态生成Authorization头
                oauth_auth_header = self._build_oauth_1_0a_signature(
                    method, url, params
                )

                # 创建临时session或更新现有session的headers
                if not hasattr(self, "_oauth_session") or self._oauth_session.closed:
                    # 如果存在旧的会话，确保它被关闭
                    if hasattr(self, "_oauth_session") and not self._oauth_session.closed:
                        await self._oauth_session.close()
                    
                    import aiohttp
                    timeout = aiohttp.ClientTimeout(total=30)
                    connector = aiohttp.TCPConnector(limit=100, limit_per_host=10)
                    self._oauth_session = aiohttp.ClientSession(
                        timeout=timeout,
                        connector=connector,
                        headers={"Authorization": oauth_auth_header},
                    )
                else:
                    # 更新已有session的认证头
                    self._oauth_session.headers.update(
                        {"Authorization": oauth_auth_header}
                    )

                session_to_use = self._oauth_session
            else:
                session_to_use = self.session

            # 添加认证参数到URL（如果需要）
            final_url = await self._add_auth_params_to_url(url)

            # 准备请求参数
            if params:
                request_params["params"] = params
            if data:
                if method.upper() == "POST":
                    request_params["json"] = data
                else:
                    request_params["data"] = data

            # 发起请求
            async with session_to_use.request(
                method, final_url, **request_params
            ) as response:
                if response.status != 200:
                    raise aiohttp.ClientResponseError(
                        request_info=response.request_info,
                        history=response.history,
                        status=response.status,
                        message=f"HTTP {response.status}",
                    )

                # 根据内容类型选择解码方式
                content_type = response.headers.get("content-type", "").lower()

                if "application/json" in content_type:
                    # JSON响应
                    json_data = await response.json()
                    import json
                    return json.dumps(json_data, ensure_ascii=False, indent=2)
                else:
                    # 文本响应
                    text = await response.text()
                    return text

        except Exception as e:
            logger.error(f"请求失败 {url}: {e}")
            raise

    async def _add_auth_params_to_url(self, url: str) -> str:
        """
        添加认证参数到URL（用于部分API需要在URL中传递认证信息的情况）

        Args:
            url: 原始URL

        Returns:
            str: 添加认证参数后的URL
        """
        try:
            # 获取认证凭证
            credentials = await self._get_data_source_credentials()
            if not credentials:
                return url

            credential_type = credentials.get("credential_type", "")

            # 对于API Key类型，可能需要作为URL参数传递
            if credential_type == "api_key":
                api_key = credentials.get("api_key")
                api_key_param = credentials.get("api_key_param")  # URL参数名称

                if api_key and api_key_param:
                    from urllib.parse import (parse_qs, urlencode, urlparse,
                                              urlunparse)

                    # 解析URL
                    parsed = urlparse(url)
                    query_params = parse_qs(parsed.query)

                    # 添加API Key参数
                    query_params[api_key_param] = [api_key]

                    # 重构URL
                    new_query = urlencode(query_params, doseq=True)
                    new_url = urlunparse(
                        (
                            parsed.scheme,
                            parsed.netloc,
                            parsed.path,
                            parsed.params,
                            new_query,
                            parsed.fragment,
                        )
                    )

                    logger.debug(f"添加API Key参数到URL: {api_key_param}")
                    return new_url

            return url

        except Exception as e:
            logger.error(f"添加认证参数到URL失败: {e}")
            return url

    def _get_extraction_config(self) -> Dict[str, Any]:
        """
        获取数据提取配置

        Returns:
            Dict: 提取配置
        """
        if hasattr(self.data_source, "configs") and self.data_source.configs:
            config = self.data_source.configs[0]  # 使用最新配置
            return config.extraction_rules or {}
        return {}

    def _extract_json_items(
        self, json_data: Dict[str, Any], config: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """
        从JSON数据中提取数据项

        Args:
            json_data: JSON数据
            config: 提取配置

        Returns:
            List[Dict]: 数据项列表
        """
        items = []

        # 获取数据路径
        data_path = config.get("data_path", "$")

        try:
            # 简单的JSONPath实现
            if data_path == "$":
                data = json_data
            elif data_path.startswith("$."):
                # 支持简单的点号路径，如 $.data.items
                keys = data_path[2:].split(".")
                data = json_data
                for key in keys:
                    if isinstance(data, dict) and key in data:
                        data = data[key]
                    else:
                        raise KeyError(f"路径不存在: {data_path}")
            else:
                data = json_data

            # 如果data是列表，直接使用
            if isinstance(data, list):
                for item in data:
                    extracted = self._extract_json_fields(item, config)
                    if extracted:
                        items.append(extracted)

            # 如果data是字典，可能包含items字段
            elif isinstance(data, dict):
                if "items" in data:
                    for item in data["items"]:
                        extracted = self._extract_json_fields(item, config)
                        if extracted:
                            items.append(extracted)
                else:
                    # 当作单个项目处理
                    extracted = self._extract_json_fields(data, config)
                    if extracted:
                        items.append(extracted)

        except Exception as e:
            logger.error(f"JSON数据提取失败: {e}")

        return items

    def _extract_json_fields(
        self, item: Dict[str, Any], config: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """
        从JSON项目中提取字段

        Args:
            item: JSON数据项
            config: 提取配置

        Returns:
            Dict: 提取的字段，失败返回None
        """
        try:
            extracted = {}

            # 提取标题
            title_field = config.get("title_field", "title")
            extracted["title"] = item.get(title_field, "")

            # 提取内容
            content_field = config.get("content_field", "content")
            extracted["content"] = item.get(content_field, "")

            # 提取作者
            author_field = config.get("author_field", "author")
            extracted["author"] = item.get(author_field, "")

            # 提取时间
            time_field = config.get("time_field", "publish_time")
            extracted["publish_time"] = item.get(time_field, "")

            # 提取URL
            url_field = config.get("url_field", "url")
            extracted["url"] = item.get(url_field, "")

            # 保留原始数据
            extracted["raw_item"] = item

            return extracted

        except Exception as e:
            logger.warning(f"JSON字段提取失败: {e}")
            return None

    def _extract_xml_items(
        self, root: ET.Element, config: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """
        从XML数据中提取数据项

        Args:
            root: XML根元素
            config: 提取配置

        Returns:
            List[Dict]: 数据项列表
        """
        items = []

        # 获取项目路径
        item_path = config.get("item_path", ".//item")

        try:
            # 查找所有项目元素
            item_elements = root.findall(item_path)

            for item_element in item_elements:
                extracted = self._extract_xml_fields(item_element, config)
                if extracted:
                    items.append(extracted)

        except Exception as e:
            logger.error(f"XML数据提取失败: {e}")

        return items

    def _extract_xml_fields(
        self, element: ET.Element, config: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """
        从XML元素中提取字段

        Args:
            element: XML元素
            config: 提取配置

        Returns:
            Dict: 提取的字段，失败返回None
        """
        try:
            extracted = {}

            # 提取标题
            title_field = config.get("title_field", "title")
            title_elem = element.find(title_field)
            extracted["title"] = title_elem.text if title_elem is not None else ""

            # 提取内容
            content_field = config.get("content_field", "description")
            content_elem = element.find(content_field)
            extracted["content"] = content_elem.text if content_elem is not None else ""

            # 提取时间
            time_field = config.get("time_field", "pubDate")
            time_elem = element.find(time_field)
            extracted["publish_time"] = time_elem.text if time_elem is not None else ""

            # 提取URL
            url_field = config.get("url_field", "link")
            url_elem = element.find(url_field)
            extracted["url"] = url_elem.text if url_elem is not None else ""

            # 转换XML为字典格式保存
            extracted["raw_xml"] = self._xml_to_dict(element)

            return extracted

        except Exception as e:
            logger.warning(f"XML字段提取失败: {e}")
            return None

    def _extract_rss_content(self, entry) -> str:
        """
        提取RSS条目的内容

        Args:
            entry: RSS条目

        Returns:
            str: 内容文本
        """
        # 尝试多个可能的内容字段
        content = ""

        if hasattr(entry, "content"):
            if isinstance(entry.content, list) and entry.content:
                content = entry.content[0].value
            else:
                content = str(entry.content)
        elif hasattr(entry, "description"):
            content = entry.description
        elif hasattr(entry, "summary"):
            content = entry.summary

        return content.strip()

    def _extract_rss_date(self, entry) -> str:
        """
        提取RSS条目的日期

        Args:
            entry: RSS条目

        Returns:
            str: 日期字符串
        """
        if hasattr(entry, "published"):
            return entry.published
        elif hasattr(entry, "updated"):
            return entry.updated
        elif hasattr(entry, "pubDate"):
            return entry.pubDate
        return ""

    def _xml_to_dict(self, element: ET.Element) -> Dict[str, Any]:
        """
        将XML元素转换为字典

        Args:
            element: XML元素

        Returns:
            Dict: 字典表示
        """
        result = {}

        # 添加元素文本
        if element.text and element.text.strip():
            result["text"] = element.text.strip()

        # 添加属性
        if element.attrib:
            result["attributes"] = element.attrib

        # 添加子元素
        children = {}
        for child in element:
            child_dict = self._xml_to_dict(child)
            if child.tag in children:
                # 如果已存在，转换为列表
                if not isinstance(children[child.tag], list):
                    children[child.tag] = [children[child.tag]]
                children[child.tag].append(child_dict)
            else:
                children[child.tag] = child_dict

        if children:
            result["children"] = children

        return result

    async def cleanup(self):
        """
        清理资源，包括OAuth session
        """
        try:
            # 清理OAuth session
            if hasattr(self, "_oauth_session") and not self._oauth_session.closed:
                await self._oauth_session.close()
                self._oauth_session = None

            # 调用父类清理方法
            await super().cleanup()

        except Exception as e:
            logger.error(f"清理资源失败: {e}")
            # 确保即使出错也调用父类清理方法
            try:
                await super().cleanup()
            except Exception as e2:
                logger.error(f"父类清理方法失败: {e2}")
