"""
网页爬虫
使用Playwright处理动态网页内容的爬取
"""

import asyncio
import re
from typing import Any, Dict, List, Optional, Union
from urllib.parse import urljoin, urlparse

from bs4 import BeautifulSoup
from loguru import logger
from playwright.async_api import <PERSON><PERSON><PERSON>, <PERSON>, Playwright, async_playwright

from ..schemas import CrawlResult
from .base_crawler import BaseCrawler


class WebCrawler(BaseCrawler):
    """
    网页爬虫
    支持静态和动态网页的数据采集
    """

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.browser = None
        self.page = None
        self.playwright = None

    async def setup(self):
        """
        设置爬虫环境，包括Playwright浏览器
        """
        await super().setup()

        # 初始化Playwright
        self.playwright = await async_playwright().start()

        # 启动浏览器
        browser_config = self._get_browser_config()
        self.browser = await self.playwright.chromium.launch(**browser_config)

        # 创建页面
        page_config = self._get_page_config()
        self.page = await self.browser.new_page(**page_config)

        # 设置页面事件监听
        await self._setup_page_handlers()

        logger.info("Playwright浏览器已启动")

    async def cleanup(self):
        """
        清理资源，关闭浏览器
        """
        if self.page:
            await self.page.close()

        if self.browser:
            await self.browser.close()

        if self.playwright:
            await self.playwright.stop()

        await super().cleanup()

    async def crawl(self) -> CrawlResult:
        """
        执行网页爬取任务

        Returns:
            CrawlResult: 爬取结果
        """
        try:
            logger.info(f"开始网页采集: {self.data_source.name}")

            # 根据采集方式选择处理函数
            if self.data_source.collection_method == "web_scraping":
                items = await self._crawl_static_pages()
            elif self.data_source.collection_method == "web_dynamic":
                items = await self._crawl_dynamic_pages()
            else:
                raise ValueError(
                    f"不支持的网页采集方式: {self.data_source.collection_method}"
                )

            self.items_found = len(items)
            logger.info(f"发现 {self.items_found} 个数据项")

            # 处理每个数据项
            mongo_ids = []
            for item in items:
                mongo_id = await self.process_item(item)
                if mongo_id:
                    mongo_ids.append(mongo_id)

            # 构建结果
            result = CrawlResult(
                success=True,
                message=f"成功采集 {self.items_success}/{self.items_found} 个数据项",
                data={"mongo_ids": mongo_ids, "statistics": self.get_statistics()},
            )

            logger.info(f"网页采集完成: {result.message}")
            return result

        except Exception as e:
            error_msg = f"网页采集失败: {str(e)}"
            logger.error(error_msg)
            return CrawlResult(
                success=False,
                message=error_msg,
                data={"statistics": self.get_statistics()},
            )

    async def _crawl_static_pages(self) -> List[Dict[str, Any]]:
        """
        采集静态网页数据

        Returns:
            List[Dict]: 数据项列表
        """
        target_url = self.task.target_url or self.data_source.base_url
        if not target_url:
            raise ValueError("缺少目标URL")

        # 获取页面内容
        html_content = await self._fetch_page_content(target_url)
        self.network_requests += 1

        # 解析页面内容
        items = await self._parse_page_content(html_content, target_url)

        return items

    async def _crawl_dynamic_pages(self) -> List[Dict[str, Any]]:
        """
        采集动态网页数据

        Returns:
            List[Dict]: 数据项列表
        """
        target_url = self.task.target_url or self.data_source.base_url
        if not target_url:
            raise ValueError("缺少目标URL")

        # 导航到页面
        await self.page.goto(target_url, wait_until="networkidle")
        self.network_requests += 1

        # 执行动态加载逻辑
        await self._handle_dynamic_content()

        # 获取最终页面内容
        html_content = await self.page.content()

        # 解析页面内容
        items = await self._parse_page_content(html_content, target_url)

        return items

    async def _fetch_page_content(self, url: str) -> str:
        """
        获取静态页面内容

        Args:
            url: 页面URL

        Returns:
            str: 页面HTML内容
        """
        try:
            async with self.session.get(url) as response:
                if response.status != 200:
                    raise Exception(f"HTTP {response.status}: {url}")

                content = await response.text()
                return content

        except Exception as e:
            raise Exception(f"获取页面内容失败: {url}, 错误: {e}")

    async def _parse_page_content(
        self, html_content: str, base_url: str
    ) -> List[Dict[str, Any]]:
        """
        解析页面内容，提取数据项

        Args:
            html_content: HTML内容
            base_url: 基础URL

        Returns:
            List[Dict]: 数据项列表
        """
        items = []

        try:
            # 使用BeautifulSoup解析HTML
            soup = BeautifulSoup(html_content, "html.parser")

            # 获取选择器配置
            config = self._get_selector_config()

            # 根据任务类型选择解析方式
            task_type = self.task.task_type

            if task_type == "news_list":
                items = await self._parse_news_list(soup, config, base_url)
            elif task_type == "flash_news":
                items = await self._parse_flash_news(soup, config, base_url)
            elif task_type == "single_page":
                items = await self._parse_single_page(soup, config, base_url)
            else:
                # 通用解析
                items = await self._parse_generic_content(soup, config, base_url)

        except Exception as e:
            logger.error(f"页面内容解析失败: {e}")

        return items

    async def _parse_news_list(
        self, soup: BeautifulSoup, config: Dict[str, Any], base_url: str
    ) -> List[Dict[str, Any]]:
        """
        解析新闻列表页面

        Args:
            soup: BeautifulSoup对象
            config: 选择器配置
            base_url: 基础URL

        Returns:
            List[Dict]: 新闻项列表
        """
        items = []

        # 获取新闻列表容器
        list_selector = config.get("list_selector", ".news-list li, .article-list li")
        news_elements = soup.select(list_selector)

        logger.info(f"找到 {len(news_elements)} 个新闻项")

        for element in news_elements:
            try:
                item = await self._extract_news_item(element, config, base_url)
                if item:
                    items.append(item)
            except Exception as e:
                logger.warning(f"提取新闻项失败: {e}")

        return items

    async def _parse_flash_news(
        self, soup: BeautifulSoup, config: Dict[str, Any], base_url: str
    ) -> List[Dict[str, Any]]:
        """
        解析快讯页面

        Args:
            soup: BeautifulSoup对象
            config: 选择器配置
            base_url: 基础URL

        Returns:
            List[Dict]: 快讯项列表
        """
        items = []
        site_domain = self._get_site_domain(base_url)

        # 获取快讯列表容器 - 使用配置中的flash_selector，默认使用通用选择器
        list_selector = config.get(
            "flash_selector",
            '.flash-item, .news-item, .timeline-item, [class*="flash"], [class*="news"], li, .item',
        )
        flash_elements = soup.select(list_selector)

        logger.info(f"使用选择器 '{list_selector}' 找到 {len(flash_elements)} 个快讯项")

        # 如果没有找到元素，尝试备用选择器
        if not flash_elements:
            backup_selectors = config.get("backup_selectors", {}).get("flash_items", [])
            for backup_selector in backup_selectors:
                logger.debug(f"尝试备用选择器: {backup_selector}")
                try:
                    flash_elements = soup.select(backup_selector)
                    if flash_elements:
                        logger.info(f"使用备用选择器 '{backup_selector}' 找到 {len(flash_elements)} 个快讯项")
                        break
                except Exception as e:
                    logger.debug(f"备用选择器 '{backup_selector}' 失败: {e}")
                    continue

        # 如果还是没有找到，尝试通用选择器（针对金十数据的特殊处理）
        if not flash_elements and "jin10.com" in base_url:
            # 尝试查找包含时间戳的元素（金十数据的特征）
            time_patterns = [
                "div:contains('19:')",  # 包含19:xx时间的div
                "div:contains('18:')",  # 包含18:xx时间的div
                "div:contains('20:')",  # 包含20:xx时间的div
                "*:contains('19:')",    # 直接包含时间的元素
                "*:contains('18:')",
                "*:contains('20:')",
                "div",                  # 最后尝试所有div
            ]

            for pattern in time_patterns:
                try:
                    elements = soup.select(pattern)
                    # 过滤出真正包含时间戳的元素
                    flash_elements = []
                    for elem in elements:
                        text = elem.get_text(strip=True)
                        if any(time_pattern in text for time_pattern in ['19:', '18:', '20:', '21:', '17:']):
                            flash_elements.append(elem)

                    if flash_elements:
                        logger.info(f"使用时间模式选择器 '{pattern}' 找到 {len(flash_elements)} 个快讯项")
                        break
                except Exception as e:
                    logger.debug(f"时间模式选择器 '{pattern}' 失败: {e}")
                    continue

        # 获取重要性过滤配置
        importance_filter_config = self._get_importance_filter_config()
        
        # 获取站点特定配置
        site_specific_configs = importance_filter_config.get("site_specific_configs", {})
        site_config = site_specific_configs.get(site_domain, site_specific_configs.get("default", {}))
        
        # 记录过滤前的数量
        total_items = len(flash_elements)
        filtered_count = 0
        
        for i, element in enumerate(flash_elements):
            try:
                logger.debug(f"处理第 {i+1}/{len(flash_elements)} 个快讯元素")

                # 检查是否启用重要性过滤
                if importance_filter_config.get("enabled", False):
                    # 获取站点特定的重要性选择器
                    importance_selector = site_config.get("importance_selector", "")
                    reject_without_importance = site_config.get("reject_without_importance", False)

                    if importance_selector:
                        # 使用站点特定的选择器检查重要性
                        if not self._element_matches_importance(element, importance_selector):
                            if reject_without_importance:
                                filtered_count += 1
                                logger.debug(f"快讯项不满足站点 {site_domain} 的重要性要求，跳过")
                                continue
                    else:
                        # 使用通用的重要性类模式
                        importance_patterns = importance_filter_config.get("importance_class_patterns", [])
                        if importance_patterns:
                            pattern_selectors = [f".{pattern}" for pattern in importance_patterns]
                            if not any(self._element_matches_importance(element, selector) for selector in pattern_selectors):
                                if reject_without_importance:
                                    filtered_count += 1
                                    logger.debug("快讯项不满足通用重要性要求，跳过")
                                    continue
                else:
                    logger.debug("重要性过滤已禁用，处理所有快讯项")

                # 提取数据项
                item = await self._extract_flash_item(element, config, base_url)
                if item:
                    items.append(item)
                    logger.debug(f"成功提取快讯项 {len(items)}: {item['title'][:50]}...")
                else:
                    logger.debug(f"快讯项 {i+1} 提取失败，跳过")

            except Exception as e:
                logger.warning(f"提取快讯项 {i+1} 失败: {e}", exc_info=True)

        # 记录过滤结果
        logger.info(f"站点 {site_domain} - 总计: {total_items}, 过滤: {filtered_count}, 保留: {len(items)}")
        return items

    async def _parse_single_page(
        self, soup: BeautifulSoup, config: Dict[str, Any], base_url: str
    ) -> List[Dict[str, Any]]:
        """
        解析单页面内容

        Args:
            soup: BeautifulSoup对象
            config: 选择器配置
            base_url: 基础URL

        Returns:
            List[Dict]: 内容项列表
        """
        items = []

        try:
            item = {
                "title": self._extract_by_selector(
                    soup, config.get("title", "h1, .title")
                ),
                "content": self._extract_by_selector(
                    soup, config.get("content", ".content, .article, main")
                ),
                "author": self._extract_by_selector(
                    soup, config.get("author", ".author, .byline")
                ),
                "publish_time": self._extract_by_selector(
                    soup, config.get("time", ".time, .date, .publish-time")
                ),
                "url": base_url,
                "source_html": str(soup),
            }

            if item["title"] or item["content"]:
                items.append(item)

        except Exception as e:
            logger.error(f"单页面解析失败: {e}")

        return items

    async def _parse_generic_content(
        self, soup: BeautifulSoup, config: Dict[str, Any], base_url: str
    ) -> List[Dict[str, Any]]:
        """
        通用内容解析

        Args:
            soup: BeautifulSoup对象
            config: 选择器配置
            base_url: 基础URL

        Returns:
            List[Dict]: 内容项列表
        """
        items = []

        # 尝试找到文章容器
        content_selectors = [
            "article",
            ".article",
            ".post",
            ".content-item",
            ".news-item",
            "main .content",
            ".main-content",
        ]

        for selector in content_selectors:
            elements = soup.select(selector)
            if elements:
                for element in elements[:10]:  # 限制数量
                    try:
                        item = await self._extract_generic_item(
                            element, config, base_url
                        )
                        if item:
                            items.append(item)
                    except Exception as e:
                        logger.warning(f"通用内容提取失败: {e}")
                break

        return items

    async def _extract_news_item(
        self, element, config: Dict[str, Any], base_url: str
    ) -> Optional[Dict[str, Any]]:
        """
        从元素中提取新闻项

        Args:
            element: BeautifulSoup元素
            config: 配置
            base_url: 基础URL

        Returns:
            Dict: 新闻项，失败返回None
        """
        try:
            title = self._extract_by_selector(
                element, config.get("title", "a, .title, h3")
            )
            link = self._extract_link(element, config.get("link", "a"), base_url)

            if not title:
                return None

            item = {
                "title": title,
                "url": link,
                "content": self._extract_by_selector(
                    element, config.get("summary", ".summary, .desc")
                ),
                "author": self._extract_by_selector(
                    element, config.get("author", ".author")
                ),
                "publish_time": self._extract_by_selector(
                    element, config.get("time", ".time, .date")
                ),
                "category": self._extract_by_selector(
                    element, config.get("category", ".category")
                ),
                "source_html": str(element),
            }

            return item

        except Exception as e:
            logger.warning(f"新闻项提取失败: {e}")
            return None

    async def _extract_flash_item(
        self, element, config: Dict[str, Any], base_url: str
    ) -> Optional[Dict[str, Any]]:
        """
        从元素中提取快讯项

        Args:
            element: BeautifulSoup元素
            config: 配置
            base_url: 基础URL

        Returns:
            Dict: 快讯项，失败返回None
        """
        try:
            # 使用金十数据专用选择器提取标题和内容
            title_selectors = [
                config.get("title_selector", ".J_flash_text"),
                config.get("content_selector", ".J_flash_text"),
                ".J_flash_text",
                ".jin-flash_text-box p",
                ".flash-text",
                "p.J_flash_text"
            ]

            # 提取内容（金十数据的标题和内容通常在同一个元素中）
            content = None
            title = None

            for selector in title_selectors:
                content = self._extract_by_selector(element, selector)
                if content and content.strip():
                    title = content  # 金十数据的快讯标题就是内容
                    break

            # 如果还是没有内容，尝试备用选择器
            if not content:
                backup_selectors = config.get("backup_selectors", {}).get("content", [])
                for selector in backup_selectors:
                    content = self._extract_by_selector(element, selector)
                    if content and content.strip():
                        title = content
                        break

            # 如果内容为空，记录调试信息并返回None
            if not content:
                logger.debug(f"无法提取内容，元素HTML: {str(element)[:200]}...")
                return None

            # 清理内容
            content = content.strip()
            title = title.strip() if title else content

            # 提取时间信息
            time_selectors = [
                config.get("time_selector", ".jin-flash_time"),
                ".jin-flash_time",
                ".J_flash_time",
                ".time",
                "[class*=\"time\"]"
            ]

            publish_time = None
            for selector in time_selectors:
                time_text = self._extract_by_selector(element, selector)
                if time_text:
                    publish_time = time_text
                    break

            # 获取重要性过滤配置
            importance_filter_config = self._get_importance_filter_config()
            site_domain = self._get_site_domain(base_url)
            site_specific_configs = importance_filter_config.get("site_specific_configs", {})
            site_config = site_specific_configs.get(site_domain, site_specific_configs.get("default", {}))

            # 确定重要性（暂时设为普通，因为我们禁用了重要性过滤）
            is_important = False
            importance_level = 1

            if importance_filter_config.get("enabled", False):
                # 使用站点特定的选择器
                importance_selector = site_config.get("importance_selector", "")
                if importance_selector:
                    is_important = self._element_matches_importance(element, importance_selector)

                # 设置重要性级别
                if is_important:
                    importance_level = site_config.get("min_importance_level", 3)

            item = {
                "title": title,
                "content": content,
                "publish_time": publish_time,
                "source": "金十数据",
                "importance": "high" if is_important else "normal",
                "is_important": is_important,
                "importance_level": importance_level,
                "url": base_url,
                "source_html": str(element)[:500],  # 限制HTML长度
            }

            logger.debug(f"成功提取快讯项: {title[:50]}...")
            return item

        except Exception as e:
            logger.warning(f"快讯项提取失败: {e}")
            return None

    async def _extract_generic_item(
        self, element, config: Dict[str, Any], base_url: str
    ) -> Optional[Dict[str, Any]]:
        """
        通用内容项提取

        Args:
            element: BeautifulSoup元素
            config: 配置
            base_url: 基础URL

        Returns:
            Dict: 内容项，失败返回None
        """
        try:
            title = self._extract_by_selector(
                element, config.get("title", "h1, h2, h3, .title")
            )
            content = self._extract_by_selector(
                element, config.get("content", ".content, .text, p")
            )

            if not title and not content:
                return None

            item = {
                "title": title,
                "content": content,
                "author": self._extract_by_selector(
                    element, config.get("author", ".author")
                ),
                "publish_time": self._extract_by_selector(
                    element, config.get("time", ".time, .date")
                ),
                "url": base_url,
                "source_html": str(element),
            }

            return item

        except Exception as e:
            logger.warning(f"通用内容项提取失败: {e}")
            return None

    def _extract_by_selector(self, element, selector: str) -> str:
        """
        通过CSS选择器提取文本

        Args:
            element: BeautifulSoup元素
            selector: CSS选择器

        Returns:
            str: 提取的文本
        """
        try:
            if isinstance(selector, str):
                selectors = [s.strip() for s in selector.split(",")]
            else:
                selectors = [selector]

            for sel in selectors:
                found = element.select_one(sel)
                if found:
                    # 清理文本
                    text = found.get_text(strip=True)
                    if text:
                        return self._clean_text(text)

            return ""

        except Exception:
            return ""

    def _extract_link(self, element, selector: str, base_url: str) -> str:
        """
        提取链接

        Args:
            element: BeautifulSoup元素
            selector: CSS选择器
            base_url: 基础URL

        Returns:
            str: 完整URL
        """
        try:
            link_element = element.select_one(selector)
            if link_element:
                href = link_element.get("href")
                if href:
                    return self._normalize_url(href, base_url)
            return ""
        except Exception:
            return ""

    def _clean_text(self, text: str) -> str:
        """
        清理提取的文本

        Args:
            text: 原始文本

        Returns:
            str: 清理后的文本
        """
        if not text:
            return ""

        # 移除多余的空白字符
        text = re.sub(r"\s+", " ", text)

        # 移除HTML实体
        import html

        text = html.unescape(text)

        return text.strip()

    def _get_selector_config(self) -> Dict[str, Any]:
        """
        获取选择器配置

        Returns:
            Dict: 选择器配置
        """
        try:
            # 优先从预加载的关系中获取
            if hasattr(self.data_source, "configs") and self.data_source.configs:
                config = self.data_source.configs[0]  # 使用最新配置
                return config.selector_config or {}

            # 如果没有预加载，主动从数据库获取
            from ....core.database import SessionLocal
            from ..service import DataSourceConfigService

            config_service = DataSourceConfigService()
            db = SessionLocal()
            try:
                active_config = config_service.get_active_config_by_source(
                    db, self.data_source.id
                )
                if active_config:
                    return active_config.selector_config or {}
                return {}
            finally:
                db.close()

        except Exception as e:
            logger.warning(f"获取选择器配置失败: {e}")
            return {}

    def _get_browser_config(self) -> Dict[str, Any]:
        """
        获取浏览器配置

        Returns:
            Dict: 浏览器配置
        """
        config = {
            "headless": True,
            "args": [
                "--no-sandbox",
                "--disable-setuid-sandbox",
                "--disable-dev-shm-usage",
                "--disable-web-security",
                "--disable-features=VizDisplayCompositor",
            ],
        }

        # 如果需要使用代理
        if self.data_source.use_proxy and self.data_source.proxy_pool:
            # TODO: 实现代理配置
            pass

        return config

    def _get_page_config(self) -> Dict[str, Any]:
        """
        获取页面配置

        Returns:
            Dict: 页面配置
        """
        config = {
            "viewport": {"width": 1920, "height": 1080},
            "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
        }

        return config

    async def _setup_page_handlers(self):
        """
        设置页面事件处理器
        """

        # 监听网络请求
        async def handle_request(request):
            logger.debug(f"Request: {request.method} {request.url}")

        # 监听响应
        async def handle_response(response):
            logger.debug(f"Response: {response.status} {response.url}")

        self.page.on("request", handle_request)
        self.page.on("response", handle_response)

    async def _handle_dynamic_content(self):
        """
        处理动态内容加载
        """
        try:
            # 获取JavaScript配置
            if hasattr(self.data_source, "configs") and self.data_source.configs:
                config = self.data_source.configs[0]
                js_config = config.javascript_config or {}

                # 等待特定元素
                wait_selector = js_config.get("wait_for_element")
                if wait_selector:
                    await self.page.wait_for_selector(wait_selector, timeout=10000)

                # 滚动加载更多内容
                if js_config.get("scroll_to_bottom"):
                    await self._scroll_to_bottom()

                # 执行自定义JavaScript
                custom_js = js_config.get("custom_script")
                if custom_js:
                    await self.page.evaluate(custom_js)

                # 等待网络空闲
                await self.page.wait_for_load_state("networkidle")

        except Exception as e:
            logger.warning(f"动态内容处理失败: {e}")

    async def _scroll_to_bottom(self):
        """
        滚动到页面底部，触发懒加载内容
        """
        try:
            await self.page.evaluate(
                """
                () => {
                    return new Promise((resolve) => {
                        let totalHeight = 0;
                        let distance = 100;
                        let timer = setInterval(() => {
                            let scrollHeight = document.body.scrollHeight;
                            window.scrollBy(0, distance);
                            totalHeight += distance;
                            
                            if(totalHeight >= scrollHeight){
                                clearInterval(timer);
                                resolve();
                            }
                        }, 100);
                    });
                }
            """
            )
        except Exception as e:
            logger.warning(f"滚动页面失败: {e}")

    def _get_importance_filter_config(self) -> Dict[str, Any]:
        """
        获取重要性过滤配置
        从selector_config中获取重要性过滤相关配置

        Returns:
            Dict: 重要性过滤配置
        """
        try:
            # 优先从预加载的关系中获取
            if hasattr(self.data_source, "configs") and self.data_source.configs:
                config = self.data_source.configs[0]  # 使用最新配置
                selector_config = config.selector_config or {}
                return selector_config.get("importance_filter", {})

            # 如果没有预加载，主动从数据库获取
            from ....core.database import SessionLocal
            from ..service import DataSourceConfigService

            config_service = DataSourceConfigService()
            db = SessionLocal()
            try:
                active_config = config_service.get_active_config_by_source(
                    db, self.data_source.id
                )
                if active_config:
                    selector_config = active_config.selector_config or {}
                    return selector_config.get("importance_filter", {})
                return {}
            finally:
                db.close()

        except Exception as e:
            logger.warning(f"获取重要性过滤配置失败: {e}")
            return {}

    def _check_item_importance(self, element, filter_config: Dict[str, Any], base_url: str) -> bool:
        """
        检查快讯项是否满足重要性要求

        Args:
            element: BeautifulSoup元素
            filter_config: 重要性过滤配置
            base_url: 基础URL

        Returns:
            bool: 是否满足重要性要求
        """
        try:
            # 获取站点特定配置
            site_domain = self._get_site_domain(base_url)
            site_specific_config = filter_config.get("site_specific_configs", {})
            
            # 获取站点配置，优先使用具体站点配置，否则使用默认配置
            site_config = site_specific_config.get(site_domain, site_specific_config.get("default", {}))
            
            # 如果没有站点配置且回退行为是接受所有，则返回True
            if not site_config and filter_config.get("fallback_behavior", "accept_all") == "accept_all":
                logger.debug(f"站点 {site_domain} 无特定配置，使用回退策略接受所有内容")
                return True
            
            # 检查重要性选择器
            importance_selector = site_config.get("importance_selector", "")
            if not importance_selector:
                # 使用通用的重要性类模式
                importance_patterns = filter_config.get("importance_class_patterns", [])
                importance_selector = ", ".join([f".{pattern}" for pattern in importance_patterns])
            
            # 检查元素是否匹配重要性选择器
            if importance_selector:
                # 检查元素本身或其父元素是否包含重要性标识
                if self._element_matches_importance(element, importance_selector):
                    logger.debug("快讯项匹配重要性选择器，通过过滤")
                    return True
            
            # 如果站点配置要求拒绝无重要性标识的内容
            reject_without_importance = site_config.get("reject_without_importance", False)
            if reject_without_importance:
                logger.debug(f"快讯项不包含重要性标识且站点 {site_domain} 要求拒绝，过滤掉")
                return False
            
            # 默认接受
            logger.debug("快讯项未找到重要性标识，但配置允许，通过过滤")
            return True

        except Exception as e:
            logger.warning(f"检查重要性失败: {e}")
            # 出错时根据回退策略决定
            return filter_config.get("fallback_behavior", "accept_all") == "accept_all"

    def _get_site_domain(self, base_url: str) -> str:
        """
        从URL中提取站点域名

        Args:
            base_url: 基础URL

        Returns:
            str: 站点域名
        """
        try:
            from urllib.parse import urlparse
            
            # 如果URL为空或无效，返回unknown
            if not base_url or not base_url.strip():
                return "unknown"
            
            parsed = urlparse(base_url)
            domain = parsed.netloc.lower()
            
            # 如果解析失败或域名为空，返回unknown
            if not domain:
                return "unknown"
            
            # 移除www前缀
            if domain.startswith("www."):
                domain = domain[4:]
            
            return domain
        except Exception as e:
            logger.warning(f"提取站点域名失败: {e}")
            return "unknown"

    def _element_matches_importance(self, element, importance_selector: str) -> bool:
        """
        检查元素是否匹配重要性选择器

        Args:
            element: BeautifulSoup元素
            importance_selector: 重要性选择器

        Returns:
            bool: 是否匹配
        """
        try:
            # 检查元素本身
            if element.select(importance_selector):
                return True
            
            # 检查父元素（向上查找3层）
            current = element
            for _ in range(3):
                parent = current.parent
                if parent is None:
                    break
                
                # 检查父元素是否匹配选择器
                if parent.select(importance_selector):
                    return True
                
                # 检查父元素的class是否包含重要性标识
                parent_classes = parent.get("class", [])
                if any("important" in cls for cls in parent_classes):
                    return True
                
                current = parent
            
            return False

        except Exception as e:
            logger.warning(f"检查元素重要性匹配失败: {e}")
            return False
