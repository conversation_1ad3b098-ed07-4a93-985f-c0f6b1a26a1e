"""
爬虫工厂
根据数据源的采集方式自动选择合适的爬虫实现
"""

from typing import Optional, Type

from loguru import logger

from ..models import CrawlTask, DataSource
from .api_crawler import ApiCrawler
from .base_crawler import BaseCrawler
from .rss_crawler import RssCrawler
from .web_crawler import WebCrawler


class CrawlerFactory:
    """
    爬虫工厂类
    根据数据源配置创建合适的爬虫实例
    """

    # 爬虫类型映射
    CRAWLER_MAPPING = {
        # API类型爬虫
        "api_json": ApiCrawler,
        "api_xml": ApiCrawler,
        "api_rss": RssCrawler,  # RSS使用专门的爬虫
        # 网页类型爬虫
        "web_scraping": WebCrawler,
        "web_dynamic": WebCrawler,
        # 文件上传类型（暂时使用基础爬虫）
        "file_upload": BaseCrawler,
    }

    @classmethod
    def create_crawler(
        cls, data_source: DataSource, task: CrawlTask, config: Optional[dict] = None
    ) -> BaseCrawler:
        """
        创建爬虫实例

        Args:
            data_source: 数据源配置
            task: 采集任务
            config: 额外配置参数

        Returns:
            BaseCrawler: 爬虫实例

        Raises:
            ValueError: 不支持的爬虫类型
        """
        collection_method = data_source.collection_method

        # 获取对应的爬虫类
        crawler_class = cls.get_crawler_class(collection_method)

        if not crawler_class:
            raise ValueError(f"不支持的采集方式: {collection_method}")

        # 创建爬虫实例
        try:
            crawler = crawler_class(data_source, task, config)
            logger.info(
                f"创建爬虫实例: {crawler_class.__name__} for {data_source.name}"
            )
            return crawler
        except Exception as e:
            logger.error(f"创建爬虫实例失败: {e}")
            raise

    @classmethod
    def get_crawler_class(cls, collection_method: str) -> Optional[Type[BaseCrawler]]:
        """
        根据采集方式获取爬虫类

        Args:
            collection_method: 采集方式

        Returns:
            Type[BaseCrawler]: 爬虫类，不支持则返回None
        """
        return cls.CRAWLER_MAPPING.get(collection_method)

    @classmethod
    def get_supported_methods(cls) -> list:
        """
        获取支持的采集方式列表

        Returns:
            list: 支持的采集方式
        """
        return list(cls.CRAWLER_MAPPING.keys())

    @classmethod
    def is_supported(cls, collection_method: str) -> bool:
        """
        检查是否支持指定的采集方式

        Args:
            collection_method: 采集方式

        Returns:
            bool: 是否支持
        """
        return collection_method in cls.CRAWLER_MAPPING

    @classmethod
    def register_crawler(cls, collection_method: str, crawler_class: Type[BaseCrawler]):
        """
        注册新的爬虫类型

        Args:
            collection_method: 采集方式
            crawler_class: 爬虫类
        """
        if not issubclass(crawler_class, BaseCrawler):
            raise ValueError("爬虫类必须继承自BaseCrawler")

        cls.CRAWLER_MAPPING[collection_method] = crawler_class
        logger.info(f"注册爬虫类型: {collection_method} -> {crawler_class.__name__}")

    @classmethod
    def get_crawler_info(cls, collection_method: str) -> dict:
        """
        获取爬虫信息

        Args:
            collection_method: 采集方式

        Returns:
            dict: 爬虫信息
        """
        crawler_class = cls.get_crawler_class(collection_method)

        if not crawler_class:
            return {}

        return {
            "method": collection_method,
            "class_name": crawler_class.__name__,
            "module": crawler_class.__module__,
            "supported": True,
            "description": crawler_class.__doc__ or "",
        }

    @classmethod
    def validate_data_source(cls, data_source: DataSource) -> tuple[bool, str]:
        """
        验证数据源配置是否支持

        Args:
            data_source: 数据源配置

        Returns:
            tuple: (是否有效, 错误信息)
        """
        try:
            # 检查采集方式是否支持
            if not cls.is_supported(data_source.collection_method):
                return False, f"不支持的采集方式: {data_source.collection_method}"

            # 检查必要字段
            if not data_source.base_url:
                return False, "缺少基础URL"

            # 根据采集方式进行特定检查
            if data_source.collection_method.startswith("api_"):
                return cls._validate_api_source(data_source)
            elif data_source.collection_method.startswith("web_"):
                return cls._validate_web_source(data_source)

            return True, ""

        except Exception as e:
            return False, f"验证失败: {str(e)}"

    @classmethod
    def _validate_api_source(cls, data_source: DataSource) -> tuple[bool, str]:
        """
        验证API数据源配置

        Args:
            data_source: 数据源配置

        Returns:
            tuple: (是否有效, 错误信息)
        """
        # 检查URL格式
        import re

        url_pattern = r"^https?://"
        if not re.match(url_pattern, data_source.base_url):
            return False, "API URL格式无效"

        # 安全地检查配置，避免Session绑定问题
        try:
            # 检查是否有提取配置
            if hasattr(data_source, "configs") and data_source.configs:
                config = data_source.configs[0]
                if not config.extraction_rules:
                    return False, "缺少API数据提取配置"
        except Exception:
            # 如果无法访问configs，说明对象可能已脱离Session
            # 这种情况下我们假设配置是有效的，让后续处理来处理
            pass

        return True, ""

    @classmethod
    def _validate_web_source(cls, data_source: DataSource) -> tuple[bool, str]:
        """
        验证网页数据源配置

        Args:
            data_source: 数据源配置

        Returns:
            tuple: (是否有效, 错误信息)
        """
        # 检查URL格式
        import re

        url_pattern = r"^https?://"
        if not re.match(url_pattern, data_source.base_url):
            return False, "网页URL格式无效"

        # 安全地检查配置，避免Session绑定问题
        try:
            # 检查是否有选择器配置
            if hasattr(data_source, "configs") and data_source.configs:
                config = data_source.configs[0]
                if not config.selector_config:
                    return False, "缺少网页元素选择器配置"
        except Exception:
            # 如果无法访问configs，说明对象可能已脱离Session
            # 这种情况下我们假设配置是有效的，让后续处理来处理
            pass

        return True, ""

    @classmethod
    def create_test_crawler(
        cls, collection_method: str, test_url: str, config: Optional[dict] = None
    ) -> BaseCrawler:
        """
        创建测试爬虫实例

        Args:
            collection_method: 采集方式
            test_url: 测试URL
            config: 测试配置

        Returns:
            BaseCrawler: 测试爬虫实例
        """
        from unittest.mock import Mock

        # 创建模拟数据源
        mock_data_source = Mock()
        mock_data_source.id = 0
        mock_data_source.name = "测试数据源"
        mock_data_source.collection_method = collection_method
        mock_data_source.content_category = "test"
        mock_data_source.base_url = test_url
        mock_data_source.use_proxy = False
        mock_data_source.request_delay_min = 1
        mock_data_source.request_delay_max = 2
        mock_data_source.configs = []

        # 创建模拟任务
        mock_task = Mock()
        mock_task.id = 0
        mock_task.target_url = test_url
        mock_task.task_type = "test"

        return cls.create_crawler(mock_data_source, mock_task, config)


# 便捷函数
def create_crawler(
    data_source: DataSource, task: CrawlTask, config: Optional[dict] = None
) -> BaseCrawler:
    """
    便捷函数：创建爬虫实例

    Args:
        data_source: 数据源配置
        task: 采集任务
        config: 额外配置参数

    Returns:
        BaseCrawler: 爬虫实例
    """
    return CrawlerFactory.create_crawler(data_source, task, config)


def get_supported_methods() -> list:
    """
    便捷函数：获取支持的采集方式

    Returns:
        list: 支持的采集方式列表
    """
    return CrawlerFactory.get_supported_methods()


def validate_data_source(data_source: DataSource) -> tuple[bool, str]:
    """
    便捷函数：验证数据源配置

    Args:
        data_source: 数据源配置

    Returns:
        tuple: (是否有效, 错误信息)
    """
    return CrawlerFactory.validate_data_source(data_source)
