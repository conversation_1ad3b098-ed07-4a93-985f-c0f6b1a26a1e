"""
Twitter 数据采集器

实现Twitter API的数据采集功能，支持多种认证方式和数据类型
"""

import asyncio
import json
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional
from urllib.parse import urljoin

from src.core.logging import get_logger

from .api_crawler import APICrawler

logger = get_logger(__name__)


class TwitterCrawler(APICrawler):
    """
    Twitter API 数据采集器

    支持功能：
    - OAuth 1.0a 认证 (用户级别API)
    - Bearer Token 认证 (应用级别API)
    - Twitter API v2 数据采集
    - 推文、用户、趋势等数据采集
    """

    def __init__(self, data_source, task_id=None):
        """
        初始化Twitter采集器

        Args:
            data_source: 数据源配置对象
            task_id: 任务ID
        """
        super().__init__(data_source, task_id)
        self.api_version = self.data_source.configs.get("api_version", "v2")
        self.max_results = self.data_source.configs.get("max_results", 100)

    async def collect_tweets_by_query(
        self, query: str, max_results: int = None
    ) -> List[Dict]:
        """
        根据搜索关键词采集推文

        Args:
            query: 搜索关键词
            max_results: 最大结果数量

        Returns:
            List[Dict]: 推文数据列表
        """
        max_results = max_results or self.max_results

        # 构建API URL
        if self.api_version == "v2":
            url = "https://api.twitter.com/2/tweets/search/recent"
            params = {
                "query": query,
                "max_results": min(max_results, 100),  # API限制
                "tweet.fields": "author_id,created_at,public_metrics,context_annotations,lang,possibly_sensitive",
                "user.fields": "name,username,public_metrics,verified",
                "expansions": "author_id",
            }
        else:
            # Twitter API v1.1
            url = "https://api.twitter.com/1.1/search/tweets.json"
            params = {
                "q": query,
                "count": min(max_results, 100),
                "result_type": "recent",
                "include_entities": "true",
            }

        try:
            logger.info(f"开始采集Twitter推文，查询: {query}")
            response_text = await self._make_request(url, "GET", params)
            response_data = json.loads(response_text)

            tweets = []

            if self.api_version == "v2":
                tweets_data = response_data.get("data", [])
                users_data = {
                    user["id"]: user
                    for user in response_data.get("includes", {}).get("users", [])
                }

                for tweet in tweets_data:
                    processed_tweet = self._process_tweet_v2(tweet, users_data)
                    tweets.append(processed_tweet)
            else:
                tweets_data = response_data.get("statuses", [])
                for tweet in tweets_data:
                    processed_tweet = self._process_tweet_v1(tweet)
                    tweets.append(processed_tweet)

            logger.info(f"成功采集到 {len(tweets)} 条推文")
            return tweets

        except Exception as e:
            logger.error(f"采集Twitter推文失败: {e}")
            raise

    async def collect_user_timeline(
        self, user_id: str, max_results: int = None
    ) -> List[Dict]:
        """
        采集用户时间线推文

        Args:
            user_id: 用户ID或用户名
            max_results: 最大结果数量

        Returns:
            List[Dict]: 推文数据列表
        """
        max_results = max_results or self.max_results

        if self.api_version == "v2":
            url = f"https://api.twitter.com/2/users/{user_id}/tweets"
            params = {
                "max_results": min(max_results, 100),
                "tweet.fields": "created_at,public_metrics,context_annotations,lang",
                "user.fields": "name,username,public_metrics,verified",
                "expansions": "author_id",
            }
        else:
            url = "https://api.twitter.com/1.1/statuses/user_timeline.json"
            params = {
                "user_id": user_id,
                "count": min(max_results, 200),
                "include_rts": "false",
                "exclude_replies": "true",
            }

        try:
            logger.info(f"开始采集用户 {user_id} 的时间线")
            response_text = await self._make_request(url, "GET", params)
            response_data = json.loads(response_text)

            tweets = []

            if self.api_version == "v2":
                tweets_data = response_data.get("data", [])
                users_data = {
                    user["id"]: user
                    for user in response_data.get("includes", {}).get("users", [])
                }

                for tweet in tweets_data:
                    processed_tweet = self._process_tweet_v2(tweet, users_data)
                    tweets.append(processed_tweet)
            else:
                tweets_data = response_data if isinstance(response_data, list) else []
                for tweet in tweets_data:
                    processed_tweet = self._process_tweet_v1(tweet)
                    tweets.append(processed_tweet)

            logger.info(f"成功采集到用户 {user_id} 的 {len(tweets)} 条推文")
            return tweets

        except Exception as e:
            logger.error(f"采集用户时间线失败: {e}")
            raise

    async def collect_trending_topics(self, woeid: int = 1) -> List[Dict]:
        """
        采集趋势话题

        Args:
            woeid: Where On Earth ID (1=全球)

        Returns:
            List[Dict]: 趋势话题列表
        """
        url = "https://api.twitter.com/1.1/trends/place.json"
        params = {"id": woeid}

        try:
            logger.info(f"开始采集趋势话题，WOEID: {woeid}")
            response_text = await self._make_request(url, "GET", params)
            response_data = json.loads(response_text)

            trends = []
            if response_data and len(response_data) > 0:
                trends_data = response_data[0].get("trends", [])
                for trend in trends_data:
                    processed_trend = self._process_trend(trend)
                    trends.append(processed_trend)

            logger.info(f"成功采集到 {len(trends)} 个趋势话题")
            return trends

        except Exception as e:
            logger.error(f"采集趋势话题失败: {e}")
            raise

    def _process_tweet_v2(self, tweet: Dict, users_data: Dict) -> Dict:
        """
        处理Twitter API v2的推文数据

        Args:
            tweet: 推文原始数据
            users_data: 用户数据映射

        Returns:
            Dict: 标准化推文数据
        """
        author_id = tweet.get("author_id")
        author_info = users_data.get(author_id, {})

        # 转换时间格式
        created_at = tweet.get("created_at")
        if created_at:
            try:
                created_at = datetime.fromisoformat(created_at.replace("Z", "+00:00"))
            except:
                created_at = datetime.now(timezone.utc)
        else:
            created_at = datetime.now(timezone.utc)

        public_metrics = tweet.get("public_metrics", {})

        return {
            "id": tweet.get("id"),
            "text": tweet.get("text", ""),
            "author_id": author_id,
            "author_name": author_info.get("name", ""),
            "author_username": author_info.get("username", ""),
            "author_verified": author_info.get("verified", False),
            "created_at": created_at.isoformat(),
            "language": tweet.get("lang", "en"),
            "possibly_sensitive": tweet.get("possibly_sensitive", False),
            "retweet_count": public_metrics.get("retweet_count", 0),
            "like_count": public_metrics.get("like_count", 0),
            "reply_count": public_metrics.get("reply_count", 0),
            "quote_count": public_metrics.get("quote_count", 0),
            "context_annotations": tweet.get("context_annotations", []),
            "source_url": f"https://twitter.com/{author_info.get('username', 'user')}/status/{tweet.get('id')}",
            "api_version": "v2",
        }

    def _process_tweet_v1(self, tweet: Dict) -> Dict:
        """
        处理Twitter API v1.1的推文数据

        Args:
            tweet: 推文原始数据

        Returns:
            Dict: 标准化推文数据
        """
        user_info = tweet.get("user", {})

        # 转换时间格式
        created_at = tweet.get("created_at")
        if created_at:
            try:
                # Twitter v1.1时间格式: "Wed Oct 10 20:19:24 +0000 2018"
                created_at = datetime.strptime(created_at, "%a %b %d %H:%M:%S %z %Y")
            except:
                created_at = datetime.now(timezone.utc)
        else:
            created_at = datetime.now(timezone.utc)

        return {
            "id": str(tweet.get("id")),
            "text": tweet.get("text", ""),
            "author_id": str(user_info.get("id", "")),
            "author_name": user_info.get("name", ""),
            "author_username": user_info.get("screen_name", ""),
            "author_verified": user_info.get("verified", False),
            "created_at": created_at.isoformat(),
            "language": tweet.get("lang", "en"),
            "possibly_sensitive": tweet.get("possibly_sensitive", False),
            "retweet_count": tweet.get("retweet_count", 0),
            "like_count": tweet.get("favorite_count", 0),
            "reply_count": 0,  # v1.1 API不提供回复数
            "quote_count": tweet.get("quote_count", 0),
            "context_annotations": [],
            "source_url": f"https://twitter.com/{user_info.get('screen_name', 'user')}/status/{tweet.get('id')}",
            "api_version": "v1.1",
        }

    def _process_trend(self, trend: Dict) -> Dict:
        """
        处理趋势话题数据

        Args:
            trend: 趋势原始数据

        Returns:
            Dict: 标准化趋势数据
        """
        return {
            "name": trend.get("name", ""),
            "query": trend.get("query", ""),
            "url": trend.get("url", ""),
            "tweet_volume": trend.get("tweet_volume"),
            "promoted_content": trend.get("promoted_content"),
            "collected_at": datetime.now(timezone.utc).isoformat(),
        }

    async def run(self) -> List[Dict]:
        """
        执行Twitter数据采集

        Returns:
            List[Dict]: 采集到的数据列表
        """
        try:
            await self.setup()

            task_config = self.data_source.configs or {}
            collection_type = task_config.get("collection_type", "search")

            if collection_type == "search":
                # 搜索推文
                query = task_config.get("search_query", "#finance OR #fintech")
                max_results = task_config.get("max_results", self.max_results)
                return await self.collect_tweets_by_query(query, max_results)

            elif collection_type == "user_timeline":
                # 用户时间线
                user_id = task_config.get("user_id")
                if not user_id:
                    raise ValueError("用户时间线采集需要指定user_id")
                max_results = task_config.get("max_results", self.max_results)
                return await self.collect_user_timeline(user_id, max_results)

            elif collection_type == "trends":
                # 趋势话题
                woeid = task_config.get("woeid", 1)
                return await self.collect_trending_topics(woeid)

            else:
                raise ValueError(f"不支持的采集类型: {collection_type}")

        except Exception as e:
            logger.error(f"Twitter数据采集失败: {e}")
            raise
        finally:
            await self.cleanup()
