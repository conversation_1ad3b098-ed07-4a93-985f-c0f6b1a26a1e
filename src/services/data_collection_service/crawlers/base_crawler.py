"""
基础爬虫类
定义爬虫的基本接口和通用功能
"""

import asyncio
import hashlib
import re
import time
from abc import ABC, abstractmethod
from datetime import datetime, timezone
from typing import Any, Dict, Optional
from urllib.parse import urljoin, urlparse

import aiohttp
from loguru import logger

from ....core.config import get_settings
from ....core.mongodb import mongodb_manager
from ..models import CrawlTask, DataSource
from ..schemas import CrawlResult

settings = get_settings()


class BaseCrawler(ABC):
    """
    基础爬虫抽象类
    定义所有爬虫的通用接口和功能
    """

    def __init__(
        self,
        data_source: DataSource,
        task: CrawlTask,
        config: Optional[Dict[str, Any]] = None,
    ):
        """
        初始化爬虫

        Args:
            data_source: 数据源配置
            task: 采集任务
            config: 额外配置参数
        """
        self.data_source = data_source
        self.task = task
        self.config = config or {}

        # 统计信息
        self.start_time = None
        self.items_found = 0
        self.items_processed = 0
        self.items_success = 0
        self.items_failed = 0
        self.network_requests = 0

        # HTTP Session
        self.session = None

        # 错误记录
        self.errors = []

    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.setup()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.cleanup()

    async def setup(self):
        """
        设置爬虫环境
        初始化数据库连接、HTTP会话等
        """
        # 确保 MongoDB 管理器连接正常
        if not mongodb_manager.connect():
            raise RuntimeError("无法连接到 MongoDB")

        # 获取认证凭证
        credentials = await self._get_data_source_credentials()

        # 初始化 HTTP 会话
        timeout = aiohttp.ClientTimeout(total=30)
        connector = aiohttp.TCPConnector(limit=100, limit_per_host=10)

        # 设置请求头
        headers = self._get_default_headers()

        # 添加认证信息到请求头
        if credentials:
            auth_headers = self._build_auth_headers(credentials)
            headers.update(auth_headers)

        # 应用数据源配置的请求头
        try:
            # 优先从预加载的关系中获取
            if hasattr(self.data_source, "configs") and self.data_source.configs:
                config = self.data_source.configs[0]  # 使用最新配置
                if config.headers_config:
                    headers.update(config.headers_config)
            else:
                # 如果没有预加载，主动从数据库获取
                from ...core.database import SessionLocal
                from .service import DataSourceConfigService

                config_service = DataSourceConfigService()
                db = SessionLocal()
                try:
                    active_config = config_service.get_active_config_by_source(
                        db, self.data_source.id
                    )
                    if active_config and active_config.headers_config:
                        headers.update(active_config.headers_config)
                finally:
                    db.close()
        except Exception as e:
            logger.warning(f"获取请求头配置失败: {e}")

        self.session = aiohttp.ClientSession(
            timeout=timeout, connector=connector, headers=headers
        )

        self.start_time = time.time()
        logger.info(f"爬虫已初始化: {self.data_source.name}")

    async def cleanup(self):
        """
        清理爬虫资源
        关闭HTTP会话等（MongoDB连接由管理器统一管理）
        """
        try:
            if self.session and not self.session.closed:
                await self.session.close()
                self.session = None

            # 如果存在OAuth会话，也需要关闭
            if hasattr(self, '_oauth_session') and not self._oauth_session.closed:
                await self._oauth_session.close()
                self._oauth_session = None

            # MongoDB 连接由 mongodb_manager 统一管理，不需要在这里关闭
            logger.info(f"爬虫已清理: {self.data_source.name}")
        except Exception as e:
            logger.error(f"清理爬虫资源时发生错误: {e}")
            # 不抛出异常，因为这是清理过程

    @abstractmethod
    async def crawl(self) -> CrawlResult:
        """
        执行爬取任务

        Returns:
            CrawlResult: 爬取结果
        """
        pass

    async def process_item(self, item_data: Dict[str, Any]) -> Optional[str]:
        """
        处理单个数据项

        Args:
            item_data: 原始数据项

        Returns:
            str: MongoDB 文档ID，失败抛出异常
        """
        try:
            # 数据清洗和验证
            processed_data = await self._process_raw_data(item_data)
            if not processed_data:
                self.items_failed += 1
                return None

            # 计算内容哈希
            content_hash = self._calculate_content_hash(
                processed_data.get("content", "")
            )
            processed_data["content_hash"] = content_hash

            # 保存到 MongoDB
            mongo_id = await mongodb_manager.save_raw_content(processed_data)
            if mongo_id:
                self.items_success += 1
                logger.debug(f"数据项处理成功: {mongo_id}")
            else:
                self.items_failed += 1
                logger.error("数据项保存到MongoDB失败，抛出异常")
                raise RuntimeError("数据项保存到MongoDB失败")

            self.items_processed += 1
            return mongo_id

        except Exception as e:
            self.items_failed += 1
            self.errors.append(str(e))
            logger.error(f"处理数据项失败: {e}")
            raise  # 关键：抛出异常

    async def _process_raw_data(
        self, raw_data: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """
        处理原始数据，进行清洗和格式化

        Args:
            raw_data: 原始数据

        Returns:
            Dict: 处理后的数据，失败返回None
        """
        try:
            # 提取基本字段
            processed = {
                "title": self._extract_title(raw_data),
                "content": self._extract_content(raw_data),
                "author": self._extract_author(raw_data),
                "publish_time": self._extract_publish_time(raw_data),
                "source_url": self._extract_source_url(raw_data),
                "crawl_time": datetime.now(timezone.utc),
                "data_source_id": self.data_source.id,
                "task_id": self.task.id,
                "content_type": self.data_source.content_category,
                "collection_method": self.data_source.collection_method,
                "raw_data": raw_data,  # 保留原始数据
            }

            # 提取重要性信息
            processed["importance_level"] = self._extract_importance_level(raw_data)

            # 标题清洗
            original_title = processed["title"]
            processed["title"] = self._clean_title(processed["title"])
            if original_title != processed["title"]:
                logger.debug(
                    f"标题已清洗: '{original_title}' -> '{processed['title']}'"
                )

            # 内容清洗
            original_content = processed["content"]
            processed["content"] = self._clean_content(processed["content"])
            if original_content != processed["content"]:
                logger.debug(
                    f"内容已清洗: '{original_content[:50]}...' -> '{processed['content'][:50]}...'"
                )

            # 广告和推广内容过滤
            if self._is_advertisement_or_promotion(
                processed["title"], processed["content"]
            ):
                logger.info(f"过滤广告/推广内容: {processed['title'][:50]}...")
                return None

            # 重要性验证
            if not self._validate_importance(processed, raw_data):
                logger.info(f"内容不满足重要性要求，过滤: {processed['title'][:50]}...")
                return None

            # 数据验证
            if not processed["title"] and not processed["content"]:
                logger.warning("数据项缺少标题和内容")
                return None

            # 内容长度检查
            content = processed.get("content", "")
            if len(content) < 10:  # 内容太短
                logger.warning(f"内容太短: {len(content)} 字符")
                return None

            if len(content) > 50000:  # 内容太长，截断
                processed["content"] = content[:50000]
                logger.warning("内容过长，已截断")

            return processed

        except Exception as e:
            logger.error(f"处理原始数据失败: {e}")
            return None

    def _extract_title(self, data: Dict[str, Any]) -> str:
        """提取标题"""
        return str(data.get("title", "")).strip()

    def _extract_content(self, data: Dict[str, Any]) -> str:
        """提取内容"""
        return str(data.get("content", "")).strip()

    def _extract_author(self, data: Dict[str, Any]) -> str:
        """提取作者"""
        return str(data.get("author", "")).strip()

    def _extract_publish_time(self, data: Dict[str, Any]) -> Optional[datetime]:
        """提取发布时间"""
        pub_time = data.get("publish_time") or data.get("pubDate") or data.get("date")
        if pub_time:
            try:
                if isinstance(pub_time, str):
                    # 尝试解析多种时间格式
                    from dateutil import parser

                    return parser.parse(pub_time)
                elif isinstance(pub_time, datetime):
                    return pub_time
            except Exception as e:
                logger.warning(f"解析发布时间失败: {pub_time}, 错误: {e}")
        return None

    def _extract_source_url(self, data: Dict[str, Any]) -> str:
        """提取源URL"""
        url = data.get("url", "") or data.get("link", "") or self.task.target_url or ""
        return str(url).strip()

    def _calculate_content_hash(self, content: str) -> str:
        """
        计算内容哈希值，用于去重

        Args:
            content: 内容文本

        Returns:
            str: MD5哈希值
        """
        if not content:
            return ""

        # 清理内容，移除空白字符
        cleaned_content = "".join(content.split())
        return hashlib.md5(cleaned_content.encode("utf-8")).hexdigest()

    def _get_default_headers(self) -> Dict[str, str]:
        """
        获取默认请求头

        Returns:
            Dict: 默认请求头
        """
        return {
            "User-Agent": (
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 "
                "(KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
            ),
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
            "Accept-Encoding": "gzip, deflate",
            "Connection": "keep-alive",
            "Upgrade-Insecure-Requests": "1",
        }

    async def _delay_request(self):
        """
        请求延迟，避免过于频繁的请求

        使用场景：
        1. 多个网络请求之间（如分页爬取、获取详情页）
        2. API调用之间（避免触发频率限制）
        3. 动态页面加载之间（控制页面访问频率）

        注意：不要在数据处理循环中调用此方法，因为数据处理不涉及网络请求
        """
        if self.data_source.request_delay_min and self.data_source.request_delay_max:
            import random

            delay = random.uniform(
                self.data_source.request_delay_min, self.data_source.request_delay_max
            )
            await asyncio.sleep(delay)

    def get_statistics(self) -> Dict[str, Any]:
        """
        获取爬虫执行统计信息

        Returns:
            Dict: 统计信息
        """
        duration = time.time() - self.start_time if self.start_time else 0

        return {
            "duration_seconds": int(duration),
            "items_found": self.items_found,
            "items_processed": self.items_processed,
            "items_success": self.items_success,
            "items_failed": self.items_failed,
            "network_requests": self.network_requests,
            "success_rate": self.items_success / max(self.items_processed, 1),
            "errors": self.errors[-10:],  # 只保留最近10个错误
        }

    def _normalize_url(self, url: str, base_url: str = None) -> str:
        """
        标准化URL

        Args:
            url: 原始URL
            base_url: 基础URL

        Returns:
            str: 标准化后的URL
        """
        if not url:
            return ""

        # 如果是相对URL，拼接基础URL
        if not url.startswith(("http://", "https://")):
            if base_url:
                url = urljoin(base_url, url)
            elif self.data_source.base_url:
                url = urljoin(self.data_source.base_url, url)

        return url.strip()

    def _get_domain(self, url: str) -> str:
        """
        从URL中提取域名

        Args:
            url: URL地址

        Returns:
            str: 域名
        """
        try:
            parsed = urlparse(url)
            return parsed.netloc
        except Exception:
            return ""

    async def _get_data_source_credentials(self) -> Optional[Dict[str, Any]]:
        """
        获取数据源的认证凭证

        Returns:
            Dict: 解密后的凭证数据，无凭证返回None
        """
        try:
            from ....core.database import SessionLocal
            from ..service import DataSourceCredentialService

            credential_service = DataSourceCredentialService()

            # 使用数据库会话
            db = SessionLocal()
            try:
                # 获取数据源的活跃凭证
                credentials = credential_service.get_credentials_by_source(
                    db, self.data_source.id, include_inactive=False
                )

                if not credentials:
                    logger.debug(f"数据源 {self.data_source.name} 无认证凭证")
                    return None

                # 使用第一个活跃凭证（通常一个数据源只有一个凭证）
                credential = credentials[0]

                # 检查凭证是否过期
                if credential.expires_at and credential.expires_at < datetime.now(
                    timezone.utc
                ):
                    logger.warning(f"数据源 {self.data_source.name} 的凭证已过期")
                    return None

                # 获取解密后的凭证数据
                decrypted_data = credential_service.get_decrypted_credential_data(
                    db, credential.id
                )

                if decrypted_data:
                    logger.info(f"已加载数据源 {self.data_source.name} 的认证凭证")
                    # 添加凭证类型信息
                    decrypted_data["credential_type"] = credential.credential_type
                    return decrypted_data

                return None

            finally:
                db.close()

        except Exception as e:
            logger.error(f"获取数据源认证凭证失败: {e}")
            return None

    def _build_auth_headers(self, credentials: Dict[str, Any]) -> Dict[str, str]:
        """
        根据凭证类型构建认证请求头

        Args:
            credentials: 解密后的凭证数据

        Returns:
            Dict: 认证请求头
        """
        auth_headers = {}
        credential_type = credentials.get("credential_type", "")

        try:
            if credential_type == "api_key":
                # API Key 认证
                api_key = credentials.get("api_key")
                if api_key:
                    # 根据API Key的使用方式设置请求头
                    api_key_header = credentials.get("api_key_header", "X-API-Key")
                    auth_headers[api_key_header] = api_key
                    logger.debug(f"添加API Key认证头: {api_key_header}")

            elif credential_type == "bearer_token":
                # Bearer Token 认证
                token = credentials.get("token") or credentials.get("bearer_token")
                if token:
                    auth_headers["Authorization"] = f"Bearer {token}"
                    logger.debug("添加Bearer Token认证头")

            elif credential_type == "basic_auth":
                # Basic Auth 认证
                username = credentials.get("username")
                password = credentials.get("password")
                if username and password:
                    import base64

                    auth_string = f"{username}:{password}"
                    encoded_auth = base64.b64encode(auth_string.encode()).decode()
                    auth_headers["Authorization"] = f"Basic {encoded_auth}"
                    logger.debug("添加Basic Auth认证头")

            elif credential_type == "oauth_token":
                # OAuth Token 认证
                access_token = credentials.get("access_token")
                if access_token:
                    auth_headers["Authorization"] = f"Bearer {access_token}"
                    logger.debug("添加OAuth Token认证头")

            elif credential_type == "oauth_1_0a":
                # OAuth 1.0a 认证 (如Twitter)
                # OAuth 1.0a需要动态生成签名，这里先设置基础参数
                # 实际签名会在请求时动态计算
                logger.debug("OAuth 1.0a认证将在请求时动态计算签名")
                # 存储认证参数供后续使用
                self._oauth_1_0a_credentials = {
                    "api_key": credentials.get("api_key"),
                    "api_secret": credentials.get("api_secret"),
                    "access_token": credentials.get("access_token"),
                    "access_token_secret": credentials.get("access_token_secret"),
                    "signature_method": credentials.get(
                        "signature_method", "HMAC-SHA1"
                    ),
                }

            elif credential_type == "custom_header":
                # 自定义请求头认证
                custom_headers = credentials.get("headers", {})
                if isinstance(custom_headers, dict):
                    auth_headers.update(custom_headers)
                    logger.debug(f"添加自定义认证头: {list(custom_headers.keys())}")

            else:
                logger.warning(f"不支持的凭证类型: {credential_type}")

        except Exception as e:
            logger.error(f"构建认证头失败: {e}")

        return auth_headers

    def _build_oauth_1_0a_signature(
        self, method: str, url: str, params: Dict[str, str] = None
    ) -> str:
        """
        构建OAuth 1.0a签名 (用于Twitter API)

        Args:
            method: HTTP方法 (GET, POST等)
            url: 请求URL
            params: 请求参数

        Returns:
            str: Authorization头的值
        """
        if not hasattr(self, "_oauth_1_0a_credentials"):
            raise ValueError("未找到OAuth 1.0a认证凭证")

        import base64
        import hashlib
        import hmac
        import secrets
        import time
        import urllib.parse

        creds = self._oauth_1_0a_credentials

        # OAuth 1.0a参数
        oauth_params = {
            "oauth_consumer_key": creds["api_key"],
            "oauth_token": creds["access_token"],
            "oauth_signature_method": creds["signature_method"],
            "oauth_timestamp": str(int(time.time())),
            "oauth_nonce": secrets.token_urlsafe(32),
            "oauth_version": "1.0",
        }

        # 合并所有参数
        all_params = {}
        if params:
            all_params.update(params)
        all_params.update(oauth_params)

        # 构建参数字符串
        param_string = "&".join(
            [
                f"{urllib.parse.quote_plus(str(k))}={urllib.parse.quote_plus(str(v))}"
                for k, v in sorted(all_params.items())
            ]
        )

        # 构建签名基串
        signature_base = f"{method.upper()}&{urllib.parse.quote_plus(url)}&{urllib.parse.quote_plus(param_string)}"

        # 构建签名密钥
        signing_key = f"{urllib.parse.quote_plus(creds['api_secret'])}&{urllib.parse.quote_plus(creds['access_token_secret'])}"

        # 计算签名
        signature = base64.b64encode(
            hmac.new(
                signing_key.encode(), signature_base.encode(), hashlib.sha1
            ).digest()
        ).decode()

        oauth_params["oauth_signature"] = signature

        # 构建Authorization头
        auth_header = "OAuth " + ", ".join(
            [
                f'{k}="{urllib.parse.quote_plus(str(v))}"'
                for k, v in sorted(oauth_params.items())
            ]
        )

        return auth_header

    def _is_advertisement_or_promotion(self, title: str, content: str) -> bool:
        """
        判断是否为广告或推广内容

        Args:
            title: 标题
            content: 内容

        Returns:
            bool: 是否为广告或推广内容
        """
        try:
            # 导入过滤配置
            from ..config.ad_filter_config import (AD_FILTER_PATTERNS,
                                                   FILTER_CONFIG,
                                                   WHITELIST_KEYWORDS,
                                                   WHITELIST_PATTERNS)

            # 合并标题和内容进行检查
            text = f"{title} {content}".lower()

            # 收集匹配的广告模式
            matched_patterns = []

            # 检查所有广告模式
            import re

            for category, patterns in AD_FILTER_PATTERNS.items():
                for pattern in patterns:
                    if re.search(pattern, text, re.IGNORECASE):
                        matched_patterns.append(f"{category}: {pattern}")

            # 如果匹配到广告模式，进一步检查白名单
            if matched_patterns:
                # 白名单检查（如果启用）
                if FILTER_CONFIG.get("enable_whitelist", True):
                    # 检查白名单模式（更精确的匹配）
                    whitelist_matched = False
                    for pattern in WHITELIST_PATTERNS:
                        if re.search(pattern, text, re.IGNORECASE):
                            logger.debug(f"白名单模式匹配: {pattern}")
                            whitelist_matched = True
                            break

                    # 如果白名单模式匹配，进一步检查是否包含明显的推广关键词
                    if whitelist_matched:
                        # 检查是否包含明显的推广关键词，如果有则仍然过滤
                        promotion_keywords = [
                            "vip",
                            "解锁",
                            "立省",
                            "立减",
                            "最后",
                            "限时",
                            "立即",
                            "赠",
                            "策略",
                            "指标",
                            "信号",
                            "监测",
                            "识别",
                            "拥挤",
                            "提速",
                            "一锤定音",
                            "能否站稳",
                            "cta",
                            "每周策略",
                            "点击获取",
                            "私人服务",
                            "领取",
                            "快讯",
                        ]

                        has_promotion_keywords = any(
                            keyword in text for keyword in promotion_keywords
                        )

                        if has_promotion_keywords:
                            logger.info(
                                f"内容包含推广关键词，即使匹配白名单模式也进行过滤: {title[:50]}..."
                            )
                            return True
                        else:
                            logger.debug(
                                f"内容匹配白名单模式且无推广关键词，不进行过滤: {title[:50]}..."
                            )
                            return False
                    else:
                        # 没有匹配白名单模式，直接过滤
                        logger.info(
                            f"内容匹配广告模式且无白名单保护，进行过滤: {title[:50]}..."
                        )
                        return True
                else:
                    # 白名单检查已禁用，直接过滤
                    logger.info(
                        f"白名单检查已禁用，匹配广告模式进行过滤: {title[:50]}..."
                    )
                    return True

            # 根据配置决定是否过滤
            if FILTER_CONFIG.get("strict_mode", True):
                # 严格模式：匹配任何模式就过滤
                should_filter = len(matched_patterns) > 0
            else:
                # 非严格模式：匹配超过最小数量的模式才过滤
                min_matches = FILTER_CONFIG.get("min_pattern_matches", 2)
                should_filter = len(matched_patterns) >= min_matches

            # 记录过滤日志
            if should_filter and FILTER_CONFIG.get("log_filtered", True):
                if FILTER_CONFIG.get("log_matched_patterns", True):
                    logger.info(
                        f"过滤广告/推广内容: {title[:50]}... 匹配模式: {matched_patterns[:3]}..."
                    )
                else:
                    logger.info(f"过滤广告/推广内容: {title[:50]}...")

            return should_filter

        except ImportError:
            # 如果配置文件不存在，使用简单的过滤逻辑
            logger.warning("广告过滤配置文件不存在，使用简单过滤逻辑")
            return self._simple_ad_filter(title, content)
        except Exception as e:
            logger.error(f"广告过滤检查失败: {e}")
            return False

    def _simple_ad_filter(self, title: str, content: str) -> bool:
        """
        简单的广告过滤逻辑（备用方案）

        Args:
            title: 标题
            content: 内容

        Returns:
            bool: 是否为广告或推广内容
        """
        # 合并标题和内容进行检查
        text = f"{title} {content}".lower()

        # 简单的广告关键词
        ad_keywords = [
            "vip",
            "解锁",
            "立省",
            "立减",
            "最后",
            "限时",
            "立即",
            "赠",
            "显示屏",
            "充电宝",
            "背离",
            "反转",
            "策略",
            "指标",
            "信号",
            "监测",
            "识别",
            "拥挤",
            "提速",
            "一锤定音",
            "能否站稳",
            "cta",
            "每周策略",
        ]

        # 检查是否包含广告关键词
        for keyword in ad_keywords:
            if keyword in text:
                return True

        return False

    def _clean_title(self, title: str) -> str:
        """
        清理标题，去除不必要的字符和格式

        Args:
            title: 原始标题

        Returns:
            str: 清理后的标题
        """
        if not title:
            return title

        try:
            # 导入清洗配置
            from ..config.title_clean_config import (ALLOWED_CHARS,
                                                     CLEAN_CONFIG,
                                                     JIN10_PATTERNS,
                                                     PROTECTED_PATTERNS,
                                                     PUNCTUATION_CHARS,
                                                     SOURCE_PREFIX_PATTERNS)
        except ImportError:
            # 如果配置文件不存在，使用默认配置
            logger.warning("标题清洗配置文件不存在，使用默认配置")
            return self._simple_clean_title(title)

        import re

        # 清理HTML标签
        if CLEAN_CONFIG.get("enable_html_clean", True):
            title = re.sub(r"<[^>]+>", "", title)

        # 清理金十数据等特定格式前缀
        for pattern in JIN10_PATTERNS:
            title = re.sub(pattern, "", title)

        # 检查是否匹配保护模式
        is_protected = False
        for pattern in PROTECTED_PATTERNS:
            if re.match(pattern, title):
                is_protected = True
                logger.debug(f"标题匹配保护模式，跳过后续清理: {pattern}")
                break

        if is_protected:
            # 只做HTML清理，保留原始格式
            return title

        # 清理前缀
        if CLEAN_CONFIG.get("enable_prefix_clean", True):
            # 清理其他常见的数据源前缀
            for pattern in SOURCE_PREFIX_PATTERNS:
                title = re.sub(pattern, "", title)

        # 清理特殊字符（只保留允许的字符）
        if CLEAN_CONFIG.get("enable_char_clean", True):
            title = re.sub(f"[^{ALLOWED_CHARS[1:-1]}]", "", title)

        # 清理多余空格
        if CLEAN_CONFIG.get("enable_space_clean", True):
            title = re.sub(r"\s+", " ", title).strip()

        # 去除开头和结尾的标点符号（但保留括号内的内容）
        if CLEAN_CONFIG.get("enable_punctuation_clean", True):
            # 只清理开头和结尾的标点，但保留括号内的内容
            # 修改正则表达式，避免误删括号
            punctuation_without_brackets = r'[,，.。!！?？;；:：_\s]'
            title = re.sub(f'^{punctuation_without_brackets}+', "", title)
            title = re.sub(f'{punctuation_without_brackets}+$', "", title)

        # 长度检查
        min_length = CLEAN_CONFIG.get("min_title_length", 2)
        max_length = CLEAN_CONFIG.get("max_title_length", 200)

        if len(title) < min_length:
            logger.debug(f"标题过短，已过滤: {title}")
            return ""

        if len(title) > max_length:
            title = title[:max_length]
            logger.debug(f"标题过长，已截断: {title}")

        # 去除特殊字符（保留中文、英文、数字、常用标点）
        # 修复：将连字符放在字符类的开头，避免正则表达式解析错误
        # 添加中间点字符 · (U+00B7) 以保留人名中的中间点
        # 添加书名号《》以保留书名、公约名等
        # 添加更多数学和货币符号
        # 添加所有类型的括号以确保正确处理日期格式
        title = re.sub(
            r'[^-\u4e00-\u9fa5a-zA-Z0-9\s，。！？；：""（）【】「」『』()[]{}_.·%°¥$€±÷×+=><≥≤/《》]',
            "",
            title,
        )

        # 检查括号是否配对，如果不配对则尝试修复
        brackets_pairs = [('（', '）'), ('(', ')'), ('[', ']'), ('{', '}'), ('《', '》'), ('「', '」'), ('『', '』')]
        for left, right in brackets_pairs:
            left_count = title.count(left)
            right_count = title.count(right)
            # 如果左括号比右括号多，添加右括号
            if left_count > right_count:
                title += right * (left_count - right_count)
            # 如果右括号比左括号多，在开头添加左括号
            elif right_count > left_count:
                title = left * (right_count - left_count) + title

        return title

    def _simple_clean_title(self, title: str) -> str:
        """
        简单的标题清洗方法

        Args:
            title: 原始标题

        Returns:
            str: 清洗后的标题
        """
        if not title:
            return ""

        # 移除多余空格
        title = re.sub(r"\s+", " ", title.strip())

        # 移除开头和结尾的标点符号（但保留括号内容）
        punctuation_without_brackets = r'[,，.。!！?？;；:：_\s]'
        title = re.sub(f'^{punctuation_without_brackets}+', "", title)
        title = re.sub(f'{punctuation_without_brackets}+$', "", title)

        # 检查括号是否配对，如果不配对则尝试修复
        brackets_pairs = [('（', '）'), ('(', ')'), ('[', ']'), ('{', '}'), ('《', '》'), ('「', '」'), ('『', '』')]
        for left, right in brackets_pairs:
            left_count = title.count(left)
            right_count = title.count(right)
            # 如果左括号比右括号多，添加右括号
            if left_count > right_count:
                title += right * (left_count - right_count)
            # 如果右括号比左括号多，在开头添加左括号
            elif right_count > left_count:
                title = left * (right_count - left_count) + title

        return title

    def _clean_content(self, content: str) -> str:
        """
        清理内容，去除不必要的字符和格式

        Args:
            content: 原始内容

        Returns:
            str: 清理后的内容
        """
        if not content:
            return content

        try:
            # 导入清洗配置
            from ..config.content_clean_config import (ALLOWED_CHARS,
                                                       CLEAN_CONFIG,
                                                       JIN10_PATTERNS,
                                                       PROTECTED_PATTERNS,
                                                       PUNCTUATION_CHARS,
                                                       SOURCE_PREFIX_PATTERNS)
        except ImportError:
            # 如果配置文件不存在，使用默认配置
            logger.warning("内容清洗配置文件不存在，使用默认配置")
            return self._simple_clean_content(content)

        import re

        # 清理HTML标签
        if CLEAN_CONFIG.get("enable_html_clean", True):
            content = re.sub(r"<[^>]+>", "", content)

        # 清理金十数据等特定格式前缀
        for pattern in JIN10_PATTERNS:
            content = re.sub(pattern, "", content)

        # 检查是否匹配保护模式
        is_protected = False
        for pattern in PROTECTED_PATTERNS:
            if re.match(pattern, content):
                is_protected = True
                logger.debug(f"内容匹配保护模式，跳过后续清理: {pattern}")
                break

        if is_protected:
            # 只做HTML清理，保留原始格式
            return content

        # 清理前缀
        if CLEAN_CONFIG.get("enable_prefix_clean", True):
            # 清理其他常见的数据源前缀
            for pattern in SOURCE_PREFIX_PATTERNS:
                content = re.sub(pattern, "", content)

        # 清理特殊字符
        if CLEAN_CONFIG.get("enable_char_clean", True):
            content = re.sub(f"[^{ALLOWED_CHARS[1:-1]}]", "", content)

        # 清理多余空格
        if CLEAN_CONFIG.get("enable_space_clean", True):
            content = re.sub(r"\s+", " ", content).strip()

        # 清理开头和结尾的标点符号
        if CLEAN_CONFIG.get("enable_punctuation_clean", True):
            punctuation_pattern = PUNCTUATION_CHARS[1:-1]
            # 只清理开头的标点，保留结尾的句号等标点
            content = re.sub(f"^[{punctuation_pattern}]+", "", content)
            # 不清理结尾的标点，保留句号等

        # 长度检查
        min_length = CLEAN_CONFIG.get("min_content_length", 10)
        max_length = CLEAN_CONFIG.get("max_content_length", 50000)

        if len(content) < min_length:
            logger.debug(f"内容过短，已过滤: {content}")
            return ""

        if len(content) > max_length:
            content = content[:max_length]
            logger.debug(f"内容过长，已截断: {content}")

        # 去除特殊字符（保留中文、英文、数字、常用标点）
        # 修复：将连字符放在字符类的开头，避免正则表达式解析错误
        # 添加中间点字符 · (U+00B7) 以保留人名中的中间点
        # 添加百分号和其他常用符号以保留数值和货币信息
        # 添加斜杠字符以保留单位表示如"美元/枚"
        content = re.sub(
            r'[^-\u4e00-\u9fa5a-zA-Z0-9\s，。！？；：""（）【】_.·%°¥$€±÷×+=><≥≤/]',
            "",
            content,
        )

        return content

    def _simple_clean_content(self, content: str) -> str:
        """
        简单的内容清理逻辑（备用方案）

        Args:
            content: 原始内容

        Returns:
            str: 清理后的内容
        """
        if not content:
            return content

        import re

        # 去除HTML标签
        content = re.sub(r"<[^>]+>", "", content)

        # 去除金十数据等特定格式前缀
        jin10_patterns = [
            r"^金十数据\d+月\d+日讯[，：:]*\s*",
            r"^金十数据\s*\d+月\d+日讯[，：:]*\s*",
            r"^金十数据\s*\d+月\s*\d+日讯[，：:]*\s*",
            r"^金十数据\s*\d+月\s*\d+日\s*讯[，：:]*\s*",
        ]

        for pattern in jin10_patterns:
            content = re.sub(pattern, "", content)

        # 去除其他常见的数据源前缀
        source_prefix_patterns = [
            r"^【.*?】\s*",  # 去除【】包围的前缀
            r"^\[.*?\]\s*",  # 去除[]包围的前缀
            r"^\(.*?\)\s*",  # 去除()包围的前缀
            r"^.*?讯[，：:]*\s*(?=\s|$)",  # 去除以"讯"结尾的前缀
            r"^.*?快讯[，：:]*\s*(?=\s|$)",  # 去除以"快讯"结尾的前缀
            r"^.*?新闻[，：:]*\s*(?=\s|$)",  # 修复：使用正向预查，确保后面是空白或结尾，避免误删正常内容
            r"^.*?资讯[，：:]*\s*(?=\s|$)",  # 去除以"资讯"结尾的前缀
        ]

        for pattern in source_prefix_patterns:
            content = re.sub(pattern, "", content)

        # 去除特殊字符（保留中文、英文、数字、常用标点）
        # 修复：将连字符放在字符类的开头，避免正则表达式解析错误
        # 添加中间点字符 · (U+00B7) 以保留人名中的中间点
        # 添加百分号和其他常用符号以保留数值和货币信息
        content = re.sub(
            r'[^-\u4e00-\u9fa5a-zA-Z0-9\s，。！？；：""（）【】_.·%°¥$€±÷×+=><≥≤/]',
            "",
            content,
        )

        # 去除多余空格
        content = re.sub(r"\s+", " ", content).strip()

        # 去除开头和结尾的标点符号
        # 修复：将连字符放在字符类的开头，避免正则表达式解析错误
        content = re.sub(r'^[-，。！？；：""（）【】_.\s]+', "", content)
        content = re.sub(r'[-，。！？；：""（）【】_.\s]+$', "", content)

        return content

    def _extract_importance_level(self, data: Dict[str, Any]) -> int:
        """
        提取重要性级别

        Args:
            data: 数据项

        Returns:
            int: 重要性级别，0表示未知
        """
        try:
            # 首先尝试从已提取的importance字段获取
            importance = data.get("importance", "") or data.get("importance_level", "")
            
            if isinstance(importance, int):
                return importance
            
            if isinstance(importance, str):
                # 如果是字符串，尝试解析
                importance_str = importance.lower().strip()
                
                # 检查常见的重要性标识
                importance_mapping = {
                    "important": 3,
                    "is-important": 3,
                    "high": 3,
                    "urgent": 4,
                    "critical": 4,
                    "breaking": 4,
                    "火": 1,
                    "热": 2,
                    "沸": 3,
                    "爆": 4,
                }
                
                for key, value in importance_mapping.items():
                    if key in importance_str:
                        return value
            
            # 检查原始HTML中的重要性标识
            source_html = data.get("source_html", "")
            if source_html and "is-important" in source_html:
                return 3
            
            # 默认返回0（未知重要性）
            return 0

        except Exception as e:
            logger.warning(f"提取重要性级别失败: {e}")
            return 0

    def _validate_importance(self, processed_data: Dict[str, Any], raw_data: Dict[str, Any]) -> bool:
        """
        验证内容的重要性是否满足要求

        Args:
            processed_data: 处理后的数据
            raw_data: 原始数据

        Returns:
            bool: 是否满足重要性要求
        """
        try:
            # 获取重要性验证配置
            validation_config = self._get_importance_validation_config()
            
            if not validation_config.get("enabled", False):
                # 重要性验证未启用，直接通过
                return True
            
            # 获取站点特定规则
            source_url = processed_data.get("source_url", "")
            site_domain = self._get_site_domain_from_url(source_url)
            site_specific_rules = validation_config.get("site_specific_rules", {})
            
            # 获取站点规则，优先使用具体站点规则，否则使用默认规则
            site_rule = site_specific_rules.get(site_domain, site_specific_rules.get("default", {}))
            
            # 检查是否要求重要性类
            require_important_class = site_rule.get("require_is_important_class", False)
            if require_important_class:
                # 检查原始HTML是否包含重要性类
                source_html = raw_data.get("source_html", "")
                if "is-important" not in source_html and "important" not in source_html:
                    logger.debug("内容缺少必需的重要性类标识")
                    return False
            
            # 检查最低重要性级别
            min_importance_level = site_rule.get("min_importance_level", 
                                                validation_config.get("min_importance_level", 0))
            
            current_importance = processed_data.get("importance_level", 0)
            
            if current_importance < min_importance_level:
                logger.debug(f"重要性级别 {current_importance} 低于最低要求 {min_importance_level}")
                return False
            
            # 检查是否允许未知重要性
            allow_unknown_importance = validation_config.get("allow_unknown_importance", True)
            if current_importance == 0 and not allow_unknown_importance:
                logger.debug("内容重要性未知且配置不允许未知重要性")
                return False
            
            return True

        except Exception as e:
            logger.warning(f"重要性验证失败: {e}")
            # 出错时默认通过
            return True

    def _get_importance_validation_config(self) -> Dict[str, Any]:
        """
        获取重要性验证配置

        Returns:
            Dict: 重要性验证配置
        """
        try:
            # 优先从预加载的关系中获取
            if hasattr(self.data_source, "configs") and self.data_source.configs:
                config = self.data_source.configs[0]  # 使用最新配置
                validation_rules = config.validation_rules or {}
                return validation_rules.get("importance_validation", {})

            # 如果没有预加载，主动从数据库获取
            from ....core.database import SessionLocal
            from ..service import DataSourceConfigService

            config_service = DataSourceConfigService()
            db = SessionLocal()
            try:
                active_config = config_service.get_active_config_by_source(
                    db, self.data_source.id
                )
                if active_config:
                    validation_rules = active_config.validation_rules or {}
                    return validation_rules.get("importance_validation", {})
                return {}
            finally:
                db.close()

        except Exception as e:
            logger.warning(f"获取重要性验证配置失败: {e}")
            return {}

    def _get_site_domain_from_url(self, url: str) -> str:
        """
        从URL中提取站点域名

        Args:
            url: URL地址

        Returns:
            str: 站点域名
        """
        try:
            domain = self._get_domain(url)
            
            # 移除www前缀
            if domain.startswith("www."):
                domain = domain[4:]
            
            return domain
        except Exception:
            return "unknown"
