"""
内容清洗配置
用于清理和标准化采集到的内容
"""

# 金十数据等特定格式前缀模式
JIN10_PATTERNS = [
    r"^金十数据\d+月\d+日讯[，：:]*\s*",
    r"^金十数据\s*\d+月\d+日讯[，：:]*\s*",
    r"^金十数据\s*\d+月\s*\d+日讯[，：:]*\s*",
    r"^金十数据\s*\d+月\s*\d+日\s*讯[，：:]*\s*",
    r"^金十数据整理[，：:]*\s*",
]

# 其他常见的数据源前缀模式
SOURCE_PREFIX_PATTERNS = [
    r"^【.*?】\s*",  # 去除【】包围的前缀
    r"^\[.*?\]\s*",  # 去除[]包围的前缀
    r"^\(.*?\)\s*",  # 去除()包围的前缀
    r"^.*?快讯[，：:]*\s*",  # 去除以"快讯"结尾的前缀
    r"^.*?新闻[，：:]*\s*(?=\s|$)",  # 修复：使用正向预查，确保后面是空白或结尾，避免误删正常内容
    r"^.*?资讯[，：:]*\s*(?=\s|$)",  # 去除以"资讯"结尾的前缀
    r"^.*?报道[，：:]*\s*(?=\s|$)",  # 去除以"报道"结尾的前缀
    r"^.*?动态[，：:]*\s*(?=\s|$)",  # 去除以"动态"结尾的前缀
    r"^.*?分析[，：:]*\s*(?=\s|$)",  # 去除以"分析"结尾的前缀
    r"^.*?评论[，：:]*\s*(?=\s|$)",  # 去除以"评论"结尾的前缀
]

# 需要保护的模式（这些模式不应该被清理）
PROTECTED_PATTERNS = [
    r"^.*?通讯社[：:]*\s*",  # 保护"通讯社"
    r"^.*?消息人士[：:]*\s*",  # 保护"消息人士"
    r"^.*?社消息[：:]*\s*",  # 保护"社消息"
    r"^.*?社报道[：:]*\s*",  # 保护"社报道"
    r"^.*?社[：:]*\s*",  # 保护"社"
]

# 需要保留的字符（中文、英文、数字、常用标点）
# 修复：将连字符放在字符类的开头，避免正则表达式解析错误
# 添加中间点字符 · (U+00B7) 以保留人名中的中间点
# 添加百分号和其他常用符号以保留数值和货币信息
# 添加斜杠字符以保留单位表示如"美元/枚"
ALLOWED_CHARS = (
    r'[-\u4e00-\u9fa5a-zA-Z0-9\s，。！？；：""' "（）【】「」『』()[]{}_.·%°¥$€±÷×+=><≥≤/《》]"
)

# 需要去除的标点符号（用于清理开头和结尾）
PUNCTUATION_CHARS = r'[-，。！？；：""' "（）【】_.\\s]"

# 清洗配置
CLEAN_CONFIG = {
    "enable_html_clean": True,  # 是否清理HTML标签
    "enable_prefix_clean": True,  # 是否清理前缀
    "enable_char_clean": True,  # 是否清理特殊字符
    "enable_space_clean": True,  # 是否清理多余空格
    "enable_punctuation_clean": True,  # 是否清理开头结尾标点
    "min_content_length": 10,  # 最小内容长度
    "max_content_length": 50000,  # 最大内容长度
}

# 广告和推广内容关键词
AD_KEYWORDS = [
    "广告",
    "推广",
    "赞助",
    "sponsored",
    "advertisement",
    "promotion",
    "点击购买",
    "立即购买",
    "限时优惠",
    "特价",
    "折扣",
    "优惠券",
    "免费试用",
    "注册即送",
    "投资理财",
    "高收益",
    "稳赚不赔",
    "快速致富",
    "赚钱项目",
    "创业机会",
]

# 垃圾内容关键词
SPAM_KEYWORDS = [
    "垃圾邮件",
    "spam",
    "病毒",
    "木马",
    "钓鱼",
    "诈骗",
    "中奖",
    "彩票",
    "博彩",
    "赌博",
    "色情",
    "成人",
    "一夜情",
    "约炮",
    "援交",
]

# 内容过滤正则表达式
CONTENT_FILTER_PATTERNS = [
    r"\s+",  # 多个空白字符
    r"[^\w\s\u4e00-\u9fff.,!?;:()\[\]{}'\"\-]",  # 保留中文、英文、数字和基本标点
    r"http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+",  # URL
    r"www\.[^\s]+",  # www链接
    r"[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}",  # 邮箱
    r"\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}",  # IP地址
]

# 内容长度限制
MIN_CONTENT_LENGTH = 10  # 最小内容长度
MAX_CONTENT_LENGTH = 50000  # 最大内容长度

# 内容质量评分权重
CONTENT_QUALITY_WEIGHTS = {
    "length": 0.3,  # 长度权重
    "readability": 0.2,  # 可读性权重
    "uniqueness": 0.2,  # 独特性权重
    "relevance": 0.3,  # 相关性权重
}

# 可读性评分配置
READABILITY_CONFIG = {
    "min_sentence_length": 5,  # 最小句子长度
    "max_sentence_length": 100,  # 最大句子长度
    "min_word_length": 2,  # 最小单词长度
    "max_word_length": 20,  # 最大单词长度
}

# 内容去重配置
DEDUPLICATION_CONFIG = {
    "similarity_threshold": 0.8,  # 相似度阈值
    "min_hash_length": 32,  # 最小哈希长度
    "max_hash_length": 128,  # 最大哈希长度
}
