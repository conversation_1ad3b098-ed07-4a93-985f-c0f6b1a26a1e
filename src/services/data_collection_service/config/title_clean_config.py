"""
标题清洗配置
用于清理和标准化采集到的标题内容
"""

# 金十数据等特定格式前缀模式
JIN10_PATTERNS = [
    r"^金十数据\d+月\d+日讯[，：:]*\s*",
    r"^金十数据\s*\d+月\d+日讯[，：:]*\s*",
    r"^金十数据\s*\d+月\s*\d+日讯[，：:]*\s*",
    r"^金十数据\s*\d+月\s*\d+日\s*讯[，：:]*\s*",
    r"^金十数据整理[，：:]*\s*",
]

# 其他常见的数据源前缀模式
SOURCE_PREFIX_PATTERNS = [
    r"^【.*?】\s*",  # 去除【】包围的前缀
    r"^\[.*?\]\s*",  # 去除[]包围的前缀
    r"^\(.*?\)\s*",  # 去除()包围的前缀
    r"^.*?快讯[，：:]*\s*(?=\s|$)",  # 去除以"快讯"结尾的前缀
    r"^.*?新闻[，：:]*\s*(?=\s|$)",  # 修复：使用正向预查，确保后面是空白或结尾，避免误删正常内容
    r"^.*?资讯[，：:]*\s*(?=\s|$)",  # 去除以"资讯"结尾的前缀
    r"^.*?报道[，：:]*\s*(?=\s|$)",  # 去除以"报道"结尾的前缀
    r"^.*?动态[，：:]*\s*(?=\s|$)",  # 去除以"动态"结尾的前缀
    r"^.*?分析[，：:]*\s*(?=\s|$)",  # 去除以"分析"结尾的前缀
    r"^.*?评论[，：:]*\s*(?=\s|$)",  # 去除以"评论"结尾的前缀
]

# 需要保护的模式（这些模式不应该被清理）
PROTECTED_PATTERNS = [
    r"^.*?通讯社[：:]*\s*",  # 保护"通讯社"
    r"^.*?消息人士[：:]*\s*",  # 保护"消息人士"
    r"^.*?社消息[：:]*\s*",  # 保护"社消息"
    r"^.*?社报道[：:]*\s*",  # 保护"社报道"
    r"^.*?社[：:]*\s*",  # 保护"社"
]

# 需要保留的字符（中文、英文、数字、常用标点）
# 添加更多数学和货币符号，确保包含所有类型的括号
ALLOWED_CHARS = (
    r'[-\u4e00-\u9fa5a-zA-Z0-9\s，。！？；：""' "（）【】「」『』()[]{}_.·%°¥$€±÷×+=><≥≤/《》]"
)

# 需要去除的标点符号（用于清理开头和结尾）
PUNCTUATION_CHARS = r'[-，。！？；：""' "（）【】_.\\s]"

# 标题长度限制
MIN_TITLE_LENGTH = 5  # 最小标题长度
MAX_TITLE_LENGTH = 200  # 最大标题长度

# 标题质量评分权重
TITLE_QUALITY_WEIGHTS = {
    "length": 0.2,  # 长度权重
    "readability": 0.3,  # 可读性权重
    "uniqueness": 0.3,  # 独特性权重
    "relevance": 0.2,  # 相关性权重
}

# 标题过滤正则表达式
TITLE_FILTER_PATTERNS = [
    r"\s+",  # 多个空白字符
    r"[^\w\s\u4e00-\u9fff.,!?;:()\[\]{}'\"\-]",  # 保留中文、英文、数字和基本标点
    r"http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+",  # URL
    r"www\.[^\s]+",  # www链接
    r"[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}",  # 邮箱
    r"\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}",  # IP地址
]

# 标题清洗规则
TITLE_CLEAN_RULES = [
    "remove_extra_spaces",  # 移除多余空格
    "remove_special_chars",  # 移除特殊字符
    "normalize_punctuation",  # 标准化标点符号
    "remove_urls",  # 移除URL
    "remove_emails",  # 移除邮箱
    "remove_ips",  # 移除IP地址
]

# 标题去重配置
TITLE_DEDUPLICATION_CONFIG = {
    "similarity_threshold": 0.9,  # 相似度阈值
    "min_hash_length": 16,  # 最小哈希长度
    "max_hash_length": 64,  # 最大哈希长度
}

# 标题可读性配置
TITLE_READABILITY_CONFIG = {
    "min_word_length": 2,  # 最小单词长度
    "max_word_length": 15,  # 最大单词长度
    "min_sentence_length": 3,  # 最小句子长度
    "max_sentence_length": 50,  # 最大句子长度
}

# 清洗配置
CLEAN_CONFIG = {
    "enable_html_clean": True,  # 是否清理HTML标签
    "enable_prefix_clean": True,  # 是否清理前缀
    "enable_char_clean": True,  # 是否清理特殊字符
    "enable_space_clean": True,  # 是否清理多余空格
    "enable_punctuation_clean": True,  # 是否清理开头结尾标点
    "min_title_length": MIN_TITLE_LENGTH,  # 最小标题长度
    "max_title_length": MAX_TITLE_LENGTH,  # 最大标题长度
}
