"""
事件驱动调度器
负责基于财经事件触发采集任务
"""

import asyncio
from dataclasses import dataclass
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Optional, Set

from loguru import logger
from sqlalchemy import and_
from sqlalchemy.orm import Session

from ....core.config import get_settings
from ....core.database import SessionLocal
from ..models import (CrawlTask, DataSource, EventDrivenCrawlRule)
from ..schemas import TaskStatus
from .crawler_engine import get_crawler_engine

settings = get_settings()


@dataclass
class EventTrigger:
    """事件触发器信息"""

    event_id: int
    event_title: str
    scheduled_time: datetime
    importance_level: int
    country_id: int
    event_type_id: int
    rules: List[EventDrivenCrawlRule]


class EventDrivenScheduler:
    """
    事件驱动调度器
    基于财经事件触发数据采集任务
    """

    def __init__(self, scheduler_name: str = "event_scheduler_default"):
        """
        初始化事件驱动调度器

        Args:
            scheduler_name: 调度器名称
        """
        self.scheduler_name = scheduler_name
        self.is_running = False
        self.scan_interval_seconds = 60  # 扫描间隔
        self.lookahead_hours = 24  # 预扫描24小时内的事件

        # 状态跟踪
        self.processed_events: Set[int] = set()
        self.generated_tasks: Set[int] = set()
        self.last_scan_time = None
        self.last_error_message = None
        self.error_count = 0

        # 统计信息
        self.total_events_processed = 0
        self.total_tasks_generated = 0

    async def start(self):
        """
        启动事件驱动调度器
        """
        if self.is_running:
            logger.warning("事件驱动调度器已在运行")
            return

        self.is_running = True

        # 初始化调度器状态
        await self._initialize_scheduler_status()

        # 启动主循环
        asyncio.create_task(self._main_loop())

        logger.info(f"事件驱动调度器已启动: {self.scheduler_name}")

    async def stop(self):
        """
        停止事件驱动调度器
        """
        if not self.is_running:
            return

        self.is_running = False

        # 更新调度器状态
        await self._update_scheduler_status(scheduler_status="stopped")

        logger.info(f"事件驱动调度器已停止: {self.scheduler_name}")

    async def _main_loop(self):
        """
        主循环，定期扫描财经事件并触发采集任务
        """
        while self.is_running:
            try:
                await self._scan_and_trigger_events()
                await asyncio.sleep(self.scan_interval_seconds)

            except Exception as e:
                self.error_count += 1
                self.last_error_message = str(e)
                logger.error(f"事件扫描失败: {e}")

                # 更新调度器状态
                await self._update_scheduler_status(
                    scheduler_status="error",
                    last_error_message=str(e),
                    error_count=self.error_count,
                )

                # 错误后等待更长时间
                await asyncio.sleep(min(300, self.scan_interval_seconds * 2))

    async def _scan_and_trigger_events(self):
        """
        扫描财经事件并触发对应的采集任务
        """
        try:
            # 获取即将发生的财经事件
            upcoming_events = await self._get_upcoming_events()

            if not upcoming_events:
                logger.debug("没有发现即将发生的财经事件")
                return

            logger.info(f"发现 {len(upcoming_events)} 个即将发生的财经事件")

            # 为每个事件触发相应的采集任务
            tasks_generated = 0
            for event_trigger in upcoming_events:
                try:
                    generated = await self._trigger_event_crawls(event_trigger)
                    tasks_generated += generated
                except Exception as e:
                    logger.error(f"处理事件失败: {event_trigger.event_id}, 错误: {e}")

            # 更新统计信息
            self.total_events_processed += len(upcoming_events)
            self.total_tasks_generated += tasks_generated
            self.last_scan_time = datetime.now(timezone.utc)

            # 更新调度器状态
            await self._update_scheduler_status(
                last_scan_time=self.last_scan_time,
                processed_events_count=self.total_events_processed,
                generated_tasks_count=self.total_tasks_generated,
                scheduler_status="active",
            )

            logger.info(
                f"事件扫描完成: 处理事件 {len(upcoming_events)}, 生成任务 {tasks_generated}"
            )

        except Exception as e:
            logger.error(f"扫描财经事件失败: {e}")
            raise

    async def _get_upcoming_events(self) -> List[EventTrigger]:
        """
        获取即将发生的财经事件

        Returns:
            List[EventTrigger]: 事件触发器列表
        """
        try:
            db = SessionLocal()
            try:
                # 计算时间范围
                now = datetime.now(timezone.utc)
                scan_end = now + timedelta(hours=self.lookahead_hours)

                # 查询财经事件（需要导入财经日历模块）
                from ...financial_calendar_service.models import FinancialEvent

                # 获取时间范围内的事件
                events = (
                    db.query(FinancialEvent)
                    .filter(
                        and_(
                            FinancialEvent.scheduled_time >= now,
                            FinancialEvent.scheduled_time <= scan_end,
                            FinancialEvent.event_status.in_(["scheduled", "published"]),
                        )
                    )
                    .order_by(FinancialEvent.scheduled_time)
                    .all()
                )

                event_triggers = []

                for event in events:
                    # 跳过已处理的事件
                    if event.id in self.processed_events:
                        continue

                    # 获取匹配的采集规则
                    rules = await self._get_matching_rules(event, db)

                    if rules:
                        event_trigger = EventTrigger(
                            event_id=event.id,
                            event_title=event.event_title,
                            scheduled_time=event.scheduled_time,
                            importance_level=event.importance_level,
                            country_id=event.country_id,
                            event_type_id=event.event_type_id,
                            rules=rules,
                        )
                        event_triggers.append(event_trigger)

                return event_triggers

            finally:
                db.close()

        except Exception as e:
            logger.error(f"获取即将发生的事件失败: {e}")
            return []

    async def _get_matching_rules(
        self, event, db: Session
    ) -> List[EventDrivenCrawlRule]:
        """
        获取与事件匹配的采集规则

        Args:
            event: 财经事件
            db: 数据库会话

        Returns:
            List[EventDrivenCrawlRule]: 匹配的采集规则
        """
        try:
            # 查询所有活跃的事件驱动规则
            rules = (
                db.query(EventDrivenCrawlRule)
                .filter(
                    and_(
                        EventDrivenCrawlRule.is_active == True,
                        EventDrivenCrawlRule.trigger_type == "financial_event",
                    )
                )
                .all()
            )

            matching_rules = []

            for rule in rules:
                if await self._rule_matches_event(rule, event):
                    matching_rules.append(rule)

            return matching_rules

        except Exception as e:
            logger.error(f"获取匹配规则失败: {e}")
            return []

    async def _rule_matches_event(self, rule: EventDrivenCrawlRule, event) -> bool:
        """
        检查规则是否匹配事件

        Args:
            rule: 采集规则
            event: 财经事件

        Returns:
            bool: 是否匹配
        """
        try:
            # 检查国家过滤器
            if rule.country_filter:
                if str(event.country_id) not in rule.country_filter:
                    return False

            # 检查重要性过滤器
            if rule.importance_filter:
                if event.importance_level not in rule.importance_filter:
                    return False

            # 检查事件类型过滤器
            if rule.event_type_filter:
                if str(event.event_type_id) not in rule.event_type_filter:
                    return False

            # 检查触发配置
            trigger_config = rule.trigger_config or {}

            # 可以根据需要添加更多匹配条件
            # 例如：关键词匹配、时间段匹配等

            return True

        except Exception as e:
            logger.error(f"规则匹配检查失败: {e}")
            return False

    async def _trigger_event_crawls(self, event_trigger: EventTrigger) -> int:
        """
        为事件触发采集任务

        Args:
            event_trigger: 事件触发器

        Returns:
            int: 生成的任务数量
        """
        tasks_generated = 0

        try:
            db = SessionLocal()
            try:
                # 为每个匹配的规则生成采集任务
                for rule in event_trigger.rules:
                    try:
                        # 计算采集时间点
                        crawl_times = self._calculate_crawl_times(event_trigger, rule)

                        # 记录事件采集关联
                        success = await self._log_event_association(
                            event_trigger, rule, crawl_times, db
                        )

                        if success:
                            # 为每个时间点创建采集任务
                            for crawl_time in crawl_times:
                                task = await self._create_crawl_task(
                                    event_trigger, rule, crawl_time, db
                                )

                                if task:
                                    # 提交给爬虫引擎
                                    engine = get_crawler_engine()
                                    success = await engine.submit_task(task)

                                    if success:
                                        tasks_generated += 1
                                        logger.info(
                                            f"已提交事件驱动任务: 事件ID={event_trigger.event_id}, 任务ID={task.id}"
                                        )
                                    else:
                                        logger.error(
                                            f"提交事件驱动任务失败: 事件ID={event_trigger.event_id}"
                                        )

                        # 更新规则统计
                        rule.trigger_count += 1
                        rule.last_triggered_at = datetime.now(timezone.utc)
                        db.commit()

                    except Exception as e:
                        logger.error(f"处理规则失败: 规则ID={rule.id}, 错误: {e}")
                        db.rollback()

                # 标记事件为已处理
                self.processed_events.add(event_trigger.event_id)

            finally:
                db.close()

        except Exception as e:
            logger.error(f"触发事件采集失败: {e}")

        return tasks_generated

    def _calculate_crawl_times(
        self, event_trigger: EventTrigger, rule: EventDrivenCrawlRule
    ) -> List[datetime]:
        """
        计算采集时间点

        Args:
            event_trigger: 事件触发器
            rule: 采集规则

        Returns:
            List[datetime]: 采集时间列表
        """
        crawl_times = []
        event_time = event_trigger.scheduled_time

        # 提前采集时间
        if rule.advance_minutes > 0:
            advance_time = event_time - timedelta(minutes=rule.advance_minutes)
            crawl_times.append(advance_time)

        # 事件时间采集
        crawl_times.append(event_time)

        # 延后采集时间
        if rule.delay_minutes > 0:
            delay_time = event_time + timedelta(minutes=rule.delay_minutes)
            crawl_times.append(delay_time)

        # 重复采集
        if rule.repeat_interval_minutes and rule.max_repeat_count > 1:
            last_time = crawl_times[-1] if crawl_times else event_time

            for i in range(1, rule.max_repeat_count):
                repeat_time = last_time + timedelta(
                    minutes=rule.repeat_interval_minutes
                )
                crawl_times.append(repeat_time)
                last_time = repeat_time

        return crawl_times

    async def _log_event_association(
        self,
        event_trigger: EventTrigger,
        rule: EventDrivenCrawlRule,
        crawl_times: List[datetime],
        db: Session,
    ) -> bool:
        """
        记录事件采集关联（简化版本）

        Args:
            event_trigger: 事件触发器
            rule: 采集规则
            crawl_times: 采集时间列表
            db: 数据库会话

        Returns:
            bool: 记录是否成功
        """
        try:
            logger.info(
                f"事件采集关联: 事件ID={event_trigger.event_id}, "
                f"规则ID={rule.id}, 采集次数={len(crawl_times)}"
            )
            return True

        except Exception as e:
            logger.error(f"记录事件采集关联失败: {e}")
            return False

    async def _create_crawl_task(
        self,
        event_trigger: EventTrigger,
        rule: EventDrivenCrawlRule,
        crawl_time: datetime,
        db: Session,
    ) -> Optional[CrawlTask]:
        """
        创建采集任务

        Args:
            event_trigger: 事件触发器
            rule: 采集规则
            crawl_time: 采集时间
            db: 数据库会话

        Returns:
            CrawlTask: 采集任务，失败返回None
        """
        try:
            # 获取数据源
            data_source = (
                db.query(DataSource).filter(DataSource.id == rule.source_id).first()
            )
            if not data_source:
                logger.error(f"数据源不存在: {rule.source_id}")
                return None

            # 合并任务配置
            task_config = dict(data_source.event_driven_config or {})
            task_config.update(rule.custom_task_config or {})
            task_config["event_info"] = {
                "event_id": event_trigger.event_id,
                "event_title": event_trigger.event_title,
                "importance_level": event_trigger.importance_level,
                "scheduled_time": event_trigger.scheduled_time.isoformat(),
            }

            # 创建采集任务
            task = CrawlTask(
                source_id=rule.source_id,
                task_type=self._determine_event_task_type(event_trigger, data_source),
                trigger_type="event",
                related_event_id=event_trigger.event_id,
                trigger_rule_id=rule.id,
                target_url=data_source.base_url,
                task_config=task_config,
                scheduled_time=crawl_time,
                status=TaskStatus.PENDING.value,
                created_at=datetime.now(timezone.utc),
                updated_at=datetime.now(timezone.utc),
            )

            db.add(task)
            db.commit()
            db.refresh(task)

            return task

        except Exception as e:
            logger.error(f"创建采集任务失败: {e}")
            db.rollback()
            return None

    def _determine_event_task_type(
        self, event_trigger: EventTrigger, data_source: DataSource
    ) -> str:
        """
        根据事件和数据源确定任务类型

        Args:
            event_trigger: 事件触发器
            data_source: 数据源

        Returns:
            str: 任务类型
        """
        # 特殊数据源的任务类型映射
        if data_source.name == "金十数据快讯":
            return "flash_news"

        # 根据事件重要性和数据源类型确定任务类型
        if event_trigger.importance_level >= 3:
            return "financial_event"  # 高重要性事件
        elif data_source.content_category == "financial_news":
            return "news_list"
        elif data_source.content_category == "market_data":
            return "market_data"
        else:
            return "general"

    async def _initialize_scheduler_status(self):
        """
        初始化调度器状态（简化版本）
        """
        try:
            logger.info(f"调度器 {self.scheduler_name} 状态初始化完成")

        except Exception as e:
            logger.error(f"初始化调度器状态失败: {e}")

    async def _update_scheduler_status(self, **kwargs):
        """
        更新调度器状态（简化版本）

        Args:
            **kwargs: 要更新的字段
        """
        try:
            logger.debug(f"调度器 {self.scheduler_name} 状态更新: {kwargs}")

        except Exception as e:
            logger.error(f"更新调度器状态失败: {e}")

    async def get_status(self) -> Dict:
        """
        获取调度器状态

        Returns:
            Dict: 调度器状态信息
        """
        return {
            "scheduler_name": self.scheduler_name,
            "is_running": self.is_running,
            "scan_interval_seconds": self.scan_interval_seconds,
            "lookahead_hours": self.lookahead_hours,
            "last_scan_time": self.last_scan_time,
            "total_events_processed": self.total_events_processed,
            "total_tasks_generated": self.total_tasks_generated,
            "error_count": self.error_count,
            "last_error_message": self.last_error_message,
            "processed_events_count": len(self.processed_events),
            "generated_tasks_count": len(self.generated_tasks),
        }


# 全局事件调度器实例
_event_scheduler_instance = None


def get_event_scheduler() -> EventDrivenScheduler:
    """
    获取全局事件驱动调度器实例

    Returns:
        EventDrivenScheduler: 事件驱动调度器实例
    """
    global _event_scheduler_instance
    if _event_scheduler_instance is None:
        _event_scheduler_instance = EventDrivenScheduler()
    return _event_scheduler_instance


async def start_event_scheduler():
    """
    启动全局事件驱动调度器
    """
    scheduler = get_event_scheduler()
    await scheduler.start()


async def stop_event_scheduler():
    """
    停止全局事件驱动调度器
    """
    scheduler = get_event_scheduler()
    await scheduler.stop()
