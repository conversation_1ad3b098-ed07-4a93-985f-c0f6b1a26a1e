"""
任务调度器模块
负责管理数据采集任务的调度和执行
"""

from dataclasses import dataclass
from datetime import datetime, timezone
from typing import Dict, Optional

from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.interval import IntervalTrigger
from loguru import logger

from ....core.database import SessionLocal
from ..engines.crawler_engine import get_crawler_engine
from ..models import CrawlTask, DataSource
from ..schemas import TaskStatus


@dataclass
class ScheduledJob:
    """调度任务信息"""

    job_id: str
    source_id: int
    source_name: str
    schedule_type: str  # interval, cron, once
    schedule_config: dict
    next_run_time: Optional[datetime] = None
    last_run_time: Optional[datetime] = None
    run_count: int = 0
    is_active: bool = True


class TaskScheduler:
    """
    任务调度器
    负责管理数据源的定时采集任务
    """

    def __init__(self):
        """
        初始化任务调度器
        """
        self.scheduler = AsyncIOScheduler()
        self.scheduled_jobs: Dict[str, ScheduledJob] = {}
        self.is_running = False

        # 配置调度器
        self._configure_scheduler()

    def _configure_scheduler(self):
        """
        配置调度器参数
        """
        # 设置任务存储
        self.scheduler.configure(
            timezone="UTC",
            job_defaults={
                "coalesce": True,  # 合并错过的任务
                "max_instances": 1,  # 同一任务最多运行1个实例
                "misfire_grace_time": 300,  # 错过任务的宽限时间（秒）
            },
        )

    async def start(self):
        """
        启动调度器
        """
        if self.is_running:
            logger.warning("任务调度器已在运行")
            return

        self.is_running = True
        self.scheduler.start()

        # 加载现有的数据源调度
        await self._load_existing_schedules()

        # 启动监控任务
        self._schedule_health_check()

        logger.info("任务调度器已启动")

    async def stop(self):
        """
        停止调度器
        """
        if not self.is_running:
            return

        self.is_running = False
        self.scheduler.shutdown(wait=True)

        logger.info("任务调度器已停止")

    async def add_data_source_schedule(self, data_source: DataSource) -> bool:
        """
        为数据源添加调度任务

        Args:
            data_source: 数据源配置

        Returns:
            bool: 是否成功添加
        """
        try:
            if data_source.crawl_mode not in ["interval", "hybrid"]:
                logger.debug(f"数据源 {data_source.name} 不需要定时调度")
                return True

            # 生成任务ID
            job_id = f"source_{data_source.id}"

            # 检查是否已存在
            if job_id in self.scheduled_jobs:
                logger.warning(f"数据源调度已存在: {data_source.name}")
                return False

            # 创建触发器
            trigger = self._create_trigger(data_source)
            if not trigger:
                logger.error(f"无法为数据源创建触发器: {data_source.name}")
                return False

            # 添加调度任务
            self.scheduler.add_job(
                func=self._execute_scheduled_crawl,
                trigger=trigger,
                id=job_id,
                args=[data_source.id],
                name=f"Crawl {data_source.name}",
                replace_existing=True,
            )

            # 记录调度信息
            self.scheduled_jobs[job_id] = ScheduledJob(
                job_id=job_id,
                source_id=data_source.id,
                source_name=data_source.name,
                schedule_type="interval",
                schedule_config={"interval_seconds": data_source.crawl_interval},
                next_run_time=self.scheduler.get_job(job_id).next_run_time,
            )

            logger.info(
                f"已添加数据源调度: {data_source.name}, 间隔: {data_source.crawl_interval}秒"
            )
            return True

        except Exception as e:
            logger.error(f"添加数据源调度失败: {e}")
            return False

    async def remove_data_source_schedule(self, source_id: int) -> bool:
        """
        移除数据源调度任务

        Args:
            source_id: 数据源ID

        Returns:
            bool: 是否成功移除
        """
        try:
            job_id = f"source_{source_id}"

            # 移除调度器中的任务
            if self.scheduler.get_job(job_id):
                self.scheduler.remove_job(job_id)

            # 移除记录
            if job_id in self.scheduled_jobs:
                del self.scheduled_jobs[job_id]

            logger.info(f"已移除数据源调度: {source_id}")
            return True

        except Exception as e:
            logger.error(f"移除数据源调度失败: {e}")
            return False

    async def update_data_source_schedule(self, data_source: DataSource) -> bool:
        """
        更新数据源调度任务

        Args:
            data_source: 数据源配置

        Returns:
            bool: 是否成功更新
        """
        try:
            # 先移除旧的调度
            await self.remove_data_source_schedule(data_source.id)

            # 添加新的调度
            return await self.add_data_source_schedule(data_source)

        except Exception as e:
            logger.error(f"更新数据源调度失败: {e}")
            return False

    async def pause_schedule(self, source_id: int) -> bool:
        """
        暂停数据源调度

        Args:
            source_id: 数据源ID

        Returns:
            bool: 是否成功暂停
        """
        try:
            job_id = f"source_{source_id}"
            job = self.scheduler.get_job(job_id)

            if job:
                self.scheduler.pause_job(job_id)
                if job_id in self.scheduled_jobs:
                    self.scheduled_jobs[job_id].is_active = False

                logger.info(f"已暂停数据源调度: {source_id}")
                return True
            else:
                logger.warning(f"数据源调度不存在: {source_id}")
                return False

        except Exception as e:
            logger.error(f"暂停数据源调度失败: {e}")
            return False

    async def resume_schedule(self, source_id: int) -> bool:
        """
        恢复数据源调度

        Args:
            source_id: 数据源ID

        Returns:
            bool: 是否成功恢复
        """
        try:
            job_id = f"source_{source_id}"
            job = self.scheduler.get_job(job_id)

            if job:
                self.scheduler.resume_job(job_id)
                if job_id in self.scheduled_jobs:
                    self.scheduled_jobs[job_id].is_active = True

                logger.info(f"已恢复数据源调度: {source_id}")
                return True
            else:
                logger.warning(f"数据源调度不存在: {source_id}")
                return False

        except Exception as e:
            logger.error(f"恢复数据源调度失败: {e}")
            return False

    async def get_schedule_status(self) -> Dict:
        """
        获取调度器状态

        Returns:
            Dict: 调度器状态信息
        """
        jobs_info = []

        for job_id, job_info in self.scheduled_jobs.items():
            scheduled_job = self.scheduler.get_job(job_id)

            job_data = {
                "job_id": job_id,
                "source_id": job_info.source_id,
                "source_name": job_info.source_name,
                "schedule_type": job_info.schedule_type,
                "schedule_config": job_info.schedule_config,
                "is_active": job_info.is_active,
                "run_count": job_info.run_count,
                "last_run_time": job_info.last_run_time,
                "next_run_time": scheduled_job.next_run_time if scheduled_job else None,
            }
            jobs_info.append(job_data)

        return {
            "is_running": self.is_running,
            "total_jobs": len(self.scheduled_jobs),
            "active_jobs": sum(1 for j in self.scheduled_jobs.values() if j.is_active),
            "paused_jobs": sum(
                1 for j in self.scheduled_jobs.values() if not j.is_active
            ),
            "jobs": jobs_info,
        }

    def _create_trigger(self, data_source: DataSource):
        """
        为数据源创建触发器

        Args:
            data_source: 数据源配置

        Returns:
            触发器对象
        """
        try:
            if (
                data_source.crawl_mode == "interval"
                or data_source.crawl_mode == "hybrid"
            ):
                # 间隔触发器
                return IntervalTrigger(seconds=data_source.crawl_interval)
            else:
                logger.warning(f"不支持的调度模式: {data_source.crawl_mode}")
                return None

        except Exception as e:
            logger.error(f"创建触发器失败: {e}")
            return None

    async def _execute_scheduled_crawl(self, source_id: int):
        """
        执行调度的采集任务

        Args:
            source_id: 数据源ID
        """
        try:
            job_id = f"source_{source_id}"

            # 更新运行统计
            if job_id in self.scheduled_jobs:
                self.scheduled_jobs[job_id].run_count += 1
                self.scheduled_jobs[job_id].last_run_time = datetime.now(timezone.utc)

            logger.info(f"开始执行调度采集: 数据源ID={source_id}")

            # 获取数据源
            data_source = await self._get_data_source(source_id)
            if not data_source:
                logger.error(f"数据源不存在: {source_id}")
                return

            # 检查数据源状态
            if data_source.status != "active":
                logger.warning(f"数据源未激活，跳过采集: {data_source.name}")
                return

            # 检查错误次数
            consecutive_errors = data_source.consecutive_error_count or 0
            max_errors = data_source.max_consecutive_errors or 10

            if consecutive_errors >= max_errors:
                logger.warning(f"数据源错误次数过多，跳过采集: {data_source.name}")
                return

            # 创建采集任务
            task = CrawlTask(
                source_id=source_id,
                task_type=self._determine_task_type(data_source),
                trigger_type="interval",
                target_url=data_source.base_url,
                scheduled_time=datetime.now(timezone.utc),
                status=TaskStatus.PENDING.value,
                created_at=datetime.now(timezone.utc),
                updated_at=datetime.now(timezone.utc),
            )

            # 保存任务到数据库
            task_id = await self._save_crawl_task(task)
            if task_id:
                task.id = task_id

                # 提交给爬虫引擎
                engine = get_crawler_engine()
                success = await engine.submit_task(task)

                if success:
                    logger.info(
                        f"调度任务已提交: 任务ID={task_id}, 数据源={data_source.name}"
                    )
                else:
                    logger.error(f"调度任务提交失败: 数据源={data_source.name}")
            else:
                logger.error(f"保存调度任务失败: 数据源={data_source.name}")

        except Exception as e:
            logger.error(f"执行调度采集失败: {e}")

    def _determine_task_type(self, data_source: DataSource) -> str:
        """
        根据数据源确定任务类型

        Args:
            data_source: 数据源配置

        Returns:
            str: 任务类型
        """
        # 特殊数据源的任务类型映射
        if data_source.name == "金十数据快讯":
            return "flash_news"

        # 根据内容分类确定任务类型
        if data_source.content_category == "financial_news":
            return "news_list"
        elif data_source.content_category == "market_data":
            return "market_data"
        elif data_source.content_category == "official_data":
            return "policy_data"
        else:
            return "general"

    async def _get_data_source(self, source_id: int) -> Optional[DataSource]:
        """
        获取数据源配置

        Args:
            source_id: 数据源ID

        Returns:
            DataSource: 数据源对象
        """
        try:
            db = SessionLocal()
            try:
                return db.query(DataSource).filter(DataSource.id == source_id).first()
            finally:
                db.close()
        except Exception as e:
            logger.error(f"获取数据源失败: {e}")
            return None

    async def _save_crawl_task(self, task: CrawlTask) -> Optional[int]:
        """
        保存采集任务到数据库

        Args:
            task: 采集任务

        Returns:
            int: 任务ID，失败返回None
        """
        try:
            db = SessionLocal()
            try:
                db.add(task)
                db.commit()
                db.refresh(task)
                return task.id
            finally:
                db.close()
        except Exception as e:
            logger.error(f"保存采集任务失败: {e}")
            return None

    async def _load_existing_schedules(self):
        """
        加载现有的数据源调度
        """
        try:
            db = SessionLocal()
            try:
                # 获取所有活跃的数据源
                data_sources = (
                    db.query(DataSource)
                    .filter(
                        DataSource.status == "active",
                        DataSource.crawl_mode.in_(["interval", "hybrid"]),
                    )
                    .all()
                )

                # 为每个数据源添加调度
                for data_source in data_sources:
                    await self.add_data_source_schedule(data_source)

                logger.info(f"加载了 {len(data_sources)} 个数据源调度")

            finally:
                db.close()

        except Exception as e:
            logger.error(f"加载现有调度失败: {e}")

    def _schedule_health_check(self):
        """
        调度健康检查任务
        """
        try:
            self.scheduler.add_job(
                func=self._health_check,
                trigger=IntervalTrigger(minutes=5),
                id="scheduler_health_check",
                name="Scheduler Health Check",
                replace_existing=True,
            )

            logger.info("已添加调度器健康检查任务")

        except Exception as e:
            logger.error(f"添加健康检查任务失败: {e}")

    async def _health_check(self):
        """
        执行健康检查
        """
        try:
            # 检查调度器状态
            if not self.scheduler.running:
                logger.error("调度器未运行")
                return

            # 检查任务执行情况
            total_jobs = len(self.scheduled_jobs)
            active_jobs = sum(1 for j in self.scheduled_jobs.values() if j.is_active)

            # 检查是否有长时间未运行的任务
            current_time = datetime.now(timezone.utc)
            stale_jobs = []

            for job_id, job_info in self.scheduled_jobs.items():
                if job_info.last_run_time:
                    time_since_last_run = (
                        current_time - job_info.last_run_time
                    ).total_seconds()
                    # 如果超过配置间隔的3倍时间未运行，标记为异常
                    expected_interval = job_info.schedule_config.get(
                        "interval_seconds", 3600
                    )
                    if time_since_last_run > expected_interval * 3:
                        stale_jobs.append(job_id)

            if stale_jobs:
                logger.warning(f"发现长时间未运行的调度任务: {stale_jobs}")

            logger.debug(
                f"调度器健康检查完成 - 总任务: {total_jobs}, 活跃: {active_jobs}, 异常: {len(stale_jobs)}"
            )

        except Exception as e:
            logger.error(f"调度器健康检查失败: {e}")


# 全局调度器实例
_scheduler_instance = None


def get_task_scheduler() -> TaskScheduler:
    """
    获取全局任务调度器实例

    Returns:
        TaskScheduler: 任务调度器实例
    """
    global _scheduler_instance
    if _scheduler_instance is None:
        _scheduler_instance = TaskScheduler()
    return _scheduler_instance


async def start_task_scheduler():
    """
    启动全局任务调度器
    """
    scheduler = get_task_scheduler()
    await scheduler.start()


async def stop_task_scheduler():
    """
    停止全局任务调度器
    """
    scheduler = get_task_scheduler()
    await scheduler.stop()
