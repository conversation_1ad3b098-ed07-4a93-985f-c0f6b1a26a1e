"""
爬虫引擎
负责协调和管理各种爬虫的执行
"""

import asyncio
import hashlib
import uuid
from concurrent.futures import ThreadPoolExecutor
from dataclasses import dataclass
from datetime import datetime, timedelta, timezone
from typing import Any, Dict, List, Optional, Set
from urllib.parse import urljoin, urlparse

from loguru import logger
from sqlalchemy.orm import Session, joinedload

from ....core.config import get_settings
from ....core.database import SessionLocal
from ....core.mongodb import mongodb_manager
from ..crawlers import CrawlerFactory
from ..models import CrawlTask, DataSource, RawDataRecord
from ..schemas import CrawlResult, TaskStatus

settings = get_settings()


@dataclass
class WorkerStats:
    """工作线程统计信息"""

    worker_id: str
    is_busy: bool = False
    current_task_id: Optional[int] = None
    tasks_completed: int = 0
    tasks_failed: int = 0
    total_runtime: float = 0.0
    last_task_time: Optional[datetime] = None


class CrawlerEngine:
    """
    爬虫引擎
    负责管理爬虫任务的执行和监控
    """

    def __init__(self, max_workers: int = 10, max_concurrent_tasks: int = 50):
        """
        初始化爬虫引擎

        Args:
            max_workers: 最大工作线程数
            max_concurrent_tasks: 最大并发任务数
        """
        self.max_workers = max_workers
        self.max_concurrent_tasks = max_concurrent_tasks

        # 任务队列和状态管理
        self.task_queue = asyncio.Queue(maxsize=1000)
        self.running_tasks: Dict[int, asyncio.Task] = {}
        self.completed_tasks: Set[int] = set()
        self.failed_tasks: Set[int] = set()

        # 工作线程池
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        self.workers: Dict[str, WorkerStats] = {}
        self.worker_threads: List[str] = []

        # 引擎状态
        self.is_running = False
        self.is_paused = False
        self.engine_id = str(uuid.uuid4())
        self.start_time = None
        self.last_health_check = None

        # 统计信息
        self.total_tasks_processed = 0
        self.total_tasks_success = 0
        self.total_tasks_failed = 0
        self.total_runtime = 0.0

        # 监控和限制
        self._task_semaphore = asyncio.Semaphore(max_concurrent_tasks)
        self._shutdown_event = asyncio.Event()

    async def start(self):
        """
        启动爬虫引擎
        """
        if self.is_running:
            logger.warning("爬虫引擎已在运行")
            return

        self.is_running = True
        self.start_time = datetime.now(timezone.utc)

        logger.info(
            f"启动爬虫引擎: ID={self.engine_id}, 工作线程={self.max_workers}, 最大并发={self.max_concurrent_tasks}"
        )

        # 启动工作线程
        for i in range(self.max_workers):
            worker_id = f"worker-{i+1}"
            self.workers[worker_id] = WorkerStats(worker_id=worker_id)
            self.worker_threads.append(worker_id)

        # 启动主循环
        asyncio.create_task(self._main_loop())
        asyncio.create_task(self._health_check_loop())
        asyncio.create_task(self._cleanup_loop())

    async def stop(self):
        """
        停止爬虫引擎
        """
        if not self.is_running:
            return

        logger.info("正在停止爬虫引擎...")

        try:
            self.is_running = False
            self._shutdown_event.set()

            # 等待运行中的任务完成
            if self.running_tasks:
                logger.info(f"等待 {len(self.running_tasks)} 个任务完成...")
                await asyncio.gather(*self.running_tasks.values(), return_exceptions=True)

            # 关闭线程池
            self.executor.shutdown(wait=True)

            # 清理资源
            self.task_queue = asyncio.Queue(maxsize=1000)  # 重置队列
            self.running_tasks.clear()
            self.workers.clear()
            self.worker_threads.clear()

            logger.info("爬虫引擎已停止")
        except Exception as e:
            logger.error(f"停止爬虫引擎时发生错误: {e}")
            raise

    async def pause(self):
        """
        暂停爬虫引擎
        """
        self.is_paused = True
        logger.info("爬虫引擎已暂停")

    async def resume(self):
        """
        恢复爬虫引擎
        """
        self.is_paused = False
        logger.info("爬虫引擎已恢复")

    async def submit_task(self, task: CrawlTask) -> bool:
        """
        提交采集任务

        Args:
            task: 采集任务

        Returns:
            bool: 是否成功提交
        """
        try:
            if not self.is_running:
                logger.error("爬虫引擎未运行，无法提交任务")
                return False

            # 检查任务是否已存在
            if task.id in self.running_tasks or task.id in self.completed_tasks:
                logger.warning(f"任务已存在: {task.id}")
                return False

            # 添加到队列
            await self.task_queue.put(task)
            logger.info(f"任务已提交: {task.id}")
            return True

        except Exception as e:
            logger.error(f"提交任务失败: {e}")
            return False

    async def cancel_task(self, task_id: int) -> bool:
        """
        取消运行中的任务

        Args:
            task_id: 任务ID

        Returns:
            bool: 是否成功取消
        """
        try:
            if task_id in self.running_tasks:
                task = self.running_tasks[task_id]
                task.cancel()
                del self.running_tasks[task_id]

                # 更新数据库状态
                await self._update_task_status(task_id, TaskStatus.CANCELLED)

                logger.info(f"任务已取消: {task_id}")
                return True
            else:
                logger.warning(f"任务不在运行中: {task_id}")
                return False

        except Exception as e:
            logger.error(f"取消任务失败: {e}")
            return False

    async def get_status(self) -> Dict[str, Any]:
        """
        获取引擎状态

        Returns:
            Dict: 引擎状态信息
        """
        return {
            "engine_id": self.engine_id,
            "is_running": self.is_running,
            "is_paused": self.is_paused,
            "start_time": self.start_time,
            "uptime_seconds": (
                (datetime.now(timezone.utc) - self.start_time).total_seconds()
                if self.start_time
                else 0
            ),
            "queue_size": self.task_queue.qsize(),
            "running_tasks": len(self.running_tasks),
            "completed_tasks": len(self.completed_tasks),
            "failed_tasks": len(self.failed_tasks),
            "total_processed": self.total_tasks_processed,
            "success_rate": self.total_tasks_success
            / max(self.total_tasks_processed, 1),
            "workers": {
                "total": len(self.workers),
                "busy": sum(1 for w in self.workers.values() if w.is_busy),
                "idle": sum(1 for w in self.workers.values() if not w.is_busy),
                "stats": {
                    wid: {
                        "is_busy": w.is_busy,
                        "current_task": w.current_task_id,
                        "completed": w.tasks_completed,
                        "failed": w.tasks_failed,
                        "avg_runtime": w.total_runtime / max(w.tasks_completed, 1),
                        "last_task": w.last_task_time,
                    }
                    for wid, w in self.workers.items()
                },
            },
        }

    async def _main_loop(self):
        """
        主循环，处理任务队列
        """
        while self.is_running:
            try:
                if self.is_paused:
                    await asyncio.sleep(1)
                    continue

                # 从队列获取任务
                try:
                    task = await asyncio.wait_for(self.task_queue.get(), timeout=1.0)
                except asyncio.TimeoutError:
                    continue

                # 检查并发限制
                async with self._task_semaphore:
                    # 启动任务
                    task_coroutine = self._execute_task(task)
                    asyncio_task = asyncio.create_task(task_coroutine)
                    self.running_tasks[task.id] = asyncio_task

            except Exception as e:
                logger.error(f"主循环错误: {e}")
                await asyncio.sleep(1)

    async def _execute_task(self, task: CrawlTask):
        """
        执行单个采集任务

        Args:
            task: 采集任务
        """
        start_time = datetime.now(timezone.utc)
        worker_id = None
        crawler = None
        result = None

        try:
            # 分配工作线程
            worker_id = self._assign_worker(task.id)

            # 更新任务状态
            await self._update_task_status(
                task.id, TaskStatus.RUNNING, worker_id=worker_id
            )

            logger.info(f"开始执行任务: {task.id}, 工作线程: {worker_id}")

            # 获取数据源
            data_source = await self._get_data_source(task.source_id)
            if not data_source:
                raise Exception(f"数据源不存在: {task.source_id}")

            # 创建爬虫实例
            crawler = CrawlerFactory.create_crawler(data_source, task)

            # 执行爬取
            async with crawler:
                result = await crawler.crawl()

                # 处理结果
                await self._process_crawl_result(task, result, start_time)

                # 更新统计信息
                self.total_tasks_success += 1
                if worker_id:
                    self.workers[worker_id].tasks_completed += 1

                logger.info(f"任务执行成功: {task.id}")

        except Exception as e:
            logger.error(f"任务执行失败: {task.id}, 错误: {e}")

            # 更新失败状态
            await self._update_task_status(
                task.id,
                TaskStatus.FAILED,
                error_message=str(e),
                completed_time=datetime.now(timezone.utc),
            )

            self.total_tasks_failed += 1
            if worker_id:
                self.workers[worker_id].tasks_failed += 1

            self.failed_tasks.add(task.id)

        finally:
            # 确保爬虫实例被清理
            if crawler is not None and hasattr(crawler, 'cleanup'):
                try:
                    await crawler.cleanup()
                except Exception as e:
                    logger.error(f"清理爬虫实例失败: {e}")

            # 清理资源
            if task.id in self.running_tasks:
                del self.running_tasks[task.id]

            if worker_id:
                self._release_worker(worker_id)

            self.total_tasks_processed += 1
            self.completed_tasks.add(task.id)

            # 计算执行时间
            duration = (datetime.now(timezone.utc) - start_time).total_seconds()
            self.total_runtime += duration

            if worker_id:
                self.workers[worker_id].total_runtime += duration
                self.workers[worker_id].last_task_time = datetime.now(timezone.utc)

    def _assign_worker(self, task_id: int) -> Optional[str]:
        """
        分配工作线程

        Args:
            task_id: 任务ID

        Returns:
            str: 工作线程ID，失败返回None
        """
        for worker_id, worker in self.workers.items():
            if not worker.is_busy:
                worker.is_busy = True
                worker.current_task_id = task_id
                return worker_id
        return None

    def _release_worker(self, worker_id: str):
        """
        释放工作线程

        Args:
            worker_id: 工作线程ID
        """
        if worker_id in self.workers:
            worker = self.workers[worker_id]
            worker.is_busy = False
            worker.current_task_id = None

    async def _get_data_source(self, source_id: int) -> Optional[DataSource]:
        """
        获取数据源配置

        Args:
            source_id: 数据源ID

        Returns:
            DataSource: 数据源对象，不存在返回None
        """
        try:
            db = SessionLocal()
            try:
                # 预加载configs关系，避免懒加载错误
                return (
                    db.query(DataSource)
                    .options(joinedload(DataSource.configs))
                    .filter(DataSource.id == source_id)
                    .first()
                )
            finally:
                db.close()
        except Exception as e:
            logger.error(f"获取数据源失败: {e}")
            return None

    async def _process_crawl_result(
        self, task: CrawlTask, result: CrawlResult, start_time: datetime
    ):
        """
        处理爬取结果

        Args:
            task: 采集任务
            result: 爬取结果
            start_time: 开始时间
        """
        try:
            # 计算统计信息
            duration = (datetime.now(timezone.utc) - start_time).total_seconds()
            statistics = result.data.get("statistics", {}) if result.data else {}

            # 更新任务状态
            await self._update_task_status(
                task.id,
                TaskStatus.COMPLETED if result.success else TaskStatus.FAILED,
                progress=100,
                items_found=statistics.get("items_found", 0),
                items_processed=statistics.get("items_processed", 0),
                items_success=statistics.get("items_success", 0),
                items_failed=statistics.get("items_failed", 0),
                duration_seconds=int(duration),
                network_requests=statistics.get("network_requests", 0),
                completed_time=datetime.now(timezone.utc),
                error_message=result.message if not result.success else None,
            )

            # 保存原始数据记录
            logger.info(
                f"处理爬取结果: task_id={task.id}, success={result.success}, has_data={result.data is not None}"
            )

            if result.success and result.data:
                mongo_ids = result.data.get("mongo_ids", [])
                logger.debug(f"获取到mongo_ids: {len(mongo_ids)} 个, ids={mongo_ids}")

                if mongo_ids:
                    # 记录成功和失败的数量
                    postgres_success_count = 0
                    postgres_failed_count = 0
                    failed_mongo_ids = []

                    for mongo_id in mongo_ids:
                        try:
                            logger.debug(f"开始创建原始数据记录: mongo_id={mongo_id}")
                            success = await self._create_raw_data_record(task, mongo_id)
                            if success:
                                postgres_success_count += 1
                                logger.debug(
                                    f"成功创建PostgreSQL记录: mongo_id={mongo_id}"
                                )
                            else:
                                postgres_failed_count += 1
                                failed_mongo_ids.append(mongo_id)
                                logger.debug(
                                    f"跳过创建PostgreSQL记录: mongo_id={mongo_id}"
                                )
                        except Exception as e:
                            postgres_failed_count += 1
                            failed_mongo_ids.append(mongo_id)
                            logger.error(
                                f"创建PostgreSQL记录异常: mongo_id={mongo_id}, error={e}",
                                exc_info=True,
                            )

                    # 记录统计结果
                    logger.info(
                        f"PostgreSQL记录创建完成: "
                        f"成功={postgres_success_count}, 失败={postgres_failed_count}, "
                        f"MongoDB总数={len(mongo_ids)}"
                    )

                else:
                    logger.warning(f"任务 {task.id} 成功但没有mongo_ids")
            else:
                logger.warning(
                    f"任务 {task.id} 失败或没有数据: success={result.success}, data={result.data}"
                )

        except Exception as e:
            logger.error(f"处理爬取结果失败: {e}", exc_info=True)
            # 重新抛出异常，确保上层能够感知到错误
            raise

    async def _update_task_status(self, task_id: int, status: TaskStatus, **kwargs):
        """
        更新任务状态

        Args:
            task_id: 任务ID
            status: 任务状态
            **kwargs: 其他更新字段
        """
        try:
            db = SessionLocal()
            try:
                task = db.query(CrawlTask).filter(CrawlTask.id == task_id).first()
                if task:
                    task.status = status.value
                    task.updated_at = datetime.now(timezone.utc)
                    for key, value in kwargs.items():
                        if hasattr(task, key):
                            setattr(task, key, value)
                    db.commit()
            finally:
                db.close()
        except Exception as e:
            logger.error(f"更新任务状态失败: {e}")

    async def _create_raw_data_record(self, task: CrawlTask, mongo_id: str) -> bool:
        """
        创建原始数据记录

        Args:
            task: 采集任务
            mongo_id: MongoDB文档ID

        Returns:
            bool: True表示成功创建记录，False表示跳过创建（如重复等），异常会抛出
        """
        db = SessionLocal()
        try:
            # 重新查询数据库获取 task 对象，确保在正确的会话中
            task_id = task.id if hasattr(task, "id") else None
            if task_id is None:
                logger.error("无法获取任务ID")
                return False

            # 重新查询任务，确保在正确的数据库会话中
            db_task = db.query(CrawlTask).filter(CrawlTask.id == task_id).first()
            if not db_task:
                logger.error(f"无法找到任务: task_id={task_id}")
                return False

            # 从MongoDB获取详细数据
            logger.debug(f"正在从MongoDB获取数据: {mongo_id}")
            mongo_data = await mongodb_manager.get_raw_content(mongo_id)
            if not mongo_data:
                logger.error(f"无法从MongoDB获取数据: {mongo_id}")
                return False

            # 提取URL信息
            source_url = mongo_data.get("source_url", db_task.target_url or "")
            canonical_url = self._normalize_url(source_url)
            url_hash = self._calculate_url_hash(source_url)
            url_domain = self._extract_domain(source_url)
            logger.debug(f"URL信息处理完成: {source_url}")

            # 提取内容信息
            content = mongo_data.get("content", "")
            title = mongo_data.get("title", "")
            content_hash = self._calculate_content_hash(content)
            content_simhash = self._calculate_simhash(content)
            content_length = len(content) if content else 0
            logger.debug(f"内容信息处理完成: 标题={title}, 长度={content_length}")

            # 提取分类信息
            category = self._extract_category(mongo_data, db_task)
            subcategory = self._extract_subcategory(mongo_data, db_task)
            language = self._detect_language(content)
            logger.debug(
                f"分类信息处理完成: 分类={category}, 子分类={subcategory}, 语言={language}"
            )

            # 提取社交媒体统计
            social_stats = self._extract_social_stats(mongo_data)
            logger.debug(f"社交媒体统计处理完成: {social_stats}")

            # 计算质量评分
            quality_score = self._calculate_quality_score(mongo_data)
            logger.debug(f"质量评分计算完成: {quality_score}")

            # 检查MongoDB ID是否已存在（正确的去重方式）
            existing_record = (
                db.query(RawDataRecord)
                .filter(RawDataRecord.mongodb_id == mongo_id)
                .first()
            )
            if existing_record:
                logger.info(
                    f"MongoDB记录已存在，跳过创建: mongodb_id={mongo_id}, existing_id={existing_record.id}"
                )
                return False

            # 检查内容哈希是否已存在（仅用于警告，不阻止创建）
            existing_content = (
                db.query(RawDataRecord)
                .filter(RawDataRecord.content_hash == content_hash)
                .first()
            )
            if existing_content:
                logger.warning(
                    f"内容哈希重复，但MongoDB ID不同，继续创建: content_hash={content_hash}, existing_id={existing_content.id}, new_mongodb_id={mongo_id}"
                )

            # 显式设置当前时间，避免SQLAlchemy server_default处理问题
            current_time = datetime.now(timezone.utc)

            # 创建原始数据记录
            record = RawDataRecord(
                task_id=db_task.id,
                source_id=db_task.source_id,
                # URL信息
                source_url=source_url,
                canonical_url=canonical_url,
                url_hash=url_hash,
                url_domain=url_domain,
                # 内容标识
                content_hash=content_hash,
                content_simhash=content_simhash,
                content_length=content_length,
                content_encoding="utf-8",
                # 基础元数据
                title=title[:1000] if title else None,  # 限制长度
                author=(
                    mongo_data.get("author", "")[:200]
                    if mongo_data.get("author")
                    else None
                ),
                # 处理发布时间
                publish_time=mongo_data.get("publish_time"),
                # MongoDB引用
                mongodb_id=mongo_id,
                mongodb_collection="raw_content",
                content_type=mongo_data.get("content_type", "financial_news"),
                # 处理状态
                processing_status="pending",
                processing_priority=5,
                quality_score=quality_score,
                # 数据生命周期管理
                retention_policy="standard",
                archive_after_days=365,
                delete_after_days=1095,
                is_archived=False,
                # 显式设置时间字段，避免server_default处理问题
                crawl_time=current_time,
                created_at=current_time,
                updated_at=current_time,
            )

            logger.info(
                f"准备创建新记录: task_id={db_task.id}, source_id={db_task.source_id}, mongo_id={mongo_id}"
            )

            db.add(record)
            db.commit()
            logger.info(f"成功创建原始数据记录: mongodb_id={mongo_id}, content_hash={content_hash}")
            return True

        except Exception as e:
            logger.error(f"创建原始数据记录失败: {e}", exc_info=True)
            try:
                db.rollback()
            except Exception as rollback_error:
                logger.error(f"回滚事务失败: {rollback_error}")
            raise
        finally:
            db.close()

    def _normalize_url(self, url: str) -> str:
        """
        标准化URL，去除查询参数和片段

        Args:
            url: 原始URL

        Returns:
            标准化后的URL
        """
        if not url:
            return ""

        try:
            parsed = urlparse(url)
            # 重新构建不包含查询参数和片段的URL
            canonical = f"{parsed.scheme}://{parsed.netloc}{parsed.path}"
            return canonical.rstrip("/")
        except Exception:
            return url

    def _calculate_url_hash(self, url: str) -> str:
        """
        计算URL哈希值

        Args:
            url: URL地址

        Returns:
            MD5哈希值
        """
        if not url:
            return ""

        # 使用标准化的URL计算哈希
        canonical_url = self._normalize_url(url)
        return hashlib.md5(canonical_url.encode("utf-8")).hexdigest()

    def _extract_domain(self, url: str) -> str:
        """
        从URL中提取域名

        Args:
            url: URL地址

        Returns:
            域名
        """
        if not url:
            return ""

        try:
            parsed = urlparse(url)
            return parsed.netloc
        except Exception:
            return ""

    def _calculate_content_hash(self, content: str) -> str:
        """
        计算内容哈希值，用于去重

        Args:
            content: 内容文本

        Returns:
            MD5哈希值
        """
        if not content:
            return ""

        # 清理内容，移除空白字符
        cleaned_content = "".join(content.split())
        return hashlib.md5(cleaned_content.encode("utf-8")).hexdigest()

    def _calculate_simhash(self, content: str) -> int:
        """
        计算内容SimHash值，用于检测相似内容

        Args:
            content: 内容文本

        Returns:
            SimHash值 (在PostgreSQL BIGINT范围内)
        """
        if not content:
            return 0

        # 简化的SimHash实现
        import hashlib
        import re

        # 分词（简单的基于空格和标点符号）
        words = re.findall(r"\w+", content.lower())
        if not words:
            return 0

        # 计算特征向量
        fingerprint = [0] * 64

        for word in words:
            # 计算单词hash
            word_hash = hashlib.md5(word.encode()).hexdigest()
            word_hash_int = int(word_hash[:16], 16)  # 使用前16个字符，转换为整数

            # 更新指纹
            for i in range(64):
                bit = (word_hash_int >> i) & 1
                if bit:
                    fingerprint[i] += 1
                else:
                    fingerprint[i] -= 1

        # 生成最终的SimHash
        simhash = 0
        for i in range(64):
            if fingerprint[i] > 0:
                simhash |= 1 << i

        # 转换为PostgreSQL BIGINT范围 (-2^63 到 2^63-1)
        # 如果值超过了有符号64位整数的最大值，将其转换为有符号范围
        if simhash > 9223372036854775807:  # 2^63 - 1
            simhash = simhash - 18446744073709551616  # 2^64，转换为有符号表示

        return simhash

    def _extract_category(
        self, mongo_data: Dict[str, Any], task: CrawlTask
    ) -> Optional[str]:
        """
        提取主要分类

        Args:
            mongo_data: MongoDB中的数据
            task: 采集任务

        Returns:
            主要分类
        """
        # 优先使用数据中的分类信息
        if "category" in mongo_data:
            return mongo_data["category"]

        # 根据数据源的内容分类推断
        # 注意：这里不直接访问 task.data_source，因为可能已脱离会话
        # 如果需要数据源信息，应该在调用此方法前通过数据库查询获取
        return None

    def _extract_subcategory(
        self, mongo_data: Dict[str, Any], task: CrawlTask
    ) -> Optional[str]:
        """
        提取子分类

        Args:
            mongo_data: MongoDB中的数据
            task: 采集任务

        Returns:
            子分类
        """
        # 从数据中提取子分类
        return mongo_data.get("subcategory")

    def _detect_language(self, content: str) -> str:
        """
        检测内容语言

        Args:
            content: 内容文本

        Returns:
            语言代码
        """
        if not content:
            return "zh"  # 默认中文

        # 简单的语言检测：统计中文字符比例
        chinese_chars = len([c for c in content if "\u4e00" <= c <= "\u9fff"])
        total_chars = len([c for c in content if c.isalpha()])

        if total_chars == 0:
            return "zh"

        chinese_ratio = chinese_chars / total_chars

        if chinese_ratio > 0.3:
            return "zh"
        else:
            return "en"

    def _extract_social_stats(self, mongo_data: Dict[str, Any]) -> Dict[str, int]:
        """
        提取社交媒体统计信息

        Args:
            mongo_data: MongoDB中的数据

        Returns:
            社交媒体统计
        """
        return {
            "view_count": int(mongo_data.get("view_count", 0)),
            "like_count": int(mongo_data.get("like_count", 0)),
            "comment_count": int(mongo_data.get("comment_count", 0)),
            "share_count": int(mongo_data.get("share_count", 0)),
        }

    def _calculate_quality_score(self, mongo_data: Dict[str, Any]) -> float:
        """
        计算内容质量评分

        Args:
            mongo_data: MongoDB中的数据

        Returns:
            质量评分 (0-1)
        """
        score = 0.5  # 基础分数

        # 内容长度评分
        content = mongo_data.get("content", "")
        if content:
            content_length = len(content)
            if content_length > 100:
                score += 0.1
            if content_length > 500:
                score += 0.1
            if content_length > 1000:
                score += 0.1

        # 标题质量评分
        title = mongo_data.get("title", "")
        if title and len(title) > 10:
            score += 0.1

        # 作者信息评分
        if mongo_data.get("author"):
            score += 0.05

        # 发布时间评分
        if mongo_data.get("publish_time"):
            score += 0.05

        # 确保分数在0-1范围内
        return min(max(score, 0.0), 1.0)

    async def _health_check_loop(self):
        """
        健康检查循环
        """
        while self.is_running:
            try:
                await asyncio.sleep(60)  # 每分钟检查一次
                await self._perform_health_check()
            except Exception as e:
                logger.error(f"健康检查失败: {e}")

    async def _perform_health_check(self):
        """
        执行健康检查
        """
        self.last_health_check = datetime.now(timezone.utc)

        # 检查内存使用
        import psutil

        process = psutil.Process()
        memory_mb = process.memory_info().rss / 1024 / 1024

        # 检查长时间运行的任务
        stuck_tasks = []
        current_time = datetime.now(timezone.utc)

        for task_id, task in self.running_tasks.items():
            # 如果任务运行超过1小时，标记为可能卡死
            if hasattr(task, "start_time"):
                if (current_time - task.start_time).total_seconds() > 3600:
                    stuck_tasks.append(task_id)

        if stuck_tasks:
            logger.warning(f"发现可能卡死的任务: {stuck_tasks}")

        logger.debug(
            f"健康检查完成 - 内存: {memory_mb:.1f}MB, 运行任务: {len(self.running_tasks)}, 队列: {self.task_queue.qsize()}"
        )

    async def _cleanup_loop(self):
        """
        清理循环，定期清理已完成的任务记录
        """
        while self.is_running:
            try:
                await asyncio.sleep(300)  # 每5分钟清理一次
                await self._cleanup_completed_tasks()
            except Exception as e:
                logger.error(f"清理任务失败: {e}")

    async def _cleanup_completed_tasks(self):
        """
        清理已完成的任务记录
        """
        # 只保留最近1000个完成的任务记录
        if len(self.completed_tasks) > 1000:
            excess = len(self.completed_tasks) - 1000
            # 移除最早的任务记录
            for _ in range(excess):
                self.completed_tasks.pop()

        # 只保留最近100个失败的任务记录
        if len(self.failed_tasks) > 100:
            excess = len(self.failed_tasks) - 100
            for _ in range(excess):
                self.failed_tasks.pop()

        logger.debug(
            f"任务记录清理完成 - 完成: {len(self.completed_tasks)}, 失败: {len(self.failed_tasks)}"
        )

    async def health_check(self) -> Dict[str, Any]:
        """
        执行健康检查

        Returns:
            Dict: 健康检查结果
        """
        try:
            # 检查引擎状态
            engine_status = "healthy" if self.is_running else "stopped"

            # 检查工作线程状态
            active_workers = sum(1 for w in self.workers.values() if not w.is_busy)
            total_workers = len(self.workers)

            # 检查队列状态
            queue_size = self.task_queue.qsize()

            # 检查运行中的任务
            running_task_count = len(self.running_tasks)

            health_result = {
                "status": engine_status,
                "workers": {
                    "total": total_workers,
                    "active": active_workers,
                    "busy": total_workers - active_workers,
                },
                "queue": {"size": queue_size, "max_size": self.task_queue.maxsize},
                "tasks": {
                    "running": running_task_count,
                    "completed": len(self.completed_tasks),
                    "failed": len(self.failed_tasks),
                },
                "uptime": (
                    (datetime.now(timezone.utc) - self.start_time).total_seconds()
                    if self.start_time
                    else 0
                ),
                "last_check": datetime.now(timezone.utc),
            }

            self.last_health_check = datetime.now(timezone.utc)
            return health_result

        except Exception as e:
            logger.error(f"健康检查失败: {e}")
            return {
                "status": "error",
                "error": str(e),
                "last_check": datetime.now(timezone.utc),
            }


# 全局引擎实例
_engine_instance = None


def get_crawler_engine() -> CrawlerEngine:
    """
    获取全局爬虫引擎实例

    Returns:
        CrawlerEngine: 爬虫引擎实例
    """
    global _engine_instance
    if _engine_instance is None:
        _engine_instance = CrawlerEngine()
    return _engine_instance


async def start_crawler_engine():
    """
    启动全局爬虫引擎
    """
    engine = get_crawler_engine()
    await engine.start()


async def stop_crawler_engine():
    """
    停止全局爬虫引擎
    """
    engine = get_crawler_engine()
    await engine.stop()


# 诊断和调试工具函数
async def diagnose_raw_data_records() -> Dict[str, Any]:
    """
    诊断raw_data_records表的状态

    Returns:
        Dict: 包含表状态信息的字典
    """

    db = SessionLocal()
    try:
        # 检查表是否存在
        from sqlalchemy import text

        table_exists_query = text(
            """
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name = 'raw_data_records'
            );
        """
        )
        table_exists = db.execute(table_exists_query).scalar()

        if not table_exists:
            return {
                "table_exists": False,
                "error": "raw_data_records表不存在",
                "suggestion": "需要运行数据库迁移创建表",
            }

        # 获取表的基本统计信息
        total_count = db.query(RawDataRecord).count()

        # 获取最近的记录
        recent_records = (
            db.query(RawDataRecord)
            .order_by(RawDataRecord.created_at.desc())
            .limit(5)
            .all()
        )

        # 按处理状态分组统计
        status_stats = db.execute(
            text(
                """
            SELECT processing_status, COUNT(*) as count
            FROM raw_data_records 
            GROUP BY processing_status
        """
            )
        ).fetchall()

        # 按数据源分组统计
        source_stats = db.execute(
            text(
                """
            SELECT source_id, COUNT(*) as count
            FROM raw_data_records 
            GROUP BY source_id
            ORDER BY count DESC
            LIMIT 10
        """
            )
        ).fetchall()

        # 检查是否有MongoDB关联
        mongo_count = (
            db.query(RawDataRecord).filter(RawDataRecord.mongodb_id.isnot(None)).count()
        )

        return {
            "table_exists": True,
            "total_records": total_count,
            "records_with_mongodb_id": mongo_count,
            "recent_records": [
                {
                    "id": r.id,
                    "mongodb_id": r.mongodb_id,
                    "source_url": (
                        r.source_url[:100] + "..."
                        if r.source_url and len(r.source_url) > 100
                        else r.source_url
                    ),
                    "title": (
                        r.title[:50] + "..."
                        if r.title and len(r.title) > 50
                        else r.title
                    ),
                    "processing_status": r.processing_status,
                    "created_at": r.created_at.isoformat() if r.created_at else None,
                }
                for r in recent_records
            ],
            "status_distribution": {status: count for status, count in status_stats},
            "top_sources": {str(source_id): count for source_id, count in source_stats},
        }

    except Exception as e:
        return {
            "table_exists": None,
            "error": f"诊断过程中发生错误: {str(e)}",
            "error_type": type(e).__name__,
        }
    finally:
        db.close()


async def check_mongodb_postgres_sync(limit: int = 10) -> Dict[str, Any]:
    """
    检查MongoDB和PostgreSQL之间的数据同步状态

    Args:
        limit: 检查的MongoDB记录数量限制

    Returns:
        Dict: 同步状态信息
    """
    # 确保MongoDB连接
    if not mongodb_manager.connect():
        return {"error": "无法连接到MongoDB"}

    db = SessionLocal()
    try:
        # 获取最近的MongoDB记录
        mongo_records = await mongodb_manager.get_recent_raw_content(limit)

        sync_results = []
        postgres_found = 0
        postgres_missing = 0

        for mongo_record in mongo_records:
            mongo_id = str(mongo_record.get("_id"))

            # 检查PostgreSQL中是否存在对应记录
            postgres_record = (
                db.query(RawDataRecord)
                .filter(RawDataRecord.mongodb_id == mongo_id)
                .first()
            )

            if postgres_record:
                postgres_found += 1
                status = "synced"
            else:
                postgres_missing += 1
                status = "missing_in_postgres"

            sync_results.append(
                {
                    "mongodb_id": mongo_id,
                    "postgres_record_id": (
                        postgres_record.id if postgres_record else None
                    ),
                    "sync_status": status,
                    "mongo_title": mongo_record.get("title", "")[:50],
                    "mongo_crawl_time": (
                        mongo_record.get("crawl_time", "").isoformat()
                        if mongo_record.get("crawl_time")
                        else None
                    ),
                }
            )

        return {
            "total_checked": len(mongo_records),
            "postgres_found": postgres_found,
            "postgres_missing": postgres_missing,
            "sync_rate": (
                (postgres_found / len(mongo_records) * 100) if mongo_records else 0
            ),
            "details": sync_results,
        }

    except Exception as e:
        return {
            "error": f"同步检查过程中发生错误: {str(e)}",
            "error_type": type(e).__name__,
        }
    finally:
        db.close()
