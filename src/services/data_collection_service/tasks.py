"""
数据采集服务Celery任务
提供基于Celery的异步数据采集功能
"""

import asyncio
import logging
from datetime import datetime, timedelta, timezone
from typing import Dict, Any, List, Optional

from celery import current_task
from sqlalchemy.orm import sessionmaker
from sqlalchemy import and_, desc

from ...core.celery_app import celery_app, data_collection_task, high_priority_task
from ...core.database import SessionLocal, Session
from .models import CrawlTask, DataSource, RawDataRecord
from .schemas import CrawlTaskCreate, TaskStatus, TriggerType
from .service import CrawlTaskService, DataSourceService
from .crawlers.crawler_factory import CrawlerFactory

logger = logging.getLogger(__name__)


@data_collection_task
def execute_crawl_task(self, data_source_id: int, task_config: Optional[Dict[str, Any]] = None):
    """
    执行单个数据采集任务
    
    Args:
        data_source_id: 数据源ID
        task_config: 任务配置参数
        
    Returns:
        Dict: 执行结果
    """
    task_id = current_task.request.id
    logger.info(f"开始执行采集任务: data_source_id={data_source_id}, celery_task_id={task_id}")
    
    db = SessionLocal()
    try:
        # 获取服务实例
        data_source_service = DataSourceService()
        crawl_task_service = CrawlTaskService()
        
        # 获取数据源
        data_source = data_source_service.get_data_source_by_id(db, data_source_id)
        if not data_source:
            raise ValueError(f"数据源不存在: {data_source_id}")
        
        if data_source.status != "active":
            logger.warning(f"数据源未激活，跳过采集: {data_source.name}")
            return {"status": "skipped", "reason": "data_source_inactive"}
        
        # 创建采集任务记录
        crawl_task_data = CrawlTaskCreate(
            source_id=data_source_id,
            task_type=_determine_task_type(data_source),
            trigger_type=TriggerType.MANUAL,  # Celery执行的任务使用manual触发类型
            target_url=data_source.base_url,
            task_config=task_config or {},
            status=TaskStatus.RUNNING.value,
            celery_task_id=task_id
        )
        
        crawl_task = crawl_task_service.create_task(db, crawl_task_data)
        logger.info(f"创建采集任务记录: crawl_task_id={crawl_task.id}")
        
        # 执行采集
        result = _execute_crawl_sync(data_source, crawl_task, task_config)
        
        # 处理采集结果并创建raw_data_records
        if result.get("success", False):
            statistics = result.get("statistics", {})

            # 获取MongoDB IDs并创建PostgreSQL记录
            mongo_ids = result.get("mongo_ids", [])
            postgres_success_count = 0
            postgres_failed_count = 0

            if mongo_ids:
                logger.info(f"开始创建 {len(mongo_ids)} 个PostgreSQL记录")
                for mongo_id in mongo_ids:
                    try:
                        success = _create_raw_data_record_sync(crawl_task, mongo_id, db)
                        if success:
                            postgres_success_count += 1
                        else:
                            postgres_failed_count += 1
                    except Exception as e:
                        postgres_failed_count += 1
                        logger.error(f"创建PostgreSQL记录异常: mongo_id={mongo_id}, error={e}")

                logger.info(f"PostgreSQL记录创建完成: 成功={postgres_success_count}, 失败={postgres_failed_count}")

            # 更新任务状态
            crawl_task_service.complete_task(
                db, crawl_task.id,
                success=True,
                items_found=statistics.get("items_found", 0),
                items_processed=statistics.get("items_processed", 0),
                items_success=statistics.get("items_success", 0),
                items_failed=statistics.get("items_failed", 0),
                network_requests=statistics.get("network_requests", 0)
            )
            logger.info(f"采集任务完成: crawl_task_id={crawl_task.id}")
        else:
            crawl_task_service.complete_task(
                db, crawl_task.id,
                success=False,
                error_message=result.get("error", "未知错误")
            )
            logger.error(f"采集任务失败: crawl_task_id={crawl_task.id}")
        
        return result
        
    except Exception as e:
        logger.error(f"执行采集任务异常: {e}")
        return {"status": "error", "error": str(e)}
    finally:
        db.close()


@celery_app.task(bind=True)
def schedule_interval_crawl(self):
    """
    定时调度采集任务
    查询需要采集的数据源并创建采集任务
    """
    logger.info("开始定时调度采集任务")
    
    db = SessionLocal()
    try:
        data_source_service = DataSourceService()
        
        # 查询需要采集的数据源
        data_sources = data_source_service.get_data_sources_for_crawl(db)
        
        scheduled_count = 0
        for data_source in data_sources:
            try:
                # 检查数据源状态
                if data_source.status != "active":
                    continue
                
                # 检查错误次数
                consecutive_errors = data_source.consecutive_error_count or 0
                max_errors = data_source.max_consecutive_errors or 10
                
                if consecutive_errors >= max_errors:
                    logger.warning(f"数据源错误次数过多，跳过采集: {data_source.name}")
                    continue
                
                # 提交采集任务
                task_result = execute_crawl_task.delay(data_source.id)
                logger.info(f"已调度采集任务: data_source={data_source.name}, task_id={task_result.id}")
                
                # 更新下次采集时间
                current_time = datetime.now(timezone.utc)
                next_crawl_time = current_time + timedelta(seconds=data_source.crawl_interval)
                data_source.next_crawl_time = next_crawl_time
                db.commit()
                
                scheduled_count += 1
                
            except Exception as e:
                logger.error(f"调度数据源采集失败: {data_source.name}, 错误: {e}")
        
        logger.info(f"定时调度完成，共调度 {scheduled_count} 个采集任务")
        return {"scheduled_count": scheduled_count}
        
    except Exception as e:
        logger.error(f"定时调度异常: {e}")
        return {"error": str(e)}
    finally:
        db.close()


@data_collection_task
def execute_event_driven_crawl(self, event_id: int, rule_id: int, crawl_config: Optional[Dict[str, Any]] = None):
    """
    执行事件驱动的采集任务
    
    Args:
        event_id: 财经事件ID
        rule_id: 事件驱动规则ID
        crawl_config: 采集配置
        
    Returns:
        Dict: 执行结果
    """
    task_id = current_task.request.id
    logger.info(f"开始执行事件驱动采集: event_id={event_id}, rule_id={rule_id}, celery_task_id={task_id}")
    
    db = SessionLocal()
    try:
        # 获取事件驱动规则
        from .service import EventDrivenCrawlRuleService
        rule_service = EventDrivenCrawlRuleService()
        
        rule = rule_service.get_rule_by_id(db, rule_id)
        if not rule:
            raise ValueError(f"事件驱动规则不存在: {rule_id}")
        
        # 执行采集任务
        result = execute_crawl_task.delay(
            rule.source_id, 
            {
                "event_id": event_id,
                "rule_id": rule_id,
                **(crawl_config or {})
            }
        )
        
        logger.info(f"事件驱动采集任务已提交: task_id={result.id}")
        return {"task_id": result.id, "status": "submitted"}
        
    except Exception as e:
        logger.error(f"执行事件驱动采集异常: {e}")
        return {"status": "error", "error": str(e)}
    finally:
        db.close()


@data_collection_task
def execute_batch_crawl(self, data_source_ids: List[int], batch_config: Optional[Dict[str, Any]] = None):
    """
    执行批量采集任务
    
    Args:
        data_source_ids: 数据源ID列表
        batch_config: 批量配置
        
    Returns:
        Dict: 执行结果
    """
    task_id = current_task.request.id
    logger.info(f"开始执行批量采集: data_source_ids={data_source_ids}, celery_task_id={task_id}")
    
    results = []
    for data_source_id in data_source_ids:
        try:
            result = execute_crawl_task.delay(data_source_id, batch_config)
            results.append({
                "data_source_id": data_source_id,
                "task_id": result.id,
                "status": "submitted"
            })
        except Exception as e:
            logger.error(f"提交批量采集任务失败: data_source_id={data_source_id}, 错误: {e}")
            results.append({
                "data_source_id": data_source_id,
                "status": "error",
                "error": str(e)
            })
    
    logger.info(f"批量采集任务提交完成，共 {len(results)} 个任务")
    return {"results": results, "total": len(results)}


@celery_app.task
def cleanup_old_crawl_tasks():
    """
    清理旧的采集任务记录
    """
    logger.info("开始清理旧的采集任务记录")
    
    db = SessionLocal()
    try:
        crawl_task_service = CrawlTaskService()
        
        # 清理30天前的已完成任务
        cutoff_date = datetime.now(timezone.utc) - timedelta(days=30)
        deleted_count = crawl_task_service.cleanup_old_tasks(db, cutoff_date)
        
        logger.info(f"清理完成，删除 {deleted_count} 条旧任务记录")
        return {"deleted_count": deleted_count}
        
    except Exception as e:
        logger.error(f"清理旧任务记录异常: {e}")
        return {"error": str(e)}
    finally:
        db.close()


@celery_app.task
def health_check_data_sources():
    """
    数据源健康检查任务
    """
    logger.info("开始数据源健康检查")
    
    db = SessionLocal()
    try:
        data_source_service = DataSourceService()
        
        # 获取所有活跃数据源
        data_sources, _ = data_source_service.get_data_sources(db, status="active", limit=1000)
        
        health_results = []
        for data_source in data_sources:
            try:
                # 检查数据源健康状态
                health_status = _check_data_source_health(data_source)
                health_results.append({
                    "data_source_id": data_source.id,
                    "name": data_source.name,
                    "status": health_status["status"],
                    "last_success": health_status.get("last_success"),
                    "error_count": health_status.get("error_count", 0)
                })
                
            except Exception as e:
                logger.error(f"检查数据源健康状态失败: {data_source.name}, 错误: {e}")
                health_results.append({
                    "data_source_id": data_source.id,
                    "name": data_source.name,
                    "status": "error",
                    "error": str(e)
                })
        
        logger.info(f"数据源健康检查完成，检查 {len(health_results)} 个数据源")
        return {"health_results": health_results, "total": len(health_results)}
        
    except Exception as e:
        logger.error(f"数据源健康检查异常: {e}")
        return {"error": str(e)}
    finally:
        db.close()


# 辅助函数
def _determine_task_type(data_source: DataSource) -> str:
    """确定任务类型"""
    if data_source.business_data_type:
        return data_source.business_data_type.value
    elif data_source.content_category:
        return data_source.content_category.value
    else:
        return "general_crawl"


def _execute_crawl_sync(data_source: DataSource, crawl_task: CrawlTask, task_config: Optional[Dict[str, Any]]) -> Dict[str, Any]:
    """
    同步执行采集任务
    """
    try:
        # 创建爬虫实例
        crawler = CrawlerFactory.create_crawler(data_source, crawl_task)
        
        # 使用asyncio运行异步爬虫
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            result = loop.run_until_complete(_run_crawler_async(crawler))
            return result
        finally:
            loop.close()
            
    except Exception as e:
        logger.error(f"执行采集异常: {e}")
        return {"success": False, "error": str(e)}


async def _run_crawler_async(crawler) -> Dict[str, Any]:
    """异步运行爬虫"""
    try:
        async with crawler:
            result = await crawler.crawl()
            # 从result.data中获取统计信息和MongoDB IDs
            statistics = result.data.get("statistics", {}) if result.data else {}
            mongo_ids = result.data.get("mongo_ids", []) if result.data else []
            return {
                "success": result.success,
                "items_count": statistics.get("items_success", 0),
                "message": result.message,
                "statistics": statistics,
                "mongo_ids": mongo_ids
            }
    except Exception as e:
        logger.error(f"爬虫执行异常: {e}")
        return {"success": False, "error": str(e)}


def _check_data_source_health(data_source: DataSource) -> Dict[str, Any]:
    """检查数据源健康状态"""
    # 这里可以实现具体的健康检查逻辑
    # 比如检查最近的采集成功率、错误次数等
    return {
        "status": "healthy" if data_source.status == "active" else "inactive",
        "last_success": data_source.last_crawl_time,
        "error_count": data_source.consecutive_error_count or 0
    }


def _create_raw_data_record_sync(crawl_task: CrawlTask, mongo_id: str, db: Session) -> bool:
    """
    异步创建原始数据记录

    Args:
        crawl_task: 采集任务
        mongo_id: MongoDB文档ID
        db: 数据库会话

    Returns:
        bool: True表示成功创建记录，False表示跳过创建（如重复等）
    """
    try:
        # 导入必要的模块
        from ...core.mongodb import mongodb_manager
        from .models import RawDataRecord
        import hashlib
        from datetime import datetime, timezone
        from urllib.parse import urlparse

        # 从MongoDB获取详细数据
        logger.debug(f"正在从MongoDB获取数据: {mongo_id}")
        # 使用同步方式获取MongoDB数据
        import asyncio
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            mongo_data = loop.run_until_complete(mongodb_manager.get_raw_content(mongo_id))
        finally:
            loop.close()

        if not mongo_data:
            logger.error(f"无法从MongoDB获取数据: {mongo_id}")
            return False

        # 检查MongoDB ID是否已存在（去重）
        existing_record = (
            db.query(RawDataRecord)
            .filter(RawDataRecord.mongodb_id == mongo_id)
            .first()
        )
        if existing_record:
            logger.info(f"MongoDB记录已存在，跳过创建: mongodb_id={mongo_id}")
            return False

        # 提取基本信息
        title = mongo_data.get("title", "")
        content = mongo_data.get("content", "")
        source_url = mongo_data.get("source_url", "")

        # 计算哈希值
        content_hash = hashlib.md5(content.encode('utf-8')).hexdigest() if content else None
        url_hash = hashlib.md5(source_url.encode('utf-8')).hexdigest() if source_url else None

        # 提取URL域名
        url_domain = None
        if source_url:
            try:
                parsed_url = urlparse(source_url)
                url_domain = parsed_url.netloc
            except Exception:
                pass

        # 计算内容长度
        content_length = len(content) if content else 0

        # 计算质量评分（简化版本）
        quality_score = 0.5  # 默认评分
        if title and content:
            quality_score = min(1.0, (len(title) + len(content)) / 1000)

        current_time = datetime.now(timezone.utc)

        # 创建原始数据记录
        record = RawDataRecord(
            task_id=crawl_task.id,
            source_id=crawl_task.source_id,
            # URL信息
            source_url=source_url,
            canonical_url=source_url,  # 简化处理
            url_hash=url_hash,
            url_domain=url_domain,
            # 内容标识
            content_hash=content_hash,
            content_simhash=None,  # 暂时不计算simhash
            content_length=content_length,
            content_encoding="utf-8",
            # 基础元数据
            title=title[:1000] if title else None,  # 限制长度
            author=mongo_data.get("author", "")[:200] if mongo_data.get("author") else None,
            publish_time=mongo_data.get("publish_time"),
            crawl_time=current_time,
            # MongoDB引用
            mongodb_id=mongo_id,
            mongodb_collection="raw_content",
            content_type=mongo_data.get("content_type", "financial_news"),
            # 处理状态
            processing_status="pending",
            processing_priority=5,
            quality_score=quality_score,
            # 数据生命周期管理
            retention_policy="standard",
            archive_after_days=365,
            delete_after_days=1095,
            is_archived=False,
            # 时间字段
            created_at=current_time,
            updated_at=current_time,
        )

        logger.info(f"准备创建新记录: task_id={crawl_task.id}, source_id={crawl_task.source_id}, mongo_id={mongo_id}")

        db.add(record)
        db.commit()
        logger.info(f"成功创建原始数据记录: mongodb_id={mongo_id}, content_hash={content_hash}")
        return True

    except Exception as e:
        db.rollback()
        logger.error(f"创建原始数据记录失败: mongo_id={mongo_id}, error={e}")
        raise
