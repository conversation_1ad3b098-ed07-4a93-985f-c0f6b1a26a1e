"""
数据采集服务B端管理API路由
定义数据源、数据源配置、原始数据记录等的管理HTTP接口
专注于数据采集功能
"""

from datetime import datetime
from typing import Any, Dict, List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session

from ...core.database import get_db
from ..permission_service.dependencies import require_permission
from ..user_service.dependencies import get_current_active_user
from ..user_service.models import User
from .dependencies import (
    get_data_source_config_service,
    get_data_source_service,
    get_raw_data_record_service,
    get_crawl_task_service,
    get_event_driven_crawl_rule_service,
    get_data_source_credential_service,
)
from .task_manager import data_collection_task_manager
from .schemas import (
    # 数据源相关
    DataSourceCreate,
    DataSourceListResponse,
    DataSourceResponse,
    DataSourceStatsResponse,
    DataSourceUpdate,
    # 数据源配置相关
    DataSourceConfigCreate,
    DataSourceConfigListResponse,
    DataSourceConfigResponse,
    DataSourceConfigUpdate,
    # 事件驱动规则相关
    EventDrivenCrawlRuleCreate,
    EventDrivenCrawlRuleListResponse,
    EventDrivenCrawlRuleResponse,
    EventDrivenCrawlRuleUpdate,
    # 采集任务相关
    CrawlTaskCreate,
    CrawlTaskListResponse,
    CrawlTaskResponse,
    CrawlTaskUpdate,
    TaskExecutionStatsResponse,
    # 数据源凭证相关
    DataSourceCredentialCreate,
    DataSourceCredentialListResponse,
    DataSourceCredentialResponse,
    DataSourceCredentialUpdate,
    # 原始数据记录相关
    RawDataRecordCreate,
    RawDataRecordListResponse,
    RawDataRecordResponse,
    RawDataRecordUpdate,
    # 批量操作相关
    BatchStatusUpdateRequest,
    BatchOperationResponse,
    DuplicateRecordsResponse,
)
from .service import (
    DataSourceConfigService,
    DataSourceService,
    RawDataRecordService,
    CrawlTaskService,
    EventDrivenCrawlRuleService,
    DataSourceCredentialService,
)

# 创建路由实例
router = APIRouter(prefix="/api/v1/admin/data-collection")

# === 新增：异步任务管理接口 ===

@router.post(
    "/tasks/crawl",
    summary="提交爬取任务",
    description="提交单个数据源的爬取任务，支持同步和异步执行模式"
)
async def submit_crawl_task(
    data_source_id: int,
    execution_mode: Optional[str] = Query("auto", description="执行模式: direct(直接), async(异步), auto(自动选择)"),
    max_pages: Optional[int] = Query(None, description="最大爬取页数"),
    estimated_duration_minutes: Optional[int] = Query(None, description="预估执行时间(分钟)"),
    priority: int = Query(5, description="任务优先级(1-10)"),
    current_user: User = Depends(require_permission("data_source.crawl_task.create")),
    db: Session = Depends(get_db),
):
    """提交爬取任务 - 支持智能执行模式选择"""
    try:
        # 构建配置参数
        config = {}
        if max_pages is not None:
            config["max_pages"] = max_pages
        if estimated_duration_minutes is not None:
            config["estimated_duration_minutes"] = estimated_duration_minutes
        
        # 确定执行模式
        if execution_mode == "auto":
            execution_mode = None  # 让系统自动选择
        
        # 提交任务
        result = await data_collection_task_manager.submit_crawl_task(
            data_source_id=data_source_id,
            config=config if config else None,
            execution_mode=execution_mode,
            priority=priority
        )
        
        return {
            "success": True,
            "message": "任务已提交",
            "data": result
        }
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"提交任务失败: {str(e)}")


@router.post(
    "/tasks/crawl/batch",
    summary="批量提交爬取任务",
    description="批量提交多个数据源的爬取任务（异步执行）"
)
async def submit_batch_crawl_tasks(
    data_source_ids: List[int],
    max_pages: Optional[int] = Query(None, description="最大爬取页数"),
    priority: int = Query(5, description="任务优先级(1-10)"),
    current_user: User = Depends(require_permission("data_source.crawl_task.create")),
    db: Session = Depends(get_db),
):
    """批量提交爬取任务"""
    try:
        # 构建配置参数
        config = {}
        if max_pages is not None:
            config["max_pages"] = max_pages
        
        # 提交批量任务
        result = await data_collection_task_manager.submit_batch_crawl_tasks(
            data_source_ids=data_source_ids,
            config=config if config else None,
            priority=priority
        )
        
        return {
            "success": True,
            "message": f"已提交 {result['batch_size']} 个爬取任务",
            "data": result
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"提交批量任务失败: {str(e)}")


@router.get(
    "/tasks/{task_id}/status",
    summary="获取任务状态",
    description="获取指定爬取任务的执行状态和详细信息"
)
async def get_task_status(
    task_id: int,
    current_user: User = Depends(require_permission("data_source.crawl_task.read")),
    db: Session = Depends(get_db),
):
    """获取任务状态"""
    try:
        status_info = await data_collection_task_manager.get_task_status(task_id)
        
        if not status_info:
            raise HTTPException(status_code=404, detail="任务不存在")
        
        return {
            "success": True,
            "data": status_info
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取任务状态失败: {str(e)}")


@router.post(
    "/tasks/{task_id}/cancel",
    summary="取消任务",
    description="取消指定的爬取任务"
)
async def cancel_task(
    task_id: int,
    current_user: User = Depends(require_permission("data_source.crawl_task.update")),
    db: Session = Depends(get_db),
):
    """取消任务"""
    try:
        success = await data_collection_task_manager.cancel_task(task_id)
        
        if not success:
            raise HTTPException(status_code=404, detail="任务不存在或无法取消")
        
        return {
            "success": True,
            "message": "任务已取消"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"取消任务失败: {str(e)}")


@router.get(
    "/tasks/stats",
    summary="获取任务统计",
    description="获取当前所有任务的统计信息"
)
async def get_task_stats(
    current_user: User = Depends(require_permission("data_source.crawl_task.read")),
    crawl_task_service: CrawlTaskService = Depends(get_crawl_task_service),
    db: Session = Depends(get_db),
):
    """获取任务统计信息"""
    try:
        # 获取任务统计
        stats = crawl_task_service.get_task_stats(db)
        
        return {
            "success": True,
            "data": stats
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取统计信息失败: {str(e)}")


# === 数据源管理 ===

@router.post(
    "/data-sources",
    response_model=DataSourceResponse,
    status_code=status.HTTP_201_CREATED,
    summary="创建数据源",
    description="创建新的数据源配置 (需要数据源创建权限)",
)
async def create_data_source_admin(
    data_source_data: DataSourceCreate,
    current_user: User = Depends(require_permission("data_source.data_source.create")),
    data_source_service: DataSourceService = Depends(get_data_source_service),
    db: Session = Depends(get_db),
):
    """创建数据源"""
    try:
        data_source = data_source_service.create_data_source(db, data_source_data)
        return data_source
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.get(
    "/data-sources",
    response_model=DataSourceListResponse,
    summary="获取数据源列表",
    description="获取数据源分页列表 (需要数据源读取权限)",
)
async def get_data_sources_admin(
    current_user: User = Depends(require_permission("data_source.list.read")),
    data_source_service: DataSourceService = Depends(get_data_source_service),
    db: Session = Depends(get_db),
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回的记录数"),
    status: Optional[str] = Query(None, description="状态过滤"),
    collection_method: Optional[str] = Query(None, description="采集方式过滤"),
    content_category: Optional[str] = Query(None, description="内容分类过滤"),
):
    """获取数据源列表"""
    data_sources, total = data_source_service.get_data_sources(
        db, skip, limit, status, collection_method, content_category
    )
    return DataSourceListResponse(
        items=data_sources,
        total=total,
        skip=skip,
        limit=limit,
    )


@router.get(
    "/data-sources/stats",
    response_model=DataSourceStatsResponse,
    summary="获取数据源统计信息",
    description="获取数据源统计信息 (需要数据源统计权限)",
)
async def get_data_source_stats_admin(
    current_user: User = Depends(require_permission("data_source.stats.read")),
    data_source_service: DataSourceService = Depends(get_data_source_service),
    db: Session = Depends(get_db),
):
    """获取数据源统计信息"""
    stats = data_source_service.get_data_source_stats(db)
    return DataSourceStatsResponse(**stats)


@router.get(
    "/data-sources/{data_source_id}",
    response_model=DataSourceResponse,
    summary="获取数据源详情",
    description="根据ID获取数据源详情 (需要数据源读取权限)",
)
async def get_data_source_admin(
    data_source_id: int,
    current_user: User = Depends(require_permission("data_source.data_source.read")),
    data_source_service: DataSourceService = Depends(get_data_source_service),
    db: Session = Depends(get_db),
):
    """获取数据源详情"""
    data_source = data_source_service.get_data_source_by_id(db, data_source_id)
    if not data_source:
        raise HTTPException(status_code=404, detail="Data source not found")
    return data_source


@router.put(
    "/data-sources/{data_source_id}",
    response_model=DataSourceResponse,
    summary="更新数据源",
    description="更新数据源配置 (需要数据源更新权限)",
)
async def update_data_source_admin(
    data_source_id: int,
    update_data: DataSourceUpdate,
    current_user: User = Depends(require_permission("data_source.data_source.update")),
    data_source_service: DataSourceService = Depends(get_data_source_service),
    db: Session = Depends(get_db),
):
    """更新数据源"""
    try:
        data_source = data_source_service.update_data_source(db, data_source_id, update_data)
        if not data_source:
            raise HTTPException(status_code=404, detail="Data source not found")
        return data_source
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.delete(
    "/data-sources/{data_source_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="删除数据源",
    description="删除数据源 (需要数据源删除权限)",
)
async def delete_data_source_admin(
    data_source_id: int,
    current_user: User = Depends(require_permission("data_source.data_source.delete")),
    data_source_service: DataSourceService = Depends(get_data_source_service),
    db: Session = Depends(get_db),
):
    """删除数据源"""
    try:
        success = data_source_service.delete_data_source(db, data_source_id)
        if not success:
            raise HTTPException(status_code=404, detail="Data source not found")
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))


# === 数据源配置管理 ===

@router.post(
    "/data-source/configs",
    response_model=DataSourceConfigResponse,
    status_code=status.HTTP_201_CREATED,
    summary="创建数据源配置",
    description="创建新的数据源配置 (需要数据源配置创建权限)",
)
async def create_data_source_config_admin(
    config_data: DataSourceConfigCreate,
    current_user: User = Depends(require_permission("data_source.config.create")),
    config_service: DataSourceConfigService = Depends(get_data_source_config_service),
    db: Session = Depends(get_db),
):
    """创建数据源配置"""
    try:
        config = config_service.create_config(db, config_data)
        return config
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.get(
    "/data-source/configs",
    response_model=DataSourceConfigListResponse,
    summary="获取数据源配置列表",
    description="获取数据源配置分页列表 (需要数据源配置读取权限)",
)
async def get_data_source_configs_admin(
    current_user: User = Depends(require_permission("data_source.config.read")),
    config_service: DataSourceConfigService = Depends(get_data_source_config_service),
    db: Session = Depends(get_db),
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回的记录数"),
    source_id: Optional[int] = Query(None, description="数据源ID过滤"),
    is_active: Optional[bool] = Query(None, description="是否活跃过滤"),
    is_validated: Optional[bool] = Query(None, description="是否已验证过滤"),
):
    """获取数据源配置列表"""
    configs, total = config_service.get_configs(
        db, skip, limit, source_id, is_active, is_validated
    )
    return DataSourceConfigListResponse(
        items=configs,
        total=total,
        skip=skip,
        limit=limit,
    )


@router.get(
    "/data-source/configs/{config_id}",
    response_model=DataSourceConfigResponse,
    summary="获取数据源配置详情",
    description="根据ID获取数据源配置详情 (需要数据源配置读取权限)",
)
async def get_data_source_config_admin(
    config_id: int,
    current_user: User = Depends(require_permission("data_source.config.read")),
    config_service: DataSourceConfigService = Depends(get_data_source_config_service),
    db: Session = Depends(get_db),
):
    """获取数据源配置详情"""
    config = config_service.get_config_by_id(db, config_id)
    if not config:
        raise HTTPException(status_code=404, detail="Data source config not found")
    return config


@router.put(
    "/data-source/configs/{config_id}",
    response_model=DataSourceConfigResponse,
    summary="更新数据源配置",
    description="更新数据源配置 (需要数据源配置更新权限)",
)
async def update_data_source_config_admin(
    config_id: int,
    update_data: DataSourceConfigUpdate,
    current_user: User = Depends(require_permission("data_source.config.update")),
    config_service: DataSourceConfigService = Depends(get_data_source_config_service),
    db: Session = Depends(get_db),
):
    """更新数据源配置"""
    try:
        config = config_service.update_config(db, config_id, update_data)
        if not config:
            raise HTTPException(status_code=404, detail="Data source config not found")
        return config
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.delete(
    "/data-source/configs/{config_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="删除数据源配置",
    description="删除数据源配置 (需要数据源配置删除权限)",
)
async def delete_data_source_config_admin(
    config_id: int,
    current_user: User = Depends(require_permission("data_source.config.delete")),
    config_service: DataSourceConfigService = Depends(get_data_source_config_service),
    db: Session = Depends(get_db),
):
    """删除数据源配置"""
    try:
        success = config_service.delete_config(db, config_id)
        if not success:
            raise HTTPException(status_code=404, detail="Data source config not found")
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.patch(
    "/data-source/configs/{config_id}/activate",
    response_model=DataSourceConfigResponse,
    summary="激活数据源配置",
    description="激活数据源配置版本 (需要数据源配置管理权限)",
)
async def activate_data_source_config_admin(
    config_id: int,
    current_user: User = Depends(require_permission("data_source.config.manage")),
    config_service: DataSourceConfigService = Depends(get_data_source_config_service),
    db: Session = Depends(get_db),
):
    """激活数据源配置"""
    try:
        config = config_service.activate_config(db, config_id)
        if not config:
            raise HTTPException(status_code=404, detail="Data source config not found")
        return config
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))


# === 事件驱动规则管理 ===

@router.post(
    "/event-rules",
    response_model=EventDrivenCrawlRuleResponse,
    status_code=status.HTTP_201_CREATED,
    summary="创建事件驱动规则",
    description="创建新的事件驱动采集规则 (需要事件规则创建权限)",
)
async def create_event_rule_admin(
    rule_data: EventDrivenCrawlRuleCreate,
    current_user: User = Depends(require_permission("event_rule.rule.create")),
    rule_service: EventDrivenCrawlRuleService = Depends(get_event_driven_crawl_rule_service),
    db: Session = Depends(get_db),
):
    """创建事件驱动规则"""
    try:
        rule = rule_service.create_rule(db, rule_data)
        return rule
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.get(
    "/event-rules",
    response_model=EventDrivenCrawlRuleListResponse,
    summary="获取事件驱动规则列表",
    description="获取事件驱动规则分页列表 (需要事件规则读取权限)",
)
async def get_event_rules_admin(
    current_user: User = Depends(require_permission("event_rule.list.read")),
    rule_service: EventDrivenCrawlRuleService = Depends(get_event_driven_crawl_rule_service),
    db: Session = Depends(get_db),
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回的记录数"),
    source_id: Optional[int] = Query(None, description="数据源ID过滤"),
    trigger_type: Optional[str] = Query(None, description="触发类型过滤"),
    is_active: Optional[bool] = Query(None, description="是否活跃过滤"),
):
    """获取事件驱动规则列表"""
    rules, total = rule_service.get_rules(
        db, skip, limit, source_id, trigger_type, is_active
    )
    return EventDrivenCrawlRuleListResponse(
        items=rules,
        total=total,
        skip=skip,
        limit=limit,
    )


@router.get(
    "/event-rules/{rule_id}",
    response_model=EventDrivenCrawlRuleResponse,
    summary="获取事件驱动规则详情",
    description="根据ID获取事件驱动规则详情 (需要事件规则读取权限)",
)
async def get_event_rule_admin(
    rule_id: int,
    current_user: User = Depends(require_permission("event_rule.rule.read")),
    rule_service: EventDrivenCrawlRuleService = Depends(get_event_driven_crawl_rule_service),
    db: Session = Depends(get_db),
):
    """获取事件驱动规则详情"""
    rule = rule_service.get_rule_by_id(db, rule_id)
    if not rule:
        raise HTTPException(status_code=404, detail="Event rule not found")
    return rule


@router.put(
    "/event-rules/{rule_id}",
    response_model=EventDrivenCrawlRuleResponse,
    summary="更新事件驱动规则",
    description="更新事件驱动规则 (需要事件规则更新权限)",
)
async def update_event_rule_admin(
    rule_id: int,
    update_data: EventDrivenCrawlRuleUpdate,
    current_user: User = Depends(require_permission("event_rule.rule.update")),
    rule_service: EventDrivenCrawlRuleService = Depends(get_event_driven_crawl_rule_service),
    db: Session = Depends(get_db),
):
    """更新事件驱动规则"""
    try:
        rule = rule_service.update_rule(db, rule_id, update_data)
        if not rule:
            raise HTTPException(status_code=404, detail="Event rule not found")
        return rule
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.delete(
    "/event-rules/{rule_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="删除事件驱动规则",
    description="删除事件驱动规则 (需要事件规则删除权限)",
)
async def delete_event_rule_admin(
    rule_id: int,
    current_user: User = Depends(require_permission("event_rule.rule.delete")),
    rule_service: EventDrivenCrawlRuleService = Depends(get_event_driven_crawl_rule_service),
    db: Session = Depends(get_db),
):
    """删除事件驱动规则"""
    try:
        success = rule_service.delete_rule(db, rule_id)
        if not success:
            raise HTTPException(status_code=404, detail="Event rule not found")
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))


# === 采集任务管理 ===

@router.post(
    "/crawl-tasks",
    response_model=CrawlTaskResponse,
    status_code=status.HTTP_201_CREATED,
    summary="创建采集任务",
    description="创建新的采集任务 (需要采集任务创建权限)",
)
async def create_crawl_task_admin(
    task_data: CrawlTaskCreate,
    current_user: User = Depends(require_permission("crawl_task.task.create")),
    task_service: CrawlTaskService = Depends(get_crawl_task_service),
    db: Session = Depends(get_db),
):
    """创建采集任务"""
    try:
        task = task_service.create_task(db, task_data)
        return task
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.get(
    "/crawl-tasks",
    response_model=CrawlTaskListResponse,
    summary="获取采集任务列表",
    description="获取采集任务分页列表 (需要采集任务读取权限)",
)
async def get_crawl_tasks_admin(
    current_user: User = Depends(require_permission("crawl_task.list.read")),
    task_service: CrawlTaskService = Depends(get_crawl_task_service),
    db: Session = Depends(get_db),
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回的记录数"),
    source_id: Optional[int] = Query(None, description="数据源ID过滤"),
    status: Optional[str] = Query(None, description="状态过滤"),
    task_type: Optional[str] = Query(None, description="任务类型过滤"),
    trigger_type: Optional[str] = Query(None, description="触发类型过滤"),
):
    """获取采集任务列表"""
    tasks, total = task_service.get_tasks(
        db, skip, limit, source_id, status, task_type, trigger_type
    )
    return CrawlTaskListResponse(
        items=tasks,
        total=total,
        skip=skip,
        limit=limit,
    )


@router.get(
    "/crawl-tasks/stats",
    response_model=TaskExecutionStatsResponse,
    summary="获取任务执行统计",
    description="获取采集任务执行统计信息 (需要任务统计权限)",
)
async def get_crawl_task_stats_admin(
    current_user: User = Depends(require_permission("crawl_task.stats.read")),
    task_service: CrawlTaskService = Depends(get_crawl_task_service),
    db: Session = Depends(get_db),
):
    """获取任务执行统计"""
    stats = task_service.get_task_stats(db)
    return TaskExecutionStatsResponse(**stats)


@router.get(
    "/crawl-tasks/{task_id}",
    response_model=CrawlTaskResponse,
    summary="获取采集任务详情",
    description="根据ID获取采集任务详情 (需要采集任务读取权限)",
)
async def get_crawl_task_admin(
    task_id: int,
    current_user: User = Depends(require_permission("crawl_task.task.read")),
    task_service: CrawlTaskService = Depends(get_crawl_task_service),
    db: Session = Depends(get_db),
):
    """获取采集任务详情"""
    task = task_service.get_task_by_id(db, task_id)
    if not task:
        raise HTTPException(status_code=404, detail="Crawl task not found")
    return task


@router.put(
    "/crawl-tasks/{task_id}",
    response_model=CrawlTaskResponse,
    summary="更新采集任务",
    description="更新采集任务 (需要采集任务更新权限)",
)
async def update_crawl_task_admin(
    task_id: int,
    update_data: CrawlTaskUpdate,
    current_user: User = Depends(require_permission("crawl_task.task.update")),
    task_service: CrawlTaskService = Depends(get_crawl_task_service),
    db: Session = Depends(get_db),
):
    """更新采集任务"""
    try:
        task = task_service.update_task(db, task_id, update_data)
        if not task:
            raise HTTPException(status_code=404, detail="Crawl task not found")
        return task
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.delete(
    "/crawl-tasks/{task_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="删除采集任务",
    description="删除采集任务 (需要采集任务删除权限)",
)
async def delete_crawl_task_admin(
    task_id: int,
    current_user: User = Depends(require_permission("crawl_task.task.delete")),
    task_service: CrawlTaskService = Depends(get_crawl_task_service),
    db: Session = Depends(get_db),
):
    """删除采集任务"""
    try:
        success = task_service.delete_task(db, task_id)
        if not success:
            raise HTTPException(status_code=404, detail="Crawl task not found")
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))


# === 数据源凭证管理 ===

@router.post(
    "/data-source/credentials",
    response_model=DataSourceCredentialResponse,
    status_code=status.HTTP_201_CREATED,
    summary="创建数据源凭证",
    description="创建新的数据源凭证 (需要凭证创建权限)",
)
async def create_data_source_credential_admin(
    credential_data: DataSourceCredentialCreate,
    current_user: User = Depends(require_permission("data_source.credential.create")),
    credential_service: DataSourceCredentialService = Depends(get_data_source_credential_service),
    db: Session = Depends(get_db),
):
    """创建数据源凭证"""
    try:
        credential = credential_service.create_credential(db, credential_data)
        return credential
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.get(
    "/data-source/credentials",
    response_model=DataSourceCredentialListResponse,
    summary="获取数据源凭证列表",
    description="获取数据源凭证分页列表 (需要凭证读取权限)",
)
async def get_data_source_credentials_admin(
    current_user: User = Depends(require_permission("data_source.credential.read")),
    credential_service: DataSourceCredentialService = Depends(get_data_source_credential_service),
    db: Session = Depends(get_db),
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回的记录数"),
    source_id: Optional[int] = Query(None, description="数据源ID过滤"),
    credential_type: Optional[str] = Query(None, description="凭证类型过滤"),
    is_active: Optional[bool] = Query(None, description="是否活跃过滤"),
):
    """获取数据源凭证列表"""
    credentials, total = credential_service.get_credentials(
        db, skip, limit, source_id, credential_type, is_active
    )
    return DataSourceCredentialListResponse(
        items=credentials,
        total=total,
        skip=skip,
        limit=limit,
    )


@router.get(
    "/data-source/credentials/{credential_id}",
    response_model=DataSourceCredentialResponse,
    summary="获取数据源凭证详情",
    description="根据ID获取数据源凭证详情 (需要凭证读取权限)",
)
async def get_data_source_credential_admin(
    credential_id: int,
    current_user: User = Depends(require_permission("data_source.credential.read")),
    credential_service: DataSourceCredentialService = Depends(get_data_source_credential_service),
    db: Session = Depends(get_db),
):
    """获取数据源凭证详情"""
    credential = credential_service.get_credential_by_id(db, credential_id)
    if not credential:
        raise HTTPException(status_code=404, detail="Data source credential not found")
    return credential


@router.put(
    "/data-source/credentials/{credential_id}",
    response_model=DataSourceCredentialResponse,
    summary="更新数据源凭证",
    description="更新数据源凭证 (需要凭证更新权限)",
)
async def update_data_source_credential_admin(
    credential_id: int,
    update_data: DataSourceCredentialUpdate,
    current_user: User = Depends(require_permission("data_source.credential.update")),
    credential_service: DataSourceCredentialService = Depends(get_data_source_credential_service),
    db: Session = Depends(get_db),
):
    """更新数据源凭证"""
    try:
        credential = credential_service.update_credential(db, credential_id, update_data)
        if not credential:
            raise HTTPException(status_code=404, detail="Data source credential not found")
        return credential
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.delete(
    "/data-source/credentials/{credential_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="删除数据源凭证",
    description="删除数据源凭证 (需要凭证删除权限)",
)
async def delete_data_source_credential_admin(
    credential_id: int,
    current_user: User = Depends(require_permission("data_source.credential.delete")),
    credential_service: DataSourceCredentialService = Depends(get_data_source_credential_service),
    db: Session = Depends(get_db),
):
    """删除数据源凭证"""
    try:
        success = credential_service.delete_credential(db, credential_id)
        if not success:
            raise HTTPException(status_code=404, detail="Data source credential not found")
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))


# === 原始数据记录管理 ===

@router.post(
    "/raw-data-records",
    response_model=RawDataRecordResponse,
    status_code=status.HTTP_201_CREATED,
    summary="创建原始数据记录",
    description="创建新的原始数据记录 (需要原始数据记录创建权限)",
)
async def create_raw_data_record_admin(
    record_data: RawDataRecordCreate,
    current_user: User = Depends(require_permission("raw_data_record.raw_data_record.create")),
    record_service: RawDataRecordService = Depends(get_raw_data_record_service),
    db: Session = Depends(get_db),
):
    """创建原始数据记录"""
    try:
        record = record_service.create_raw_data_record(db, record_data)
        return record
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.get(
    "/raw-data-records",
    response_model=RawDataRecordListResponse,
    summary="获取原始数据记录列表",
    description="获取原始数据记录分页列表 (需要原始数据记录读取权限)",
)
async def get_raw_data_records_admin(
    current_user: User = Depends(require_permission("raw_data_record.list.read")),
    record_service: RawDataRecordService = Depends(get_raw_data_record_service),
    db: Session = Depends(get_db),
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回的记录数"),
    source_id: Optional[int] = Query(None, description="数据源ID过滤"),
    task_id: Optional[int] = Query(None, description="任务ID过滤"),
    processing_status: Optional[str] = Query(None, description="处理状态过滤"),
    is_archived: Optional[bool] = Query(None, description="是否归档过滤"),
    start_date: Optional[datetime] = Query(None, description="开始日期过滤"),
    end_date: Optional[datetime] = Query(None, description="结束日期过滤"),
):
    """获取原始数据记录列表"""
    records, total = record_service.get_raw_data_records(
        db, skip, limit, source_id, task_id, processing_status, 
        is_archived, start_date, end_date
    )
    return RawDataRecordListResponse(
        items=records,
        total=total,
        skip=skip,
        limit=limit,
    )


@router.get(
    "/raw-data-records/stats",
    response_model=Dict[str, Any],
    summary="获取原始数据记录统计信息",
    description="获取原始数据记录统计信息 (需要原始数据记录统计权限)",
)
async def get_raw_data_record_stats_admin(
    current_user: User = Depends(require_permission("raw_data_record.stats.read")),
    record_service: RawDataRecordService = Depends(get_raw_data_record_service),
    db: Session = Depends(get_db),
):
    """获取原始数据记录统计信息"""
    stats = record_service.get_raw_data_record_stats(db)
    return stats


@router.get(
    "/raw-data-records/{record_id}",
    response_model=RawDataRecordResponse,
    summary="获取原始数据记录详情",
    description="根据ID获取原始数据记录详情 (需要原始数据记录读取权限)",
)
async def get_raw_data_record_admin(
    record_id: int,
    current_user: User = Depends(require_permission("raw_data_record.raw_data_record.read")),
    record_service: RawDataRecordService = Depends(get_raw_data_record_service),
    db: Session = Depends(get_db),
):
    """获取原始数据记录详情"""
    record = record_service.get_raw_data_record_by_id(db, record_id)
    if not record:
        raise HTTPException(status_code=404, detail="Raw data record not found")
    return record


@router.put(
    "/raw-data-records/{record_id}",
    response_model=RawDataRecordResponse,
    summary="更新原始数据记录",
    description="更新原始数据记录 (需要原始数据记录更新权限)",
)
async def update_raw_data_record_admin(
    record_id: int,
    update_data: RawDataRecordUpdate,
    current_user: User = Depends(require_permission("raw_data_record.raw_data_record.update")),
    record_service: RawDataRecordService = Depends(get_raw_data_record_service),
    db: Session = Depends(get_db),
):
    """更新原始数据记录"""
    try:
        record = record_service.update_raw_data_record(db, record_id, update_data)
        if not record:
            raise HTTPException(status_code=404, detail="Raw data record not found")
        return record
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.delete(
    "/raw-data-records/{record_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="删除原始数据记录",
    description="删除原始数据记录 (需要原始数据记录删除权限)",
)
async def delete_raw_data_record_admin(
    record_id: int,
    current_user: User = Depends(require_permission("raw_data_record.raw_data_record.delete")),
    record_service: RawDataRecordService = Depends(get_raw_data_record_service),
    db: Session = Depends(get_db),
):
    """删除原始数据记录"""
    try:
        success = record_service.delete_raw_data_record(db, record_id)
        if not success:
            raise HTTPException(status_code=404, detail="Raw data record not found")
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.patch(
    "/raw-data-records/{record_id}/archive",
    response_model=RawDataRecordResponse,
    summary="归档原始数据记录",
    description="归档原始数据记录 (需要原始数据记录管理权限)",
)
async def archive_raw_data_record_admin(
    record_id: int,
    current_user: User = Depends(require_permission("raw_data_record.raw_data_record.manage")),
    record_service: RawDataRecordService = Depends(get_raw_data_record_service),
    db: Session = Depends(get_db),
):
    """归档原始数据记录"""
    try:
        success = record_service.archive_raw_data_record(db, record_id)
        if not success:
            raise HTTPException(status_code=404, detail="Raw data record not found")
        
        # 返回更新后的记录
        record = record_service.get_raw_data_record_by_id(db, record_id)
        return record
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.post(
    "/raw-data-records/batch-update-status",
    response_model=BatchOperationResponse,
    summary="批量更新处理状态",
    description="批量更新原始数据记录的处理状态 (需要原始数据记录批量管理权限)",
)
async def batch_update_processing_status_admin(
    request: BatchStatusUpdateRequest,
    current_user: User = Depends(require_permission("raw_data_record.batch.manage")),
    record_service: RawDataRecordService = Depends(get_raw_data_record_service),
    db: Session = Depends(get_db),
):
    """批量更新处理状态"""
    try:
        affected_count = record_service.batch_update_processing_status(
            db, request.record_ids, request.new_status
        )
        return BatchOperationResponse(
            success=True,
            affected_count=affected_count,
            message=f"Successfully updated {affected_count} records to status '{request.new_status}'"
        )
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.get(
    "/raw-data-records/duplicates",
    response_model=DuplicateRecordsResponse,
    summary="获取重复记录",
    description="获取重复的原始数据记录 (需要原始数据记录分析权限)",
)
async def get_duplicate_records_admin(
    current_user: User = Depends(require_permission("raw_data_record.raw_data_record.analyze")),
    record_service: RawDataRecordService = Depends(get_raw_data_record_service),
    db: Session = Depends(get_db),
    limit: int = Query(100, ge=1, le=1000, description="限制记录数"),
):
    """获取重复记录"""
    duplicates = record_service.get_duplicate_records(db, limit)
    return DuplicateRecordsResponse(
        duplicates=duplicates,
        total_groups=len(duplicates)
    ) 