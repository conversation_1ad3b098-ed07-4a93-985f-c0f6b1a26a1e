"""
数据采集服务数据模型
定义数据采集相关的数据库模型
根据最新架构设计，专注于数据采集功能
"""

from datetime import datetime, timezone
from typing import List

from sqlalchemy import (
    ARRAY,
    DECIMAL,
    TIMESTAMP,
    BigInteger,
    Boolean,
    Column,
    DateTime,
    ForeignKey,
    Index,
    Integer,
    String,
    Text,
    Time,
    Enum as SQLEnum,
)
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from enum import Enum

from ...core.database import Base


class BusinessDataType(str, Enum):
    """业务数据类型枚举"""

    FLASH_NEWS = "flash_news"  # 快讯
    NEWS_ARTICLE = "news_article"  # 新闻文章
    RESEARCH_REPORT = "research_report"  # 研究报告
    ECONOMIC_DATA = "economic_data"  # 经济数据
    COMPANY_ANNOUNCEMENT = "company_announcement"  # 公司公告
    SOCIAL_SENTIMENT = "social_sentiment"  # 社交舆情


class DataSource(Base):
    """
    数据源表
    管理不同的数据采集源配置
    """

    __tablename__ = "data_sources"

    id = Column(
        BigInteger, primary_key=True, index=True, comment="数据源唯一标识符"
    )
    name = Column(
        String(100),
        unique=True,
        nullable=False,
        comment="数据源名称",
    )

    # 技术维度分类
    collection_method = Column(
        String(30),
        nullable=False,
        comment="数据采集方式：api_json JSON接口/web_scraping网页解析/api_xml XML接口/api_rss RSS订阅/web_dynamic动态网页/file_upload文件上传",
    )

    # 业务维度分类
    content_category = Column(
        String(30),
        nullable=False,
        comment="内容分类：financial_news/official_data/research_report/social_media/regulatory_filing",
    )

    # 业务数据类型
    business_data_type = Column(
        SQLEnum(BusinessDataType, values_callable=lambda obj: [e.value for e in obj]),
        nullable=False,
        default=BusinessDataType.NEWS_ARTICLE,
        comment="业务数据类型",
    )

    base_url = Column(String(1000), comment="数据源基础URL地址，用于构建完整的采集链接")
    description = Column(Text, comment="数据源描述，详细说明数据源的特点和数据类型")

    # 采集模式配置
    crawl_mode = Column(
        String(20),
        default="interval",
        comment="采集模式：interval定时采集/event_driven事件驱动/hybrid混合模式",
    )
    crawl_interval = Column(
        Integer,
        default=3600,
        comment="采集间隔时间，单位秒，仅在interval或hybrid模式下有效",
    )
    priority = Column(
        Integer, default=5, comment="采集优先级，1-10，数值越大优先级越高"
    )
    max_concurrent_tasks = Column(
        Integer, default=1, comment="最大并发任务数，控制同时进行的采集任务数量"
    )

    # 事件驱动配置
    event_driven_config = Column(
        JSONB, default=dict, comment="事件驱动配置，存储事件触发条件和参数"
    )
    supports_realtime = Column(
        Boolean,
        default=False,
        comment="是否支持实时采集，true表示可在事件发生时立即采集",
    )

    # 反爬虫配置
    use_proxy = Column(
        Boolean, default=False, comment="是否使用代理，true表示需要通过代理进行采集"
    )
    proxy_pool = Column(String(50), comment="代理池标识符，指定使用的代理池名称")
    request_delay_min = Column(
        Integer, default=2, comment="请求最小延迟时间，单位秒，防止频率过高被封"
    )
    request_delay_max = Column(
        Integer, default=10, comment="请求最大延迟时间，单位秒，随机延迟在min和max之间"
    )

    # 状态管理
    status = Column(
        String(20),
        default="active",
        comment="状态：active/inactive/disabled/maintenance",
    )
    health_score = Column(
        DECIMAL(3, 2),
        default=1.00,
        comment="健康评分，取值范围0-1，反映数据源的可用性和稳定性",
    )
    last_health_check = Column(
        TIMESTAMP, comment="最后健康检查时间，记录最近一次检查数据源可用性的时间"
    )

    # 时间信息
    last_crawl_time = Column(
        TIMESTAMP, comment="最后采集时间，记录最近一次成功采集的时间戳"
    )
    last_success_time = Column(
        TIMESTAMP, comment="最后成功时间，记录最近一次采集成功的时间戳"
    )
    next_crawl_time = Column(
        TIMESTAMP, comment="下次采集时间，计划下次采集的时间戳，仅在interval模式下有效"
    )

    # 错误管理
    error_count = Column(Integer, default=0, comment="总错误次数，累计发生的错误次数")
    consecutive_error_count = Column(
        Integer, default=0, comment="连续错误次数，连续失败的采集次数"
    )
    max_consecutive_errors = Column(
        Integer, default=10, comment="最大连续错误次数，超过此数量将暂停采集"
    )

    # 统计信息
    total_crawled_count = Column(
        BigInteger, default=0, comment="总采集次数，累计执行的采集任务数量"
    )
    total_success_count = Column(
        BigInteger, default=0, comment="总成功次数，累计成功的采集任务数量"
    )
    avg_response_time_ms = Column(
        Integer, comment="平均响应时间，单位毫秒，用于评估数据源性能"
    )

    current_config_version = Column(
        Integer, default=1, comment="当前配置版本号"
    )

    created_at = Column(
        TIMESTAMP, server_default=func.now(), comment="创建时间"
    )
    updated_at = Column(
        TIMESTAMP,
        server_default=func.now(),
        onupdate=func.now(),
        comment="更新时间",
    )
    created_by = Column(String(100), comment="创建者")
    tags = Column(
        JSONB,
        default=list,
        comment="标签数组，存储为JSON格式",
    )

    # 关系定义
    configs = relationship(
        "DataSourceConfig", back_populates="data_source", cascade="all, delete-orphan"
    )
    credentials = relationship(
        "DataSourceCredential",
        back_populates="data_source",
        cascade="all, delete-orphan",
    )
    crawl_tasks = relationship(
        "CrawlTask", back_populates="data_source", cascade="all, delete-orphan"
    )
    event_rules = relationship(
        "EventDrivenCrawlRule",
        back_populates="data_source",
        cascade="all, delete-orphan",
    )
    raw_data_records = relationship(
        "RawDataRecord",
        back_populates="data_source",
        cascade="all, delete-orphan",
    )

    # 索引定义
    __table_args__ = (
        Index("idx_data_sources_status_health", "status", "health_score"),
        Index("idx_data_sources_method_category", "collection_method", "content_category", "status"),
        Index("idx_data_sources_crawl_time", "next_crawl_time"),
        Index("idx_data_sources_business_type", "business_data_type"),
    )


class DataSourceConfig(Base):
    """
    数据源配置表
    存储数据源的版本化配置信息
    """

    __tablename__ = "data_source_configs"

    id = Column(
        BigInteger, primary_key=True, index=True, comment="配置记录唯一标识符"
    )
    source_id = Column(
        BigInteger,
        ForeignKey("data_sources.id", ondelete="CASCADE"),
        nullable=False,
        comment="数据源ID",
    )
    version = Column(
        Integer, nullable=False, comment="配置版本号"
    )

    # 数据获取技术配置
    selector_config = Column(
        JSONB, default=dict, comment="CSS选择器、XPath等数据定位配置"
    )
    headers_config = Column(
        JSONB, default=dict, comment="请求头配置"
    )
    cookies_config = Column(
        JSONB, default=dict, comment="Cookie配置"
    )
    request_params_config = Column(
        JSONB, default=dict, comment="请求参数配置"
    )

    # 反爬虫和网络请求技术配置
    javascript_config = Column(
        JSONB, default=dict, comment="JavaScript执行配置"
    )
    anti_crawler_config = Column(
        JSONB, default=dict, comment="反爬虫配置"
    )
    retry_config = Column(
        JSONB, default=dict, comment="重试配置"
    )
    proxy_config = Column(
        JSONB, default=dict, comment="代理配置"
    )

    is_active = Column(
        Boolean, default=True, comment="是否启用该配置版本"
    )
    is_validated = Column(
        Boolean, default=False, comment="是否已验证"
    )
    validation_result = Column(
        JSONB, comment="验证结果"
    )

    change_reason = Column(Text, comment="变更原因")
    changed_by = Column(String(100), comment="变更人")
    created_at = Column(
        TIMESTAMP, server_default=func.now(), comment="创建时间"
    )

    # 关系定义
    data_source = relationship("DataSource", back_populates="configs")

    # 索引定义
    __table_args__ = (
        Index("idx_source_config_unique", "source_id", "version", unique=True),
        Index("idx_source_config_active", "source_id", "is_active"),
    )


class EventDrivenCrawlRule(Base):
    """
    事件驱动采集规则表
    定义基于财经事件触发的采集规则
    """

    __tablename__ = "event_driven_crawl_rules"

    id = Column(
        BigInteger,
        primary_key=True,
        index=True,
        comment="事件驱动采集规则唯一标识符",
    )
    source_id = Column(
        BigInteger,
        ForeignKey("data_sources.id", ondelete="CASCADE"),
        nullable=False,
        comment="关联的数据源ID",
    )
    rule_name = Column(
        String(100), nullable=False, comment="规则名称"
    )

    # 触发条件
    trigger_type = Column(
        String(50),
        nullable=False,
        comment="触发类型：financial_event/time_based/external_signal",
    )
    trigger_config = Column(
        JSONB, nullable=False, comment="触发配置"
    )

    # 时间配置
    advance_minutes = Column(
        Integer, default=0, comment="提前采集分钟数"
    )
    delay_minutes = Column(
        Integer, default=0, comment="延后采集分钟数"
    )
    repeat_interval_minutes = Column(
        Integer, comment="重复采集间隔（分钟）"
    )
    max_repeat_count = Column(
        Integer, default=1, comment="最大重复次数"
    )

    # 采集配置
    custom_task_config = Column(
        JSONB, default=dict, comment="自定义任务配置"
    )
    priority_boost = Column(
        Integer, default=0, comment="优先级提升"
    )

    # 状态管理
    is_active = Column(
        Boolean, default=True, comment="是否启用该规则"
    )
    last_triggered_at = Column(
        TIMESTAMP, comment="最后触发时间"
    )
    trigger_count = Column(Integer, default=0, comment="触发次数")
    success_count = Column(
        Integer, default=0, comment="成功次数"
    )

    created_at = Column(
        TIMESTAMP, server_default=func.now(), comment="创建时间"
    )
    updated_at = Column(
        TIMESTAMP,
        server_default=func.now(),
        onupdate=func.now(),
        comment="更新时间",
    )

    # 关系定义
    data_source = relationship("DataSource", back_populates="event_rules")

    # 索引定义
    __table_args__ = (
        Index("idx_event_rule_source_name", "source_id", "rule_name", unique=True),
        Index("idx_event_rule_trigger_type", "trigger_type", "is_active"),
        Index("idx_event_rule_last_triggered", "last_triggered_at"),
    )


class CrawlTask(Base):
    """
    采集任务表
    记录具体的数据采集任务执行情况
    """

    __tablename__ = "crawl_tasks"

    id = Column(
        BigInteger, primary_key=True, index=True, comment="采集任务唯一标识符"
    )
    source_id = Column(
        BigInteger,
        ForeignKey("data_sources.id", ondelete="CASCADE"),
        nullable=False,
        comment="关联的数据源ID",
    )
    task_type = Column(
        String(50),
        nullable=False,
        comment="任务类型：news_list/flash_news/research_report",
    )

    # 任务来源
    trigger_type = Column(
        String(20),
        default="interval",
        comment="触发类型：interval/event/manual",
    )
    related_event_id = Column(
        BigInteger,
        comment="关联的财经事件ID",
    )
    trigger_rule_id = Column(
        BigInteger,
        ForeignKey("event_driven_crawl_rules.id"),
        comment="触发规则ID",
    )

    target_url = Column(String(1000), comment="目标URL")
    task_config = Column(
        JSONB, default=dict, comment="任务配置"
    )

    scheduled_time = Column(TIMESTAMP, comment="计划开始时间")
    started_time = Column(TIMESTAMP, comment="实际开始时间")
    completed_time = Column(TIMESTAMP, comment="完成时间")

    status = Column(
        String(20),
        default="pending",
        comment="任务状态：pending/running/completed/failed/cancelled",
    )
    progress = Column(
        Integer, default=0, comment="执行进度（0-100）"
    )

    worker_id = Column(String(100), comment="工作线程ID")
    execution_node = Column(
        String(100), comment="执行节点"
    )

    items_found = Column(
        Integer, default=0, comment="发现项目数"
    )
    items_processed = Column(
        Integer, default=0, comment="已处理项目数"
    )
    items_success = Column(
        Integer, default=0, comment="成功项目数"
    )
    items_failed = Column(
        Integer, default=0, comment="失败项目数"
    )

    error_message = Column(Text, comment="错误信息")
    retry_count = Column(Integer, default=0, comment="重试次数")
    max_retry_count = Column(
        Integer, default=3, comment="最大重试次数"
    )
    next_retry_time = Column(
        TIMESTAMP, comment="下次重试时间"
    )

    duration_seconds = Column(Integer, comment="执行耗时（秒）")
    memory_usage_mb = Column(
        Integer, comment="内存使用量（MB）"
    )
    network_requests = Column(
        Integer, default=0, comment="网络请求数"
    )

    created_at = Column(
        TIMESTAMP, server_default=func.now(), comment="创建时间"
    )
    updated_at = Column(
        TIMESTAMP,
        server_default=func.now(),
        onupdate=func.now(),
        comment="更新时间",
    )

    # 关系定义
    data_source = relationship("DataSource", back_populates="crawl_tasks")
    trigger_rule = relationship("EventDrivenCrawlRule")
    raw_data_records = relationship(
        "RawDataRecord", 
        back_populates="crawl_task",
        cascade="all, delete-orphan"
    )

    # 索引定义
    __table_args__ = (
        Index("idx_crawl_tasks_status_scheduled", "status", "scheduled_time"),
        Index("idx_crawl_tasks_source_status", "source_id", "status", "created_at"),
        Index("idx_crawl_tasks_trigger_type", "trigger_type", "status"),
        Index("idx_crawl_tasks_worker", "worker_id", "status"),
    )


class DataSourceCredential(Base):
    """
    数据源凭证表
    存储数据源访问所需的认证凭据
    """

    __tablename__ = "data_source_credentials"

    id = Column(
        BigInteger, primary_key=True, index=True, comment="凭证记录唯一标识符"
    )
    source_id = Column(
        BigInteger,
        ForeignKey("data_sources.id", ondelete="CASCADE"),
        nullable=False,
        comment="数据源ID",
    )
    credential_type = Column(
        String(50),
        nullable=False,
        comment="凭证类型：api_key/username_password/oauth_token/certificate",
    )

    encrypted_data = Column(
        Text,
        nullable=False,
        comment="加密后的凭证数据",
    )
    encryption_method = Column(
        String(50), default="AES-256-GCM", comment="加密方法"
    )
    salt = Column(String, comment="加密盐值")

    is_active = Column(
        Boolean, default=True, comment="是否启用该凭证"
    )
    expires_at = Column(TIMESTAMP, comment="凭证过期时间")
    last_validated = Column(
        TIMESTAMP, comment="最后验证时间"
    )
    validation_status = Column(
        String(20),
        default="unknown",
        comment="验证状态：valid/invalid/expired/unknown",
    )

    created_at = Column(
        TIMESTAMP, server_default=func.now(), comment="创建时间"
    )
    updated_at = Column(
        TIMESTAMP,
        server_default=func.now(),
        onupdate=func.now(),
        comment="更新时间",
    )

    # 关系定义
    data_source = relationship("DataSource", back_populates="credentials")

    # 索引定义
    __table_args__ = (
        Index("idx_credential_source_type", "source_id", "credential_type", unique=True),
        Index("idx_credential_validation_status", "validation_status", "is_active"),
        Index("idx_credential_expires", "expires_at"),
    )


class RawDataRecord(Base):
    """
    原始数据记录表
    存储爬虫采集到的原始数据元数据
    """

    __tablename__ = "raw_data_records"

    id = Column(
        BigInteger,
        primary_key=True,
        index=True,
        comment="原始数据记录唯一标识符",
    )
    task_id = Column(
        BigInteger,
        ForeignKey("crawl_tasks.id", ondelete="SET NULL"),
        comment="关联采集任务ID",
    )
    source_id = Column(
        BigInteger,
        ForeignKey("data_sources.id", ondelete="CASCADE"),
        nullable=False,
        comment="数据源ID",
    )

    # URL信息
    source_url = Column(
        String(1000), nullable=False, comment="数据来源URL"
    )
    canonical_url = Column(String(1000), comment="规范化URL")
    url_hash = Column(
        String(64),
        nullable=False,
        index=True,
        comment="URL哈希值",
    )
    url_domain = Column(String(200), comment="URL域名")

    # 内容标识
    content_hash = Column(
        String(64), index=True, comment="内容哈希值"
    )
    content_simhash = Column(
        BigInteger, comment="内容相似性哈希"
    )
    content_length = Column(Integer, comment="内容长度")
    content_encoding = Column(
        String(20), default="utf-8", comment="内容编码格式"
    )

    # 基础元数据
    title = Column(String(1000), comment="标题")
    author = Column(String(200), comment="作者")
    publish_time = Column(TIMESTAMP, comment="发布时间")
    crawl_time = Column(
        TIMESTAMP,
        server_default=func.now(),
        comment="采集时间",
    )

    # MongoDB引用
    mongodb_id = Column(
        String(24), comment="MongoDB文档ID"
    )
    mongodb_collection = Column(
        String(50),
        default="raw_content",
        comment="MongoDB集合名",
    )
    content_type = Column(
        String(100), comment="内容类型"
    )

    # 处理状态
    processing_status = Column(
        String(20),
        default="pending",
        comment="处理状态：pending/processing/processed/failed",
    )
    processing_priority = Column(
        Integer, default=5, comment="处理优先级"
    )
    quality_score = Column(
        DECIMAL(3, 2), comment="质量评分"
    )

    # 数据生命周期管理
    retention_policy = Column(
        String(20),
        default="standard",
        comment="保留策略",
    )
    archive_after_days = Column(
        Integer, default=365, comment="归档天数"
    )
    delete_after_days = Column(
        Integer, default=1095, comment="删除天数"
    )
    is_archived = Column(
        Boolean, default=False, comment="是否已归档"
    )
    archived_at = Column(TIMESTAMP, comment="归档时间")

    created_at = Column(
        TIMESTAMP,
        server_default=func.now(),
        comment="创建时间",
    )
    updated_at = Column(
        TIMESTAMP,
        server_default=func.now(),
        onupdate=func.now(),
        comment="更新时间",
    )

    # 关系定义
    data_source = relationship("DataSource", back_populates="raw_data_records")
    crawl_task = relationship("CrawlTask", back_populates="raw_data_records")

    # 索引定义
    __table_args__ = (
        Index("idx_raw_data_processing_status", "processing_status", "processing_priority"),
        Index("idx_raw_data_source_crawl", "source_id", "crawl_time"),
        Index("idx_raw_data_url_hash", "url_hash"),
        Index("idx_raw_data_content_hash", "content_hash"),
        Index("idx_raw_data_publish_time", "publish_time"),
        Index("idx_raw_data_mongodb", "mongodb_id"),
        Index("idx_raw_data_archive", "is_archived", "archived_at"),
    )


