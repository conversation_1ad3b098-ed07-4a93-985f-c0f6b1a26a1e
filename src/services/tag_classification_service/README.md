# 标签分类服务 (Tag Classification Service)

## 概述

标签分类服务是FinSight系统的核心模块，提供了完整的标签管理、分类管理和用户兴趣分析功能。该服务支持财经新闻和事件的智能标签化，并为个性化推荐提供基础数据支持。

## 功能特性

### 🏷️ 标签管理
- **多层级标签体系**：支持标签的层次结构管理
- **动态权重系统**：基于使用频率、质量反馈等计算综合权重
- **生命周期管理**：支持标签从新兴到成熟的全生命周期追踪
- **同义词支持**：支持标签同义词管理
- **智能搜索**：支持按名称、代码、描述的模糊搜索

### 📂 分类管理
- **层次化分类**：支持多级分类树结构
- **分类维度**：支持多维度分类（如行业、情感、地区等）
- **灵活配置**：可动态添加新的分类维度和分类值

### 👤 用户画像
- **兴趣标签**：跟踪用户对不同标签的兴趣程度
- **行为分析**：基于用户行为数据计算隐式兴趣
- **偏好管理**：支持用户在不同分类维度上的偏好设置
- **画像快照**：定期生成用户画像快照，便于分析趋势

### 🎯 智能推荐
- **多因子评分**：综合考虑标签权重、用户兴趣、内容质量等
- **实时计算**：支持实时推荐分数计算
- **统一接口**：为新闻和财经事件提供统一的推荐接口

## 技术架构

### 数据模型
```
TagType (标签类型)
├── TagCategory (标签分类)
│   └── Tag (标签)
│       ├── TagRelationship (标签关系)
│       └── UserInterestTag (用户兴趣标签)
├── ClassificationDimension (分类维度)
│   └── ClassificationValue (分类值)
│       └── UserClassificationPreference (用户分类偏好)
└── UserProfileSnapshot (用户画像快照)
```

### 服务层
- **TagTypeService**: 标签类型管理
- **TagCategoryService**: 标签分类管理  
- **TagService**: 标签管理
- **UserInterestService**: 用户兴趣管理
- **ClassificationService**: 分类管理

### API接口
- **REST API**: 提供完整的CRUD操作
- **依赖注入**: 使用FastAPI的依赖注入系统
- **权限控制**: 集成用户认证和权限验证
- **参数验证**: 使用Pydantic进行数据验证

## 快速开始

### 1. 环境准备
```bash
# 激活虚拟环境
source venv/finsight/bin/activate

# 安装依赖
pip install -r requirements.txt
```

### 2. 数据库初始化
```bash
# 创建数据库表
python -c "from src.core.database import create_tables; create_tables()"
```

### 3. 启动服务
```bash
# 启动开发服务器
python src/main.py
```

### 4. 访问文档
- API文档: http://localhost:8000/docs
- 健康检查: http://localhost:8000/health

## API使用示例

### 创建标签类型
```python
import requests

# 创建标签类型
response = requests.post("http://localhost:8000/api/v1/tags/types", 
    json={
        "type_code": "financial",
        "type_name": "金融标签",
        "description": "金融相关的标签类型",
        "icon": "financial",
        "color": "#2196F3"
    },
    headers={"Authorization": "Bearer YOUR_TOKEN"}
)
```

### 创建标签
```python
# 创建标签
response = requests.post("http://localhost:8000/api/v1/tags/", 
    json={
        "tag_name": "股票IPO",
        "tag_code": "stock_ipo",
        "tag_slug": "stock-ipo",
        "tag_type_id": 1,
        "description": "首次公开募股相关"
    },
    headers={"Authorization": "Bearer YOUR_TOKEN"}
)
```

### 搜索标签
```python
# 搜索标签
response = requests.get("http://localhost:8000/api/v1/tags/search?q=股票")
tags = response.json()
```

### 获取热门标签
```python
# 获取热门标签
response = requests.get("http://localhost:8000/api/v1/tags/popular?limit=10")
popular_tags = response.json()
```

## 数据库设计亮点

### 1. LTREE路径支持
使用PostgreSQL的LTREE扩展实现高效的层次结构查询：
```sql
-- 查询所有金融子标签
SELECT * FROM tags WHERE path <@ 'finance';

-- 查询标签的所有祖先
SELECT * FROM tags WHERE 'finance.stock.ipo' ~ ('.*' || path || '.*')::lquery;
```

### 2. 动态权重计算
支持基于多个因子的权重动态计算：
```sql
-- 综合权重自动计算
computed_weight = base_weight * 0.4 + popularity_weight * 0.3 + 
                 quality_weight * 0.2 + temporal_weight * 0.1
```

### 3. 生成列支持
使用数据库生成列提高查询性能：
```sql
-- 自动计算的反馈分数
feedback_score = positive_feedback_count / (positive_feedback_count + negative_feedback_count)
```

## 测试

### 运行单元测试
```bash
# 运行所有测试
python tests/services/tag_classification_service/run_tests.py

# 运行特定测试
python -m pytest tests/services/tag_classification_service/test_tag_service.py -v

# 生成覆盖率报告
python -m pytest tests/services/tag_classification_service/ --cov=src/services/tag_classification_service
```

### 测试覆盖
- ✅ 服务层单元测试
- ✅ API接口测试
- ✅ 数据模型测试
- ✅ 异常处理测试
- ✅ 权限验证测试

## 性能优化

### 1. 数据库索引
```sql
-- 路径查询优化
CREATE INDEX idx_tags_path ON tags(path);  -- B-tree索引支持前缀查询

-- 权重排序优化
CREATE INDEX idx_tags_computed_weight ON tags(computed_weight DESC) WHERE is_active = TRUE;

-- 用户兴趣查询优化
CREATE INDEX idx_user_interest_tags_user_computed ON user_interest_tags(user_id, computed_interest DESC);
```

### 2. 查询优化
- 使用预加载减少N+1查询问题
- 支持分页查询避免大结果集
- 使用数据库函数计算复杂推荐分数

### 3. 缓存策略
- 热门标签结果缓存
- 用户画像数据缓存
- 分类树结构缓存

## 配置说明

### 环境变量
```bash
# 数据库配置
DATABASE_URL=postgresql://user:password@localhost:5432/finsight

# 标签配置
TAG_CACHE_TTL=3600
USER_PROFILE_SNAPSHOT_INTERVAL=86400
```

### 配置文件
参考 `.env.develop` 文件中的配置项

## 扩展开发

### 添加新的标签类型
1. 在数据库中添加新的标签类型记录
2. 更新前端枚举定义
3. 添加相应的业务逻辑

### 添加新的分类维度
1. 创建新的ClassificationDimension记录
2. 添加对应的ClassificationValue记录
3. 更新推荐算法权重计算

### 自定义推荐算法
1. 继承现有的推荐服务类
2. 实现自定义的评分计算逻辑
3. 在依赖注入中注册新的服务

## 故障排查

### 常见问题

1. **数据库连接失败**
   - 检查数据库服务是否启动
   - 验证连接字符串配置
   - 确认网络连通性

2. **标签创建失败**
   - 检查标签代码唯一性
   - 验证关联的标签类型存在
   - 确认用户权限

3. **权重计算异常**
   - 检查基础权重范围(0-1)
   - 验证统计数据完整性
   - 确认计算公式正确性

### 日志分析
```bash
# 查看服务日志
tail -f logs/finsight.log | grep "tag_classification"

# 查看错误日志
grep "ERROR" logs/finsight.log | grep "tag"
```

## 贡献指南

1. Fork 项目仓库
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建 Pull Request

### 代码规范
- 遵循 PEP 8 代码风格
- 添加完整的类型注解
- 编写全面的单元测试
- 更新相关文档

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情 