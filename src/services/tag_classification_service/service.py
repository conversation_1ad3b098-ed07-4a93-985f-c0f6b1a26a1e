"""
标签和分类服务模块（重构版）
包含标签管理、分类管理、用户兴趣等核心业务逻辑
"""

import json
import logging
from datetime import date, datetime, timezone
from decimal import Decimal
from typing import Any, Dict, List, Optional, Tuple

from sqlalchemy import and_, asc, desc, func, or_, text
from sqlalchemy.exc import IntegrityError
from sqlalchemy.orm import Session, joinedload, selectinload

from .models import (ClassificationDimension, ClassificationValue, Tag,
                     TagClassification, TagRelationship,
                     UserClassificationPreference, UserInterestTag,
                     UserProfileSnapshot)
from .schemas import (ClassificationDimensionCreate,
                      ClassificationDimensionUpdate, ClassificationValueCreate,
                      ClassificationValueUpdate, LifecycleStage,
                      TagClassificationCreate, TagClassificationUpdate, TagCreate,
                      TagListQuery, TagUpdate,
                      UserClassificationPreferenceCreate,
                      UserClassificationPreferenceUpdate)


class TagClassificationService:
    """标签分类服务（合并原TagTypeService和TagCategoryService）"""

    def __init__(self, db: Session):
        """初始化服务"""
        self.db = db

    def create_classification(self, classification_data: TagClassificationCreate) -> TagClassification:
        """
        创建标签分类

        Args:
            classification_data: 标签分类创建数据

        Returns:
            创建的标签分类对象

        Raises:
            ValueError: 当分类代码已存在时
        """
        try:
            # 检查代码是否已存在
            existing = (
                self.db.query(TagClassification)
                .filter(TagClassification.classification_code == classification_data.classification_code)
                .first()
            )

            if existing:
                raise ValueError(
                    f"Classification with code '{classification_data.classification_code}' already exists"
                )

            # 计算层级和路径
            level = 1
            path = classification_data.classification_code
            if classification_data.parent_id:
                parent = self.get_classification_by_id(classification_data.parent_id)
                if parent:
                    level = parent.level + 1
                    path = f"{parent.path}.{classification_data.classification_code}"

            # 创建新分类
            classification = TagClassification(
                classification_code=classification_data.classification_code,
                classification_name=classification_data.classification_name,
                parent_id=classification_data.parent_id,
                level=level,
                path=path,
                description=classification_data.description,
                icon=classification_data.icon,
                color=classification_data.color,
                sort_order=classification_data.sort_order,
            )

            self.db.add(classification)
            self.db.commit()
            self.db.refresh(classification)

            logging.info(f"Created classification: {classification.classification_code}")
            return classification

        except IntegrityError as e:
            self.db.rollback()
            logging.error(f"Failed to create classification: {e}")
            raise ValueError("Failed to create classification due to database constraints")

    def get_classification_by_id(self, classification_id: int) -> Optional[TagClassification]:
        """
        根据ID获取标签分类

        Args:
            classification_id: 分类ID

        Returns:
            分类对象或None
        """
        return self.db.query(TagClassification).filter(TagClassification.id == classification_id).first()

    def get_classification_by_code(self, classification_code: str) -> Optional[TagClassification]:
        """
        根据代码获取标签分类

        Args:
            classification_code: 分类代码

        Returns:
            分类对象或None
        """
        return (
            self.db.query(TagClassification)
            .filter(TagClassification.classification_code == classification_code)
            .first()
        )

    def get_classifications(
        self,
        parent_id: Optional[int] = None,
        is_active: bool = True,
        skip: int = 0,
        limit: int = 100,
    ) -> List[TagClassification]:
        """
        获取分类列表

        Args:
            parent_id: 父分类ID过滤
            is_active: 是否只返回活跃分类
            skip: 跳过记录数
            limit: 限制记录数

        Returns:
            分类列表
        """
        query = self.db.query(TagClassification)

        if parent_id is not None:
            query = query.filter(TagClassification.parent_id == parent_id)
        if is_active:
            query = query.filter(TagClassification.is_active == True)

        return query.order_by(TagClassification.sort_order.desc(), TagClassification.id).offset(skip).limit(limit).all()

    def update_classification(
        self, classification_id: int, classification_data: TagClassificationUpdate
    ) -> Optional[TagClassification]:
        """
        更新标签分类

        Args:
            classification_id: 分类ID
            classification_data: 更新数据

        Returns:
            更新后的分类对象或None
        """
        classification = self.get_classification_by_id(classification_id)
        if not classification:
            return None

        try:
            # 更新字段
            update_data = classification_data.model_dump(exclude_unset=True)
            for field, value in update_data.items():
                if hasattr(classification, field):
                    setattr(classification, field, value)

            # 如果更新了父分类，重新计算层级和路径
            if "parent_id" in update_data:
                if classification.parent_id:
                    parent = self.get_classification_by_id(classification.parent_id)
                    if parent:
                        classification.level = parent.level + 1
                        classification.path = f"{parent.path}.{classification.classification_code}"
                else:
                    classification.level = 1
                    classification.path = classification.classification_code

            self.db.commit()
            self.db.refresh(classification)

            logging.info(f"Updated classification: {classification.classification_code}")
            return classification

        except IntegrityError as e:
            self.db.rollback()
            logging.error(f"Failed to update classification: {e}")
            raise ValueError("Failed to update classification due to database constraints")

    def delete_classification(self, classification_id: int) -> bool:
        """
        删除标签分类

        Args:
            classification_id: 分类ID

        Returns:
            是否删除成功
        """
        classification = self.get_classification_by_id(classification_id)
        if not classification:
            return False

        try:
            # 检查是否有子分类
            children_count = (
                self.db.query(TagClassification)
                .filter(TagClassification.parent_id == classification_id)
                .count()
            )
            if children_count > 0:
                raise ValueError("Cannot delete classification with child classifications")

            # 检查是否有关联的标签
            tags_count = (
                self.db.query(Tag)
                .filter(Tag.classification_id == classification_id)
                .count()
            )
            if tags_count > 0:
                raise ValueError("Cannot delete classification with associated tags")

            self.db.delete(classification)
            self.db.commit()

            logging.info(f"Deleted classification: {classification.classification_code}")
            return True

        except Exception as e:
            self.db.rollback()
            logging.error(f"Failed to delete classification: {e}")
            raise


class TagService:
    """标签服务"""

    def __init__(self, db: Session):
        """初始化服务"""
        self.db = db

    def create_tag(self, tag_data: TagCreate) -> Tag:
        """创建标签"""
        try:
            # 检查代码和slug的唯一性
            if self.db.query(Tag).filter(Tag.tag_code == tag_data.tag_code).first():
                raise ValueError(f"Tag with code '{tag_data.tag_code}' already exists")

            if self.db.query(Tag).filter(Tag.tag_slug == tag_data.tag_slug).first():
                raise ValueError(f"Tag with slug '{tag_data.tag_slug}' already exists")

            # 验证分类存在性
            if (
                not self.db.query(TagClassification)
                .filter(TagClassification.id == tag_data.classification_id)
                .first()
            ):
                raise ValueError(f"Classification with ID {tag_data.classification_id} not found")

            # 处理父标签和层级
            level = 1
            path = tag_data.tag_code
            if tag_data.parent_id:
                parent = self.get_tag_by_id(tag_data.parent_id)
                if not parent:
                    raise ValueError(
                        f"Parent tag with ID {tag_data.parent_id} not found"
                    )
                level = parent.level + 1
                path = f"{parent.path}.{tag_data.tag_code}"

            # 处理synonyms字段，转换为JSON字符串以兼容SQLite
            synonyms_json = None
            if tag_data.synonyms:
                synonyms_json = (
                    json.dumps(tag_data.synonyms)
                    if isinstance(tag_data.synonyms, list)
                    else tag_data.synonyms
                )

            tag = Tag(
                tag_name=tag_data.tag_name,
                tag_code=tag_data.tag_code,
                tag_slug=tag_data.tag_slug,
                parent_id=tag_data.parent_id,
                level=level,
                path=path,
                classification_id=tag_data.classification_id,
                color=tag_data.color,
                icon=tag_data.icon,
                base_weight=tag_data.base_weight,
                description=tag_data.description,
                synonyms=synonyms_json,
                lifecycle_stage=tag_data.lifecycle_stage,
                is_system=False,  # 用户创建的标签默认不是系统标签
            )

            self.db.add(tag)
            self.db.commit()
            self.db.refresh(tag)

            logging.info(f"Created tag: {tag.tag_code}")
            return tag

        except IntegrityError as e:
            self.db.rollback()
            logging.error(f"Failed to create tag: {e}")
            raise ValueError("Failed to create tag due to database constraints")

    def get_tag_by_id(
        self, tag_id: int, include_relations: bool = False
    ) -> Optional[Tag]:
        """根据ID获取标签"""
        query = self.db.query(Tag)

        if include_relations:
            query = query.options(
                joinedload(Tag.classification),
                joinedload(Tag.parent),
                selectinload(Tag.children),
            )

        return query.filter(Tag.id == tag_id).first()

    def get_tag_by_code(self, tag_code: str) -> Optional[Tag]:
        """根据代码获取标签"""
        return self.db.query(Tag).filter(Tag.tag_code == tag_code).first()

    def search_tags(self, query: str, limit: int = 10) -> List[Tag]:
        """搜索标签"""
        return (
            self.db.query(Tag)
            .filter(
                and_(
                    Tag.is_active == True,
                    or_(
                        Tag.tag_name.ilike(f"%{query}%"),
                        Tag.tag_code.ilike(f"%{query}%"),
                        Tag.description.ilike(f"%{query}%"),
                    ),
                )
            )
            .order_by(Tag.usage_count.desc())
            .limit(limit)
            .all()
        )

    def list_tags(self, query: TagListQuery) -> Tuple[List[Tag], int]:
        """获取标签列表"""
        db_query = self.db.query(Tag)

        # 应用过滤条件
        if query.search:
            search_filter = or_(
                Tag.tag_name.ilike(f"%{query.search}%"),
                Tag.tag_code.ilike(f"%{query.search}%"),
                Tag.description.ilike(f"%{query.search}%"),
            )
            db_query = db_query.filter(search_filter)

        if query.classification_id:
            db_query = db_query.filter(Tag.classification_id == query.classification_id)

        if query.parent_id is not None:
            db_query = db_query.filter(Tag.parent_id == query.parent_id)

        if query.is_active is not None:
            db_query = db_query.filter(Tag.is_active == query.is_active)

        if query.lifecycle_stage:
            db_query = db_query.filter(Tag.lifecycle_stage == query.lifecycle_stage)

        # 计算总数
        total = db_query.count()

        # 应用分页和排序
        skip = (query.page - 1) * query.size
        tags = (
            db_query.order_by(Tag.usage_count.desc(), Tag.created_at.desc())
            .offset(skip)
            .limit(query.size)
            .all()
        )

        return tags, total

    def update_tag(self, tag_id: int, update_data: TagUpdate) -> Optional[Tag]:
        """更新标签"""
        tag = self.get_tag_by_id(tag_id)
        if not tag:
            return None

        try:
            # 更新字段
            update_dict = update_data.model_dump(exclude_unset=True)
            for field, value in update_dict.items():
                if field == "synonyms" and value:
                    # 处理synonyms字段
                    value = json.dumps(value) if isinstance(value, list) else value
                if hasattr(tag, field):
                    setattr(tag, field, value)

            self.db.commit()
            self.db.refresh(tag)

            logging.info(f"Updated tag: {tag.tag_code}")
            return tag

        except IntegrityError as e:
            self.db.rollback()
            logging.error(f"Failed to update tag: {e}")
            raise ValueError("Failed to update tag due to database constraints")

    def delete_tag(self, tag_id: int) -> bool:
        """删除标签"""
        tag = self.get_tag_by_id(tag_id)
        if not tag:
            return False

        try:
            # 检查是否有子标签
            children_count = (
                self.db.query(Tag)
                .filter(Tag.parent_id == tag_id)
                .count()
            )
            if children_count > 0:
                raise ValueError("Cannot delete tag with child tags")

            self.db.delete(tag)
            self.db.commit()

            logging.info(f"Deleted tag: {tag.tag_code}")
            return True

        except Exception as e:
            self.db.rollback()
            logging.error(f"Failed to delete tag: {e}")
            raise

    def get_popular_tags(self, limit: int = 10) -> List[Tag]:
        """获取热门标签"""
        return (
            self.db.query(Tag)
            .filter(Tag.is_active == True)
            .order_by(Tag.usage_count.desc())
            .limit(limit)
            .all()
        )

    def get_trending_tags(self, limit: int = 10) -> List[Tag]:
        """获取趋势标签"""
        return (
            self.db.query(Tag)
            .filter(Tag.is_active == True)
            .order_by(Tag.daily_usage_count.desc())
            .limit(limit)
            .all()
        )

    def activate_tag(self, tag_id: int) -> bool:
        """激活标签"""
        tag = self.get_tag_by_id(tag_id)
        if not tag:
            return False

        tag.is_active = True
        self.db.commit()
        return True

    def deactivate_tag(self, tag_id: int) -> bool:
        """停用标签"""
        tag = self.get_tag_by_id(tag_id)
        if not tag:
            return False

        tag.is_active = False
        self.db.commit()
        return True


# 其他服务类的简化版本...
class ClassificationService:
    """分类服务"""

    def __init__(self, db: Session):
        self.db = db


class UserInterestService:
    """用户兴趣服务"""

    def __init__(self, db: Session):
        self.db = db
