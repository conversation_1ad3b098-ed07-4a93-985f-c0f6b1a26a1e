"""
标签和分类服务依赖注入
定义服务层的依赖注入函数
"""

from fastapi import Depends
from sqlalchemy.orm import Session

from ...core.database import get_db
from .service import (
    ClassificationService,
    TagClassificationService,
    TagService,
    UserInterestService,
)


def get_tag_classification_service(db: Session = Depends(get_db)) -> TagClassificationService:
    """获取标签分类服务实例"""
    return TagClassificationService(db)


def get_tag_service(db: Session = Depends(get_db)) -> TagService:
    """获取标签服务实例"""
    return TagService(db)


def get_classification_service(db: Session = Depends(get_db)) -> ClassificationService:
    """获取分类服务实例"""
    return ClassificationService(db)


def get_user_interest_service(db: Session = Depends(get_db)) -> UserInterestService:
    """获取用户兴趣服务实例"""
    return UserInterestService(db)
