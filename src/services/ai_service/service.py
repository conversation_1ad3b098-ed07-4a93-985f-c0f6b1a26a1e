"""
AI服务业务逻辑实现
提供AI模型管理的核心功能
"""

from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from decimal import Decimal
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func, desc, asc
from fastapi import HTTPException, status

from .models import AIModel, ModelMetrics, ModelType, ModelProvider, ModelStatus
from .schemas import (
    AIModelCreate, AIModelUpdate, AIModelResponse,
    ModelMetricsCreate, ModelMetricsResponse,
    ModelListQuery, ModelUsageStats, SystemModelInfo
)
from ...core.database import get_db
import logging

logger = logging.getLogger(__name__)


class AIModelService:
    """AI模型管理服务"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def create_model(self, model_data: AIModelCreate) -> AIModelResponse:
        """创建AI模型"""
        try:
            # 检查模型名称是否已存在
            existing_model = self.db.query(AIModel).filter(
                AIModel.model_name == model_data.model_name
            ).first()
            
            if existing_model:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"模型名称 '{model_data.model_name}' 已存在"
                )
            
            # 如果设为默认模型，需要取消同类型的其他默认模型
            if model_data.is_default:
                self._update_default_models(model_data.model_type, None)
            
            # 创建模型
            db_model = AIModel(**model_data.dict())
            self.db.add(db_model)
            self.db.commit()
            self.db.refresh(db_model)
            
            logger.info(f"Created AI model: {db_model.model_name}")
            return self._model_to_response(db_model)
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"Error creating AI model: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"创建AI模型失败: {str(e)}"
            )
    
    def get_model(self, model_id: int) -> AIModelResponse:
        """获取AI模型详情"""
        db_model = self.db.query(AIModel).filter(AIModel.id == model_id).first()
        if not db_model:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"模型ID {model_id} 不存在"
            )
        return self._model_to_response(db_model)
    
    def get_model_by_name(self, model_name: str) -> AIModelResponse:
        """通过名称获取AI模型"""
        db_model = self.db.query(AIModel).filter(AIModel.model_name == model_name).first()
        if not db_model:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"模型名称 '{model_name}' 不存在"
            )
        return self._model_to_response(db_model)
    
    def update_model(self, model_id: int, model_data: AIModelUpdate) -> AIModelResponse:
        """更新AI模型"""
        try:
            db_model = self.db.query(AIModel).filter(AIModel.id == model_id).first()
            if not db_model:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"模型ID {model_id} 不存在"
                )
            
            # 如果设为默认模型，需要取消同类型的其他默认模型
            if model_data.is_default and model_data.is_default != db_model.is_default:
                self._update_default_models(db_model.model_type, model_id)
            
            # 更新模型字段
            update_data = model_data.dict(exclude_unset=True)
            for field, value in update_data.items():
                setattr(db_model, field, value)
            
            self.db.commit()
            self.db.refresh(db_model)
            
            logger.info(f"Updated AI model: {db_model.model_name}")
            return self._model_to_response(db_model)
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"Error updating AI model: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"更新AI模型失败: {str(e)}"
            )
    
    def delete_model(self, model_id: int) -> bool:
        """删除AI模型"""
        try:
            db_model = self.db.query(AIModel).filter(AIModel.id == model_id).first()
            if not db_model:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"模型ID {model_id} 不存在"
                )
            
            # 如果是默认模型，需要设置其他模型为默认
            if db_model.is_default:
                self._set_new_default_model(db_model.model_type, model_id)
            
            self.db.delete(db_model)
            self.db.commit()
            
            logger.info(f"Deleted AI model: {db_model.model_name}")
            return True
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"Error deleting AI model: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"删除AI模型失败: {str(e)}"
            )
    
    def list_models(
        self, 
        query: ModelListQuery,
        page: int = 1,
        page_size: int = 20
    ) -> Tuple[List[AIModelResponse], int]:
        """获取模型列表"""
        try:
            # 构建查询条件
            conditions = []
            
            if query.model_type:
                conditions.append(AIModel.model_type == query.model_type)
            
            if query.provider:
                conditions.append(AIModel.provider == query.provider)
            
            if query.status:
                conditions.append(AIModel.status == query.status)
            
            if query.is_default is not None:
                conditions.append(AIModel.is_default == query.is_default)
            
            if query.search:
                search_term = f"%{query.search}%"
                conditions.append(
                    or_(
                        AIModel.model_name.ilike(search_term),
                        AIModel.display_name.ilike(search_term),
                        AIModel.description.ilike(search_term)
                    )
                )
            
            # 执行查询
            query_obj = self.db.query(AIModel)
            if conditions:
                query_obj = query_obj.filter(and_(*conditions))
            
            # 获取总数
            total = query_obj.count()
            
            # 分页查询
            offset = (page - 1) * page_size
            models = query_obj.order_by(asc(AIModel.priority), desc(AIModel.created_at)).offset(offset).limit(page_size).all()
            
            return [self._model_to_response(model) for model in models], total
            
        except Exception as e:
            logger.error(f"Error listing AI models: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"获取模型列表失败: {str(e)}"
            )
    
    def get_default_model(self, model_type: ModelType) -> Optional[AIModelResponse]:
        """获取指定类型的默认模型"""
        db_model = self.db.query(AIModel).filter(
            and_(
                AIModel.model_type == model_type,
                AIModel.is_default == True,
                AIModel.status == ModelStatus.ACTIVE
            )
        ).first()
        
        if not db_model:
            # 如果没有默认模型，返回第一个可用模型
            db_model = self.db.query(AIModel).filter(
                and_(
                    AIModel.model_type == model_type,
                    AIModel.status == ModelStatus.ACTIVE
                )
            ).order_by(asc(AIModel.priority)).first()
        
        return self._model_to_response(db_model) if db_model else None
    
    def get_available_models(self, model_type: ModelType) -> List[AIModelResponse]:
        """获取指定类型的可用模型列表"""
        models = self.db.query(AIModel).filter(
            and_(
                AIModel.model_type == model_type,
                AIModel.status == ModelStatus.ACTIVE
            )
        ).order_by(asc(AIModel.priority)).all()
        
        return [self._model_to_response(model) for model in models]
    
    def update_model_status(self, model_id: int, status: ModelStatus) -> AIModelResponse:
        """更新模型状态"""
        try:
            db_model = self.db.query(AIModel).filter(AIModel.id == model_id).first()
            if not db_model:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"模型ID {model_id} 不存在"
                )
            
            # 如果是默认模型被禁用，需要设置其他模型为默认
            if db_model.is_default and status != ModelStatus.ACTIVE:
                self._set_new_default_model(db_model.model_type, model_id)
            
            db_model.status = status
            self.db.commit()
            self.db.refresh(db_model)
            
            logger.info(f"Updated model status: {db_model.model_name} -> {status}")
            return self._model_to_response(db_model)
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"Error updating model status: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"更新模型状态失败: {str(e)}"
            )
    
    def set_default_model(self, model_id: int) -> AIModelResponse:
        """设置默认模型"""
        try:
            db_model = self.db.query(AIModel).filter(AIModel.id == model_id).first()
            if not db_model:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"模型ID {model_id} 不存在"
                )
            
            if db_model.status != ModelStatus.ACTIVE:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="只能设置活跃状态的模型为默认模型"
                )
            
            # 取消同类型的其他默认模型
            self._update_default_models(db_model.model_type, model_id)
            
            db_model.is_default = True
            self.db.commit()
            self.db.refresh(db_model)
            
            logger.info(f"Set default model: {db_model.model_name}")
            return self._model_to_response(db_model)
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"Error setting default model: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"设置默认模型失败: {str(e)}"
            )
    
    def update_usage_stats(self, model_id: int, stats: ModelUsageStats) -> AIModelResponse:
        """更新模型使用统计"""
        try:
            db_model = self.db.query(AIModel).filter(AIModel.id == model_id).first()
            if not db_model:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"模型ID {model_id} 不存在"
                )
            
            # 更新使用统计
            db_model.update_usage_stats(
                success=stats.success,
                input_tokens=stats.input_tokens,
                output_tokens=stats.output_tokens,
                latency_ms=stats.latency_ms
            )
            
            self.db.commit()
            self.db.refresh(db_model)
            
            return self._model_to_response(db_model)
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"Error updating usage stats: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"更新使用统计失败: {str(e)}"
            )
    
    def get_system_info(self) -> SystemModelInfo:
        """获取系统模型信息"""
        try:
            # 基础统计
            total_models = self.db.query(AIModel).count()
            active_models = self.db.query(AIModel).filter(AIModel.status == ModelStatus.ACTIVE).count()
            
            # 获取各类型默认模型
            default_models = {}
            for model_type in ModelType:
                default_model = self.db.query(AIModel).filter(
                    and_(
                        AIModel.model_type == model_type,
                        AIModel.is_default == True
                    )
                ).first()
                if default_model:
                    default_models[model_type.value] = default_model.model_name
            
            # 提供商分布
            provider_distribution = {}
            provider_stats = self.db.query(
                AIModel.provider,
                func.count(AIModel.id).label('count')
            ).group_by(AIModel.provider).all()
            
            for provider, count in provider_stats:
                provider_distribution[provider] = count
            
            # 类型分布
            type_distribution = {}
            type_stats = self.db.query(
                AIModel.model_type,
                func.count(AIModel.id).label('count')
            ).group_by(AIModel.model_type).all()
            
            for model_type, count in type_stats:
                type_distribution[model_type] = count
            
            # 今日统计
            today = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
            
            total_requests_today = self.db.query(
                func.sum(AIModel.total_requests)
            ).filter(AIModel.last_used_at >= today).scalar() or 0
            
            total_cost_today = self.db.query(
                func.sum(AIModel.total_cost)
            ).filter(AIModel.last_used_at >= today).scalar() or Decimal('0.0')
            
            return SystemModelInfo(
                total_models=total_models,
                active_models=active_models,
                default_models=default_models,
                provider_distribution=provider_distribution,
                type_distribution=type_distribution,
                total_requests_today=int(total_requests_today),
                total_cost_today=total_cost_today
            )
            
        except Exception as e:
            logger.error(f"Error getting system info: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"获取系统信息失败: {str(e)}"
            )
    
    def batch_update_status(self, model_ids: List[int], status: ModelStatus) -> Dict[str, Any]:
        """批量更新模型状态"""
        try:
            success_ids = []
            failed_ids = []
            errors = []
            
            for model_id in model_ids:
                try:
                    self.update_model_status(model_id, status)
                    success_ids.append(model_id)
                except Exception as e:
                    failed_ids.append(model_id)
                    errors.append(f"模型ID {model_id}: {str(e)}")
            
            return {
                "success_count": len(success_ids),
                "failed_count": len(failed_ids),
                "success_ids": success_ids,
                "failed_ids": failed_ids,
                "errors": errors
            }
            
        except Exception as e:
            logger.error(f"Error batch updating model status: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"批量更新模型状态失败: {str(e)}"
            )
    
    def _update_default_models(self, model_type: ModelType, exclude_id: Optional[int] = None):
        """更新默认模型（取消同类型的其他默认模型）"""
        query = self.db.query(AIModel).filter(
            and_(
                AIModel.model_type == model_type,
                AIModel.is_default == True
            )
        )
        
        if exclude_id:
            query = query.filter(AIModel.id != exclude_id)
        
        default_models = query.all()
        for model in default_models:
            model.is_default = False
    
    def _set_new_default_model(self, model_type: ModelType, exclude_id: int):
        """设置新的默认模型"""
        new_default = self.db.query(AIModel).filter(
            and_(
                AIModel.model_type == model_type,
                AIModel.status == ModelStatus.ACTIVE,
                AIModel.id != exclude_id
            )
        ).order_by(asc(AIModel.priority)).first()
        
        if new_default:
            new_default.is_default = True
            logger.info(f"Set new default model: {new_default.model_name}")
    
    def _model_to_response(self, model: AIModel) -> AIModelResponse:
        """转换模型对象为响应格式"""
        return AIModelResponse(
            id=model.id,
            model_name=model.model_name,
            display_name=model.display_name,
            description=model.description,
            model_type=model.model_type,
            provider=model.provider,
            model_version=model.model_version,
            api_endpoint=model.api_endpoint,
            api_key_name=model.api_key_name,
                            model_config=model.model_params,
            max_tokens=model.max_tokens,
            temperature=model.temperature,
            top_p=model.top_p,
            frequency_penalty=model.frequency_penalty,
            presence_penalty=model.presence_penalty,
            rate_limit_rpm=model.rate_limit_rpm,
            rate_limit_tpm=model.rate_limit_tpm,
            timeout_seconds=model.timeout_seconds,
            max_retries=model.max_retries,
            cost_per_1k_input_tokens=model.cost_per_1k_input_tokens,
            cost_per_1k_output_tokens=model.cost_per_1k_output_tokens,
            cost_per_request=model.cost_per_request,
            accuracy=model.accuracy,
            latency_ms=model.latency_ms,
            availability=model.availability,
            status=model.status,
            is_default=model.is_default,
            priority=model.priority,
            total_requests=model.total_requests,
            success_requests=model.success_requests,
            failed_requests=model.failed_requests,
            total_input_tokens=model.total_input_tokens,
            total_output_tokens=model.total_output_tokens,
            total_cost=model.total_cost,
            success_rate=model.success_rate,
            failure_rate=model.failure_rate,
            average_cost_per_request=model.average_cost_per_request,
            created_at=model.created_at,
            updated_at=model.updated_at,
            last_used_at=model.last_used_at
        )
    
    def get_model_by_id(self, model_id: int) -> Optional[AIModelResponse]:
        """通过ID获取AI模型"""
        db_model = self.db.query(AIModel).filter(AIModel.id == model_id).first()
        if not db_model:
            return None
        return self._model_to_response(db_model)
    
    def get_models_list(
        self, 
        model_type: Optional[str] = None,
        provider: Optional[str] = None,
        status: Optional[str] = None
    ) -> List[AIModelResponse]:
        """获取模型列表（简化版，不分页）"""
        try:
            # 构建查询条件
            conditions = []
            
            if model_type:
                conditions.append(AIModel.model_type == model_type)
            
            if provider:
                conditions.append(AIModel.provider == provider)
            
            if status:
                conditions.append(AIModel.status == status)
            
            # 执行查询
            query_obj = self.db.query(AIModel)
            if conditions:
                query_obj = query_obj.filter(and_(*conditions))
            
            models = query_obj.order_by(asc(AIModel.priority), desc(AIModel.created_at)).all()
            
            return [self._model_to_response(model) for model in models]
            
        except Exception as e:
            logger.error(f"Error getting models list: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"获取模型列表失败: {str(e)}"
            )

    def batch_delete_models(self, model_ids: List[int]) -> Dict[str, Any]:
        """批量删除模型"""
        try:
            success_ids = []
            failed_ids = []
            errors = []
            
            for model_id in model_ids:
                try:
                    success = self.delete_model(model_id)
                    if success:
                        success_ids.append(model_id)
                    else:
                        failed_ids.append(model_id)
                        errors.append(f"模型ID {model_id}: 删除失败")
                except Exception as e:
                    failed_ids.append(model_id)
                    errors.append(f"模型ID {model_id}: {str(e)}")
            
            return {
                "success_count": len(success_ids),
                "failed_count": len(failed_ids),
                "success_ids": success_ids,
                "failed_ids": failed_ids,
                "errors": errors
            }
            
        except Exception as e:
            logger.error(f"Error batch deleting models: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"批量删除模型失败: {str(e)}"
            )


class ModelMetricsService:
    """模型指标管理服务"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def create_metrics(self, metrics_data: ModelMetricsCreate) -> ModelMetricsResponse:
        """创建模型指标"""
        try:
            # 检查模型是否存在
            model = self.db.query(AIModel).filter(AIModel.id == metrics_data.model_id).first()
            if not model:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"模型ID {metrics_data.model_id} 不存在"
                )
            
            # 创建指标记录
            db_metrics = ModelMetrics(**metrics_data.dict())
            self.db.add(db_metrics)
            self.db.commit()
            self.db.refresh(db_metrics)
            
            return self._metrics_to_response(db_metrics)
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"Error creating model metrics: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"创建模型指标失败: {str(e)}"
            )
    
    def get_model_metrics(
        self, 
        model_id: int, 
        time_window: str = "day",
        days: int = 7
    ) -> List[ModelMetricsResponse]:
        """获取模型指标"""
        try:
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)
            
            metrics = self.db.query(ModelMetrics).filter(
                and_(
                    ModelMetrics.model_id == model_id,
                    ModelMetrics.time_window == time_window,
                    ModelMetrics.metric_date >= start_date,
                    ModelMetrics.metric_date <= end_date
                )
            ).order_by(desc(ModelMetrics.metric_date)).all()
            
            return [self._metrics_to_response(metric) for metric in metrics]
            
        except Exception as e:
            logger.error(f"Error getting model metrics: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"获取模型指标失败: {str(e)}"
            )
    
    def _metrics_to_response(self, metrics: ModelMetrics) -> ModelMetricsResponse:
        """转换指标对象为响应格式"""
        return ModelMetricsResponse(
            id=metrics.id,
            model_id=metrics.model_id,
            metric_date=metrics.metric_date,
            time_window=metrics.time_window,
            total_requests=metrics.total_requests,
            success_requests=metrics.success_requests,
            failed_requests=metrics.failed_requests,
            success_rate=metrics.success_rate,
            error_rate=metrics.error_rate,
            avg_latency_ms=metrics.avg_latency_ms,
            p95_latency_ms=metrics.p95_latency_ms,
            p99_latency_ms=metrics.p99_latency_ms,
            total_input_tokens=metrics.total_input_tokens,
            total_output_tokens=metrics.total_output_tokens,
            avg_input_tokens=metrics.avg_input_tokens,
            avg_output_tokens=metrics.avg_output_tokens,
            total_cost=metrics.total_cost,
            avg_cost_per_request=metrics.avg_cost_per_request,
            accuracy_score=metrics.accuracy_score,
            quality_score=metrics.quality_score,
            user_satisfaction=metrics.user_satisfaction,
            timeout_errors=metrics.timeout_errors,
            rate_limit_errors=metrics.rate_limit_errors,
            api_errors=metrics.api_errors,
            other_errors=metrics.other_errors,
            metadata=metrics.extra_metadata,
            created_at=metrics.created_at
        )
    
    def get_metrics_by_model(
        self, 
        model_id: int,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        time_window: str = "hour"
    ) -> List[ModelMetricsResponse]:
        """获取指定模型的指标数据"""
        try:
            # 构建查询条件
            conditions = [ModelMetrics.model_id == model_id]
            
            if time_window:
                conditions.append(ModelMetrics.time_window == time_window)
            
            if start_date:
                conditions.append(ModelMetrics.metric_date >= start_date)
                
            if end_date:
                conditions.append(ModelMetrics.metric_date <= end_date)
            
            metrics = self.db.query(ModelMetrics).filter(
                and_(*conditions)
            ).order_by(desc(ModelMetrics.metric_date)).all()
            
            return [self._metrics_to_response(metric) for metric in metrics]
            
        except Exception as e:
            logger.error(f"Error getting metrics by model: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"获取模型指标失败: {str(e)}"
            )

    def get_model_performance(self, model_id: int, days: int = 7) -> Dict[str, Any]:
        """获取模型性能摘要"""
        try:
            # 获取模型基本信息
            model = self.db.query(AIModel).filter(AIModel.id == model_id).first()
            if not model:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"模型ID {model_id} 不存在"
                )
            
            # 获取指定天数内的指标
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)
            
            metrics = self.db.query(ModelMetrics).filter(
                and_(
                    ModelMetrics.model_id == model_id,
                    ModelMetrics.metric_date >= start_date,
                    ModelMetrics.metric_date <= end_date
                )
            ).all()
            
            if not metrics:
                return {
                    "model_id": model_id,
                    "model_name": model.model_name,
                    "period_days": days,
                    "total_requests": 0,
                    "success_rate": 0.0,
                    "avg_latency_ms": 0,
                    "total_cost": Decimal("0.0"),
                    "metrics_count": 0
                }
            
            # 计算聚合指标
            total_requests = sum(m.total_requests for m in metrics)
            total_success = sum(m.success_requests for m in metrics)
            total_cost = sum(m.total_cost or Decimal("0.0") for m in metrics)
            
            success_rate = total_success / total_requests if total_requests > 0 else 0.0
            
            # 计算平均延迟（加权平均）
            weighted_latency = 0
            total_weight = 0
            for m in metrics:
                if m.avg_latency_ms and m.total_requests:
                    weighted_latency += m.avg_latency_ms * m.total_requests
                    total_weight += m.total_requests
            
            avg_latency_ms = int(weighted_latency / total_weight) if total_weight > 0 else 0
            
            return {
                "model_id": model_id,
                "model_name": model.model_name,
                "period_days": days,
                "total_requests": total_requests,
                "success_rate": round(success_rate, 4),
                "avg_latency_ms": avg_latency_ms,
                "total_cost": total_cost,
                "metrics_count": len(metrics)
            }
            
        except Exception as e:
            logger.error(f"Error getting model performance: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"获取模型性能失败: {str(e)}"
            ) 