"""
AI服务工具类
提供API密钥获取、模型配置验证等实用功能
"""

import os
import logging
from typing import Optional, Dict, Any
from sqlalchemy.orm import Session

from .models import AIModel

logger = logging.getLogger(__name__)


class AIServiceUtils:
    """AI服务工具类"""
    
    @staticmethod
    def get_api_key_from_env(api_key_name: str) -> Optional[str]:
        """
        从环境变量中获取API密钥
        
        Args:
            api_key_name: 环境变量名称，如 "DEEPSEEK_API_KEY"
            
        Returns:
            API密钥值，如果未找到则返回None
        """
        if not api_key_name:
            return None
            
        api_key = os.getenv(api_key_name)
        if not api_key:
            logger.warning(f"环境变量 {api_key_name} 未设置或为空")
            return None
            
        # 检查是否为占位符值
        placeholder_values = [
            "your_api_key_here",
            "your_key_here", 
            "placeholder",
            "demo_key",
            "test_key"
        ]
        
        if api_key.lower() in placeholder_values:
            logger.warning(f"环境变量 {api_key_name} 包含占位符值: {api_key}")
            return None
            
        return api_key
    
    @staticmethod
    def validate_model_config(model: AIModel) -> Dict[str, Any]:
        """
        验证AI模型配置的完整性
        
        Args:
            model: AI模型实例
            
        Returns:
            验证结果字典，包含is_valid、errors、warnings字段
        """
        result = {
            "is_valid": True,
            "errors": [],
            "warnings": []
        }
        
        # 检查基本配置
        if not model.model_name:
            result["errors"].append("模型名称不能为空")
            result["is_valid"] = False
            
        if not model.api_endpoint:
            result["errors"].append("API接口地址不能为空")
            result["is_valid"] = False
            
        # 检查API密钥配置
        if model.api_key_name:
            api_key = AIServiceUtils.get_api_key_from_env(model.api_key_name)
            if not api_key:
                result["errors"].append(f"环境变量 {model.api_key_name} 未设置或无效")
                result["is_valid"] = False
        else:
            result["warnings"].append("未配置API密钥环境变量名")
            
        # 检查模型参数范围
        if model.temperature < 0 or model.temperature > 2:
            result["errors"].append("温度参数必须在0-2之间")
            result["is_valid"] = False
            
        if model.top_p < 0 or model.top_p > 1:
            result["errors"].append("top_p参数必须在0-1之间")
            result["is_valid"] = False
            
        # 检查限制配置
        if model.rate_limit_rpm <= 0:
            result["errors"].append("每分钟请求数限制必须大于0")
            result["is_valid"] = False
            
        if model.timeout_seconds <= 0:
            result["errors"].append("超时时间必须大于0")
            result["is_valid"] = False
            
        # 检查成本配置
        if model.cost_per_1k_input_tokens < 0:
            result["warnings"].append("输入token成本为负数，请检查配置")
            
        if model.cost_per_1k_output_tokens < 0:
            result["warnings"].append("输出token成本为负数，请检查配置")
            
        return result
    
    @staticmethod
    def get_model_with_api_key(db: Session, model_id: int) -> Optional[Dict[str, Any]]:
        """
        获取模型配置并解析API密钥
        
        Args:
            db: 数据库会话
            model_id: 模型ID
            
        Returns:
            包含模型配置和API密钥的字典
        """
        model = db.query(AIModel).filter(AIModel.id == model_id).first()
        if not model:
            return None
            
        # 构建返回数据
        model_data = {
            "id": model.id,
            "model_name": model.model_name,
            "api_endpoint": model.api_endpoint,
            "api_key_name": model.api_key_name,
            "api_key": None,
            "model_params": model.model_params,
            "max_tokens": model.max_tokens,
            "temperature": float(model.temperature) if model.temperature is not None else 0.7,
            "top_p": float(model.top_p) if model.top_p is not None else 0.9,
            "frequency_penalty": float(model.frequency_penalty) if model.frequency_penalty is not None else 0.0,
            "presence_penalty": float(model.presence_penalty) if model.presence_penalty is not None else 0.0,
            "timeout_seconds": model.timeout_seconds,
            "max_retries": model.max_retries,
            "status": model.status,
            "is_active": model.status == "active"
        }
        
        # 获取API密钥
        if model.api_key_name:
            api_key = AIServiceUtils.get_api_key_from_env(model.api_key_name)
            model_data["api_key"] = api_key
            model_data["api_key_available"] = api_key is not None
        else:
            model_data["api_key_available"] = False
            
        return model_data
    
    @staticmethod
    def test_model_connection(model_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        测试模型连接
        
        Args:
            model_data: 模型配置数据
            
        Returns:
            测试结果字典
        """
        result = {
            "success": False,
            "message": "",
            "latency_ms": None,
            "error": None
        }
        
        try:
            import time
            import aiohttp
            import asyncio
            
            async def test_connection():
                start_time = time.time()
                
                if not model_data.get("api_key"):
                    return {
                        "success": False,
                        "message": "API密钥未配置",
                        "error": "Missing API key"
                    }
                
                headers = {
                    "Authorization": f"Bearer {model_data['api_key']}",
                    "Content-Type": "application/json"
                }
                
                # 构建测试请求
                test_data = {
                    "model": model_data["model_name"],
                    "messages": [{"role": "user", "content": "Hello"}],
                    "max_tokens": 10,
                    "temperature": 0.1
                }
                
                timeout = aiohttp.ClientTimeout(total=model_data.get("timeout_seconds", 30))
                
                async with aiohttp.ClientSession(timeout=timeout) as session:
                    async with session.post(
                        model_data["api_endpoint"],
                        headers=headers,
                        json=test_data
                    ) as response:
                        end_time = time.time()
                        latency_ms = int((end_time - start_time) * 1000)
                        
                        if response.status == 200:
                            return {
                                "success": True,
                                "message": "连接测试成功",
                                "latency_ms": latency_ms
                            }
                        else:
                            error_text = await response.text()
                            return {
                                "success": False,
                                "message": f"连接测试失败: HTTP {response.status}",
                                "error": error_text,
                                "latency_ms": latency_ms
                            }
            
            # 运行异步测试
            result = asyncio.run(test_connection())
            
        except Exception as e:
            result = {
                "success": False,
                "message": f"连接测试异常: {str(e)}",
                "error": str(e)
            }
            
        return result
    
    @staticmethod
    def get_env_var_suggestions(provider: str) -> Dict[str, str]:
        """
        根据提供商获取环境变量建议
        
        Args:
            provider: 模型提供商
            
        Returns:
            环境变量建议字典
        """
        suggestions = {
            "deepseek": {
                "api_key_name": "DEEPSEEK_API_KEY",
                "api_endpoint": "https://api.deepseek.com/v1/chat/completions",
                "description": "DeepSeek API配置"
            },
            "openai": {
                "api_key_name": "OPENAI_API_KEY", 
                "api_endpoint": "https://api.openai.com/v1/chat/completions",
                "description": "OpenAI API配置"
            },
            "zhipu": {
                "api_key_name": "ZHIPU_API_KEY",
                "api_endpoint": "https://open.bigmodel.cn/api/paas/v4/chat/completions",
                "description": "智谱AI API配置"
            },
            "qwen": {
                "api_key_name": "QWEN_API_KEY",
                "api_endpoint": "https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation",
                "description": "通义千问API配置"
            },
            "baidu": {
                "api_key_name": "BAIDU_API_KEY",
                "api_endpoint": "https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/completions",
                "description": "百度文心一言API配置"
            }
        }
        
        return suggestions.get(provider.lower(), {
            "api_key_name": f"{provider.upper()}_API_KEY",
            "api_endpoint": "",
            "description": f"{provider} API配置"
        }) 