"""
AI服务数据模型
包含AI模型管理相关的数据表定义
"""

from datetime import datetime
from typing import Dict, Any, Optional
from decimal import Decimal
from sqlalchemy import Column, Integer, String, Text, Boolean, DECIMAL, TIMESTAMP, ForeignKey, BigInteger, JSON, Index
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from enum import Enum

# 导入基类
from ...core.database import Base


class ModelType(str, Enum):
    """模型类型枚举"""
    LLM = "llm"  # 大语言模型
    CHAT = "chat"  # 聊天模型
    CLASSIFICATION = "classification"  # 分类模型
    NER = "ner"  # 命名实体识别
    SENTIMENT = "sentiment"  # 情感分析
    EMBEDDING = "embedding"  # 嵌入模型
    RERANK = "rerank"  # 重排序模型
    IMAGE = "image"  # 图像模型
    AUDIO = "audio"  # 音频模型


class ModelProvider(str, Enum):
    """模型提供商枚举"""
    DEEPSEEK = "deepseek"
    OPENAI = "openai"
    ANTHROPIC = "anthropic"
    ZHIPU = "zhipu"
    QWEN = "qwen"
    BAIDU = "baidu"
    LOCAL = "local"
    CUSTOM = "custom"


class ModelStatus(str, Enum):
    """模型状态枚举"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    TESTING = "testing"
    DEPRECATED = "deprecated"


class AIModel(Base):
    """
    AI模型表
    管理系统使用的AI模型信息
    """
    __tablename__ = "ai_models"
    
    id = Column(BigInteger, primary_key=True, index=True, comment="模型唯一标识符")
    
    # 基本信息
    model_name = Column(String(100), nullable=False, unique=True, comment="模型名称")
    display_name = Column(String(200), comment="显示名称")
    description = Column(Text, comment="模型描述")
    model_type = Column(String(50), nullable=False, comment="模型类型")
    provider = Column(String(50), nullable=False, comment="模型提供商")
    model_version = Column(String(50), nullable=False, comment="模型版本")
    
    # API配置
    api_endpoint = Column(String(500), comment="API接口地址")
    api_key_name = Column(String(100), comment="API密钥环境变量名")
    model_params = Column(JSON, comment="模型配置参数")
    
    # 模型参数
    max_tokens = Column(Integer, default=4096, comment="最大token数")
    temperature = Column(DECIMAL(3, 2), default=0.7, comment="温度参数")
    top_p = Column(DECIMAL(3, 2), default=0.9, comment="top_p参数")
    frequency_penalty = Column(DECIMAL(3, 2), default=0.0, comment="频率惩罚")
    presence_penalty = Column(DECIMAL(3, 2), default=0.0, comment="存在惩罚")
    
    # 限制配置
    rate_limit_rpm = Column(Integer, default=60, comment="每分钟请求数限制")
    rate_limit_tpm = Column(Integer, default=100000, comment="每分钟token数限制")
    timeout_seconds = Column(Integer, default=30, comment="超时时间(秒)")
    max_retries = Column(Integer, default=3, comment="最大重试次数")
    
    # 成本配置
    cost_per_1k_input_tokens = Column(DECIMAL(10, 6), default=0.0, comment="每1K输入token成本")
    cost_per_1k_output_tokens = Column(DECIMAL(10, 6), default=0.0, comment="每1K输出token成本")
    cost_per_request = Column(DECIMAL(10, 6), default=0.0, comment="每次请求成本")
    
    # 性能指标
    accuracy = Column(DECIMAL(5, 4), comment="准确率")
    latency_ms = Column(Integer, comment="平均延迟(毫秒)")
    availability = Column(DECIMAL(5, 4), comment="可用性")
    
    # 状态管理
    status = Column(String(20), default=ModelStatus.ACTIVE, comment="模型状态")
    is_default = Column(Boolean, default=False, comment="是否为默认模型")
    priority = Column(Integer, default=5, comment="优先级(1-10,数值越小优先级越高)")
    
    # 使用统计
    total_requests = Column(BigInteger, default=0, comment="总请求数")
    success_requests = Column(BigInteger, default=0, comment="成功请求数")
    failed_requests = Column(BigInteger, default=0, comment="失败请求数")
    total_input_tokens = Column(BigInteger, default=0, comment="总输入token数")
    total_output_tokens = Column(BigInteger, default=0, comment="总输出token数")
    total_cost = Column(DECIMAL(12, 4), default=0.0, comment="总成本")
    
    # 时间戳
    created_at = Column(TIMESTAMP, server_default=func.now(), comment="创建时间")
    updated_at = Column(TIMESTAMP, server_default=func.now(), onupdate=func.now(), comment="更新时间")
    last_used_at = Column(TIMESTAMP, comment="最后使用时间")
    
    # 关系
    metrics = relationship("ModelMetrics", back_populates="ai_model", cascade="all, delete-orphan")
    
    # 索引
    __table_args__ = (
        Index("idx_ai_model_type", "model_type"),
        Index("idx_ai_model_provider", "provider"),
        Index("idx_ai_model_status", "status"),
        Index("idx_ai_model_default", "is_default"),
        Index("idx_ai_model_priority", "priority"),
        Index("idx_ai_model_performance", "accuracy", "latency_ms"),
        Index("idx_ai_model_usage", "total_requests", "success_requests"),
    )

    @property
    def success_rate(self) -> float:
        """计算成功率"""
        if self.total_requests == 0:
            return 0.0
        return float(self.success_requests / self.total_requests)
    
    @property
    def failure_rate(self) -> float:
        """计算失败率"""
        if self.total_requests == 0:
            return 0.0
        return float(self.failed_requests / self.total_requests)
    
    @property
    def average_cost_per_request(self) -> Decimal:
        """计算平均每次请求成本"""
        if self.total_requests == 0:
            return Decimal("0.0")
        return self.total_cost / self.total_requests

    def update_usage_stats(
        self, 
        success: bool, 
        input_tokens: int = 0, 
        output_tokens: int = 0,
        latency_ms: int = 0
    ):
        """更新使用统计"""
        self.total_requests += 1
        if success:
            self.success_requests += 1
        else:
            self.failed_requests += 1
        
        self.total_input_tokens += input_tokens
        self.total_output_tokens += output_tokens
        
        # 计算成本
        input_cost = (input_tokens / 1000) * float(self.cost_per_1k_input_tokens or 0)
        output_cost = (output_tokens / 1000) * float(self.cost_per_1k_output_tokens or 0)
        request_cost = float(self.cost_per_request or 0)
        total_request_cost = Decimal(str(input_cost + output_cost + request_cost))
        self.total_cost += total_request_cost
        
        # 更新延迟（移动平均）
        if latency_ms > 0:
            if self.latency_ms is None:
                self.latency_ms = latency_ms
            else:
                # 使用指数移动平均
                alpha = 0.1
                self.latency_ms = int(alpha * latency_ms + (1 - alpha) * self.latency_ms)
        
        self.last_used_at = func.now()


class ModelMetrics(Base):
    """
    模型指标表
    存储模型的详细性能指标
    """
    __tablename__ = "model_metrics"
    
    id = Column(BigInteger, primary_key=True, index=True, comment="指标唯一标识符")
    model_id = Column(
        BigInteger,
        ForeignKey("ai_models.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        comment="关联的AI模型ID"
    )
    
    # 时间信息
    metric_date = Column(TIMESTAMP, nullable=False, comment="指标日期")
    time_window = Column(String(20), default="hour", comment="时间窗口：hour/day/week/month")
    
    # 基础指标
    total_requests = Column(Integer, default=0, comment="请求总数")
    success_requests = Column(Integer, default=0, comment="成功请求数")
    failed_requests = Column(Integer, default=0, comment="失败请求数")
    
    # 性能指标
    avg_latency_ms = Column(Integer, comment="平均延迟(毫秒)")
    p95_latency_ms = Column(Integer, comment="95分位延迟(毫秒)")
    p99_latency_ms = Column(Integer, comment="99分位延迟(毫秒)")
    
    # token统计
    total_input_tokens = Column(BigInteger, default=0, comment="输入token总数")
    total_output_tokens = Column(BigInteger, default=0, comment="输出token总数")
    avg_input_tokens = Column(Integer, comment="平均输入token数")
    avg_output_tokens = Column(Integer, comment="平均输出token数")
    
    # 成本统计
    total_cost = Column(DECIMAL(12, 4), default=0.0, comment="总成本")
    avg_cost_per_request = Column(DECIMAL(8, 4), comment="平均每次请求成本")
    
    # 质量指标
    accuracy_score = Column(DECIMAL(5, 4), comment="准确率评分")
    quality_score = Column(DECIMAL(5, 4), comment="质量评分")
    user_satisfaction = Column(DECIMAL(3, 2), comment="用户满意度")
    
    # 错误统计
    timeout_errors = Column(Integer, default=0, comment="超时错误数")
    rate_limit_errors = Column(Integer, default=0, comment="限流错误数")
    api_errors = Column(Integer, default=0, comment="API错误数")
    other_errors = Column(Integer, default=0, comment="其他错误数")
    
    # 扩展信息
    extra_metadata = Column(JSON, comment="扩展元数据")
    
    # 时间戳
    created_at = Column(TIMESTAMP, server_default=func.now(), comment="创建时间")
    
    # 关系
    ai_model = relationship("AIModel", back_populates="metrics")
    
    # 索引
    __table_args__ = (
        Index("idx_model_metrics_date", "metric_date"),
        Index("idx_model_metrics_window", "time_window"),
        Index("idx_model_metrics_model_date", "model_id", "metric_date"),
        Index("idx_model_metrics_performance", "avg_latency_ms", "total_requests"),
        # 确保同一模型的同一时间窗口只有一条记录
        Index("idx_model_metrics_unique", "model_id", "metric_date", "time_window", unique=True),
    )

    @property
    def success_rate(self) -> float:
        """计算成功率"""
        if self.total_requests == 0:
            return 0.0
        return float(self.success_requests / self.total_requests)
    
    @property
    def error_rate(self) -> float:
        """计算错误率"""
        if self.total_requests == 0:
            return 0.0
        return float(self.failed_requests / self.total_requests) 