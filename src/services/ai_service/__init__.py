"""
AI服务模块
提供AI模型管理功能
"""

from .models import AIModel, ModelMetrics
from .schemas import (
    AIModelCreate, AIModelUpdate, AIModelResponse,
    ModelMetricsCreate, ModelMetricsResponse,
    ModelListResponse, ModelPerformanceResponse
)
from .service import AIModelService, ModelMetricsService
from .dependencies import get_ai_model_service, get_model_metrics_service
from .admin_router import router as admin_router
from .utils import AIServiceUtils

__all__ = [
    # Models
    "AIModel",
    "ModelMetrics",
    # Schemas
    "AIModelCreate",
    "AIModelUpdate", 
    "AIModelResponse",
    "ModelMetricsCreate",
    "ModelMetricsResponse",
    "ModelListResponse",
    "ModelPerformanceResponse",
    # Services
    "AIModelService",
    "ModelMetricsService",
    # Dependencies
    "get_ai_model_service",
    "get_model_metrics_service",
    # Router
    "admin_router",
    # Utils
    "AIServiceUtils",
] 