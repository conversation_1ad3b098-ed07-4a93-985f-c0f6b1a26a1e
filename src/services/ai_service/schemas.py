"""
AI服务API接口模型定义
包含请求和响应的数据结构
"""

from datetime import datetime
from typing import Dict, List, Optional, Any
from decimal import Decimal
from pydantic import BaseModel, Field, validator
from .models import ModelType, ModelProvider, ModelStatus


# ==================== 基础模型 ====================

class AIModelBase(BaseModel):
    """AI模型基础模型"""
    model_name: str = Field(..., description="模型名称", min_length=1, max_length=100)
    display_name: Optional[str] = Field(None, description="显示名称", max_length=200)
    description: Optional[str] = Field(None, description="模型描述")
    model_type: ModelType = Field(..., description="模型类型")
    provider: ModelProvider = Field(..., description="模型提供商")
    model_version: str = Field(..., description="模型版本", min_length=1, max_length=50)
    
    class Config:
        protected_namespaces = ()


class AIModelCreate(AIModelBase):
    """创建AI模型请求"""
    # API配置
    api_endpoint: Optional[str] = Field(None, description="API接口地址", max_length=500)
    api_key_name: Optional[str] = Field(None, description="API密钥环境变量名", max_length=100)
    model_params: Optional[Dict[str, Any]] = Field(None, description="模型配置参数")
    
    # 模型参数
    max_tokens: int = Field(4096, description="最大token数", ge=1, le=128000)
    temperature: Decimal = Field(0.7, description="温度参数", ge=0.0, le=2.0)
    top_p: Decimal = Field(0.9, description="top_p参数", ge=0.0, le=1.0)
    frequency_penalty: Decimal = Field(0.0, description="频率惩罚", ge=-2.0, le=2.0)
    presence_penalty: Decimal = Field(0.0, description="存在惩罚", ge=-2.0, le=2.0)
    
    # 限制配置
    rate_limit_rpm: int = Field(60, description="每分钟请求数限制", ge=1, le=10000)
    rate_limit_tpm: int = Field(100000, description="每分钟token数限制", ge=1000)
    timeout_seconds: int = Field(30, description="超时时间(秒)", ge=1, le=300)
    max_retries: int = Field(3, description="最大重试次数", ge=0, le=10)
    
    # 成本配置
    cost_per_1k_input_tokens: Decimal = Field(0.0, description="每1K输入token成本", ge=0.0)
    cost_per_1k_output_tokens: Decimal = Field(0.0, description="每1K输出token成本", ge=0.0)
    cost_per_request: Decimal = Field(0.0, description="每次请求成本", ge=0.0)
    
    # 状态管理
    status: ModelStatus = Field(ModelStatus.ACTIVE, description="模型状态")
    is_default: bool = Field(False, description="是否为默认模型")
    priority: int = Field(5, description="优先级(1-10,数值越小优先级越高)", ge=1, le=10)

    @validator('model_name')
    def validate_model_name(cls, v):
        """验证模型名称格式"""
        if not v.replace('_', '').replace('-', '').replace('.', '').isalnum():
            raise ValueError('模型名称只能包含字母、数字、下划线、连字符和点号')
        return v


class AIModelUpdate(BaseModel):
    """更新AI模型请求"""
    display_name: Optional[str] = Field(None, description="显示名称", max_length=200)
    description: Optional[str] = Field(None, description="模型描述")
    model_version: Optional[str] = Field(None, description="模型版本", max_length=50)
    
    # API配置
    api_endpoint: Optional[str] = Field(None, description="API接口地址", max_length=500)
    api_key_name: Optional[str] = Field(None, description="API密钥环境变量名", max_length=100)
    model_params: Optional[Dict[str, Any]] = Field(None, description="模型配置参数")
    
    # 模型参数
    max_tokens: Optional[int] = Field(None, description="最大token数", ge=1, le=128000)
    temperature: Optional[Decimal] = Field(None, description="温度参数", ge=0.0, le=2.0)
    top_p: Optional[Decimal] = Field(None, description="top_p参数", ge=0.0, le=1.0)
    frequency_penalty: Optional[Decimal] = Field(None, description="频率惩罚", ge=-2.0, le=2.0)
    presence_penalty: Optional[Decimal] = Field(None, description="存在惩罚", ge=-2.0, le=2.0)
    
    # 限制配置
    rate_limit_rpm: Optional[int] = Field(None, description="每分钟请求数限制", ge=1, le=10000)
    rate_limit_tpm: Optional[int] = Field(None, description="每分钟token数限制", ge=1000)
    timeout_seconds: Optional[int] = Field(None, description="超时时间(秒)", ge=1, le=300)
    max_retries: Optional[int] = Field(None, description="最大重试次数", ge=0, le=10)
    
    # 成本配置
    cost_per_1k_input_tokens: Optional[Decimal] = Field(None, description="每1K输入token成本", ge=0.0)
    cost_per_1k_output_tokens: Optional[Decimal] = Field(None, description="每1K输出token成本", ge=0.0)
    cost_per_request: Optional[Decimal] = Field(None, description="每次请求成本", ge=0.0)
    
    # 状态管理
    status: Optional[ModelStatus] = Field(None, description="模型状态")
    is_default: Optional[bool] = Field(None, description="是否为默认模型")
    priority: Optional[int] = Field(None, description="优先级(1-10,数值越小优先级越高)", ge=1, le=10)


class AIModelResponse(AIModelBase):
    """AI模型响应"""
    id: int = Field(..., description="模型ID")
    
    # API配置
    api_endpoint: Optional[str] = Field(None, description="API接口地址")
    api_key_name: Optional[str] = Field(None, description="API密钥环境变量名")
    model_params: Optional[Dict[str, Any]] = Field(None, description="模型配置参数")
    
    # 模型参数
    max_tokens: Optional[int] = Field(4096, description="最大token数")
    temperature: Optional[Decimal] = Field(0.7, description="温度参数")
    top_p: Optional[Decimal] = Field(0.9, description="top_p参数")
    frequency_penalty: Optional[Decimal] = Field(0.0, description="频率惩罚")
    presence_penalty: Optional[Decimal] = Field(0.0, description="存在惩罚")
    
    # 限制配置
    rate_limit_rpm: Optional[int] = Field(60, description="每分钟请求数限制")
    rate_limit_tpm: Optional[int] = Field(100000, description="每分钟token数限制")
    timeout_seconds: Optional[int] = Field(30, description="超时时间(秒)")
    max_retries: Optional[int] = Field(3, description="最大重试次数")
    
    # 成本配置
    cost_per_1k_input_tokens: Decimal = Field(..., description="每1K输入token成本")
    cost_per_1k_output_tokens: Decimal = Field(..., description="每1K输出token成本")
    cost_per_request: Decimal = Field(..., description="每次请求成本")
    
    # 性能指标
    accuracy: Optional[Decimal] = Field(None, description="准确率")
    latency_ms: Optional[int] = Field(None, description="平均延迟(毫秒)")
    availability: Optional[Decimal] = Field(None, description="可用性")
    
    # 状态管理
    status: ModelStatus = Field(..., description="模型状态")
    is_default: bool = Field(..., description="是否为默认模型")
    priority: int = Field(..., description="优先级")
    
    # 使用统计
    total_requests: int = Field(..., description="总请求数")
    success_requests: int = Field(..., description="成功请求数")
    failed_requests: int = Field(..., description="失败请求数")
    total_input_tokens: int = Field(..., description="总输入token数")
    total_output_tokens: int = Field(..., description="总输出token数")
    total_cost: Decimal = Field(..., description="总成本")
    success_rate: float = Field(..., description="成功率")
    failure_rate: float = Field(..., description="失败率")
    average_cost_per_request: float = Field(..., description="平均每次请求成本")
    
    # 时间戳
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    last_used_at: Optional[datetime] = Field(None, description="最后使用时间")

    class Config:
        from_attributes = True


# ==================== 指标相关模型 ====================

class ModelMetricsCreate(BaseModel):
    """创建模型指标请求"""
    model_id: int = Field(..., description="模型ID")
    
    class Config:
        protected_namespaces = ()
    metric_date: datetime = Field(..., description="指标日期")
    time_window: str = Field("hour", description="时间窗口")
    
    # 基础指标
    total_requests: int = Field(0, description="请求总数", ge=0)
    success_requests: int = Field(0, description="成功请求数", ge=0)
    failed_requests: int = Field(0, description="失败请求数", ge=0)
    
    # 性能指标
    avg_latency_ms: Optional[int] = Field(None, description="平均延迟(毫秒)", ge=0)
    p95_latency_ms: Optional[int] = Field(None, description="95分位延迟(毫秒)", ge=0)
    p99_latency_ms: Optional[int] = Field(None, description="99分位延迟(毫秒)", ge=0)
    
    # token统计
    total_input_tokens: int = Field(0, description="输入token总数", ge=0)
    total_output_tokens: int = Field(0, description="输出token总数", ge=0)
    avg_input_tokens: Optional[int] = Field(None, description="平均输入token数", ge=0)
    avg_output_tokens: Optional[int] = Field(None, description="平均输出token数", ge=0)
    
    # 成本统计
    total_cost: Decimal = Field(0.0, description="总成本", ge=0.0)
    avg_cost_per_request: Optional[Decimal] = Field(None, description="平均每次请求成本", ge=0.0)
    
    # 质量指标
    accuracy_score: Optional[Decimal] = Field(None, description="准确率评分", ge=0.0, le=1.0)
    quality_score: Optional[Decimal] = Field(None, description="质量评分", ge=0.0, le=1.0)
    user_satisfaction: Optional[Decimal] = Field(None, description="用户满意度", ge=0.0, le=5.0)
    
    # 错误统计
    timeout_errors: int = Field(0, description="超时错误数", ge=0)
    rate_limit_errors: int = Field(0, description="限流错误数", ge=0)
    api_errors: int = Field(0, description="API错误数", ge=0)
    other_errors: int = Field(0, description="其他错误数", ge=0)
    
    # 扩展信息
    extra_metadata: Optional[Dict[str, Any]] = Field(None, description="扩展元数据")


class ModelMetricsResponse(BaseModel):
    """模型指标响应"""
    id: int = Field(..., description="指标ID")
    model_id: int = Field(..., description="模型ID")
    metric_date: datetime = Field(..., description="指标日期")
    time_window: str = Field(..., description="时间窗口")
    
    # 基础指标
    total_requests: int = Field(..., description="请求总数")
    success_requests: int = Field(..., description="成功请求数")
    failed_requests: int = Field(..., description="失败请求数")
    success_rate: float = Field(..., description="成功率")
    error_rate: float = Field(..., description="错误率")
    
    # 性能指标
    avg_latency_ms: Optional[int] = Field(None, description="平均延迟(毫秒)")
    p95_latency_ms: Optional[int] = Field(None, description="95分位延迟(毫秒)")
    p99_latency_ms: Optional[int] = Field(None, description="99分位延迟(毫秒)")
    
    # token统计
    total_input_tokens: int = Field(..., description="输入token总数")
    total_output_tokens: int = Field(..., description="输出token总数")
    avg_input_tokens: Optional[int] = Field(None, description="平均输入token数")
    avg_output_tokens: Optional[int] = Field(None, description="平均输出token数")
    
    # 成本统计
    total_cost: Decimal = Field(..., description="总成本")
    avg_cost_per_request: Optional[Decimal] = Field(None, description="平均每次请求成本")
    
    # 质量指标
    accuracy_score: Optional[Decimal] = Field(None, description="准确率评分")
    quality_score: Optional[Decimal] = Field(None, description="质量评分")
    user_satisfaction: Optional[Decimal] = Field(None, description="用户满意度")
    
    # 错误统计
    timeout_errors: int = Field(..., description="超时错误数")
    rate_limit_errors: int = Field(..., description="限流错误数")
    api_errors: int = Field(..., description="API错误数")
    other_errors: int = Field(..., description="其他错误数")
    
    # 扩展信息
    extra_metadata: Optional[Dict[str, Any]] = Field(None, description="扩展元数据")
    
    # 时间戳
    created_at: datetime = Field(..., description="创建时间")

    class Config:
        from_attributes = True


# ==================== 列表和查询模型 ====================

class ModelListQuery(BaseModel):
    """模型列表查询参数"""
    model_type: Optional[ModelType] = Field(None, description="模型类型过滤")
    
    class Config:
        protected_namespaces = ()
    provider: Optional[ModelProvider] = Field(None, description="模型提供商过滤")
    status: Optional[ModelStatus] = Field(None, description="模型状态过滤")
    is_default: Optional[bool] = Field(None, description="是否默认模型过滤")
    search: Optional[str] = Field(None, description="搜索关键词", max_length=100)


class ModelListResponse(BaseModel):
    """模型列表响应"""
    models: List[AIModelResponse] = Field(..., description="模型列表")
    total: int = Field(..., description="总数量")
    page: int = Field(..., description="当前页码")
    page_size: int = Field(..., description="每页大小")
    total_pages: int = Field(..., description="总页数")


class ModelPerformanceResponse(BaseModel):
    """模型性能响应"""
    model_id: int = Field(..., description="模型ID")
    model_name: str = Field(..., description="模型名称")
    
    class Config:
        protected_namespaces = ()
    
    # 性能概览
    success_rate: float = Field(..., description="成功率")
    avg_latency_ms: Optional[int] = Field(None, description="平均延迟")
    total_cost: Decimal = Field(..., description="总成本")
    total_requests: int = Field(..., description="总请求数")
    
    # 时间序列数据
    metrics: List[ModelMetricsResponse] = Field(..., description="指标数据")


# ==================== 操作相关模型 ====================

class ModelStatusUpdate(BaseModel):
    """模型状态更新"""
    status: ModelStatus = Field(..., description="新状态")


class ModelDefaultUpdate(BaseModel):
    """模型默认设置更新"""
    is_default: bool = Field(..., description="是否设为默认")


class ModelUsageStats(BaseModel):
    """模型使用统计"""
    success: bool = Field(..., description="是否成功")
    input_tokens: int = Field(0, description="输入token数", ge=0)
    output_tokens: int = Field(0, description="输出token数", ge=0)
    latency_ms: int = Field(0, description="延迟(毫秒)", ge=0)


class ModelBatchOperation(BaseModel):
    """批量操作请求"""
    model_ids: List[int] = Field(..., description="模型ID列表", min_items=1)
    
    class Config:
        protected_namespaces = ()
    operation: str = Field(..., description="操作类型：activate/deactivate/delete")


class ModelBatchOperationResponse(BaseModel):
    """批量操作响应"""
    success_count: int = Field(..., description="成功数量")
    failed_count: int = Field(..., description="失败数量")
    success_ids: List[int] = Field(..., description="成功的模型ID列表")
    failed_ids: List[int] = Field(..., description="失败的模型ID列表")
    errors: List[str] = Field(..., description="错误信息列表")


# ==================== 系统信息模型 ====================

class SystemModelInfo(BaseModel):
    """系统模型信息"""
    total_models: int = Field(..., description="总模型数")
    active_models: int = Field(..., description="活跃模型数")
    default_models: Dict[str, str] = Field(..., description="各类型默认模型")
    provider_distribution: Dict[str, int] = Field(..., description="提供商分布")
    type_distribution: Dict[str, int] = Field(..., description="类型分布")
    total_requests_today: int = Field(..., description="今日总请求数")
    total_cost_today: Decimal = Field(..., description="今日总成本")


class ModelComparisonRequest(BaseModel):
    """模型对比请求"""
    model_ids: List[int] = Field(..., description="模型ID列表", min_items=2, max_items=5)
    
    class Config:
        protected_namespaces = ()
    time_range: str = Field("7d", description="时间范围：1d/7d/30d/90d")


class ModelComparisonResponse(BaseModel):
    """模型对比响应"""
    models: List[Dict[str, Any]] = Field(..., description="模型对比数据")
    comparison_date_range: Dict[str, datetime] = Field(..., description="对比时间范围") 