"""
AI模型管理B端API路由
定义AI模型管理相关的HTTP接口，带权限验证
"""

from datetime import datetime
from typing import Annotated, List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query, Path, status
from fastapi.responses import J<PERSON><PERSON>esponse
from sqlalchemy.orm import Session

from ...core.database import get_db
from ..permission_service.dependencies import require_permission
from ..user_service.models import User
from .schemas import (
    AIModelCreate, AIModelUpdate, AIModelResponse,
    ModelListQuery, ModelListResponse, ModelPerformanceResponse,
    ModelMetricsCreate, ModelMetricsResponse,
    ModelStatusUpdate, ModelDefaultUpdate, ModelUsageStats,
    ModelBatchOperation, ModelBatchOperationResponse,
    SystemModelInfo, ModelComparisonRequest, ModelComparisonResponse
)
from .models import ModelType, ModelProvider, ModelStatus
from .service import AIModelService, ModelMetricsService
from .dependencies import get_ai_model_service, get_model_metrics_service

router = APIRouter()


# ==================== AI模型管理接口 ====================

@router.post(
    "/models",
    response_model=AIModelResponse,
    status_code=status.HTTP_201_CREATED,
    summary="创建AI模型",
    description="创建新的AI模型配置",
)
async def create_ai_model(
    model_data: AIModelCreate,
    current_user: Annotated[User, Depends(require_permission("ai_model.model.create"))],
    service: Annotated[AIModelService, Depends(get_ai_model_service)]
):
    """创建新的AI模型"""
    try:
        return service.create_model(model_data)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"创建AI模型失败: {str(e)}"
        )


@router.get(
    "/models",
    response_model=ModelListResponse,
    summary="获取AI模型列表",
    description="分页获取AI模型列表，支持过滤、搜索",
)
async def list_ai_models(
    current_user: Annotated[User, Depends(require_permission("ai_model.list.read"))],
    service: Annotated[AIModelService, Depends(get_ai_model_service)],
    model_type: Optional[ModelType] = Query(None, description="模型类型过滤"),
    provider: Optional[ModelProvider] = Query(None, description="模型提供商过滤"),
    status: Optional[ModelStatus] = Query(None, description="模型状态过滤"),
    is_default: Optional[bool] = Query(None, description="是否默认模型过滤"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页大小"),
):
    """获取AI模型列表，支持过滤、搜索和分页"""
    try:
        query = ModelListQuery(
            model_type=model_type,
            provider=provider,
            status=status,
            is_default=is_default,
            search=search
        )
        
        models, total = service.list_models(query, page, page_size)
        total_pages = (total + page_size - 1) // page_size
        
        return ModelListResponse(
            models=models,
            total=total,
            page=page,
            page_size=page_size,
            total_pages=total_pages
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取模型列表失败: {str(e)}"
        )


@router.get(
    "/models/{model_id}",
    response_model=AIModelResponse,
    summary="获取AI模型详情",
    description="根据模型ID获取详细信息",
)
async def get_ai_model(
    model_id: int,
    current_user: Annotated[User, Depends(require_permission("ai_model.model.read"))],
    service: Annotated[AIModelService, Depends(get_ai_model_service)]
):
    """获取指定AI模型的详细信息"""
    try:
        return service.get_model(model_id)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取模型详情失败: {str(e)}"
        )


@router.get(
    "/models/by-name/{model_name}",
    response_model=AIModelResponse,
    summary="通过名称获取AI模型",
    description="根据模型名称获取模型详情",
)
async def get_ai_model_by_name(
    model_name: str,
    current_user: Annotated[User, Depends(require_permission("ai_model.model.read"))],
    service: Annotated[AIModelService, Depends(get_ai_model_service)]
):
    """通过模型名称获取AI模型详情"""
    try:
        return service.get_model_by_name(model_name)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取模型详情失败: {str(e)}"
        )


@router.put(
    "/models/{model_id}",
    response_model=AIModelResponse,
    summary="更新AI模型",
    description="更新指定AI模型的配置",
)
async def update_ai_model(
    model_id: int,
    model_data: AIModelUpdate,
    current_user: Annotated[User, Depends(require_permission("ai_model.model.update"))],
    service: Annotated[AIModelService, Depends(get_ai_model_service)]
):
    """更新指定AI模型的配置"""
    try:
        return service.update_model(model_id, model_data)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新AI模型失败: {str(e)}"
        )


@router.delete(
    "/models/{model_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="删除AI模型",
    description="删除指定的AI模型",
)
async def delete_ai_model(
    model_id: int,
    current_user: Annotated[User, Depends(require_permission("ai_model.model.delete"))],
    service: Annotated[AIModelService, Depends(get_ai_model_service)]
):
    """删除指定的AI模型"""
    try:
        success = service.delete_model(model_id)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="删除AI模型失败"
            )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除AI模型失败: {str(e)}"
        )


# ==================== 模型类型和状态管理 ====================

@router.get(
    "/models/type/{model_type}/default",
    response_model=AIModelResponse,
    summary="获取默认模型",
    description="获取指定类型的默认模型",
)
async def get_default_model(
    model_type: ModelType,
    current_user: Annotated[User, Depends(require_permission("ai_model.model.read"))],
    service: Annotated[AIModelService, Depends(get_ai_model_service)]
):
    """获取指定类型的默认模型"""
    try:
        default_model = service.get_default_model(model_type)
        if not default_model:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"未找到类型为 {model_type.value} 的默认模型"
            )
        return default_model
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取默认模型失败: {str(e)}"
        )


@router.get(
    "/models/type/{model_type}/available",
    response_model=List[AIModelResponse],
    summary="获取可用模型",
    description="获取指定类型的所有可用模型",
)
async def get_available_models(
    model_type: ModelType,
    current_user: Annotated[User, Depends(require_permission("ai_model.list.read"))],
    service: Annotated[AIModelService, Depends(get_ai_model_service)]
):
    """获取指定类型的所有可用模型"""
    try:
        return service.get_available_models(model_type)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取可用模型失败: {str(e)}"
        )


@router.patch(
    "/models/{model_id}/status",
    response_model=AIModelResponse,
    summary="更新模型状态",
    description="更新模型状态（激活/停用/测试/废弃）",
)
async def update_model_status(
    model_id: int,
    status_update: ModelStatusUpdate,
    current_user: Annotated[User, Depends(require_permission("ai_model.status.update"))],
    service: Annotated[AIModelService, Depends(get_ai_model_service)]
):
    """更新模型状态（激活/停用/测试/废弃）"""
    try:
        return service.update_model_status(model_id, status_update.status)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新模型状态失败: {str(e)}"
        )


@router.patch(
    "/models/{model_id}/default",
    response_model=AIModelResponse,
    summary="设置默认模型",
    description="设置或取消模型为默认模型",
)
async def set_default_model(
    model_id: int,
    default_update: ModelDefaultUpdate,
    current_user: Annotated[User, Depends(require_permission("ai_model.default.update"))],
    service: Annotated[AIModelService, Depends(get_ai_model_service)]
):
    """设置或取消模型为默认模型"""
    try:
        if default_update.is_default:
            return service.set_default_model(model_id)
        else:
            # 取消默认状态
            model_data = AIModelUpdate(is_default=False)
            return service.update_model(model_id, model_data)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"设置默认模型失败: {str(e)}"
        )


@router.patch(
    "/models/{model_id}/usage",
    response_model=AIModelResponse,
    summary="更新使用统计",
    description="更新模型使用统计信息",
)
async def update_usage_stats(
    model_id: int,
    stats: ModelUsageStats,
    current_user: Annotated[User, Depends(require_permission("ai_model.model.update"))],
    service: Annotated[AIModelService, Depends(get_ai_model_service)]
):
    """更新模型使用统计信息"""
    try:
        return service.update_usage_stats(model_id, stats)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新使用统计失败: {str(e)}"
        )


# ==================== 批量操作 ====================

@router.post(
    "/models/batch/status",
    response_model=ModelBatchOperationResponse,
    summary="批量更新状态",
    description="批量更新模型状态",
)
async def batch_update_status(
    operation: ModelBatchOperation,
    current_user: Annotated[User, Depends(require_permission("ai_model.status.update"))],
    service: Annotated[AIModelService, Depends(get_ai_model_service)]
):
    """批量更新模型状态"""
    try:
        if operation.operation == "activate":
            status_value = ModelStatus.ACTIVE
        elif operation.operation == "deactivate":
            status_value = ModelStatus.INACTIVE
        elif operation.operation == "deprecate":
            status_value = ModelStatus.DEPRECATED
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"不支持的操作类型: {operation.operation}"
            )
        
        result = service.batch_update_status(operation.model_ids, status_value)
        return ModelBatchOperationResponse(**result)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"批量更新状态失败: {str(e)}"
        )


@router.post(
    "/models/batch/delete",
    response_model=ModelBatchOperationResponse,
    summary="批量删除模型",
    description="批量删除模型",
)
async def batch_delete_models(
    operation: ModelBatchOperation,
    current_user: Annotated[User, Depends(require_permission("ai_model.model.delete"))],
    service: Annotated[AIModelService, Depends(get_ai_model_service)]
):
    """批量删除模型"""
    try:
        if operation.operation != "delete":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="操作类型必须为 delete"
            )
        
        success_ids = []
        failed_ids = []
        errors = []
        
        for model_id in operation.model_ids:
            try:
                service.delete_model(model_id)
                success_ids.append(model_id)
            except Exception as e:
                failed_ids.append(model_id)
                errors.append(f"模型ID {model_id}: {str(e)}")
        
        return ModelBatchOperationResponse(
            success_count=len(success_ids),
            failed_count=len(failed_ids),
            success_ids=success_ids,
            failed_ids=failed_ids,
            errors=errors
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"批量删除模型失败: {str(e)}"
        )


# ==================== 系统信息和分析 ====================

@router.get(
    "/system/info",
    response_model=SystemModelInfo,
    summary="获取系统信息",
    description="获取系统模型统计信息",
)
async def get_system_info(
    current_user: Annotated[User, Depends(require_permission("ai_model.analytics.read"))],
    service: Annotated[AIModelService, Depends(get_ai_model_service)]
):
    """获取系统模型统计信息"""
    try:
        return service.get_system_info()
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取系统信息失败: {str(e)}"
        )


@router.post(
    "/models/compare",
    response_model=ModelComparisonResponse,
    summary="模型对比",
    description="对比多个模型的性能",
)
async def compare_models(
    request: ModelComparisonRequest,
    current_user: Annotated[User, Depends(require_permission("ai_model.analytics.read"))],
    ai_service: Annotated[AIModelService, Depends(get_ai_model_service)],
    metrics_service: Annotated[ModelMetricsService, Depends(get_model_metrics_service)]
):
    """对比多个模型的性能"""
    try:
        comparison_data = []
        
        # 根据时间范围确定天数
        days_mapping = {
            "1d": 1,
            "7d": 7,
            "30d": 30,
            "90d": 90
        }
        days = days_mapping.get(request.time_range, 7)
        
        for model_id in request.model_ids:
            try:
                # 获取模型信息
                model = ai_service.get_model(model_id)
                
                # 获取指标数据
                metrics = metrics_service.get_model_metrics(model_id, "day", days)
                
                # 计算平均指标
                if metrics:
                    avg_success_rate = sum(m.success_rate for m in metrics) / len(metrics)
                    avg_latency = sum(m.avg_latency_ms for m in metrics if m.avg_latency_ms) / len([m for m in metrics if m.avg_latency_ms]) if any(m.avg_latency_ms for m in metrics) else 0
                    total_cost = sum(m.total_cost for m in metrics)
                    total_requests = sum(m.total_requests for m in metrics)
                else:
                    avg_success_rate = model.success_rate
                    avg_latency = model.latency_ms or 0
                    total_cost = model.total_cost
                    total_requests = model.total_requests
                
                comparison_data.append({
                    "model_id": model_id,
                    "model_name": model.model_name,
                    "provider": model.provider,
                    "success_rate": avg_success_rate,
                    "avg_latency_ms": avg_latency,
                    "total_cost": float(total_cost),
                    "total_requests": total_requests,
                    "metrics_count": len(metrics)
                })
                
            except Exception as e:
                comparison_data.append({
                    "model_id": model_id,
                    "error": str(e)
                })
        
        from datetime import timedelta
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        
        return ModelComparisonResponse(
            models=comparison_data,
            comparison_date_range={
                "start_date": start_date,
                "end_date": end_date
            }
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"模型对比失败: {str(e)}"
        )


# ==================== 模型指标管理 ====================

@router.post(
    "/models/{model_id}/metrics",
    response_model=ModelMetricsResponse,
    status_code=status.HTTP_201_CREATED,
    summary="创建模型指标",
    description="为指定模型创建指标记录",
)
async def create_model_metrics(
    model_id: int,
    metrics_data: ModelMetricsCreate,
    current_user: Annotated[User, Depends(require_permission("ai_model.metrics.create"))],
    service: Annotated[ModelMetricsService, Depends(get_model_metrics_service)]
):
    """为指定模型创建指标记录"""
    try:
        # 确保指标数据中的模型ID与路径参数一致
        metrics_data.model_id = model_id
        return service.create_metrics(metrics_data)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建模型指标失败: {str(e)}"
        )


@router.get(
    "/models/{model_id}/metrics",
    response_model=List[ModelMetricsResponse],
    summary="获取模型指标",
    description="获取指定模型的指标数据",
)
async def get_model_metrics(
    model_id: int,
    current_user: Annotated[User, Depends(require_permission("ai_model.metrics.read"))],
    service: Annotated[ModelMetricsService, Depends(get_model_metrics_service)],
    time_window: str = Query("day", description="时间窗口：hour/day/week/month"),
    days: int = Query(7, ge=1, le=90, description="获取最近N天的数据")
):
    """获取指定模型的指标数据"""
    try:
        return service.get_model_metrics(model_id, time_window, days)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取模型指标失败: {str(e)}"
        )


@router.get(
    "/models/{model_id}/performance",
    response_model=ModelPerformanceResponse,
    summary="获取模型性能",
    description="获取模型性能概览",
)
async def get_model_performance(
    model_id: int,
    current_user: Annotated[User, Depends(require_permission("ai_model.analytics.read"))],
    ai_service: Annotated[AIModelService, Depends(get_ai_model_service)],
    metrics_service: Annotated[ModelMetricsService, Depends(get_model_metrics_service)],
    days: int = Query(7, ge=1, le=90, description="获取最近N天的数据")
):
    """获取模型性能概览"""
    try:
        # 获取模型基本信息
        model = ai_service.get_model(model_id)
        
        # 获取指标数据
        metrics = metrics_service.get_model_metrics(model_id, "day", days)
        
        return ModelPerformanceResponse(
            model_id=model_id,
            model_name=model.model_name,
            success_rate=model.success_rate,
            avg_latency_ms=model.latency_ms,
            total_cost=model.total_cost,
            total_requests=model.total_requests,
            metrics=metrics
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取模型性能失败: {str(e)}"
        )


# ==================== 健康检查 ====================

@router.get(
    "/health",
    summary="健康检查",
    description="AI服务健康检查",
)
async def health_check():
    """AI服务健康检查"""
    return JSONResponse(
        status_code=status.HTTP_200_OK,
        content={
            "status": "healthy",
            "service": "ai_service",
            "timestamp": datetime.now().isoformat()
        }
    ) 