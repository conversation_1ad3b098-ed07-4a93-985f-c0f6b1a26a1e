"""
AI服务依赖注入
提供服务实例的依赖注入
"""

from typing import Generator
from sqlalchemy.orm import Session
from fastapi import Depends

from .service import AIModelService, ModelMetricsService
from ...core.database import get_db


def get_ai_model_service(db: Session = Depends(get_db)) -> AIModelService:
    """获取AI模型服务实例"""
    return AIModelService(db)


def get_model_metrics_service(db: Session = Depends(get_db)) -> ModelMetricsService:
    """获取模型指标服务实例"""
    return ModelMetricsService(db) 