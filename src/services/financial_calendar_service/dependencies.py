"""
财经日历服务依赖注入
提供财经日历相关的依赖注入功能
"""

from fastapi import Depends, HTTPException, status
from sqlalchemy.orm import Session

from ...core.database import get_db
from .models import CountryRegion, EventType, FinancialEvent
from .service import FinancialCalendarService


def get_financial_calendar_service(
    db: Session = Depends(get_db),
) -> FinancialCalendarService:
    """
    获取财经日历服务实例

    Args:
        db: 数据库会话

    Returns:
        财经日历服务实例
    """
    return FinancialCalendarService(db)


def validate_country_exists(
    country_id: int, db: Session = Depends(get_db)
) -> CountryRegion:
    """
    验证国家是否存在且启用

    Args:
        country_id: 国家ID
        db: 数据库会话

    Returns:
        国家对象

    Raises:
        HTTPException: 当国家不存在或未启用时
    """
    country = (
        db.query(CountryRegion)
        .filter(CountryRegion.id == country_id, CountryRegion.is_active is True)
        .first()
    )

    if not country:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Country with id {country_id} not found or inactive",
        )

    return country


def validate_event_type_exists(
    event_type_id: int, db: Session = Depends(get_db)
) -> EventType:
    """
    验证事件类型是否存在且启用

    Args:
        event_type_id: 事件类型ID
        db: 数据库会话

    Returns:
        事件类型对象

    Raises:
        HTTPException: 当事件类型不存在或未启用时
    """
    event_type = (
        db.query(EventType)
        .filter(EventType.id == event_type_id, EventType.is_active is True)
        .first()
    )

    if not event_type:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Event type with id {event_type_id} not found or inactive",
        )

    return event_type


def validate_financial_event_exists(
    event_id: int, db: Session = Depends(get_db)
) -> FinancialEvent:
    """
    验证财经事件是否存在

    Args:
        event_id: 事件ID
        db: 数据库会话

    Returns:
        财经事件对象

    Raises:
        HTTPException: 当事件不存在时
    """
    event = db.query(FinancialEvent).filter(FinancialEvent.id == event_id).first()

    if not event:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Financial event with id {event_id} not found",
        )

    return event
