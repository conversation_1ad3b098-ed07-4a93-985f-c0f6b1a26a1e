"""
财经日历服务数据传输对象(DTO)
定义API请求和响应的数据格式
"""

from datetime import datetime, time, timezone
from decimal import Decimal
from enum import IntEnum
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field, field_validator


class ImportanceLevel(IntEnum):
    """重要性级别枚举"""

    LOW = 1  # 低
    MEDIUM = 2  # 中
    HIGH = 3  # 高


class MarketImpact(str):
    """市场影响程度"""

    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    EXTREME = "extreme"


class EventStatus(str):
    """事件状态"""

    SCHEDULED = "scheduled"
    PUBLISHED = "published"
    CANCELLED = "cancelled"
    POSTPONED = "postponed"


class MarketType(str):
    """市场类型"""

    CURRENCY = "currency"
    STOCK = "stock"
    BOND = "bond"
    COMMODITY = "commodity"
    INDEX = "index"
    CRYPTO = "crypto"


class RelationType(str):
    """关系类型"""

    PRECEDES = "precedes"
    FOLLOWS = "follows"
    RELATED = "related"
    CAUSES = "causes"
    AFFECTED_BY = "affected_by"
    CONCURRENT = "concurrent"


# 基础响应模型
class CountryRegionResponse(BaseModel):
    """国家地区响应"""

    id: int
    country_code: str
    country_name: str
    country_name_en: Optional[str]
    region: Optional[str]
    flag_icon: Optional[str]
    time_zone: str
    dst_support: bool
    primary_currency: str
    currency_symbol: Optional[str]
    exchange_rate_to_usd: Optional[Decimal]
    exchange_rate_updated_at: Optional[datetime]
    global_importance_score: Decimal
    financial_market_weight: Decimal
    typical_data_release_time: Optional[time]
    market_open_time: Optional[time]
    market_close_time: Optional[time]
    is_active: bool
    data_quality_score: Decimal
    created_at: datetime

    class Config:
        from_attributes = True


class EventTypeResponse(BaseModel):
    """事件类型响应"""

    id: int
    type_code: str
    type_name: str
    type_name_en: Optional[str]
    category: str
    subcategory: Optional[str]
    icon: Optional[str]
    color: Optional[str]
    default_importance: int
    typical_market_impact: str
    description: Optional[str]
    is_active: bool
    created_at: datetime

    class Config:
        from_attributes = True


class EconomicIndicatorResponse(BaseModel):
    """经济指标响应"""

    id: int
    indicator_code: str
    indicator_name: str
    indicator_name_en: Optional[str]
    country_id: int
    event_type_id: int
    unit: Optional[str]
    data_type: str
    decimal_places: int
    release_agency: Optional[str]
    release_frequency: Optional[str]
    typical_release_day: Optional[int]
    typical_release_time: Optional[time]
    market_importance: int
    volatility_impact: Decimal
    historical_avg: Optional[Decimal]
    historical_max: Optional[Decimal]
    historical_min: Optional[Decimal]
    description: Optional[str]
    methodology: Optional[str]
    is_active: bool
    created_at: datetime

    # 关联数据
    country: Optional[CountryRegionResponse] = None
    event_type: Optional[EventTypeResponse] = None

    class Config:
        from_attributes = True


class EventAffectedMarketResponse(BaseModel):
    """事件影响市场响应"""

    id: int
    event_id: int
    market_type: str
    impact_level: str
    expected_volatility: Optional[Decimal]
    actual_volatility: Optional[Decimal]
    created_at: datetime

    class Config:
        from_attributes = True


class EventRelatedEventResponse(BaseModel):
    """事件关联关系响应"""

    id: int
    source_event_id: int
    related_event_id: int
    relation_type: str
    relevance_score: Decimal
    created_at: datetime

    class Config:
        from_attributes = True


class FinancialEventResponse(BaseModel):
    """财经事件响应"""

    id: int
    event_title: str
    event_title_en: Optional[str]
    event_type_id: int
    country_id: int
    indicator_id: Optional[int]
    scheduled_time: datetime
    actual_time: Optional[datetime]
    time_zone: str
    time_precision: str
    importance_level: int
    market_impact: str
    previous_value: Optional[Decimal]
    forecast_value: Optional[Decimal]
    actual_value: Optional[Decimal]
    revised_value: Optional[Decimal]
    value_unit: Optional[str]
    value_format: str
    impact_direction: Optional[str]
    volatility_expected: Optional[Decimal]
    actual_market_impact: Optional[Dict[str, Any]]
    event_description: Optional[str]
    impact_analysis: Optional[str]
    historical_context: Optional[str]
    event_status: str
    data_source: Optional[str]
    source_url: Optional[str]
    version: int
    parent_event_id: Optional[int]
    subscriber_count: int
    notification_sent: bool
    notification_sent_at: Optional[datetime]
    created_at: datetime
    updated_at: datetime
    last_data_update: Optional[datetime]

    # 关联数据
    event_type: Optional[EventTypeResponse] = None
    country: Optional[CountryRegionResponse] = None
    indicator: Optional[EconomicIndicatorResponse] = None
    affected_markets: List[EventAffectedMarketResponse] = []

    class Config:
        from_attributes = True


# 创建请求模型
class CountryRegionCreate(BaseModel):
    """创建国家地区请求"""

    country_code: str = Field(..., max_length=10, description="国家代码")
    country_name: str = Field(..., max_length=100, description="国家名称")
    country_name_en: Optional[str] = Field(
        None, max_length=100, description="英文国家名称"
    )
    region: Optional[str] = Field(None, max_length=50, description="所属地区")
    flag_icon: Optional[str] = Field(None, max_length=100, description="国旗图标")
    time_zone: str = Field(..., max_length=50, description="时区标识")
    dst_support: bool = Field(default=False, description="是否支持夏令时")
    primary_currency: str = Field(..., max_length=10, description="主要货币代码")
    currency_symbol: Optional[str] = Field(None, max_length=10, description="货币符号")
    exchange_rate_to_usd: Optional[Decimal] = Field(None, description="对美元汇率")
    global_importance_score: Decimal = Field(
        default=1.0, ge=0.0, le=1.0, description="全球重要性评分"
    )
    financial_market_weight: Decimal = Field(
        default=1.0, ge=0.0, le=1.0, description="金融市场权重"
    )
    typical_data_release_time: Optional[time] = Field(
        None, description="典型数据发布时间"
    )
    market_open_time: Optional[time] = Field(None, description="市场开盘时间")
    market_close_time: Optional[time] = Field(None, description="市场收盘时间")
    data_quality_score: Decimal = Field(
        default=0.8, ge=0.0, le=1.0, description="数据质量评分"
    )

    @field_validator("country_code")
    def validate_country_code(cls, v):
        """验证国家代码格式"""
        if not v.isupper() or len(v) < 2:
            raise ValueError("Country code must be uppercase and at least 2 characters")
        return v


class EventTypeCreate(BaseModel):
    """创建事件类型请求"""

    type_code: str = Field(..., max_length=50, description="事件类型代码")
    type_name: str = Field(..., max_length=100, description="事件类型名称")
    type_name_en: Optional[str] = Field(
        None, max_length=100, description="英文事件类型名称"
    )
    category: str = Field(..., max_length=50, description="事件大类")
    subcategory: Optional[str] = Field(None, max_length=50, description="事件子类")
    icon: Optional[str] = Field(None, max_length=100, description="事件图标")
    color: Optional[str] = Field(None, max_length=7, description="事件颜色")
    default_importance: int = Field(default=2, ge=1, le=3, description="默认重要性级别")
    typical_market_impact: str = Field(default="medium", description="典型市场影响程度")
    description: Optional[str] = Field(None, description="事件类型描述")

    @field_validator("color")
    def validate_color(cls, v):
        """验证颜色格式"""
        if v and not v.startswith("#") or len(v) != 7:
            raise ValueError("Color must be in hex format (#RRGGBB)")
        return v


class EconomicIndicatorCreate(BaseModel):
    """创建经济指标请求"""

    indicator_code: str = Field(..., max_length=50, description="指标代码")
    indicator_name: str = Field(..., max_length=200, description="指标名称")
    indicator_name_en: Optional[str] = Field(
        None, max_length=200, description="英文指标名称"
    )
    country_id: int = Field(..., description="所属国家ID")
    event_type_id: int = Field(..., description="事件类型ID")
    unit: Optional[str] = Field(None, max_length=20, description="数据单位")
    data_type: str = Field(default="percentage", description="数据类型")
    decimal_places: int = Field(default=1, ge=0, le=6, description="小数位数")
    release_agency: Optional[str] = Field(None, max_length=200, description="发布机构")
    release_frequency: Optional[str] = Field(
        None, max_length=50, description="发布频率"
    )
    typical_release_day: Optional[int] = Field(
        None, ge=1, le=31, description="典型发布日期"
    )
    typical_release_time: Optional[time] = Field(None, description="典型发布时间")
    market_importance: int = Field(default=2, ge=1, le=3, description="市场重要性级别")
    volatility_impact: Decimal = Field(
        default=1.0, ge=0.0, le=1.0, description="波动性影响"
    )
    historical_avg: Optional[Decimal] = Field(None, description="历史平均值")
    historical_max: Optional[Decimal] = Field(None, description="历史最大值")
    historical_min: Optional[Decimal] = Field(None, description="历史最小值")
    description: Optional[str] = Field(None, description="指标描述")
    methodology: Optional[str] = Field(None, description="计算方法")


class FinancialEventCreate(BaseModel):
    """创建财经事件请求"""

    event_title: str = Field(..., max_length=500, description="事件标题")
    event_title_en: Optional[str] = Field(
        None, max_length=500, description="英文事件标题"
    )
    event_type_id: int = Field(..., description="事件类型ID")
    country_id: int = Field(..., description="国家地区ID")
    indicator_id: Optional[int] = Field(None, description="经济指标ID")
    scheduled_time: datetime = Field(..., description="计划时间")
    actual_time: Optional[datetime] = Field(None, description="实际时间")
    time_zone: str = Field(default="UTC", max_length=10, description="时区")
    time_precision: str = Field(default="minute", max_length=10, description="时间精度")
    importance_level: int = Field(..., ge=1, le=3, description="重要性级别")
    market_impact: str = Field(default="medium", description="市场影响程度")
    previous_value: Optional[Decimal] = Field(None, description="前值")
    forecast_value: Optional[Decimal] = Field(None, description="预期值")
    actual_value: Optional[Decimal] = Field(None, description="实际值")
    revised_value: Optional[Decimal] = Field(None, description="修正值")
    value_unit: Optional[str] = Field(None, max_length=20, description="数值单位")
    value_format: str = Field(default="decimal", description="数值格式")
    impact_direction: Optional[str] = Field(None, description="影响方向")
    volatility_expected: Optional[Decimal] = Field(
        None, ge=0.0, le=1.0, description="预期波动性"
    )
    actual_market_impact: Optional[Dict[str, Any]] = Field(
        None, description="实际市场影响"
    )
    event_description: Optional[str] = Field(None, description="事件描述")
    impact_analysis: Optional[str] = Field(None, description="影响分析")
    historical_context: Optional[str] = Field(None, description="历史背景")
    event_status: str = Field(default="scheduled", description="事件状态")
    data_source: Optional[str] = Field(None, max_length=100, description="数据来源")
    source_url: Optional[str] = Field(None, max_length=1000, description="来源链接")
    parent_event_id: Optional[int] = Field(None, description="父事件ID")

    @field_validator("market_impact")
    def validate_market_impact(cls, v):
        """验证市场影响程度"""
        allowed_values = ["low", "medium", "high", "extreme"]
        if v not in allowed_values:
            raise ValueError(
                f"Market impact must be one of: {', '.join(allowed_values)}"
            )
        return v

    @field_validator("event_status")
    def validate_event_status(cls, v):
        """验证事件状态"""
        allowed_values = ["scheduled", "published", "cancelled", "postponed"]
        if v not in allowed_values:
            raise ValueError(
                f"Event status must be one of: {', '.join(allowed_values)}"
            )
        return v


class EventAffectedMarketCreate(BaseModel):
    """创建事件影响市场请求"""

    event_id: int = Field(..., description="财经事件ID")
    market_type: str = Field(..., description="市场类型")
    impact_level: str = Field(default="medium", description="影响程度")
    expected_volatility: Optional[Decimal] = Field(
        None, ge=0.0, le=1.0, description="预期波动率"
    )
    actual_volatility: Optional[Decimal] = Field(
        None, ge=0.0, le=1.0, description="实际波动率"
    )

    @field_validator("market_type")
    def validate_market_type(cls, v):
        """验证市场类型"""
        allowed_types = ["currency", "stock", "bond", "commodity", "index", "crypto"]
        if v not in allowed_types:
            raise ValueError(f"Market type must be one of: {', '.join(allowed_types)}")
        return v


class EventRelatedEventCreate(BaseModel):
    """创建事件关联关系请求"""

    source_event_id: int = Field(..., description="源事件ID")
    related_event_id: int = Field(..., description="关联事件ID")
    relation_type: str = Field(..., description="关系类型")
    relevance_score: Decimal = Field(
        default=1.0, ge=0.0, le=1.0, description="相关性评分"
    )

    @field_validator("relation_type")
    def validate_relation_type(cls, v):
        """验证关系类型"""
        allowed_types = [
            "precedes",
            "follows",
            "related",
            "causes",
            "affected_by",
            "concurrent",
        ]
        if v not in allowed_types:
            raise ValueError(
                f"Relation type must be one of: {', '.join(allowed_types)}"
            )
        return v

    @field_validator("related_event_id")
    def validate_not_self_relation(cls, v, values):
        """验证不能自己关联自己"""
        if "source_event_id" in values and v == values["source_event_id"]:
            raise ValueError("Event cannot be related to itself")
        return v


# 更新请求模型
class EconomicIndicatorUpdate(BaseModel):
    """更新经济指标请求"""

    indicator_name: Optional[str] = Field(None, max_length=200, description="指标名称")
    indicator_name_en: Optional[str] = Field(
        None, max_length=200, description="英文指标名称"
    )
    unit: Optional[str] = Field(None, max_length=20, description="数据单位")
    data_type: Optional[str] = Field(None, description="数据类型")
    decimal_places: Optional[int] = Field(None, ge=0, le=6, description="小数位数")
    release_agency: Optional[str] = Field(None, max_length=200, description="发布机构")
    release_frequency: Optional[str] = Field(
        None, max_length=50, description="发布频率"
    )
    typical_release_day: Optional[int] = Field(
        None, ge=1, le=31, description="典型发布日期"
    )
    typical_release_time: Optional[time] = Field(None, description="典型发布时间")
    market_importance: Optional[int] = Field(
        None, ge=1, le=3, description="市场重要性级别"
    )
    volatility_impact: Optional[Decimal] = Field(
        None, ge=0.0, le=1.0, description="波动性影响"
    )
    historical_avg: Optional[Decimal] = Field(None, description="历史平均值")
    historical_max: Optional[Decimal] = Field(None, description="历史最大值")
    historical_min: Optional[Decimal] = Field(None, description="历史最小值")
    description: Optional[str] = Field(None, description="指标描述")
    methodology: Optional[str] = Field(None, description="计算方法")
    is_active: Optional[bool] = Field(None, description="是否启用")


class EventTypeUpdate(BaseModel):
    """更新事件类型请求"""

    type_name: Optional[str] = Field(None, max_length=100, description="事件类型名称")
    type_name_en: Optional[str] = Field(
        None, max_length=100, description="英文事件类型名称"
    )
    category: Optional[str] = Field(None, max_length=50, description="事件大类")
    subcategory: Optional[str] = Field(None, max_length=50, description="事件子类")
    icon: Optional[str] = Field(None, max_length=100, description="事件图标")
    color: Optional[str] = Field(None, max_length=7, description="事件颜色")
    default_importance: Optional[int] = Field(
        None, ge=1, le=3, description="默认重要性级别"
    )
    typical_market_impact: Optional[str] = Field(None, description="典型市场影响程度")
    description: Optional[str] = Field(None, description="事件类型描述")
    is_active: Optional[bool] = Field(None, description="是否启用")

    @field_validator("color")
    def validate_color(cls, v):
        """验证颜色格式"""
        if v and (not v.startswith("#") or len(v) != 7):
            raise ValueError("Color must be in hex format (#RRGGBB)")
        return v

    @field_validator("typical_market_impact")
    def validate_market_impact(cls, v):
        """验证市场影响程度"""
        if v is not None:
            allowed_values = ["low", "medium", "high", "extreme"]
            if v not in allowed_values:
                raise ValueError(
                    f"Market impact must be one of: {', '.join(allowed_values)}"
                )
        return v


class CountryRegionUpdate(BaseModel):
    """更新国家地区请求"""

    country_name: Optional[str] = Field(None, max_length=100, description="国家名称")
    country_name_en: Optional[str] = Field(
        None, max_length=100, description="英文国家名称"
    )
    region: Optional[str] = Field(None, max_length=50, description="所属地区")
    flag_icon: Optional[str] = Field(None, max_length=100, description="国旗图标")
    time_zone: Optional[str] = Field(None, max_length=50, description="时区标识")
    dst_support: Optional[bool] = Field(None, description="是否支持夏令时")
    currency_symbol: Optional[str] = Field(None, max_length=10, description="货币符号")
    exchange_rate_to_usd: Optional[Decimal] = Field(None, description="对美元汇率")
    global_importance_score: Optional[Decimal] = Field(
        None, ge=0.0, le=1.0, description="全球重要性评分"
    )
    financial_market_weight: Optional[Decimal] = Field(
        None, ge=0.0, le=1.0, description="金融市场权重"
    )
    typical_data_release_time: Optional[time] = Field(
        None, description="典型数据发布时间"
    )
    market_open_time: Optional[time] = Field(None, description="市场开盘时间")
    market_close_time: Optional[time] = Field(None, description="市场收盘时间")
    data_quality_score: Optional[Decimal] = Field(
        None, ge=0.0, le=1.0, description="数据质量评分"
    )
    is_active: Optional[bool] = Field(None, description="是否启用")


class FinancialEventUpdate(BaseModel):
    """更新财经事件请求"""

    event_title: Optional[str] = Field(None, max_length=500, description="事件标题")
    event_title_en: Optional[str] = Field(
        None, max_length=500, description="英文事件标题"
    )
    scheduled_time: Optional[datetime] = Field(None, description="计划时间")
    actual_time: Optional[datetime] = Field(None, description="实际时间")
    time_zone: Optional[str] = Field(None, max_length=10, description="时区")
    time_precision: Optional[str] = Field(None, max_length=10, description="时间精度")
    importance_level: Optional[int] = Field(None, ge=1, le=3, description="重要性级别")
    market_impact: Optional[str] = Field(None, description="市场影响程度")
    previous_value: Optional[Decimal] = Field(None, description="前值")
    forecast_value: Optional[Decimal] = Field(None, description="预期值")
    actual_value: Optional[Decimal] = Field(None, description="实际值")
    revised_value: Optional[Decimal] = Field(None, description="修正值")
    value_unit: Optional[str] = Field(None, max_length=20, description="数值单位")
    value_format: Optional[str] = Field(None, description="数值格式")
    impact_direction: Optional[str] = Field(None, description="影响方向")
    volatility_expected: Optional[Decimal] = Field(
        None, ge=0.0, le=1.0, description="预期波动性"
    )
    actual_market_impact: Optional[Dict[str, Any]] = Field(
        None, description="实际市场影响"
    )
    event_description: Optional[str] = Field(None, description="事件描述")
    impact_analysis: Optional[str] = Field(None, description="影响分析")
    historical_context: Optional[str] = Field(None, description="历史背景")
    event_status: Optional[str] = Field(None, description="事件状态")
    data_source: Optional[str] = Field(None, max_length=100, description="数据来源")
    source_url: Optional[str] = Field(None, max_length=1000, description="来源链接")


# 查询参数模型
class FinancialEventFilter(BaseModel):
    """财经事件查询过滤器"""

    country_ids: Optional[List[int]] = Field(None, description="国家ID列表")
    event_type_ids: Optional[List[int]] = Field(None, description="事件类型ID列表")
    importance_levels: Optional[List[int]] = Field(None, description="重要性级别列表")
    event_status: Optional[List[str]] = Field(None, description="事件状态列表")
    start_time: Optional[datetime] = Field(None, description="开始时间")
    end_time: Optional[datetime] = Field(None, description="结束时间")
    market_impact: Optional[List[str]] = Field(None, description="市场影响程度列表")
    has_actual_value: Optional[bool] = Field(None, description="是否有实际值")


class PaginationParams(BaseModel):
    """分页参数"""

    page: int = Field(default=1, ge=1, description="页码")
    size: int = Field(default=20, ge=1, le=100, description="每页大小")
    order_by: str = Field(default="scheduled_time", description="排序字段")
    order_direction: str = Field(default="desc", description="排序方向")

    @field_validator("order_direction")
    def validate_order_direction(cls, v):
        """验证排序方向"""
        if v not in ["asc", "desc"]:
            raise ValueError("Order direction must be 'asc' or 'desc'")
        return v


class PaginatedResponse(BaseModel):
    """分页响应"""

    items: List[Any]
    total: int
    page: int
    size: int
    pages: int
