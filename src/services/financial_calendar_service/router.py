"""
财经日历服务API路由
提供财经事件相关的HTTP接口
"""

from datetime import datetime, timezone
from typing import Annotated, List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session

from ...core.database import get_db
from ..user_service.dependencies import get_current_active_user
from ..user_service.models import User
from .schemas import (CountryRegionCreate, CountryRegionResponse,
                      CountryRegionUpdate, EconomicIndicatorCreate,
                      EconomicIndicatorResponse, EconomicIndicatorUpdate,
                      EventTypeCreate, EventTypeResponse, EventTypeUpdate,
                      FinancialEventCreate, FinancialEventFilter,
                      FinancialEventResponse, FinancialEventUpdate,
                      PaginatedResponse, PaginationParams)
from .service import FinancialCalendarService

router = APIRouter(prefix="/financial-calendar", tags=["财经日历"])


def get_financial_calendar_service(
    db: Session = Depends(get_db),
) -> FinancialCalendarService:
    """
    获取财经日历服务实例

    Args:
        db: 数据库会话

    Returns:
        财经日历服务实例
    """
    return FinancialCalendarService(db)


@router.post(
    "/events",
    response_model=FinancialEventResponse,
    status_code=status.HTTP_201_CREATED,
)
async def create_event(
    event_data: FinancialEventCreate,
    current_user: Annotated[User, Depends(get_current_active_user)],
    service: FinancialCalendarService = Depends(get_financial_calendar_service),
):
    """
    创建财经事件（需要用户认证）

    Args:
        event_data: 事件创建数据
        current_user: 当前认证用户
        service: 财经日历服务

    Returns:
        创建的事件信息

    Raises:
        HTTPException: 当创建失败时
    """
    try:
        return service.create_financial_event(event_data)
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create financial event",
        )


@router.get("/events", response_model=PaginatedResponse)
async def list_events(
    page: int = Query(default=1, ge=1, description="页码"),
    size: int = Query(default=20, ge=1, le=100, description="每页大小"),
    order_by: str = Query(default="scheduled_time", description="排序字段"),
    order_direction: str = Query(default="desc", description="排序方向"),
    country_ids: Optional[List[int]] = Query(None, description="国家ID列表"),
    event_type_ids: Optional[List[int]] = Query(None, description="事件类型ID列表"),
    importance_levels: Optional[List[int]] = Query(None, description="重要性级别列表"),
    event_status: Optional[List[str]] = Query(None, description="事件状态列表"),
    start_time: Optional[datetime] = Query(None, description="开始时间"),
    end_time: Optional[datetime] = Query(None, description="结束时间"),
    market_impact: Optional[List[str]] = Query(None, description="市场影响程度列表"),
    has_actual_value: Optional[bool] = Query(None, description="是否有实际值"),
    service: FinancialCalendarService = Depends(get_financial_calendar_service),
):
    """
    获取财经事件列表

    Args:
        page: 页码
        size: 每页大小
        order_by: 排序字段
        order_direction: 排序方向
        country_ids: 国家ID列表
        event_type_ids: 事件类型ID列表
        importance_levels: 重要性级别列表
        event_status: 事件状态列表
        start_time: 开始时间
        end_time: 结束时间
        market_impact: 市场影响程度列表
        has_actual_value: 是否有实际值
        service: 财经日历服务

    Returns:
        分页的事件列表
    """
    try:
        # 构建过滤器
        filters = FinancialEventFilter(
            country_ids=country_ids,
            event_type_ids=event_type_ids,
            importance_levels=importance_levels,
            event_status=event_status,
            start_time=start_time,
            end_time=end_time,
            market_impact=market_impact,
            has_actual_value=has_actual_value,
        )

        # 构建分页参数
        pagination = PaginationParams(
            page=page, size=size, order_by=order_by, order_direction=order_direction
        )

        return service.list_financial_events(filters, pagination)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve financial events",
        )


@router.get("/events/upcoming", response_model=List[FinancialEventResponse])
async def get_upcoming_events(
    hours: int = Query(default=24, ge=1, le=168, description="未来多少小时内的事件"),
    importance_levels: Optional[List[int]] = Query(None, description="重要性级别过滤"),
    service: FinancialCalendarService = Depends(get_financial_calendar_service),
):
    """
    获取即将到来的财经事件（无需认证）

    Args:
        hours: 未来多少小时内的事件
        importance_levels: 重要性级别过滤
        service: 财经日历服务

    Returns:
        即将到来的事件列表
    """
    try:
        return service.get_upcoming_events(hours, importance_levels)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve upcoming events",
        )


@router.get("/events/high-impact", response_model=List[FinancialEventResponse])
async def get_high_impact_events(
    days: int = Query(default=7, ge=1, le=30, description="未来多少天内的事件"),
    service: FinancialCalendarService = Depends(get_financial_calendar_service),
):
    """
    获取高影响财经事件（无需认证）

    Args:
        days: 未来多少天内的事件
        service: 财经日历服务

    Returns:
        高影响事件列表
    """
    try:
        return service.get_high_impact_events(days)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve high impact events",
        )


@router.get("/events/search", response_model=List[FinancialEventResponse])
async def search_events(
    q: str = Query(..., min_length=2, description="搜索关键词"),
    limit: int = Query(default=50, ge=1, le=100, description="结果数量限制"),
    service: FinancialCalendarService = Depends(get_financial_calendar_service),
):
    """
    搜索财经事件（无需认证）

    Args:
        q: 搜索关键词
        limit: 结果数量限制
        service: 财经日历服务

    Returns:
        搜索结果列表
    """
    try:
        return service.search_events(q, limit)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to search events",
        )


@router.get(
    "/events/by-country/{country_id}", response_model=List[FinancialEventResponse]
)
async def get_events_by_country(
    country_id: int,
    start_date: Optional[datetime] = Query(None, description="开始日期"),
    end_date: Optional[datetime] = Query(None, description="结束日期"),
    service: FinancialCalendarService = Depends(get_financial_calendar_service),
):
    """
    获取特定国家的财经事件（无需认证）

    Args:
        country_id: 国家ID
        start_date: 开始日期
        end_date: 结束日期
        service: 财经日历服务

    Returns:
        国家的事件列表
    """
    try:
        return service.get_events_by_country(country_id, start_date, end_date)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve country events",
        )


@router.get("/events/{event_id}", response_model=FinancialEventResponse)
async def get_event(
    event_id: int,
    service: FinancialCalendarService = Depends(get_financial_calendar_service),
):
    """
    获取单个财经事件

    Args:
        event_id: 事件ID
        service: 财经日历服务

    Returns:
        事件信息

    Raises:
        HTTPException: 当事件不存在时
    """
    event = service.get_financial_event(event_id)
    if not event:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Financial event not found"
        )
    return event


@router.put("/events/{event_id}", response_model=FinancialEventResponse)
async def update_event(
    event_id: int,
    event_data: FinancialEventUpdate,
    current_user: Annotated[User, Depends(get_current_active_user)],
    service: FinancialCalendarService = Depends(get_financial_calendar_service),
):
    """
    更新财经事件（需要用户认证）

    Args:
        event_id: 事件ID
        event_data: 更新数据
        current_user: 当前认证用户
        service: 财经日历服务

    Returns:
        更新后的事件信息

    Raises:
        HTTPException: 当事件不存在时
    """
    updated_event = service.update_financial_event(event_id, event_data)
    if not updated_event:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Financial event not found"
        )
    return updated_event


@router.delete("/events/{event_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_event(
    event_id: int,
    current_user: Annotated[User, Depends(get_current_active_user)],
    service: FinancialCalendarService = Depends(get_financial_calendar_service),
):
    """
    删除财经事件（需要用户认证）

    Args:
        event_id: 事件ID
        current_user: 当前认证用户
        service: 财经日历服务

    Raises:
        HTTPException: 当事件不存在时
    """
    success = service.delete_financial_event(event_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Financial event not found"
        )


@router.patch("/events/{event_id}/actual-value", response_model=FinancialEventResponse)
async def update_event_actual_value(
    event_id: int,
    actual_value: float,
    current_user: Annotated[User, Depends(get_current_active_user)],
    actual_time: Optional[datetime] = None,
    service: FinancialCalendarService = Depends(get_financial_calendar_service),
):
    """
    更新事件实际值（需要用户认证）

    Args:
        event_id: 事件ID
        actual_value: 实际值
        current_user: 当前认证用户
        actual_time: 实际发布时间
        service: 财经日历服务

    Returns:
        更新后的事件信息

    Raises:
        HTTPException: 当事件不存在时
    """
    try:
        updated_event = service.update_event_actual_value(
            event_id, actual_value, actual_time
        )
        if not updated_event:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Financial event not found",
            )
        return updated_event
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update event actual value",
        )


@router.post(
    "/countries",
    response_model=CountryRegionResponse,
    status_code=status.HTTP_201_CREATED,
)
async def create_country(
    country_data: CountryRegionCreate,
    current_user: Annotated[User, Depends(get_current_active_user)],
    service: FinancialCalendarService = Depends(get_financial_calendar_service),
):
    """
    创建国家地区（需要用户认证）

    Args:
        country_data: 国家地区创建数据
        current_user: 当前认证用户
        service: 财经日历服务

    Returns:
        创建的国家地区信息

    Raises:
        HTTPException: 当创建失败时
    """
    try:
        return service.create_country_region(country_data)
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create country",
        )


@router.get("/countries/{country_id}", response_model=CountryRegionResponse)
async def get_country(
    country_id: int,
    service: FinancialCalendarService = Depends(get_financial_calendar_service),
):
    """
    获取单个国家地区

    Args:
        country_id: 国家地区ID
        service: 财经日历服务

    Returns:
        国家地区信息

    Raises:
        HTTPException: 当国家地区不存在时
    """
    country = service.get_country_region(country_id)
    if not country:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Country not found"
        )
    return country


@router.put("/countries/{country_id}", response_model=CountryRegionResponse)
async def update_country(
    country_id: int,
    country_data: CountryRegionUpdate,
    current_user: Annotated[User, Depends(get_current_active_user)],
    service: FinancialCalendarService = Depends(get_financial_calendar_service),
):
    """
    更新国家地区（需要用户认证）

    Args:
        country_id: 国家地区ID
        country_data: 更新数据
        current_user: 当前认证用户
        service: 财经日历服务

    Returns:
        更新后的国家地区信息

    Raises:
        HTTPException: 当国家地区不存在时
    """
    updated_country = service.update_country_region(country_id, country_data)
    if not updated_country:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Country not found"
        )
    return updated_country


@router.delete("/countries/{country_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_country(
    country_id: int,
    current_user: Annotated[User, Depends(get_current_active_user)],
    service: FinancialCalendarService = Depends(get_financial_calendar_service),
):
    """
    删除国家地区（需要用户认证）

    Args:
        country_id: 国家地区ID
        current_user: 当前认证用户
        service: 财经日历服务

    Raises:
        HTTPException: 当国家地区不存在或有关联数据时
    """
    try:
        success = service.delete_country_region(country_id)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Country not found"
            )
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete country",
        )


@router.get("/countries", response_model=List[CountryRegionResponse])
async def get_countries(
    active_only: bool = Query(default=True, description="是否只返回活跃的国家"),
    service: FinancialCalendarService = Depends(get_financial_calendar_service),
):
    """
    获取国家地区列表

    Args:
        active_only: 是否只返回活跃的国家
        service: 财经日历服务

    Returns:
        国家地区列表
    """
    try:
        return service.get_countries(active_only)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve countries",
        )


@router.post(
    "/event-types",
    response_model=EventTypeResponse,
    status_code=status.HTTP_201_CREATED,
)
async def create_event_type(
    event_type_data: EventTypeCreate,
    current_user: Annotated[User, Depends(get_current_active_user)],
    service: FinancialCalendarService = Depends(get_financial_calendar_service),
):
    """
    创建事件类型（需要用户认证）

    Args:
        event_type_data: 事件类型创建数据
        current_user: 当前认证用户
        service: 财经日历服务

    Returns:
        创建的事件类型信息

    Raises:
        HTTPException: 当创建失败时
    """
    try:
        return service.create_event_type(event_type_data)
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create event type",
        )


@router.get("/event-types", response_model=List[EventTypeResponse])
async def get_event_types(
    active_only: bool = Query(default=True, description="是否只返回活跃的事件类型"),
    service: FinancialCalendarService = Depends(get_financial_calendar_service),
):
    """
    获取事件类型列表

    Args:
        active_only: 是否只返回活跃的事件类型
        service: 财经日历服务

    Returns:
        事件类型列表
    """
    try:
        return service.get_event_types(active_only)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve event types",
        )


@router.get("/event-types/{event_type_id}", response_model=EventTypeResponse)
async def get_event_type(
    event_type_id: int,
    service: FinancialCalendarService = Depends(get_financial_calendar_service),
):
    """
    获取单个事件类型

    Args:
        event_type_id: 事件类型ID
        service: 财经日历服务

    Returns:
        事件类型信息

    Raises:
        HTTPException: 当事件类型不存在时
    """
    try:
        event_type = service.get_event_type(event_type_id)
        if not event_type:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Event type with id {event_type_id} not found",
            )
        return event_type
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve event type",
        )


@router.put("/event-types/{event_type_id}", response_model=EventTypeResponse)
async def update_event_type(
    event_type_id: int,
    event_type_data: EventTypeUpdate,
    current_user: Annotated[User, Depends(get_current_active_user)],
    service: FinancialCalendarService = Depends(get_financial_calendar_service),
):
    """
    更新事件类型（需要用户认证）

    Args:
        event_type_id: 事件类型ID
        event_type_data: 更新数据
        current_user: 当前认证用户
        service: 财经日历服务

    Returns:
        更新后的事件类型信息

    Raises:
        HTTPException: 当更新失败时
    """
    try:
        updated_event_type = service.update_event_type(event_type_id, event_type_data)
        if not updated_event_type:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Event type with id {event_type_id} not found",
            )
        return updated_event_type
    except HTTPException:
        raise
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update event type",
        )


@router.delete("/event-types/{event_type_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_event_type(
    event_type_id: int,
    current_user: Annotated[User, Depends(get_current_active_user)],
    service: FinancialCalendarService = Depends(get_financial_calendar_service),
):
    """
    删除事件类型（需要用户认证）

    Args:
        event_type_id: 事件类型ID
        current_user: 当前认证用户
        service: 财经日历服务

    Raises:
        HTTPException: 当删除失败时
    """
    try:
        success = service.delete_event_type(event_type_id)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Event type with id {event_type_id} not found",
            )
    except HTTPException:
        raise
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete event type",
        )


@router.get("/calendar/summary")
async def get_calendar_summary(
    start_date: datetime = Query(..., description="开始日期"),
    end_date: datetime = Query(..., description="结束日期"),
    service: FinancialCalendarService = Depends(get_financial_calendar_service),
):
    """
    获取日历摘要

    Args:
        start_date: 开始日期
        end_date: 结束日期
        service: 财经日历服务

    Returns:
        日历摘要信息
    """
    try:
        if start_date >= end_date:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Start date must be before end date",
            )

        return service.get_calendar_summary(start_date, end_date)
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve calendar summary",
        )


# ==================== 经济指标相关路由 ====================


@router.post(
    "/economic-indicators",
    response_model=EconomicIndicatorResponse,
    status_code=status.HTTP_201_CREATED,
)
async def create_economic_indicator(
    indicator_data: EconomicIndicatorCreate,
    current_user: Annotated[User, Depends(get_current_active_user)],
    service: FinancialCalendarService = Depends(get_financial_calendar_service),
):
    """
    创建经济指标（需要用户认证）

    Args:
        indicator_data: 指标创建数据
        current_user: 当前认证用户
        service: 财经日历服务

    Returns:
        创建的指标信息

    Raises:
        HTTPException: 当创建失败时
    """
    try:
        return service.create_economic_indicator(indicator_data)
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create economic indicator",
        )


@router.get(
    "/economic-indicators/search", response_model=List[EconomicIndicatorResponse]
)
async def search_economic_indicators(
    q: str = Query(..., min_length=2, description="搜索关键词"),
    limit: int = Query(default=50, ge=1, le=100, description="结果数量限制"),
    service: FinancialCalendarService = Depends(get_financial_calendar_service),
):
    """
    搜索经济指标

    Args:
        q: 搜索关键词
        limit: 结果数量限制
        service: 财经日历服务

    Returns:
        搜索结果列表
    """
    try:
        return service.search_economic_indicators(q, limit)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to search economic indicators",
        )


@router.get(
    "/economic-indicators/by-country/{country_id}",
    response_model=List[EconomicIndicatorResponse],
)
async def get_indicators_by_country(
    country_id: int,
    active_only: bool = Query(default=True, description="是否只返回活跃的指标"),
    service: FinancialCalendarService = Depends(get_financial_calendar_service),
):
    """
    获取指定国家的经济指标列表

    Args:
        country_id: 国家ID
        active_only: 是否只返回活跃的指标
        service: 财经日历服务

    Returns:
        该国家的指标列表
    """
    try:
        return service.get_indicators_by_country(country_id, active_only)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve indicators by country",
        )


@router.get(
    "/economic-indicators/by-event-type/{event_type_id}",
    response_model=List[EconomicIndicatorResponse],
)
async def get_indicators_by_event_type(
    event_type_id: int,
    active_only: bool = Query(default=True, description="是否只返回活跃的指标"),
    service: FinancialCalendarService = Depends(get_financial_calendar_service),
):
    """
    获取指定事件类型的经济指标列表

    Args:
        event_type_id: 事件类型ID
        active_only: 是否只返回活跃的指标
        service: 财经日历服务

    Returns:
        该事件类型的指标列表
    """
    try:
        return service.get_indicators_by_event_type(event_type_id, active_only)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve indicators by event type",
        )


@router.get(
    "/economic-indicators/{indicator_id}", response_model=EconomicIndicatorResponse
)
async def get_economic_indicator(
    indicator_id: int,
    service: FinancialCalendarService = Depends(get_financial_calendar_service),
):
    """
    获取单个经济指标

    Args:
        indicator_id: 指标ID
        service: 财经日历服务

    Returns:
        指标信息

    Raises:
        HTTPException: 当指标不存在时
    """
    indicator = service.get_economic_indicator(indicator_id)
    if not indicator:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Economic indicator not found"
        )
    return indicator


@router.put(
    "/economic-indicators/{indicator_id}", response_model=EconomicIndicatorResponse
)
async def update_economic_indicator(
    indicator_id: int,
    indicator_data: EconomicIndicatorUpdate,
    current_user: Annotated[User, Depends(get_current_active_user)],
    service: FinancialCalendarService = Depends(get_financial_calendar_service),
):
    """
    更新经济指标（需要用户认证）

    Args:
        indicator_id: 指标ID
        indicator_data: 更新数据
        current_user: 当前认证用户
        service: 财经日历服务

    Returns:
        更新后的指标信息

    Raises:
        HTTPException: 当指标不存在时
    """
    updated_indicator = service.update_economic_indicator(indicator_id, indicator_data)
    if not updated_indicator:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Economic indicator not found"
        )
    return updated_indicator


@router.delete(
    "/economic-indicators/{indicator_id}", status_code=status.HTTP_204_NO_CONTENT
)
async def delete_economic_indicator(
    indicator_id: int,
    current_user: Annotated[User, Depends(get_current_active_user)],
    service: FinancialCalendarService = Depends(get_financial_calendar_service),
):
    """
    删除经济指标（需要用户认证）

    Args:
        indicator_id: 指标ID
        current_user: 当前认证用户
        service: 财经日历服务

    Raises:
        HTTPException: 当指标不存在或有关联数据时
    """
    try:
        success = service.delete_economic_indicator(indicator_id)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Economic indicator not found",
            )
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete economic indicator",
        )


@router.get("/economic-indicators", response_model=PaginatedResponse)
async def list_economic_indicators(
    page: int = Query(default=1, ge=1, description="页码"),
    size: int = Query(default=20, ge=1, le=100, description="每页大小"),
    country_id: Optional[int] = Query(None, description="国家ID过滤"),
    event_type_id: Optional[int] = Query(None, description="事件类型ID过滤"),
    active_only: bool = Query(default=True, description="是否只返回活跃的指标"),
    service: FinancialCalendarService = Depends(get_financial_calendar_service),
):
    """
    获取经济指标列表

    Args:
        page: 页码
        size: 每页大小
        country_id: 国家ID过滤
        event_type_id: 事件类型ID过滤
        active_only: 是否只返回活跃的指标
        service: 财经日历服务

    Returns:
        分页的指标列表
    """
    try:
        return service.list_economic_indicators(
            country_id=country_id,
            event_type_id=event_type_id,
            active_only=active_only,
            page=page,
            size=size,
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve economic indicators",
        )
