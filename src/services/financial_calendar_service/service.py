"""
财经日历服务业务逻辑
提供财经事件、国家地区、经济指标等相关业务功能
"""

from datetime import datetime, timedelta, timezone
from typing import List, Optional, Tuple

from sqlalchemy import and_, desc, func, or_
from sqlalchemy.orm import Session, selectinload

from .models import (CountryRegion, EconomicIndicator, EventAffectedMarket,
                     EventRelatedEvent, EventType, FinancialEvent)
from .schemas import (CountryRegionCreate, CountryRegionResponse,
                      CountryRegionUpdate, EconomicIndicatorCreate,
                      EconomicIndicatorResponse, EconomicIndicatorUpdate,
                      EventTypeCreate, EventTypeResponse, EventTypeUpdate,
                      FinancialEventCreate, FinancialEventFilter,
                      FinancialEventResponse, FinancialEventUpdate,
                      PaginatedResponse, PaginationParams)


class FinancialCalendarService:
    """
    财经日历服务类
    处理财经事件相关的业务逻辑
    """

    def __init__(self, db: Session):
        """
        初始化服务

        Args:
            db: 数据库会话
        """
        self.db = db

    def create_financial_event(
        self, event_data: FinancialEventCreate
    ) -> FinancialEventResponse:
        """
        创建财经事件

        Args:
            event_data: 事件创建数据

        Returns:
            创建的事件响应

        Raises:
            ValueError: 当参数验证失败时
        """
        # 验证国家和事件类型是否存在
        country = (
            self.db.query(CountryRegion)
            .filter(
                CountryRegion.id == event_data.country_id,
                CountryRegion.is_active == True,
            )
            .first()
        )
        if not country:
            raise ValueError(
                f"Country with id {event_data.country_id} not found or inactive"
            )

        event_type = (
            self.db.query(EventType)
            .filter(
                EventType.id == event_data.event_type_id, EventType.is_active == True
            )
            .first()
        )
        if not event_type:
            raise ValueError(
                f"Event type with id {event_data.event_type_id} not found or inactive"
            )

        # 验证经济指标（如果提供）
        if event_data.indicator_id:
            indicator = (
                self.db.query(EconomicIndicator)
                .filter(
                    EconomicIndicator.id == event_data.indicator_id,
                    EconomicIndicator.is_active == True,
                )
                .first()
            )
            if not indicator:
                raise ValueError(
                    f"Economic indicator with id {event_data.indicator_id} not found or inactive"
                )

        # 创建财经事件
        db_event = FinancialEvent(**event_data.model_dump())
        self.db.add(db_event)
        self.db.commit()
        self.db.refresh(db_event)

        return self._get_event_response(db_event)

    def get_financial_event(self, event_id: int) -> Optional[FinancialEventResponse]:
        """
        获取单个财经事件

        Args:
            event_id: 事件ID

        Returns:
            事件响应或None
        """
        event = (
            self.db.query(FinancialEvent)
            .options(
                selectinload(FinancialEvent.event_type),
                selectinload(FinancialEvent.country),
                selectinload(FinancialEvent.indicator),
                selectinload(FinancialEvent.affected_markets),
            )
            .filter(FinancialEvent.id == event_id)
            .first()
        )

        if not event:
            return None

        return self._get_event_response(event)

    def update_financial_event(
        self, event_id: int, event_data: FinancialEventUpdate
    ) -> Optional[FinancialEventResponse]:
        """
        更新财经事件

        Args:
            event_id: 事件ID
            event_data: 更新数据

        Returns:
            更新后的事件响应或None
        """
        event = (
            self.db.query(FinancialEvent).filter(FinancialEvent.id == event_id).first()
        )

        if not event:
            return None

        # 更新字段
        update_data = event_data.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(event, field, value)

        # 更新时间戳
        event.updated_at = datetime.now(timezone.utc)

        self.db.commit()
        self.db.refresh(event)

        return self._get_event_response(event)

    def delete_financial_event(self, event_id: int) -> bool:
        """
        删除财经事件

        Args:
            event_id: 事件ID

        Returns:
            是否删除成功
        """
        event = (
            self.db.query(FinancialEvent).filter(FinancialEvent.id == event_id).first()
        )

        if not event:
            return False

        self.db.delete(event)
        self.db.commit()
        return True

    def list_financial_events(
        self,
        filters: Optional[FinancialEventFilter] = None,
        pagination: Optional[PaginationParams] = None,
    ) -> PaginatedResponse:
        """
        获取财经事件列表

        Args:
            filters: 过滤条件
            pagination: 分页参数

        Returns:
            分页的事件列表
        """
        if pagination is None:
            pagination = PaginationParams()

        # 构建查询
        query = self.db.query(FinancialEvent).options(
            selectinload(FinancialEvent.event_type),
            selectinload(FinancialEvent.country),
            selectinload(FinancialEvent.indicator),
        )

        # 应用过滤条件
        if filters:
            query = self._apply_filters(query, filters)

        # 应用排序
        query = self._apply_sorting(query, pagination)

        # 计算总数
        total = query.count()

        # 应用分页
        offset = (pagination.page - 1) * pagination.size
        events = query.offset(offset).limit(pagination.size).all()

        # 转换为响应格式
        items = [self._get_event_response(event) for event in events]

        # 计算页数
        pages = (total + pagination.size - 1) // pagination.size

        return PaginatedResponse(
            items=items,
            total=total,
            page=pagination.page,
            size=pagination.size,
            pages=pages,
        )

    def get_upcoming_events(
        self, hours: int = 24, importance_levels: Optional[List[int]] = None
    ) -> List[FinancialEventResponse]:
        """
        获取即将到来的财经事件

        Args:
            hours: 未来多少小时内的事件
            importance_levels: 重要性级别过滤

        Returns:
            即将到来的事件列表
        """
        now = datetime.now(timezone.utc)
        end_time = now + timedelta(hours=hours)

        query = (
            self.db.query(FinancialEvent)
            .options(
                selectinload(FinancialEvent.event_type),
                selectinload(FinancialEvent.country),
                selectinload(FinancialEvent.indicator),
            )
            .filter(
                and_(
                    FinancialEvent.scheduled_time >= now,
                    FinancialEvent.scheduled_time <= end_time,
                    FinancialEvent.event_status.in_(["scheduled", "published"]),
                )
            )
        )

        if importance_levels:
            query = query.filter(FinancialEvent.importance_level.in_(importance_levels))

        events = query.order_by(FinancialEvent.scheduled_time).all()

        return [self._get_event_response(event) for event in events]

    def get_events_by_country(
        self,
        country_id: int,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
    ) -> List[FinancialEventResponse]:
        """
        获取特定国家的财经事件

        Args:
            country_id: 国家ID
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            该国家的事件列表
        """
        query = (
            self.db.query(FinancialEvent)
            .options(
                selectinload(FinancialEvent.event_type),
                selectinload(FinancialEvent.country),
                selectinload(FinancialEvent.indicator),
            )
            .filter(FinancialEvent.country_id == country_id)
        )

        if start_date:
            query = query.filter(FinancialEvent.scheduled_time >= start_date)

        if end_date:
            query = query.filter(FinancialEvent.scheduled_time <= end_date)

        events = query.order_by(FinancialEvent.scheduled_time).all()

        return [self._get_event_response(event) for event in events]

    def get_high_impact_events(self, days: int = 7) -> List[FinancialEventResponse]:
        """
        获取高影响力的财经事件

        Args:
            days: 未来多少天内的事件

        Returns:
            高影响力事件列表
        """
        now = datetime.now(timezone.utc)
        end_time = now + timedelta(days=days)

        events = (
            self.db.query(FinancialEvent)
            .options(
                selectinload(FinancialEvent.event_type),
                selectinload(FinancialEvent.country),
                selectinload(FinancialEvent.indicator),
            )
            .filter(
                and_(
                    FinancialEvent.scheduled_time >= now,
                    FinancialEvent.scheduled_time <= end_time,
                    FinancialEvent.importance_level == 3,
                    FinancialEvent.market_impact.in_(["high", "extreme"]),
                    FinancialEvent.event_status.in_(["scheduled", "published"]),
                )
            )
            .order_by(FinancialEvent.scheduled_time)
            .all()
        )

        return [self._get_event_response(event) for event in events]

    def search_events(
        self, query_text: str, limit: int = 50
    ) -> List[FinancialEventResponse]:
        """
        搜索财经事件

        Args:
            query_text: 搜索文本
            limit: 结果限制

        Returns:
            搜索结果列表
        """
        events = (
            self.db.query(FinancialEvent)
            .options(
                selectinload(FinancialEvent.event_type),
                selectinload(FinancialEvent.country),
                selectinload(FinancialEvent.indicator),
            )
            .filter(
                or_(
                    FinancialEvent.event_title.contains(query_text),
                    FinancialEvent.event_title_en.contains(query_text),
                    FinancialEvent.event_description.contains(query_text),
                )
            )
            .limit(limit)
            .all()
        )

        return [self._get_event_response(event) for event in events]

    def update_event_actual_value(
        self, event_id: int, actual_value: float, actual_time: Optional[datetime] = None
    ) -> Optional[FinancialEventResponse]:
        """
        更新事件实际值

        Args:
            event_id: 事件ID
            actual_value: 实际值
            actual_time: 实际发布时间

        Returns:
            更新后的事件响应或None
        """
        event = (
            self.db.query(FinancialEvent).filter(FinancialEvent.id == event_id).first()
        )

        if not event:
            return None

        event.actual_value = actual_value
        if actual_time:
            event.actual_time = actual_time
        else:
            event.actual_time = datetime.now(timezone.utc)

        event.last_data_update = datetime.now(timezone.utc)
        event.updated_at = datetime.now(timezone.utc)

        # 如果事件还未发布，更新状态
        if event.event_status == "scheduled":
            event.event_status = "published"

        self.db.commit()
        self.db.refresh(event)

        return self._get_event_response(event)

    def create_country_region(
        self, country_data: CountryRegionCreate
    ) -> CountryRegionResponse:
        """
        创建国家地区

        Args:
            country_data: 国家地区创建数据

        Returns:
            创建的国家地区响应

        Raises:
            ValueError: 当参数验证失败时
        """
        # 检查国家代码是否已存在
        existing_country = (
            self.db.query(CountryRegion)
            .filter(CountryRegion.country_code == country_data.country_code)
            .first()
        )
        if existing_country:
            raise ValueError(
                f"Country with code {country_data.country_code} already exists"
            )

        # 创建国家地区
        db_country = CountryRegion(**country_data.model_dump())
        self.db.add(db_country)
        self.db.commit()
        self.db.refresh(db_country)

        return CountryRegionResponse.model_validate(db_country)

    def get_country_region(self, country_id: int) -> Optional[CountryRegionResponse]:
        """
        获取单个国家地区

        Args:
            country_id: 国家地区ID

        Returns:
            国家地区响应或None
        """
        country = (
            self.db.query(CountryRegion).filter(CountryRegion.id == country_id).first()
        )

        if not country:
            return None

        return CountryRegionResponse.model_validate(country)

    def update_country_region(
        self, country_id: int, country_data: CountryRegionUpdate
    ) -> Optional[CountryRegionResponse]:
        """
        更新国家地区

        Args:
            country_id: 国家地区ID
            country_data: 更新数据

        Returns:
            更新后的国家地区响应或None
        """
        country = (
            self.db.query(CountryRegion).filter(CountryRegion.id == country_id).first()
        )

        if not country:
            return None

        # 更新字段
        update_data = country_data.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(country, field, value)

        self.db.commit()
        self.db.refresh(country)

        return CountryRegionResponse.model_validate(country)

    def delete_country_region(self, country_id: int) -> bool:
        """
        删除国家地区

        Args:
            country_id: 国家地区ID

        Returns:
            是否删除成功
        """
        country = (
            self.db.query(CountryRegion).filter(CountryRegion.id == country_id).first()
        )

        if not country:
            return False

        # 检查是否有关联的财经事件
        associated_events = (
            self.db.query(FinancialEvent)
            .filter(FinancialEvent.country_id == country_id)
            .first()
        )

        if associated_events:
            raise ValueError("Cannot delete country with associated financial events")

        # 检查是否有关联的经济指标
        associated_indicators = (
            self.db.query(EconomicIndicator)
            .filter(EconomicIndicator.country_id == country_id)
            .first()
        )

        if associated_indicators:
            raise ValueError(
                "Cannot delete country with associated economic indicators"
            )

        self.db.delete(country)
        self.db.commit()
        return True

    def get_countries(self, active_only: bool = True) -> List[CountryRegionResponse]:
        """
        获取国家地区列表

        Args:
            active_only: 是否只返回活跃的国家

        Returns:
            国家地区列表
        """
        query = self.db.query(CountryRegion)

        if active_only:
            query = query.filter(CountryRegion.is_active == True)

        countries = query.order_by(CountryRegion.country_name).all()

        return [CountryRegionResponse.model_validate(country) for country in countries]

    def get_event_types(self, active_only: bool = True) -> List[EventTypeResponse]:
        """
        获取事件类型列表

        Args:
            active_only: 是否只返回活跃的事件类型

        Returns:
            事件类型列表
        """
        query = self.db.query(EventType)

        if active_only:
            query = query.filter(EventType.is_active == True)

        event_types = query.order_by(EventType.type_name).all()

        return [
            EventTypeResponse.model_validate(event_type) for event_type in event_types
        ]

    def create_event_type(self, event_type_data: EventTypeCreate) -> EventTypeResponse:
        """
        创建事件类型

        Args:
            event_type_data: 事件类型创建数据

        Returns:
            创建的事件类型响应

        Raises:
            ValueError: 当参数验证失败时
        """
        # 检查类型代码是否已存在
        existing_type = (
            self.db.query(EventType)
            .filter(EventType.type_code == event_type_data.type_code)
            .first()
        )
        if existing_type:
            raise ValueError(
                f"Event type with code '{event_type_data.type_code}' already exists"
            )

        # 创建事件类型
        db_event_type = EventType(**event_type_data.model_dump())
        self.db.add(db_event_type)
        self.db.commit()
        self.db.refresh(db_event_type)

        return EventTypeResponse.model_validate(db_event_type)

    def get_event_type(self, event_type_id: int) -> Optional[EventTypeResponse]:
        """
        获取单个事件类型

        Args:
            event_type_id: 事件类型ID

        Returns:
            事件类型响应或None
        """
        event_type = (
            self.db.query(EventType).filter(EventType.id == event_type_id).first()
        )

        if not event_type:
            return None

        return EventTypeResponse.model_validate(event_type)

    def update_event_type(
        self, event_type_id: int, event_type_data: EventTypeUpdate
    ) -> Optional[EventTypeResponse]:
        """
        更新事件类型

        Args:
            event_type_id: 事件类型ID
            event_type_data: 更新数据

        Returns:
            更新后的事件类型响应或None
        """
        event_type = (
            self.db.query(EventType).filter(EventType.id == event_type_id).first()
        )

        if not event_type:
            return None

        # 更新字段
        update_data = event_type_data.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(event_type, field, value)

        self.db.commit()
        self.db.refresh(event_type)

        return EventTypeResponse.model_validate(event_type)

    def delete_event_type(self, event_type_id: int) -> bool:
        """
        删除事件类型

        Args:
            event_type_id: 事件类型ID

        Returns:
            是否删除成功

        Raises:
            ValueError: 当事件类型被使用时
        """
        event_type = (
            self.db.query(EventType).filter(EventType.id == event_type_id).first()
        )

        if not event_type:
            return False

        # 检查是否有关联的财经事件
        events_count = (
            self.db.query(FinancialEvent)
            .filter(FinancialEvent.event_type_id == event_type_id)
            .count()
        )

        if events_count > 0:
            raise ValueError(
                f"Cannot delete event type: {events_count} financial events are using this event type"
            )

        # 检查是否有关联的经济指标
        indicators_count = (
            self.db.query(EconomicIndicator)
            .filter(EconomicIndicator.event_type_id == event_type_id)
            .count()
        )

        if indicators_count > 0:
            raise ValueError(
                f"Cannot delete event type: {indicators_count} economic indicators are using this event type"
            )

        self.db.delete(event_type)
        self.db.commit()
        return True

    def get_calendar_summary(self, start_date: datetime, end_date: datetime) -> dict:
        """
        获取日历摘要

        Args:
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            日历摘要信息
        """
        # 总事件数
        total_events = (
            self.db.query(FinancialEvent)
            .filter(
                and_(
                    FinancialEvent.scheduled_time >= start_date,
                    FinancialEvent.scheduled_time <= end_date,
                )
            )
            .count()
        )

        # 按重要性级别分组
        importance_stats = (
            self.db.query(
                FinancialEvent.importance_level,
                func.count(FinancialEvent.id).label("count"),
            )
            .filter(
                and_(
                    FinancialEvent.scheduled_time >= start_date,
                    FinancialEvent.scheduled_time <= end_date,
                )
            )
            .group_by(FinancialEvent.importance_level)
            .all()
        )

        # 按国家分组
        country_stats = (
            self.db.query(
                CountryRegion.country_name, func.count(FinancialEvent.id).label("count")
            )
            .join(FinancialEvent, CountryRegion.id == FinancialEvent.country_id)
            .filter(
                and_(
                    FinancialEvent.scheduled_time >= start_date,
                    FinancialEvent.scheduled_time <= end_date,
                )
            )
            .group_by(CountryRegion.country_name)
            .order_by(func.count(FinancialEvent.id).desc())
            .limit(10)
            .all()
        )

        # 按事件类型分组
        event_type_stats = (
            self.db.query(
                EventType.type_name, func.count(FinancialEvent.id).label("count")
            )
            .join(FinancialEvent, EventType.id == FinancialEvent.event_type_id)
            .filter(
                and_(
                    FinancialEvent.scheduled_time >= start_date,
                    FinancialEvent.scheduled_time <= end_date,
                )
            )
            .group_by(EventType.type_name)
            .order_by(func.count(FinancialEvent.id).desc())
            .limit(10)
            .all()
        )

        return {
            "total_events": total_events,
            "importance_distribution": {
                str(level): count for level, count in importance_stats
            },
            "top_countries": [
                {"country": name, "count": count} for name, count in country_stats
            ],
            "top_event_types": [
                {"event_type": name, "count": count} for name, count in event_type_stats
            ],
        }

    def _get_event_response(self, event: FinancialEvent) -> FinancialEventResponse:
        """
        将数据库模型转换为响应模型

        Args:
            event: 财经事件模型

        Returns:
            事件响应
        """
        return FinancialEventResponse.model_validate(event)

    def _apply_filters(self, query, filters: FinancialEventFilter):
        """
        应用过滤条件

        Args:
            query: 查询对象
            filters: 过滤条件

        Returns:
            应用过滤后的查询对象
        """
        if filters.country_ids:
            query = query.filter(FinancialEvent.country_id.in_(filters.country_ids))

        if filters.event_type_ids:
            query = query.filter(
                FinancialEvent.event_type_id.in_(filters.event_type_ids)
            )

        if filters.importance_levels:
            query = query.filter(
                FinancialEvent.importance_level.in_(filters.importance_levels)
            )

        if filters.event_status:
            query = query.filter(FinancialEvent.event_status.in_(filters.event_status))

        if filters.start_time:
            query = query.filter(FinancialEvent.scheduled_time >= filters.start_time)

        if filters.end_time:
            query = query.filter(FinancialEvent.scheduled_time <= filters.end_time)

        if filters.market_impact:
            query = query.filter(
                FinancialEvent.market_impact.in_(filters.market_impact)
            )

        if filters.has_actual_value is not None:
            if filters.has_actual_value:
                query = query.filter(FinancialEvent.actual_value.isnot(None))
            else:
                query = query.filter(FinancialEvent.actual_value.is_(None))

        return query

    def _apply_sorting(self, query, pagination: PaginationParams):
        """
        应用排序

        Args:
            query: 查询对象
            pagination: 分页参数

        Returns:
            应用排序后的查询对象
        """
        # 获取排序字段
        sort_field = getattr(FinancialEvent, pagination.order_by, None)
        if not sort_field:
            sort_field = FinancialEvent.scheduled_time

        # 应用排序方向
        if pagination.order_direction == "desc":
            query = query.order_by(desc(sort_field))
        else:
            query = query.order_by(sort_field)

        return query

    # ==================== 经济指标相关方法 ====================

    def create_economic_indicator(
        self, indicator_data: EconomicIndicatorCreate
    ) -> EconomicIndicatorResponse:
        """
        创建经济指标

        Args:
            indicator_data: 指标创建数据

        Returns:
            创建的指标响应

        Raises:
            ValueError: 当参数验证失败时
        """
        # 验证国家是否存在
        country = (
            self.db.query(CountryRegion)
            .filter(
                CountryRegion.id == indicator_data.country_id,
                CountryRegion.is_active == True,
            )
            .first()
        )
        if not country:
            raise ValueError(
                f"Country with id {indicator_data.country_id} not found or inactive"
            )

        # 验证事件类型是否存在
        event_type = (
            self.db.query(EventType)
            .filter(
                EventType.id == indicator_data.event_type_id,
                EventType.is_active == True,
            )
            .first()
        )
        if not event_type:
            raise ValueError(
                f"Event type with id {indicator_data.event_type_id} not found or inactive"
            )

        # 检查指标代码在该国家下是否已存在
        existing_indicator = (
            self.db.query(EconomicIndicator)
            .filter(
                EconomicIndicator.country_id == indicator_data.country_id,
                EconomicIndicator.indicator_code == indicator_data.indicator_code,
            )
            .first()
        )
        if existing_indicator:
            raise ValueError(
                f"Indicator with code '{indicator_data.indicator_code}' already exists for this country"
            )

        # 创建经济指标
        db_indicator = EconomicIndicator(**indicator_data.model_dump())
        self.db.add(db_indicator)
        self.db.commit()
        self.db.refresh(db_indicator)

        return self._get_indicator_response(db_indicator)

    def get_economic_indicator(
        self, indicator_id: int
    ) -> Optional[EconomicIndicatorResponse]:
        """
        获取单个经济指标

        Args:
            indicator_id: 指标ID

        Returns:
            指标响应或None
        """
        indicator = (
            self.db.query(EconomicIndicator)
            .options(
                selectinload(EconomicIndicator.country),
                selectinload(EconomicIndicator.event_type),
            )
            .filter(EconomicIndicator.id == indicator_id)
            .first()
        )

        if not indicator:
            return None

        return self._get_indicator_response(indicator)

    def update_economic_indicator(
        self, indicator_id: int, indicator_data: EconomicIndicatorUpdate
    ) -> Optional[EconomicIndicatorResponse]:
        """
        更新经济指标

        Args:
            indicator_id: 指标ID
            indicator_data: 更新数据

        Returns:
            更新后的指标响应或None
        """
        indicator = (
            self.db.query(EconomicIndicator)
            .filter(EconomicIndicator.id == indicator_id)
            .first()
        )

        if not indicator:
            return None

        # 更新字段
        update_data = indicator_data.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(indicator, field, value)

        self.db.commit()
        self.db.refresh(indicator)

        return self._get_indicator_response(indicator)

    def delete_economic_indicator(self, indicator_id: int) -> bool:
        """
        删除经济指标

        Args:
            indicator_id: 指标ID

        Returns:
            是否删除成功

        Raises:
            ValueError: 当有关联的财经事件时
        """
        indicator = (
            self.db.query(EconomicIndicator)
            .filter(EconomicIndicator.id == indicator_id)
            .first()
        )

        if not indicator:
            return False

        # 检查是否有关联的财经事件
        associated_events = (
            self.db.query(FinancialEvent)
            .filter(FinancialEvent.indicator_id == indicator_id)
            .first()
        )

        if associated_events:
            raise ValueError("Cannot delete indicator with associated financial events")

        self.db.delete(indicator)
        self.db.commit()
        return True

    def list_economic_indicators(
        self,
        country_id: Optional[int] = None,
        event_type_id: Optional[int] = None,
        active_only: bool = True,
        page: int = 1,
        size: int = 20,
    ) -> PaginatedResponse:
        """
        获取经济指标列表

        Args:
            country_id: 国家ID过滤
            event_type_id: 事件类型ID过滤
            active_only: 是否只返回活跃的指标
            page: 页码
            size: 每页大小

        Returns:
            分页的指标列表
        """
        query = self.db.query(EconomicIndicator).options(
            selectinload(EconomicIndicator.country),
            selectinload(EconomicIndicator.event_type),
        )

        # 应用过滤条件
        if country_id:
            query = query.filter(EconomicIndicator.country_id == country_id)

        if event_type_id:
            query = query.filter(EconomicIndicator.event_type_id == event_type_id)

        if active_only:
            query = query.filter(EconomicIndicator.is_active == True)

        # 计算总数
        total = query.count()

        # 应用分页
        offset = (page - 1) * size
        indicators = (
            query.order_by(EconomicIndicator.indicator_name)
            .offset(offset)
            .limit(size)
            .all()
        )

        # 转换为响应格式
        items = [self._get_indicator_response(indicator) for indicator in indicators]

        # 计算页数
        pages = (total + size - 1) // size

        return PaginatedResponse(
            items=items, total=total, page=page, size=size, pages=pages
        )

    def get_indicators_by_country(
        self, country_id: int, active_only: bool = True
    ) -> List[EconomicIndicatorResponse]:
        """
        获取指定国家的经济指标列表

        Args:
            country_id: 国家ID
            active_only: 是否只返回活跃的指标

        Returns:
            该国家的指标列表
        """
        query = (
            self.db.query(EconomicIndicator)
            .options(
                selectinload(EconomicIndicator.country),
                selectinload(EconomicIndicator.event_type),
            )
            .filter(EconomicIndicator.country_id == country_id)
        )

        if active_only:
            query = query.filter(EconomicIndicator.is_active == True)

        indicators = query.order_by(EconomicIndicator.indicator_name).all()

        return [self._get_indicator_response(indicator) for indicator in indicators]

    def get_indicators_by_event_type(
        self, event_type_id: int, active_only: bool = True
    ) -> List[EconomicIndicatorResponse]:
        """
        获取指定事件类型的经济指标列表

        Args:
            event_type_id: 事件类型ID
            active_only: 是否只返回活跃的指标

        Returns:
            该事件类型的指标列表
        """
        query = (
            self.db.query(EconomicIndicator)
            .options(
                selectinload(EconomicIndicator.country),
                selectinload(EconomicIndicator.event_type),
            )
            .filter(EconomicIndicator.event_type_id == event_type_id)
        )

        if active_only:
            query = query.filter(EconomicIndicator.is_active == True)

        indicators = query.order_by(EconomicIndicator.indicator_name).all()

        return [self._get_indicator_response(indicator) for indicator in indicators]

    def search_economic_indicators(
        self, query_text: str, limit: int = 50
    ) -> List[EconomicIndicatorResponse]:
        """
        搜索经济指标

        Args:
            query_text: 搜索文本
            limit: 结果限制

        Returns:
            搜索结果列表
        """
        indicators = (
            self.db.query(EconomicIndicator)
            .options(
                selectinload(EconomicIndicator.country),
                selectinload(EconomicIndicator.event_type),
            )
            .filter(
                or_(
                    EconomicIndicator.indicator_name.contains(query_text),
                    EconomicIndicator.indicator_name_en.contains(query_text),
                    EconomicIndicator.description.contains(query_text),
                    EconomicIndicator.indicator_code.contains(query_text),
                )
            )
            .filter(EconomicIndicator.is_active == True)
            .limit(limit)
            .all()
        )

        return [self._get_indicator_response(indicator) for indicator in indicators]

    def _get_indicator_response(
        self, indicator: EconomicIndicator
    ) -> EconomicIndicatorResponse:
        """
        将数据库模型转换为响应模型

        Args:
            indicator: 经济指标模型

        Returns:
            指标响应
        """
        return EconomicIndicatorResponse.model_validate(indicator)
