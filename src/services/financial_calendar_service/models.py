"""
财经日历服务数据模型
定义财经事件、国家地区、经济指标等相关的数据库模型
"""

from datetime import time

from sqlalchemy import (BIGINT, DECIMAL, Boolean, CheckConstraint, Column,
                        DateTime, ForeignKey, Index, Integer, String, Text,
                        Time, TIMESTAMP)
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from ...core.database import Base


class CountryRegion(Base):
    """
    国家地区表
    存储全球主要国家和地区的基础信息
    """

    __tablename__ = "countries_regions"

    id = Column(
        BIGINT, primary_key=True, index=True, comment="国家地区唯一标识符，自增主键"
    )
    country_code = Column(String(10), unique=True, nullable=False, comment="国家代码")
    country_name = Column(String(100), nullable=False, comment="国家名称")
    country_name_en = Column(String(100), comment="英文国家名称")
    region = Column(String(50), comment="所属地区")

    # 显示属性
    flag_icon = Column(String(100), comment="国旗图标")
    time_zone = Column(String(50), nullable=False, comment="时区标识")
    dst_support = Column(Boolean, default=False, comment="是否支持夏令时")

    # 货币信息
    primary_currency = Column(String(10), nullable=False, comment="主要货币代码")
    currency_symbol = Column(String(10), comment="货币符号")
    exchange_rate_to_usd = Column(DECIMAL(10, 6), comment="对美元汇率")
    exchange_rate_updated_at = Column(DateTime, comment="汇率更新时间")

    # 重要性评级
    global_importance_score = Column(
        DECIMAL(3, 2), default=1.0, comment="全球重要性评分"
    )
    financial_market_weight = Column(DECIMAL(3, 2), default=1.0, comment="金融市场权重")

    # 数据发布配置
    typical_data_release_time = Column(
        Time, default=time(9, 30), comment="典型数据发布时间"
    )
    market_open_time = Column(Time, comment="市场开盘时间")
    market_close_time = Column(Time, comment="市场收盘时间")

    is_active = Column(Boolean, default=True, comment="是否启用")
    data_quality_score = Column(DECIMAL(3, 2), default=0.8, comment="数据质量评分")
    created_at = Column(TIMESTAMP, server_default=func.now(), comment="创建时间")

    # 关系定义
    economic_indicators = relationship(
        "EconomicIndicator", back_populates="country", cascade="all, delete-orphan"
    )
    financial_events = relationship(
        "FinancialEvent", back_populates="country", cascade="all, delete-orphan"
    )

    # 索引定义
    __table_args__ = (
        Index("idx_country_code", "country_code"),
        Index("idx_country_active", "is_active"),
        Index("idx_country_importance", "global_importance_score"),
    )


class EventType(Base):
    """
    事件类型表
    定义各种财经事件的分类和属性
    """

    __tablename__ = "event_types"

    id = Column(BIGINT, primary_key=True, index=True, comment="事件类型唯一标识符")
    type_code = Column(String(50), unique=True, nullable=False, comment="事件类型代码")
    type_name = Column(String(100), nullable=False, comment="事件类型名称")
    type_name_en = Column(String(100), comment="英文事件类型名称")

    category = Column(String(50), nullable=False, comment="事件大类")
    subcategory = Column(String(50), comment="事件子类")

    icon = Column(String(100), comment="事件图标")
    color = Column(String(7), comment="事件颜色")

    default_importance = Column(Integer, default=2, comment="默认重要性级别")
    typical_market_impact = Column(
        String(20), default="medium", comment="典型市场影响程度"
    )

    description = Column(Text, comment="事件类型描述")

    is_active = Column(Boolean, default=True, comment="是否启用")
    created_at = Column(TIMESTAMP, server_default=func.now(), comment="创建时间")

    # 关系定义
    economic_indicators = relationship(
        "EconomicIndicator", back_populates="event_type", cascade="all, delete-orphan"
    )
    financial_events = relationship(
        "FinancialEvent", back_populates="event_type", cascade="all, delete-orphan"
    )

    # 索引定义
    __table_args__ = (
        Index("idx_event_type_code", "type_code"),
        Index("idx_event_type_category", "category", "subcategory"),
        Index("idx_event_type_active", "is_active"),
        CheckConstraint(
            "default_importance BETWEEN 1 AND 3", name="check_default_importance"
        ),
    )


class EconomicIndicator(Base):
    """
    经济指标表
    存储各国的具体经济指标信息
    """

    __tablename__ = "economic_indicators"

    id = Column(BIGINT, primary_key=True, index=True, comment="经济指标唯一标识符")
    indicator_code = Column(String(50), nullable=False, comment="指标代码")
    indicator_name = Column(String(200), nullable=False, comment="指标名称")
    indicator_name_en = Column(String(200), comment="英文指标名称")

    country_id = Column(
        BIGINT, ForeignKey("countries_regions.id"), nullable=False, comment="所属国家ID"
    )
    event_type_id = Column(
        BIGINT, ForeignKey("event_types.id"), nullable=False, comment="事件类型ID"
    )

    unit = Column(String(20), comment="数据单位")
    data_type = Column(String(20), default="percentage", comment="数据类型")
    decimal_places = Column(Integer, default=1, comment="小数位数")

    release_agency = Column(String(200), comment="发布机构")
    release_frequency = Column(String(50), comment="发布频率")
    typical_release_day = Column(Integer, comment="典型发布日期")
    typical_release_time = Column(Time, comment="典型发布时间")

    market_importance = Column(Integer, default=2, comment="市场重要性级别")
    volatility_impact = Column(DECIMAL(3, 2), default=1.0, comment="波动性影响")

    historical_avg = Column(DECIMAL(10, 2), comment="历史平均值")
    historical_max = Column(DECIMAL(10, 2), comment="历史最大值")
    historical_min = Column(DECIMAL(10, 2), comment="历史最小值")

    description = Column(Text, comment="指标描述")
    methodology = Column(Text, comment="计算方法")

    is_active = Column(Boolean, default=True, comment="是否启用")
    created_at = Column(TIMESTAMP, server_default=func.now(), comment="创建时间")

    # 关系定义
    country = relationship("CountryRegion", back_populates="economic_indicators")
    event_type = relationship("EventType", back_populates="economic_indicators")
    financial_events = relationship(
        "FinancialEvent", back_populates="indicator", cascade="all, delete-orphan"
    )

    # 索引定义
    __table_args__ = (
        Index("idx_indicator_country_code", "country_id", "indicator_code"),
        Index("idx_indicator_importance", "market_importance"),
        Index("idx_indicator_active", "is_active"),
        CheckConstraint(
            "market_importance BETWEEN 1 AND 3", name="check_market_importance"
        ),
        CheckConstraint(
            "typical_release_day BETWEEN 1 AND 31", name="check_release_day"
        ),
    )


class FinancialEvent(Base):
    """
    财经事件表
    存储具体的财经事件数据
    """

    __tablename__ = "financial_events"

    id = Column(BIGINT, primary_key=True, index=True, comment="财经事件唯一标识符")

    # 基础信息
    event_title = Column(String(500), nullable=False, comment="事件标题")
    event_title_en = Column(String(500), comment="英文事件标题")
    event_type_id = Column(
        BIGINT, ForeignKey("event_types.id"), nullable=False, comment="事件类型ID"
    )
    country_id = Column(
        BIGINT, ForeignKey("countries_regions.id"), nullable=False, comment="国家地区ID"
    )
    indicator_id = Column(
        BIGINT, ForeignKey("economic_indicators.id"), comment="经济指标ID"
    )

    # 时间信息
    scheduled_time = Column(DateTime, nullable=False, comment="计划时间")
    actual_time = Column(DateTime, comment="实际时间")
    time_zone = Column(String(10), default="UTC", comment="时区")
    time_precision = Column(String(10), default="minute", comment="时间精度")

    # 重要性评级
    importance_level = Column(Integer, nullable=False, comment="重要性级别")
    market_impact = Column(String(20), default="medium", comment="市场影响程度")

    # 数据值
    previous_value = Column(DECIMAL(15, 4), comment="前值")
    forecast_value = Column(DECIMAL(15, 4), comment="预期值")
    actual_value = Column(DECIMAL(15, 4), comment="实际值")
    revised_value = Column(DECIMAL(15, 4), comment="修正值")

    # 数据单位和格式
    value_unit = Column(String(20), comment="数值单位")
    value_format = Column(String(20), default="decimal", comment="数值格式")

    # 市场影响分析
    impact_direction = Column(String(20), comment="影响方向")
    volatility_expected = Column(DECIMAL(3, 2), comment="预期波动性")
    actual_market_impact = Column(JSONB, comment="实际市场影响")

    # 内容描述
    event_description = Column(Text, comment="事件描述")
    impact_analysis = Column(Text, comment="影响分析")
    historical_context = Column(Text, comment="历史背景")

    # 状态管理
    event_status = Column(String(20), default="scheduled", comment="事件状态")
    data_source = Column(String(100), comment="数据来源")
    source_url = Column(String(1000), comment="来源链接")

    # 版本管理
    version = Column(Integer, default=1, comment="版本号")
    parent_event_id = Column(
        BIGINT, ForeignKey("financial_events.id"), comment="父事件ID"
    )

    # 订阅和通知
    subscriber_count = Column(Integer, default=0, comment="订阅人数")
    notification_sent = Column(Boolean, default=False, comment="是否已发送通知")
    notification_sent_at = Column(DateTime, comment="通知发送时间")

    created_at = Column(TIMESTAMP, server_default=func.now(), comment="创建时间")
    updated_at = Column(
        TIMESTAMP,
        server_default=func.now(),
        onupdate=func.now(),
        comment="最后更新时间",
    )
    last_data_update = Column(DateTime, comment="最后数据更新时间")

    # 关系定义
    event_type = relationship("EventType", back_populates="financial_events")
    country = relationship("CountryRegion", back_populates="financial_events")
    indicator = relationship("EconomicIndicator", back_populates="financial_events")
    parent_event = relationship("FinancialEvent", remote_side=[id])
    related_events_source = relationship(
        "EventRelatedEvent",
        foreign_keys="EventRelatedEvent.source_event_id",
        cascade="all, delete-orphan",
    )
    related_events_target = relationship(
        "EventRelatedEvent",
        foreign_keys="EventRelatedEvent.related_event_id",
        cascade="all, delete-orphan",
    )
    affected_markets = relationship(
        "EventAffectedMarket", back_populates="event", cascade="all, delete-orphan"
    )

    # 索引定义
    __table_args__ = (
        Index("idx_event_scheduled_time", "scheduled_time"),
        Index("idx_event_country_importance", "country_id", "importance_level"),
        Index("idx_event_type_status", "event_type_id", "event_status"),
        Index("idx_event_status_time", "event_status", "scheduled_time"),
        CheckConstraint(
            "importance_level BETWEEN 1 AND 3", name="check_importance_level"
        ),
    )


class EventRelatedEvent(Base):
    """
    事件相关事件表
    记录财经事件之间的关联关系
    """

    __tablename__ = "event_related_events"

    id = Column(BIGINT, primary_key=True, index=True, comment="事件关联关系唯一标识符")
    source_event_id = Column(
        BIGINT, ForeignKey("financial_events.id"), nullable=False, comment="源事件ID"
    )
    related_event_id = Column(
        BIGINT, ForeignKey("financial_events.id"), nullable=False, comment="关联事件ID"
    )

    relation_type = Column(String(50), nullable=False, comment="关系类型")
    relevance_score = Column(DECIMAL(3, 2), default=1.0, comment="相关性评分")

    created_at = Column(TIMESTAMP, server_default=func.now(), comment="创建时间")

    # 关系定义
    source_event = relationship(
        "FinancialEvent",
        foreign_keys=[source_event_id],
        overlaps="related_events_source",
    )
    related_event = relationship(
        "FinancialEvent",
        foreign_keys=[related_event_id],
        overlaps="related_events_target",
    )

    # 索引定义
    __table_args__ = (
        Index("idx_event_relation_source", "source_event_id", "relation_type"),
        Index("idx_event_relation_target", "related_event_id", "relation_type"),
        Index(
            "idx_event_relation_unique",
            "source_event_id",
            "related_event_id",
            "relation_type",
            unique=True,
        ),
        CheckConstraint(
            "source_event_id != related_event_id", name="check_no_self_relation"
        ),
    )


class EventAffectedMarket(Base):
    """
    事件影响市场表
    记录财经事件对不同市场的影响程度
    """

    __tablename__ = "event_affected_markets"

    id = Column(BIGINT, primary_key=True, index=True, comment="事件影响市场唯一标识符")
    event_id = Column(
        BIGINT, ForeignKey("financial_events.id"), nullable=False, comment="财经事件ID"
    )

    market_type = Column(String(50), nullable=False, comment="市场类型")
    impact_level = Column(String(20), default="medium", comment="影响程度")
    expected_volatility = Column(DECIMAL(3, 2), comment="预期波动率")
    actual_volatility = Column(DECIMAL(3, 2), comment="实际波动率")

    created_at = Column(TIMESTAMP, server_default=func.now(), comment="创建时间")

    # 关系定义
    event = relationship("FinancialEvent", back_populates="affected_markets")

    # 索引定义
    __table_args__ = (
        Index("idx_market_event_type", "event_id", "market_type"),
        Index("idx_market_impact_level", "market_type", "impact_level"),
        Index("idx_market_event_unique", "event_id", "market_type", unique=True),
    )
