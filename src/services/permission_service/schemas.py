"""
权限服务数据传输对象
定义API请求和响应的数据格式
"""

from datetime import datetime
from typing import List, Optional
from pydantic import BaseModel, Field


# ==================== 权限相关 ====================

class PermissionBase(BaseModel):
    """权限基础模型"""
    code: str = Field(..., description="权限编码", max_length=100)
    name: str = Field(..., description="权限名称", max_length=100)
    description: Optional[str] = Field(None, description="权限描述")
    module: str = Field(..., description="所属模块", max_length=50)
    resource: str = Field(..., description="资源类型", max_length=50)
    action: str = Field(..., description="操作类型", max_length=50)


class PermissionCreate(PermissionBase):
    """权限创建请求"""
    pass


class PermissionUpdate(BaseModel):
    """权限更新请求"""
    code: Optional[str] = Field(None, description="权限编码", max_length=100)
    name: Optional[str] = Field(None, description="权限名称", max_length=100)
    description: Optional[str] = Field(None, description="权限描述")
    module: Optional[str] = Field(None, description="所属模块", max_length=50)
    resource: Optional[str] = Field(None, description="资源类型", max_length=50)
    action: Optional[str] = Field(None, description="操作类型", max_length=50)


class PermissionResponse(PermissionBase):
    """权限响应"""
    id: int
    created_at: Optional[datetime] = Field(None, description="创建时间")
    updated_at: Optional[datetime] = Field(None, description="更新时间")
    
    class Config:
        from_attributes = True


# ==================== 角色相关 ====================

class RoleBase(BaseModel):
    """角色基础模型"""
    name: str = Field(..., description="角色名称", max_length=50)
    description: Optional[str] = Field(None, description="角色描述")
    parent_id: Optional[int] = Field(None, description="父角色ID")


class RoleCreate(RoleBase):
    """角色创建请求"""
    permission_ids: Optional[List[int]] = Field(default=[], description="权限ID列表")


class RoleUpdate(BaseModel):
    """角色更新请求"""
    name: Optional[str] = Field(None, description="角色名称", max_length=50)
    description: Optional[str] = Field(None, description="角色描述")
    parent_id: Optional[int] = Field(None, description="父角色ID")


class RoleResponse(RoleBase):
    """角色响应"""
    id: int
    is_system: bool
    created_at: Optional[datetime] = Field(None, description="创建时间")
    updated_at: Optional[datetime] = Field(None, description="更新时间")
    permissions: List[PermissionResponse] = Field(default=[], description="角色权限列表")
    children: List["RoleResponse"] = Field(default=[], description="子角色列表")
    
    class Config:
        from_attributes = True


# ==================== 用户角色相关 ====================

class UserRoleAssign(BaseModel):
    """用户角色分配请求"""
    user_id: int = Field(..., description="用户ID")
    role_ids: List[int] = Field(..., description="角色ID列表")


class UserRoleResponse(BaseModel):
    """用户角色响应"""
    id: int
    user_id: int
    role_id: int
    created_at: Optional[datetime] = Field(None, description="创建时间")
    created_by: Optional[int] = None
    
    class Config:
        from_attributes = True


# ==================== 角色权限相关 ====================

class RolePermissionAssign(BaseModel):
    """角色权限分配请求"""
    role_id: int = Field(..., description="角色ID")
    permission_ids: List[int] = Field(..., description="权限ID列表")


class RolePermissionResponse(BaseModel):
    """角色权限响应"""
    id: int
    role_id: int
    permission_id: int
    created_at: Optional[datetime] = Field(None, description="创建时间")
    
    class Config:
        from_attributes = True


# ==================== 权限验证相关 ====================

class PermissionCheckRequest(BaseModel):
    """权限检查请求"""
    user_id: int = Field(..., description="用户ID")
    permission_code: str = Field(..., description="权限编码")


class PermissionCheckResponse(BaseModel):
    """权限检查响应"""
    has_permission: bool = Field(..., description="是否有权限")
    permission_code: str = Field(..., description="权限编码")
    user_id: int = Field(..., description="用户ID")


class UserPermissionsResponse(BaseModel):
    """用户权限列表响应"""
    user_id: int = Field(..., description="用户ID")
    roles: List[RoleResponse] = Field(..., description="用户角色列表")
    permissions: List[PermissionResponse] = Field(..., description="用户权限列表")
    permission_codes: List[str] = Field(..., description="权限编码列表")


# ==================== 权限操作日志相关 ====================

class PermissionOperationLogResponse(BaseModel):
    """权限操作日志响应"""
    id: int
    operator_id: int
    operation_type: str
    target_type: str
    target_id: int
    role_id: Optional[int] = None
    permission_id: Optional[int] = None
    operation_time: Optional[datetime] = Field(None, description="操作时间")
    ip_address: Optional[str] = None
    
    class Config:
        from_attributes = True


# ==================== 批量操作相关 ====================

class BatchRoleAssignRequest(BaseModel):
    """批量角色分配请求"""
    user_ids: List[int] = Field(..., description="用户ID列表")
    role_id: int = Field(..., description="角色ID")


class BatchPermissionAssignRequest(BaseModel):
    """批量权限分配请求"""
    role_ids: List[int] = Field(..., description="角色ID列表")
    permission_id: int = Field(..., description="权限ID")


class OperationResult(BaseModel):
    """操作结果"""
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="操作信息")
    affected_count: int = Field(default=0, description="影响的记录数")


# ==================== 分页相关 ====================

class PageRequest(BaseModel):
    """分页请求"""
    page: int = Field(default=1, ge=1, description="页码")
    size: int = Field(default=20, ge=1, le=100, description="每页大小")


class PageResponse(BaseModel):
    """分页响应"""
    page: int = Field(..., description="当前页码")
    size: int = Field(..., description="每页大小")
    total: int = Field(..., description="总记录数")
    pages: int = Field(..., description="总页数")


class RolePageResponse(PageResponse):
    """角色分页响应"""
    items: List[RoleResponse] = Field(..., description="角色列表")


class PermissionPageResponse(PageResponse):
    """权限分页响应"""
    items: List[PermissionResponse] = Field(..., description="权限列表")


class PermissionOperationLogPageResponse(PageResponse):
    """权限操作日志分页响应"""
    items: List[PermissionOperationLogResponse] = Field(..., description="操作日志列表")


# 更新前向引用
RoleResponse.model_rebuild() 