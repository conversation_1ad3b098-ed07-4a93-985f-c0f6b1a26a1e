"""
权限服务依赖注入
定义权限相关的依赖
"""

from typing import Annotated
from fastapi import Depends, HTTPException, status
from sqlalchemy.orm import Session

from ...core.database import get_db
from ...core.cache import get_cache_service
from ..user_service.dependencies import get_current_active_user
from ..user_service.models import User
from .service import PermissionService, RoleService, UserRoleService, AuthorizationService


def get_permission_service(
    db: Annotated[Session, Depends(get_db)],
    cache_service = Depends(get_cache_service)
) -> PermissionService:
    """
    获取权限服务
    
    Args:
        db: 数据库会话
        cache_service: 缓存服务
        
    Returns:
        权限服务实例
    """
    return PermissionService(db, cache_service)


def get_role_service(
    db: Annotated[Session, Depends(get_db)],
    cache_service = Depends(get_cache_service)
) -> RoleService:
    """
    获取角色服务
    
    Args:
        db: 数据库会话
        cache_service: 缓存服务
        
    Returns:
        角色服务实例
    """
    return RoleService(db, cache_service)


def get_user_role_service(
    db: Annotated[Session, Depends(get_db)],
    cache_service = Depends(get_cache_service)
) -> UserRoleService:
    """
    获取用户角色服务
    
    Args:
        db: 数据库会话
        cache_service: 缓存服务
        
    Returns:
        用户角色服务实例
    """
    return UserRoleService(db, cache_service)


def get_authorization_service(
    db: Annotated[Session, Depends(get_db)],
    cache_service = Depends(get_cache_service)
) -> AuthorizationService:
    """
    获取权限验证服务
    
    Args:
        db: 数据库会话
        cache_service: 缓存服务
        
    Returns:
        权限验证服务实例
    """
    return AuthorizationService(db, cache_service)


def require_permission(permission_code: str):
    """
    权限验证装饰器依赖
    
    Args:
        permission_code: 需要的权限编码
        
    Returns:
        权限检查依赖函数
    """
    def permission_checker(
        current_user: Annotated[User, Depends(get_current_active_user)],
        auth_service: Annotated[AuthorizationService, Depends(get_authorization_service)]
    ):
        """
        检查用户是否拥有指定权限
        
        Args:
            current_user: 当前用户
            auth_service: 权限验证服务
            
        Returns:
            当前用户对象
            
        Raises:
            HTTPException: 权限不足时抛出403错误
        """
        if not auth_service.has_permission(current_user.id, permission_code):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Permission required: {permission_code}"
            )
        return current_user
    
    return permission_checker


def require_admin(
    current_user: Annotated[User, Depends(get_current_active_user)],
    user_role_service: Annotated[UserRoleService, Depends(get_user_role_service)]
):
    """
    要求管理员权限
    
    Args:
        current_user: 当前用户
        user_role_service: 用户角色服务
        
    Returns:
        当前用户对象
        
    Raises:
        HTTPException: 非管理员时抛出403错误
    """
    if not user_role_service.has_role_by_name(current_user.id, "Admin"):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin permission required"
        )
    return current_user


def require_roles(*role_names: str):
    """
    要求指定角色之一
    
    Args:
        role_names: 角色名称列表
        
    Returns:
        角色检查依赖函数
    """
    def role_checker(
        current_user: Annotated[User, Depends(get_current_active_user)],
        user_role_service: Annotated[UserRoleService, Depends(get_user_role_service)]
    ):
        """
        检查用户是否拥有指定角色之一
        
        Args:
            current_user: 当前用户
            user_role_service: 用户角色服务
            
        Returns:
            当前用户对象
            
        Raises:
            HTTPException: 角色不匹配时抛出403错误
        """
        has_role = any(
            user_role_service.has_role_by_name(current_user.id, role_name)
            for role_name in role_names
        )
        
        if not has_role:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Required roles: {', '.join(role_names)}"
            )
        return current_user
    
    return role_checker


def require_any_permission(*permission_codes: str):
    """
    要求指定权限之一
    
    Args:
        permission_codes: 权限编码列表
        
    Returns:
        权限检查依赖函数
    """
    def permission_checker(
        current_user: Annotated[User, Depends(get_current_active_user)],
        auth_service: Annotated[AuthorizationService, Depends(get_authorization_service)]
    ):
        """
        检查用户是否拥有指定权限之一
        
        Args:
            current_user: 当前用户
            auth_service: 权限验证服务
            
        Returns:
            当前用户对象
            
        Raises:
            HTTPException: 权限不足时抛出403错误
        """
        has_permission = any(
            auth_service.has_permission(current_user.id, perm_code)
            for perm_code in permission_codes
        )
        
        if not has_permission:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Required permissions: {', '.join(permission_codes)}"
            )
        return current_user
    
    return permission_checker


def require_all_permissions(*permission_codes: str):
    """
    要求所有指定权限
    
    Args:
        permission_codes: 权限编码列表
        
    Returns:
        权限检查依赖函数
    """
    def permission_checker(
        current_user: Annotated[User, Depends(get_current_active_user)],
        auth_service: Annotated[AuthorizationService, Depends(get_authorization_service)]
    ):
        """
        检查用户是否拥有所有指定权限
        
        Args:
            current_user: 当前用户
            auth_service: 权限验证服务
            
        Returns:
            当前用户对象
            
        Raises:
            HTTPException: 权限不足时抛出403错误
        """
        missing_permissions = [
            perm_code for perm_code in permission_codes
            if not auth_service.has_permission(current_user.id, perm_code)
        ]
        
        if missing_permissions:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Missing permissions: {', '.join(missing_permissions)}"
            )
        return current_user
    
    return permission_checker 