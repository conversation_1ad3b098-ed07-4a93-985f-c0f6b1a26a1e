"""
权限服务C端API路由
定义面向用户的权限查询相关HTTP接口
"""

from typing import Annotated, List
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from ...core.database import get_db
from ..user_service.dependencies import get_current_active_user
from ..user_service.models import User
from .dependencies import (
    get_authorization_service,
    get_role_service,
    get_user_role_service,
)
from .schemas import (
    PermissionCheckRequest,
    PermissionCheckResponse,
    PermissionResponse,
    RoleResponse,
    UserPermissionsResponse,
)
from .service import AuthorizationService, RoleService, UserRoleService

router = APIRouter()


# ==================== C端权限查询接口 ====================


@router.get(
    "/me",
    response_model=UserPermissionsResponse,
    summary="获取我的权限",
    description="获取当前用户的所有权限和角色",
)
async def get_my_permissions(
    current_user: Annotated[User, Depends(get_current_active_user)],
    user_role_service: Annotated[UserRoleService, Depends(get_user_role_service)],
    auth_service: Annotated[AuthorizationService, Depends(get_authorization_service)],
):
    """获取当前用户的权限信息"""
    roles = user_role_service.get_user_roles(current_user.id)
    permissions = auth_service.get_user_permissions(current_user.id)
    permission_codes = [perm.code for perm in permissions]

    return UserPermissionsResponse(
        user_id=current_user.id,
        roles=[RoleResponse.model_validate(role) for role in roles],
        permissions=[PermissionResponse.model_validate(perm) for perm in permissions],
        permission_codes=permission_codes,
    )


@router.get(
    "/me/roles",
    response_model=List[RoleResponse],
    summary="获取我的角色",
    description="获取当前用户的所有角色",
)
async def get_my_roles(
    current_user: Annotated[User, Depends(get_current_active_user)],
    user_role_service: Annotated[UserRoleService, Depends(get_user_role_service)],
):
    """获取当前用户的角色"""
    roles = user_role_service.get_user_roles(current_user.id)
    return [RoleResponse.model_validate(role) for role in roles]


@router.post(
    "/check",
    response_model=PermissionCheckResponse,
    summary="检查权限",
    description="检查当前用户是否拥有指定权限",
)
async def check_permission(
    check_request: PermissionCheckRequest,
    current_user: Annotated[User, Depends(get_current_active_user)],
    auth_service: Annotated[AuthorizationService, Depends(get_authorization_service)],
):
    """检查当前用户是否拥有指定权限"""
    has_permission = auth_service.check_user_permission(
        current_user.id, check_request.permission_code
    )
    return PermissionCheckResponse(has_permission=has_permission)
