"""
权限服务业务逻辑
实现角色权限管理的核心功能
"""

import logging
from typing import List, Optional, Set, Tuple
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_, text

from .models import Permission, Role, RolePermission, UserRole, PermissionOperationLog
from .schemas import (
    PermissionCreate, PermissionUpdate, RoleCreate, RoleUpdate,
    UserRoleAssign, RolePermissionAssign
)
from .cache import PermissionCacheService
from ..user_service.models import User

logger = logging.getLogger(__name__)


class PermissionService:
    """权限管理服务"""
    
    def __init__(self, db: Session, cache_service: PermissionCacheService):
        """
        初始化权限服务
        
        Args:
            db: 数据库会话
            cache_service: 缓存服务
        """
        self.db = db
        self.cache = cache_service
    
    def create_permission(self, permission_data: PermissionCreate) -> Permission:
        """
        创建权限
        
        Args:
            permission_data: 权限创建数据
            
        Returns:
            创建的权限对象
            
        Raises:
            ValueError: 权限编码已存在
        """
        # 检查权限编码是否已存在
        existing_permission = self.db.query(Permission).filter(
            Permission.code == permission_data.code
        ).first()
        
        if existing_permission:
            raise ValueError(f"Permission code '{permission_data.code}' already exists")
        
        # 创建权限
        permission = Permission(**permission_data.model_dump())
        self.db.add(permission)
        self.db.commit()
        self.db.refresh(permission)
        
        logger.info(f"Created permission: {permission.code}")
        return permission
    
    def get_permission(self, permission_id: int) -> Optional[Permission]:
        """
        获取权限详情
        
        Args:
            permission_id: 权限ID
            
        Returns:
            权限对象或None
        """
        return self.db.query(Permission).filter(Permission.id == permission_id).first()
    
    def get_permission_by_code(self, code: str) -> Optional[Permission]:
        """
        根据编码获取权限
        
        Args:
            code: 权限编码
            
        Returns:
            权限对象或None
        """
        return self.db.query(Permission).filter(Permission.code == code).first()
    
    def get_all_permissions(self, module: Optional[str] = None) -> List[Permission]:
        """
        获取所有权限
        
        Args:
            module: 模块过滤条件
            
        Returns:
            权限列表
        """
        query = self.db.query(Permission)
        
        if module:
            query = query.filter(Permission.module == module)
        
        return query.order_by(Permission.module, Permission.resource, Permission.action).all()
    
    def update_permission(self, permission_id: int, permission_data: PermissionUpdate) -> Optional[Permission]:
        """
        更新权限
        
        Args:
            permission_id: 权限ID
            permission_data: 更新数据
            
        Returns:
            更新后的权限对象或None
        """
        permission = self.get_permission(permission_id)
        if not permission:
            return None
        
        # 更新字段
        update_data = permission_data.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(permission, field, value)
        
        self.db.commit()
        self.db.refresh(permission)
        
        # 清除相关缓存
        self._invalidate_permission_caches(permission_id)
        
        logger.info(f"Updated permission: {permission.code}")
        return permission
    
    def delete_permission(self, permission_id: int) -> bool:
        """
        删除权限
        
        Args:
            permission_id: 权限ID
            
        Returns:
            是否删除成功
        """
        permission = self.get_permission(permission_id)
        if not permission:
            return False
        
        self.db.delete(permission)
        self.db.commit()
        
        # 清除相关缓存
        self._invalidate_permission_caches(permission_id)
        
        logger.info(f"Deleted permission: {permission.code}")
        return True
    
    def _invalidate_permission_caches(self, permission_id: int):
        """使权限相关缓存失效"""
        try:
            # 查找所有使用此权限的角色
            role_permissions = self.db.query(RolePermission).filter(
                RolePermission.permission_id == permission_id
            ).all()
            
            # 使角色缓存失效
            for rp in role_permissions:
                self.cache.invalidate_role_permissions(rp.role_id)
            
            # 使所有用户缓存失效（简化处理）
            self.cache.invalidate_all_user_caches()
            
        except Exception as e:
            logger.error(f"Failed to invalidate permission caches: {e}")


class RoleService:
    """角色管理服务"""
    
    def __init__(self, db: Session, cache_service: PermissionCacheService):
        """
        初始化角色服务
        
        Args:
            db: 数据库会话
            cache_service: 缓存服务
        """
        self.db = db
        self.cache = cache_service
    
    def create_role(self, role_data: RoleCreate) -> Role:
        """
        创建角色
        
        Args:
            role_data: 角色创建数据
            
        Returns:
            创建的角色对象
            
        Raises:
            ValueError: 角色名称已存在或父角色不存在
        """
        # 检查角色名称是否已存在
        existing_role = self.db.query(Role).filter(Role.name == role_data.name).first()
        if existing_role:
            raise ValueError(f"Role name '{role_data.name}' already exists")
        
        # 验证父角色是否存在
        if role_data.parent_id:
            parent_role = self.db.query(Role).filter(Role.id == role_data.parent_id).first()
            if not parent_role:
                raise ValueError(f"Parent role with ID {role_data.parent_id} not found")
        
        # 创建角色（不包含permissions字段）
        role_dict = role_data.model_dump(exclude={'permission_ids'})
        role = Role(**role_dict)
        self.db.add(role)
        self.db.flush()  # 获取角色ID但不提交
        
        # 分配权限
        if role_data.permission_ids:
            self._assign_permissions_to_role(role.id, role_data.permission_ids)
        
        self.db.commit()
        self.db.refresh(role)
        
        logger.info(f"Created role: {role.name}")
        return role
    
    def get_role(self, role_id: int) -> Optional[Role]:
        """
        获取角色详情
        
        Args:
            role_id: 角色ID
            
        Returns:
            角色对象或None
        """
        return self.db.query(Role).options(
            joinedload(Role.permissions),
            joinedload(Role.children)
        ).filter(Role.id == role_id).first()
    
    def get_role_by_name(self, name: str) -> Optional[Role]:
        """
        根据名称获取角色
        
        Args:
            name: 角色名称
            
        Returns:
            角色对象或None
        """
        return self.db.query(Role).filter(Role.name == name).first()
    
    def get_all_roles(self) -> List[Role]:
        """
        获取所有角色
        
        Returns:
            角色列表
        """
        return self.db.query(Role).options(
            joinedload(Role.permissions),
            joinedload(Role.children)
        ).order_by(Role.name).all()
    
    def update_role(self, role_id: int, role_data: RoleUpdate) -> Optional[Role]:
        """
        更新角色
        
        Args:
            role_id: 角色ID
            role_data: 更新数据
            
        Returns:
            更新后的角色对象或None
        """
        role = self.get_role(role_id)
        if not role:
            return None
        
        # 验证父角色（如果有更新）
        if role_data.parent_id is not None and role_data.parent_id != role.parent_id:
            if role_data.parent_id != 0:  # 0表示移除父角色
                parent_role = self.db.query(Role).filter(Role.id == role_data.parent_id).first()
                if not parent_role:
                    raise ValueError(f"Parent role with ID {role_data.parent_id} not found")
                
                # 检查循环引用
                if self._would_create_cycle(role_id, role_data.parent_id):
                    raise ValueError("Cannot set parent role: would create a cycle")
        
        # 更新字段
        update_data = role_data.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            if field == 'parent_id' and value == 0:
                setattr(role, field, None)
            else:
                setattr(role, field, value)
        
        self.db.commit()
        self.db.refresh(role)
        
        # 清除相关缓存
        self.cache.invalidate_role_permissions(role_id)
        self.cache.invalidate_all_user_caches()
        
        logger.info(f"Updated role: {role.name}")
        return role
    
    def delete_role(self, role_id: int) -> bool:
        """
        删除角色
        
        Args:
            role_id: 角色ID
            
        Returns:
            是否删除成功
        """
        role = self.get_role(role_id)
        if not role:
            return False
        
        # 检查是否为系统角色
        if role.is_system:
            raise ValueError("Cannot delete system role")
        
        # 检查是否有子角色
        if role.children:
            raise ValueError("Cannot delete role with child roles")
        
        # 检查是否有用户使用此角色
        user_count = self.db.query(UserRole).filter(UserRole.role_id == role_id).count()
        if user_count > 0:
            raise ValueError(f"Cannot delete role: {user_count} users are assigned to this role")
        
        self.db.delete(role)
        self.db.commit()
        
        # 清除相关缓存
        self.cache.invalidate_role_permissions(role_id)
        self.cache.invalidate_all_user_caches()
        
        logger.info(f"Deleted role: {role.name}")
        return True
    
    def assign_permissions_to_role(self, role_id: int, permission_ids: List[int]) -> bool:
        """
        为角色分配权限
        
        Args:
            role_id: 角色ID
            permission_ids: 权限ID列表
            
        Returns:
            是否分配成功
        """
        role = self.get_role(role_id)
        if not role:
            return False
        
        # 清除现有权限
        self.db.query(RolePermission).filter(RolePermission.role_id == role_id).delete()
        
        # 分配新权限
        self._assign_permissions_to_role(role_id, permission_ids)
        
        self.db.commit()
        
        # 清除相关缓存
        self.cache.invalidate_role_permissions(role_id)
        self.cache.invalidate_all_user_caches()
        
        logger.info(f"Assigned {len(permission_ids)} permissions to role {role.name}")
        return True
    
    def get_role_permissions(self, role_id: int, include_inherited: bool = True) -> List[Permission]:
        """
        获取角色权限
        
        Args:
            role_id: 角色ID
            include_inherited: 是否包含继承的权限
            
        Returns:
            权限列表
        """
        if include_inherited:
            return self._get_role_hierarchy_permissions(role_id)
        else:
            role = self.get_role(role_id)
            return role.permissions if role else []
    
    def _assign_permissions_to_role(self, role_id: int, permission_ids: List[int]):
        """为角色分配权限（内部方法）"""
        for permission_id in permission_ids:
            # 验证权限是否存在
            permission = self.db.query(Permission).filter(Permission.id == permission_id).first()
            if permission:
                role_permission = RolePermission(role_id=role_id, permission_id=permission_id)
                self.db.add(role_permission)
    
    def _would_create_cycle(self, role_id: int, parent_id: int) -> bool:
        """检查是否会创建循环引用"""
        current_id = parent_id
        visited = set()
        
        while current_id and current_id not in visited:
            if current_id == role_id:
                return True
            
            visited.add(current_id)
            parent_role = self.db.query(Role).filter(Role.id == current_id).first()
            current_id = parent_role.parent_id if parent_role else None
        
        return False
    
    def _get_role_hierarchy_permissions(self, role_id: int) -> List[Permission]:
        """获取角色层级权限（包含继承的权限）"""
        permissions = set()
        visited_roles = set()
        
        def collect_permissions(current_role_id):
            if current_role_id in visited_roles:
                return
            
            visited_roles.add(current_role_id)
            
            # 获取当前角色的直接权限
            role = self.db.query(Role).options(joinedload(Role.permissions)).filter(
                Role.id == current_role_id
            ).first()
            
            if role:
                permissions.update(role.permissions)
                
                # 递归获取父角色权限
                if role.parent_id:
                    collect_permissions(role.parent_id)
        
        collect_permissions(role_id)
        return list(permissions)


class UserRoleService:
    """用户角色管理服务"""
    
    def __init__(self, db: Session, cache_service: PermissionCacheService):
        """
        初始化用户角色服务
        
        Args:
            db: 数据库会话
            cache_service: 缓存服务
        """
        self.db = db
        self.cache = cache_service
    
    def assign_roles_to_user(self, user_id: int, role_ids: List[int], operator_id: Optional[int] = None) -> bool:
        """
        为用户分配角色
        
        Args:
            user_id: 用户ID
            role_ids: 角色ID列表
            operator_id: 操作人ID
            
        Returns:
            是否分配成功
        """
        # 验证用户是否存在
        user = self.db.query(User).filter(User.id == user_id).first()
        if not user:
            return False
        
        # 清除现有角色
        self.db.query(UserRole).filter(UserRole.user_id == user_id).delete()
        
        # 分配新角色
        for role_id in role_ids:
            # 验证角色是否存在
            role = self.db.query(Role).filter(Role.id == role_id).first()
            if role:
                user_role = UserRole(
                    user_id=user_id,
                    role_id=role_id,
                    created_by=operator_id
                )
                self.db.add(user_role)
                
                # 记录操作日志
                self._log_operation("GRANT", "USER", user_id, role_id, None, operator_id)
        
        self.db.commit()
        
        # 清除用户权限缓存
        self.cache.invalidate_user_permissions(user_id)
        
        logger.info(f"Assigned {len(role_ids)} roles to user {user_id}")
        return True
    
    def remove_role_from_user(self, user_id: int, role_id: int, operator_id: Optional[int] = None) -> bool:
        """
        移除用户角色
        
        Args:
            user_id: 用户ID
            role_id: 角色ID
            operator_id: 操作人ID
            
        Returns:
            是否移除成功
        """
        user_role = self.db.query(UserRole).filter(
            and_(UserRole.user_id == user_id, UserRole.role_id == role_id)
        ).first()
        
        if not user_role:
            return False
        
        self.db.delete(user_role)
        self.db.commit()
        
        # 记录操作日志
        self._log_operation("REVOKE", "USER", user_id, role_id, None, operator_id)
        
        # 清除用户权限缓存
        self.cache.invalidate_user_permissions(user_id)
        
        logger.info(f"Removed role {role_id} from user {user_id}")
        return True
    
    def get_user_roles(self, user_id: int) -> List[Role]:
        """
        获取用户角色
        
        Args:
            user_id: 用户ID
            
        Returns:
            角色列表
        """
        return self.db.query(Role).join(UserRole).filter(
            UserRole.user_id == user_id
        ).all()
    
    def has_role_by_name(self, user_id: int, role_name: str) -> bool:
        """
        检查用户是否拥有指定角色
        
        Args:
            user_id: 用户ID
            role_name: 角色名称
            
        Returns:
            是否拥有角色
        """
        return self.db.query(UserRole).join(Role).filter(
            and_(UserRole.user_id == user_id, Role.name == role_name)
        ).first() is not None
    
    def get_users_by_role(self, role_id: int) -> List[User]:
        """
        获取拥有指定角色的用户
        
        Args:
            role_id: 角色ID
            
        Returns:
            用户列表
        """
        return self.db.query(User).join(UserRole).filter(
            UserRole.role_id == role_id
        ).all()
    
    def _log_operation(self, operation_type: str, target_type: str, target_id: int,
                      role_id: Optional[int], permission_id: Optional[int], operator_id: Optional[int]):
        """记录权限操作日志"""
        if operator_id:
            log = PermissionOperationLog(
                operator_id=operator_id,
                operation_type=operation_type,
                target_type=target_type,
                target_id=target_id,
                role_id=role_id,
                permission_id=permission_id
            )
            self.db.add(log)


class AuthorizationService:
    """权限验证服务"""
    
    def __init__(self, db: Session, cache_service: PermissionCacheService):
        """
        初始化权限验证服务
        
        Args:
            db: 数据库会话
            cache_service: 缓存服务
        """
        self.db = db
        self.cache = cache_service
    
    def has_permission(self, user_id: int, permission_code: str) -> bool:
        """
        检查用户是否拥有指定权限
        
        Args:
            user_id: 用户ID
            permission_code: 权限编码
            
        Returns:
            是否拥有权限
        """
        # 先从缓存查询
        cached_result = self.cache.has_user_permission(user_id, permission_code)
        if cached_result is not None:
            return cached_result
        
        # 缓存未命中，从数据库查询
        user_permissions = self._get_user_permissions_from_db(user_id)
        permission_codes = [p.code for p in user_permissions]
        
        # 更新缓存
        self.cache.cache_user_permissions(user_id, permission_codes)
        
        return permission_code in permission_codes
    
    def get_user_permissions(self, user_id: int) -> List[Permission]:
        """
        获取用户所有权限
        
        Args:
            user_id: 用户ID
            
        Returns:
            权限列表
        """
        # 先尝试从缓存获取权限编码
        cached_codes = self.cache.get_user_permissions(user_id)
        
        if cached_codes:
            # 从数据库获取权限详情
            return self.db.query(Permission).filter(
                Permission.code.in_(cached_codes)
            ).all()
        
        # 缓存未命中，从数据库查询
        permissions = self._get_user_permissions_from_db(user_id)
        permission_codes = [p.code for p in permissions]
        
        # 更新缓存
        self.cache.cache_user_permissions(user_id, permission_codes)
        
        return permissions
    
    def _get_user_permissions_from_db(self, user_id: int) -> List[Permission]:
        """从数据库获取用户权限"""
        # 使用复杂查询获取用户的所有权限（包含角色继承）
        query = text("""
            WITH RECURSIVE role_hierarchy AS (
                -- 获取用户直接角色
                SELECT r.id, r.parent_id, r.name
                FROM roles r
                JOIN user_roles ur ON r.id = ur.role_id
                WHERE ur.user_id = :user_id
                
                UNION ALL
                
                -- 递归获取父角色
                SELECT r.id, r.parent_id, r.name
                FROM roles r
                JOIN role_hierarchy rh ON r.id = rh.parent_id
            )
            SELECT DISTINCT p.*
            FROM permissions p
            JOIN role_permissions rp ON p.id = rp.permission_id
            JOIN role_hierarchy rh ON rp.role_id = rh.id
        """)
        
        result = self.db.execute(query, {"user_id": user_id})
        
        # 将结果转换为Permission对象
        permissions = []
        for row in result:
            permission = Permission(
                id=row.id,
                code=row.code,
                name=row.name,
                description=row.description,
                module=row.module,
                resource=row.resource,
                action=row.action,
                created_at=row.created_at,
                updated_at=row.updated_at
            )
            permissions.append(permission)
        
        return permissions 