"""
权限系统缓存服务
提供权限相关数据的缓存功能
"""

import json
import logging
from typing import List, Optional, Set
import redis
from redis import Redis

logger = logging.getLogger(__name__)


class PermissionCacheService:
    """权限缓存服务"""
    
    def __init__(self, redis_client: Redis):
        """
        初始化权限缓存服务
        
        Args:
            redis_client: Redis客户端实例
        """
        self.redis = redis_client
        
        # 缓存键模板
        self.CACHE_KEYS = {
            'user_permissions': 'user:permissions:{user_id}',
            'user_roles': 'user:roles:{user_id}',
            'role_permissions': 'role:permissions:{role_id}',
            'role_hierarchy': 'role:hierarchy:{role_id}',
            'permission_details': 'permission:details:{permission_id}',
        }
        
        # 缓存过期时间（秒）
        self.TTL = {
            'user_permissions': 1800,  # 30分钟
            'user_roles': 1800,        # 30分钟
            'role_permissions': 3600,  # 1小时
            'role_hierarchy': 3600,    # 1小时
            'permission_details': 7200, # 2小时
        }
    
    def cache_user_permissions(self, user_id: int, permissions: List[str]) -> bool:
        """
        缓存用户权限列表
        
        Args:
            user_id: 用户ID
            permissions: 权限编码列表
            
        Returns:
            是否缓存成功
        """
        try:
            key = self.CACHE_KEYS['user_permissions'].format(user_id=user_id)
            
            # 使用集合存储权限，便于快速查找
            pipe = self.redis.pipeline()
            pipe.delete(key)
            if permissions:
                pipe.sadd(key, *permissions)
            pipe.expire(key, self.TTL['user_permissions'])
            pipe.execute()
            
            logger.debug(f"Cached {len(permissions)} permissions for user {user_id}")
            return True
        except Exception as e:
            logger.error(f"Failed to cache user permissions: {e}")
            return False
    
    def get_user_permissions(self, user_id: int) -> Set[str]:
        """
        获取用户权限缓存
        
        Args:
            user_id: 用户ID
            
        Returns:
            权限编码集合
        """
        try:
            key = self.CACHE_KEYS['user_permissions'].format(user_id=user_id)
            permissions = self.redis.smembers(key)
            return {p.decode() for p in permissions} if permissions else set()
        except Exception as e:
            logger.error(f"Failed to get user permissions from cache: {e}")
            return set()
    
    def has_user_permission(self, user_id: int, permission_code: str) -> Optional[bool]:
        """
        检查用户是否有指定权限（从缓存）
        
        Args:
            user_id: 用户ID
            permission_code: 权限编码
            
        Returns:
            是否有权限，None表示缓存不存在
        """
        try:
            key = self.CACHE_KEYS['user_permissions'].format(user_id=user_id)
            
            # 先检查键是否存在
            if not self.redis.exists(key):
                return None  # 缓存不存在
            
            return self.redis.sismember(key, permission_code)
        except Exception as e:
            logger.error(f"Failed to check user permission from cache: {e}")
            return None
    
    def cache_user_roles(self, user_id: int, roles: List[str]) -> bool:
        """
        缓存用户角色列表
        
        Args:
            user_id: 用户ID
            roles: 角色名称列表
            
        Returns:
            是否缓存成功
        """
        try:
            key = self.CACHE_KEYS['user_roles'].format(user_id=user_id)
            
            pipe = self.redis.pipeline()
            pipe.delete(key)
            if roles:
                pipe.sadd(key, *roles)
            pipe.expire(key, self.TTL['user_roles'])
            pipe.execute()
            
            logger.debug(f"Cached {len(roles)} roles for user {user_id}")
            return True
        except Exception as e:
            logger.error(f"Failed to cache user roles: {e}")
            return False
    
    def get_user_roles(self, user_id: int) -> Set[str]:
        """
        获取用户角色缓存
        
        Args:
            user_id: 用户ID
            
        Returns:
            角色名称集合
        """
        try:
            key = self.CACHE_KEYS['user_roles'].format(user_id=user_id)
            roles = self.redis.smembers(key)
            return {r.decode() for r in roles} if roles else set()
        except Exception as e:
            logger.error(f"Failed to get user roles from cache: {e}")
            return set()
    
    def cache_role_permissions(self, role_id: int, permissions: List[str]) -> bool:
        """
        缓存角色权限列表
        
        Args:
            role_id: 角色ID
            permissions: 权限编码列表
            
        Returns:
            是否缓存成功
        """
        try:
            key = self.CACHE_KEYS['role_permissions'].format(role_id=role_id)
            
            pipe = self.redis.pipeline()
            pipe.delete(key)
            if permissions:
                pipe.sadd(key, *permissions)
            pipe.expire(key, self.TTL['role_permissions'])
            pipe.execute()
            
            logger.debug(f"Cached {len(permissions)} permissions for role {role_id}")
            return True
        except Exception as e:
            logger.error(f"Failed to cache role permissions: {e}")
            return False
    
    def get_role_permissions(self, role_id: int) -> Set[str]:
        """
        获取角色权限缓存
        
        Args:
            role_id: 角色ID
            
        Returns:
            权限编码集合
        """
        try:
            key = self.CACHE_KEYS['role_permissions'].format(role_id=role_id)
            permissions = self.redis.smembers(key)
            return {p.decode() for p in permissions} if permissions else set()
        except Exception as e:
            logger.error(f"Failed to get role permissions from cache: {e}")
            return set()
    
    def cache_role_hierarchy(self, role_id: int, hierarchy_permissions: List[str]) -> bool:
        """
        缓存角色层级权限（包含继承的权限）
        
        Args:
            role_id: 角色ID
            hierarchy_permissions: 层级权限编码列表
            
        Returns:
            是否缓存成功
        """
        try:
            key = self.CACHE_KEYS['role_hierarchy'].format(role_id=role_id)
            
            pipe = self.redis.pipeline()
            pipe.delete(key)
            if hierarchy_permissions:
                pipe.sadd(key, *hierarchy_permissions)
            pipe.expire(key, self.TTL['role_hierarchy'])
            pipe.execute()
            
            logger.debug(f"Cached {len(hierarchy_permissions)} hierarchy permissions for role {role_id}")
            return True
        except Exception as e:
            logger.error(f"Failed to cache role hierarchy: {e}")
            return False
    
    def get_role_hierarchy_permissions(self, role_id: int) -> Set[str]:
        """
        获取角色层级权限缓存
        
        Args:
            role_id: 角色ID
            
        Returns:
            权限编码集合
        """
        try:
            key = self.CACHE_KEYS['role_hierarchy'].format(role_id=role_id)
            permissions = self.redis.smembers(key)
            return {p.decode() for p in permissions} if permissions else set()
        except Exception as e:
            logger.error(f"Failed to get role hierarchy from cache: {e}")
            return set()
    
    def invalidate_user_permissions(self, user_id: int) -> bool:
        """
        使用户权限缓存失效
        
        Args:
            user_id: 用户ID
            
        Returns:
            是否操作成功
        """
        try:
            keys_to_delete = [
                self.CACHE_KEYS['user_permissions'].format(user_id=user_id),
                self.CACHE_KEYS['user_roles'].format(user_id=user_id)
            ]
            
            deleted_count = self.redis.delete(*keys_to_delete)
            logger.debug(f"Invalidated {deleted_count} cache keys for user {user_id}")
            return True
        except Exception as e:
            logger.error(f"Failed to invalidate user permissions cache: {e}")
            return False
    
    def invalidate_role_permissions(self, role_id: int) -> bool:
        """
        使角色权限缓存失效
        
        Args:
            role_id: 角色ID
            
        Returns:
            是否操作成功
        """
        try:
            keys_to_delete = [
                self.CACHE_KEYS['role_permissions'].format(role_id=role_id),
                self.CACHE_KEYS['role_hierarchy'].format(role_id=role_id)
            ]
            
            deleted_count = self.redis.delete(*keys_to_delete)
            logger.debug(f"Invalidated {deleted_count} cache keys for role {role_id}")
            return True
        except Exception as e:
            logger.error(f"Failed to invalidate role permissions cache: {e}")
            return False
    
    def invalidate_all_user_caches(self) -> bool:
        """
        使所有用户权限缓存失效
        
        Returns:
            是否操作成功
        """
        try:
            # 查找所有用户权限相关的键
            user_permission_pattern = self.CACHE_KEYS['user_permissions'].format(user_id='*')
            user_role_pattern = self.CACHE_KEYS['user_roles'].format(user_id='*')
            
            user_permission_keys = self.redis.keys(user_permission_pattern)
            user_role_keys = self.redis.keys(user_role_pattern)
            
            all_keys = user_permission_keys + user_role_keys
            
            if all_keys:
                deleted_count = self.redis.delete(*all_keys)
                logger.info(f"Invalidated {deleted_count} user cache keys")
            
            return True
        except Exception as e:
            logger.error(f"Failed to invalidate all user caches: {e}")
            return False
    
    def invalidate_all_role_caches(self) -> bool:
        """
        使所有角色权限缓存失效
        
        Returns:
            是否操作成功
        """
        try:
            # 查找所有角色权限相关的键
            role_permission_pattern = self.CACHE_KEYS['role_permissions'].format(role_id='*')
            role_hierarchy_pattern = self.CACHE_KEYS['role_hierarchy'].format(role_id='*')
            
            role_permission_keys = self.redis.keys(role_permission_pattern)
            role_hierarchy_keys = self.redis.keys(role_hierarchy_pattern)
            
            all_keys = role_permission_keys + role_hierarchy_keys
            
            if all_keys:
                deleted_count = self.redis.delete(*all_keys)
                logger.info(f"Invalidated {deleted_count} role cache keys")
            
            return True
        except Exception as e:
            logger.error(f"Failed to invalidate all role caches: {e}")
            return False
    
    def get_cache_stats(self) -> dict:
        """
        获取缓存统计信息
        
        Returns:
            缓存统计信息字典
        """
        try:
            stats = {}
            
            for cache_type, pattern in self.CACHE_KEYS.items():
                key_pattern = pattern.format(**{k: '*' for k in ['user_id', 'role_id', 'permission_id']})
                keys = self.redis.keys(key_pattern)
                stats[cache_type] = {
                    'count': len(keys),
                    'keys': [k.decode() for k in keys[:10]]  # 只显示前10个键
                }
            
            return stats
        except Exception as e:
            logger.error(f"Failed to get cache stats: {e}")
            return {}
    
    def clear_all_permission_caches(self) -> bool:
        """
        清空所有权限相关缓存
        
        Returns:
            是否操作成功
        """
        try:
            all_patterns = [
                self.CACHE_KEYS['user_permissions'].format(user_id='*'),
                self.CACHE_KEYS['user_roles'].format(user_id='*'),
                self.CACHE_KEYS['role_permissions'].format(role_id='*'),
                self.CACHE_KEYS['role_hierarchy'].format(role_id='*'),
                self.CACHE_KEYS['permission_details'].format(permission_id='*'),
            ]
            
            all_keys = []
            for pattern in all_patterns:
                keys = self.redis.keys(pattern)
                all_keys.extend(keys)
            
            if all_keys:
                deleted_count = self.redis.delete(*all_keys)
                logger.info(f"Cleared {deleted_count} permission cache keys")
            
            return True
        except Exception as e:
            logger.error(f"Failed to clear all permission caches: {e}")
            return False 