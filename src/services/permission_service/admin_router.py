"""
权限服务B端管理API路由
定义权限和角色管理相关的HTTP接口
"""

from typing import Annotated, List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session

from ...core.database import get_db
from ..user_service.dependencies import get_current_active_user
from ..user_service.models import User
from .dependencies import (
    get_authorization_service,
    get_permission_service,
    get_role_service,
    get_user_role_service,
    require_admin,
    require_permission,
)
from .schemas import (
    BatchRoleAssignRequest,
    OperationResult,
    PermissionCreate,
    PermissionResponse,
    PermissionUpdate,
    RoleCreate,
    RolePermissionAssign,
    RoleResponse,
    RoleUpdate,
    UserPermissionsResponse,
    UserRoleAssign,
)
from .service import (
    AuthorizationService,
    PermissionService,
    RoleService,
    UserRoleService,
)

router = APIRouter()


# ==================== 权限管理接口 ====================


@router.post(
    "/permissions",
    response_model=PermissionResponse,
    status_code=status.HTTP_201_CREATED,
    summary="创建权限",
    description="创建新的系统权限",
)
async def create_permission(
    permission_data: PermissionCreate,
    current_user: Annotated[User, Depends(require_permission("permission.permission.create"))],
    permission_service: Annotated[PermissionService, Depends(get_permission_service)],
):
    """创建权限"""
    try:
        permission = permission_service.create_permission(permission_data)
        return PermissionResponse.model_validate(permission)
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


@router.get(
    "/permissions",
    response_model=List[PermissionResponse],
    summary="获取权限列表",
    description="获取系统中所有权限",
)
async def get_permissions(
    current_user: Annotated[User, Depends(require_permission("permission.list.read"))],
    permission_service: Annotated[PermissionService, Depends(get_permission_service)],
    module: Optional[str] = Query(None, description="按模块过滤"),
):
    """获取权限列表"""
    permissions = permission_service.get_all_permissions(module=module)
    return [PermissionResponse.model_validate(perm) for perm in permissions]


@router.get(
    "/permissions/{permission_id}",
    response_model=PermissionResponse,
    summary="获取权限详情",
    description="根据ID获取权限详细信息",
)
async def get_permission(
    permission_id: int,
    current_user: Annotated[User, Depends(require_permission("permission.permission.read"))],
    permission_service: Annotated[PermissionService, Depends(get_permission_service)],
):
    """获取权限详情"""
    permission = permission_service.get_permission(permission_id)
    if not permission:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Permission not found",
        )
    return PermissionResponse.model_validate(permission)


@router.put(
    "/permissions/{permission_id}",
    response_model=PermissionResponse,
    summary="更新权限",
    description="更新权限信息",
)
async def update_permission(
    permission_id: int,
    permission_data: PermissionUpdate,
    current_user: Annotated[User, Depends(require_permission("permission.permission.update"))],
    permission_service: Annotated[PermissionService, Depends(get_permission_service)],
):
    """更新权限"""
    permission = permission_service.update_permission(permission_id, permission_data)
    if not permission:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Permission not found",
        )
    return PermissionResponse.model_validate(permission)


@router.delete(
    "/permissions/{permission_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="删除权限",
    description="删除权限",
)
async def delete_permission(
    permission_id: int,
    current_user: Annotated[User, Depends(require_permission("permission.permission.delete"))],
    permission_service: Annotated[PermissionService, Depends(get_permission_service)],
):
    """删除权限"""
    success = permission_service.delete_permission(permission_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Permission not found",
        )


# ==================== 角色管理接口 ====================


@router.post(
    "/roles",
    response_model=RoleResponse,
    status_code=status.HTTP_201_CREATED,
    summary="创建角色",
    description="创建新的角色",
)
async def create_role(
    role_data: RoleCreate,
    current_user: Annotated[User, Depends(require_permission("role.role.create"))],
    role_service: Annotated[RoleService, Depends(get_role_service)],
):
    """创建角色"""
    try:
        role = role_service.create_role(role_data)
        return RoleResponse.model_validate(role)
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


@router.get(
    "/roles",
    response_model=List[RoleResponse],
    summary="获取角色列表",
    description="获取系统中所有角色",
)
async def get_roles(
    current_user: Annotated[User, Depends(require_permission("role.list.read"))],
    role_service: Annotated[RoleService, Depends(get_role_service)],
):
    """获取角色列表"""
    roles = role_service.get_all_roles()
    return [RoleResponse.model_validate(role) for role in roles]


@router.get(
    "/roles/{role_id}",
    response_model=RoleResponse,
    summary="获取角色详情",
    description="根据ID获取角色详细信息",
)
async def get_role(
    role_id: int,
    current_user: Annotated[User, Depends(require_permission("role.role.read"))],
    role_service: Annotated[RoleService, Depends(get_role_service)],
):
    """获取角色详情"""
    role = role_service.get_role(role_id)
    if not role:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Role not found",
        )
    return RoleResponse.model_validate(role)


@router.put(
    "/roles/{role_id}",
    response_model=RoleResponse,
    summary="更新角色",
    description="更新角色信息",
)
async def update_role(
    role_id: int,
    role_data: RoleUpdate,
    current_user: Annotated[User, Depends(require_permission("role.role.update"))],
    role_service: Annotated[RoleService, Depends(get_role_service)],
):
    """更新角色"""
    try:
        role = role_service.update_role(role_id, role_data)
        if not role:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Role not found",
            )
        return RoleResponse.model_validate(role)
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


@router.delete(
    "/roles/{role_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="删除角色",
    description="删除角色",
)
async def delete_role(
    role_id: int,
    current_user: Annotated[User, Depends(require_permission("role.role.delete"))],
    role_service: Annotated[RoleService, Depends(get_role_service)],
):
    """删除角色"""
    try:
        success = role_service.delete_role(role_id)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Role not found",
            )
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


# ==================== 角色权限分配接口 ====================


@router.post(
    "/roles/{role_id}/permissions",
    response_model=OperationResult,
    summary="为角色分配权限",
    description="为指定角色分配权限列表",
)
async def assign_permissions_to_role(
    role_id: int,
    assign_data: RolePermissionAssign,
    current_user: Annotated[
        User, Depends(require_permission("role.permission.assign"))
    ],
    role_service: Annotated[RoleService, Depends(get_role_service)],
):
    """为角色分配权限"""
    if assign_data.role_id != role_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Role ID mismatch",
        )

    success = role_service.assign_permissions_to_role(
        role_id, assign_data.permission_ids
    )
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Role not found",
        )

    return OperationResult(
        success=True,
        message="Permissions assigned successfully",
        affected_count=len(assign_data.permission_ids),
    )


@router.delete(
    "/roles/{role_id}/permissions/{permission_id}",
    response_model=OperationResult,
    summary="撤销角色权限",
    description="撤销角色的指定权限",
)
async def revoke_permission_from_role(
    role_id: int,
    permission_id: int,
    current_user: Annotated[
        User, Depends(require_permission("role.permission.revoke"))
    ],
    role_service: Annotated[RoleService, Depends(get_role_service)],
):
    """撤销角色权限"""
    success = role_service.revoke_permission_from_role(role_id, permission_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Role or permission not found",
        )

    return OperationResult(
        success=True,
        message="Permission revoked successfully",
        affected_count=1,
    )


@router.get(
    "/roles/{role_id}/permissions",
    response_model=List[PermissionResponse],
    summary="获取角色权限",
    description="获取角色的所有权限",
)
async def get_role_permissions(
    role_id: int,
    current_user: Annotated[User, Depends(require_permission("role.permission.read"))],
    role_service: Annotated[RoleService, Depends(get_role_service)],
    include_inherited: bool = Query(True, description="是否包含继承的权限"),
):
    """获取角色权限"""
    permissions = role_service.get_role_permissions(role_id, include_inherited)
    return [PermissionResponse.model_validate(perm) for perm in permissions]


# ==================== 用户角色分配接口 ====================


@router.post(
    "/users/{user_id}/roles",
    response_model=OperationResult,
    summary="为用户分配角色",
    description="为指定用户分配角色列表",
)
async def assign_roles_to_user(
    user_id: int,
    assign_data: UserRoleAssign,
    current_user: Annotated[User, Depends(require_permission("user.role.assign"))],
    user_role_service: Annotated[UserRoleService, Depends(get_user_role_service)],
):
    """为用户分配角色"""
    if assign_data.user_id != user_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="User ID mismatch",
        )

    success = user_role_service.assign_roles_to_user(
        user_id, assign_data.role_ids, current_user.id
    )
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found",
        )

    return OperationResult(
        success=True,
        message="Roles assigned successfully",
        affected_count=len(assign_data.role_ids),
    )


@router.delete(
    "/users/{user_id}/roles/{role_id}",
    response_model=OperationResult,
    summary="撤销用户角色",
    description="撤销用户的指定角色",
)
async def revoke_role_from_user(
    user_id: int,
    role_id: int,
    current_user: Annotated[User, Depends(require_permission("user.role.revoke"))],
    user_role_service: Annotated[UserRoleService, Depends(get_user_role_service)],
):
    """撤销用户角色"""
    success = user_role_service.revoke_role_from_user(user_id, role_id, current_user.id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User or role not found",
        )

    return OperationResult(
        success=True,
        message="Role revoked successfully",
        affected_count=1,
    )


@router.get(
    "/users/{user_id}/roles",
    response_model=List[RoleResponse],
    summary="获取用户角色",
    description="获取用户的所有角色",
)
async def get_user_roles(
    user_id: int,
    current_user: Annotated[User, Depends(require_permission("user.role.read"))],
    user_role_service: Annotated[UserRoleService, Depends(get_user_role_service)],
):
    """获取用户角色"""
    roles = user_role_service.get_user_roles(user_id)
    return [RoleResponse.model_validate(role) for role in roles]


@router.get(
    "/users/{user_id}/permissions",
    response_model=UserPermissionsResponse,
    summary="获取用户权限",
    description="获取用户的所有权限（包含角色继承）",
)
async def get_user_permissions(
    user_id: int,
    current_user: Annotated[User, Depends(require_permission("user.permission.read"))],
    user_role_service: Annotated[UserRoleService, Depends(get_user_role_service)],
    auth_service: Annotated[AuthorizationService, Depends(get_authorization_service)],
):
    """获取用户权限"""
    roles = user_role_service.get_user_roles(user_id)
    permissions = auth_service.get_user_permissions(user_id)
    permission_codes = [perm.code for perm in permissions]

    return UserPermissionsResponse(
        user_id=user_id,
        roles=[RoleResponse.model_validate(role) for role in roles],
        permissions=[PermissionResponse.model_validate(perm) for perm in permissions],
        permission_codes=permission_codes,
    )


# ==================== 批量操作接口 ====================


@router.post(
    "/batch/roles/assign",
    response_model=OperationResult,
    summary="批量分配角色",
    description="为多个用户分配同一个角色",
)
async def batch_assign_role(
    assign_data: BatchRoleAssignRequest,
    current_user: Annotated[
        User, Depends(require_permission("user.role.batch.assign"))
    ],
    user_role_service: Annotated[UserRoleService, Depends(get_user_role_service)],
):
    """批量为用户分配角色"""
    success_count = 0

    for user_id in assign_data.user_ids:
        try:
            success = user_role_service.assign_roles_to_user(
                user_id, [assign_data.role_id], current_user.id
            )
            if success:
                success_count += 1
        except Exception:
            continue  # 跳过失败的用户

    return OperationResult(
        success=success_count > 0,
        message=f"Successfully assigned role to {success_count} users",
        affected_count=success_count,
    )


@router.post(
    "/batch/roles/revoke",
    response_model=OperationResult,
    summary="批量撤销角色",
    description="从多个用户撤销同一个角色",
)
async def batch_revoke_role(
    assign_data: BatchRoleAssignRequest,
    current_user: Annotated[
        User, Depends(require_permission("user.role.batch.revoke"))
    ],
    user_role_service: Annotated[UserRoleService, Depends(get_user_role_service)],
):
    """批量撤销用户角色"""
    success_count = 0

    for user_id in assign_data.user_ids:
        try:
            success = user_role_service.revoke_role_from_user(
                user_id, assign_data.role_id, current_user.id
            )
            if success:
                success_count += 1
        except Exception:
            continue  # 跳过失败的用户

    return OperationResult(
        success=success_count > 0,
        message=f"Successfully revoked role from {success_count} users",
        affected_count=success_count,
    )


# ==================== 权限分析接口 ====================


@router.get(
    "/analytics/permissions",
    response_model=dict,
    summary="权限统计分析",
    description="获取权限使用统计信息",
)
async def get_permission_analytics(
    current_user: Annotated[
        User, Depends(require_permission("permission.analytics.read"))
    ],
    permission_service: Annotated[PermissionService, Depends(get_permission_service)],
    auth_service: Annotated[AuthorizationService, Depends(get_authorization_service)],
):
    """获取权限统计分析"""
    try:
        # 获取权限统计
        permissions = permission_service.get_all_permissions()
        total_permissions = len(permissions)

        # 按模块分组统计
        module_stats = {}
        for perm in permissions:
            module = perm.module or "default"
            if module not in module_stats:
                module_stats[module] = 0
            module_stats[module] += 1

        return {
            "total_permissions": total_permissions,
            "module_distribution": module_stats,
            "most_used_permissions": [],  # 可以扩展统计最常用的权限
            "permission_coverage": 100,  # 权限覆盖率
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get permission analytics: {str(e)}",
        )


@router.get(
    "/analytics/roles",
    response_model=dict,
    summary="角色统计分析",
    description="获取角色使用统计信息",
)
async def get_role_analytics(
    current_user: Annotated[User, Depends(require_permission("role.analytics.read"))],
    role_service: Annotated[RoleService, Depends(get_role_service)],
    user_role_service: Annotated[UserRoleService, Depends(get_user_role_service)],
):
    """获取角色统计分析"""
    try:
        # 获取角色统计
        roles = role_service.get_all_roles()
        total_roles = len(roles)
        active_roles = len([role for role in roles if role.is_active])

        return {
            "total_roles": total_roles,
            "active_roles": active_roles,
            "inactive_roles": total_roles - active_roles,
            "role_usage": [],  # 可以扩展统计每个角色的用户数量
            "most_assigned_roles": [],  # 最常分配的角色
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get role analytics: {str(e)}",
        )
