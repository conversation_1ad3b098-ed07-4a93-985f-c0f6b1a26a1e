"""
权限服务数据模型
定义角色、权限相关的数据库模型
"""

from datetime import datetime
from sqlalchemy import (
    Boolean, Column, DateTime, ForeignKey, Index, Integer, String, Text,
    UniqueConstraint
)
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from ...core.database import Base


class Role(Base):
    """角色表"""
    
    __tablename__ = "roles"
    
    id = Column(Integer, primary_key=True, index=True, comment="角色ID")
    name = Column(String(50), unique=True, nullable=False, comment="角色名称")
    description = Column(Text, comment="角色描述")
    is_system = Column(Boolean, default=False, comment="是否系统内置角色")
    parent_id = Column(Integer, ForeignKey("roles.id"), nullable=True, comment="父角色ID")
    created_at = Column(DateTime, default=func.current_timestamp(), comment="创建时间")
    updated_at = Column(
        DateTime,
        default=func.current_timestamp(),
        onupdate=func.current_timestamp(),
        comment="更新时间"
    )
    
    # 关系定义
    parent = relationship("Role", remote_side=[id], backref="children")
    permissions = relationship("Permission", secondary="role_permissions", back_populates="roles")
    # users = relationship("User", secondary="user_roles", back_populates="roles")  # 避免循环导入
    
    # 索引定义
    __table_args__ = (
        Index("idx_role_parent_id", "parent_id"),
        Index("idx_role_name", "name"),
    )

    def __repr__(self):
        return f"<Role(id={self.id}, name='{self.name}')>"


class Permission(Base):
    """权限表"""
    
    __tablename__ = "permissions"
    
    id = Column(Integer, primary_key=True, index=True, comment="权限ID")
    code = Column(String(100), unique=True, nullable=False, comment="权限编码")
    name = Column(String(100), nullable=False, comment="权限名称")
    description = Column(Text, comment="权限描述")
    module = Column(String(50), nullable=False, comment="所属模块")
    resource = Column(String(50), nullable=False, comment="资源类型")
    action = Column(String(50), nullable=False, comment="操作类型")
    created_at = Column(DateTime, default=func.current_timestamp(), comment="创建时间")
    updated_at = Column(
        DateTime,
        default=func.current_timestamp(),
        onupdate=func.current_timestamp(),
        comment="更新时间"
    )
    
    # 关系定义
    roles = relationship("Role", secondary="role_permissions", back_populates="permissions")
    
    # 索引定义
    __table_args__ = (
        Index("idx_permission_module", "module"),
        Index("idx_permission_resource", "resource"),
        Index("idx_permission_action", "action"),
        Index("idx_permission_code", "code"),
    )

    def __repr__(self):
        return f"<Permission(id={self.id}, code='{self.code}')>"


class RolePermission(Base):
    """角色权限关联表"""
    
    __tablename__ = "role_permissions"
    
    id = Column(Integer, primary_key=True, index=True, comment="关联ID")
    role_id = Column(Integer, ForeignKey("roles.id", ondelete="CASCADE"), nullable=False, comment="角色ID")
    permission_id = Column(Integer, ForeignKey("permissions.id", ondelete="CASCADE"), nullable=False, comment="权限ID")
    created_at = Column(DateTime, default=func.current_timestamp(), comment="创建时间")
    
    # 唯一约束
    __table_args__ = (
        UniqueConstraint("role_id", "permission_id", name="uk_role_permission"),
        Index("idx_rp_role_id", "role_id"),
        Index("idx_rp_permission_id", "permission_id"),
    )

    def __repr__(self):
        return f"<RolePermission(role_id={self.role_id}, permission_id={self.permission_id})>"


class UserRole(Base):
    """用户角色关联表"""
    
    __tablename__ = "user_roles"
    
    id = Column(Integer, primary_key=True, index=True, comment="关联ID")
    user_id = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"), nullable=False, comment="用户ID")
    role_id = Column(Integer, ForeignKey("roles.id", ondelete="CASCADE"), nullable=False, comment="角色ID")
    created_at = Column(DateTime, default=func.current_timestamp(), comment="创建时间")
    created_by = Column(Integer, comment="创建人ID")
    
    # 唯一约束
    __table_args__ = (
        UniqueConstraint("user_id", "role_id", name="uk_user_role"),
        Index("idx_ur_user_id", "user_id"),
        Index("idx_ur_role_id", "role_id"),
    )

    def __repr__(self):
        return f"<UserRole(user_id={self.user_id}, role_id={self.role_id})>"


class PermissionOperationLog(Base):
    """权限操作日志表"""
    
    __tablename__ = "permission_operation_logs"
    
    id = Column(Integer, primary_key=True, index=True, comment="日志ID")
    operator_id = Column(Integer, ForeignKey("users.id"), nullable=False, comment="操作人ID")
    operation_type = Column(String(20), nullable=False, comment="操作类型：GRANT/REVOKE")
    target_type = Column(String(20), nullable=False, comment="目标类型：USER/ROLE")
    target_id = Column(Integer, nullable=False, comment="目标ID")
    role_id = Column(Integer, comment="角色ID")
    permission_id = Column(Integer, comment="权限ID")
    operation_time = Column(DateTime, default=func.current_timestamp(), comment="操作时间")
    ip_address = Column(String(45), comment="操作IP")
    
    # 索引定义
    __table_args__ = (
        Index("idx_pol_operator_id", "operator_id"),
        Index("idx_pol_target_id", "target_id"),
        Index("idx_pol_operation_time", "operation_time"),
    )

    def __repr__(self):
        return f"<PermissionOperationLog(id={self.id}, type={self.operation_type})>" 