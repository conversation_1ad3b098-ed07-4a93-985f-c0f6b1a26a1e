"""
权限服务数据库迁移脚本

此脚本用于：
- 创建权限相关数据库表
- 初始化系统角色和权限
- 设置基础的RBAC配置
"""

import logging
from datetime import datetime
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from ...core.config import settings
from .models import Base, Permission, Role, RolePermission, UserRole

logger = logging.getLogger(__name__)


def create_permission_tables(engine):
    """创建权限相关表"""
    Base.metadata.create_all(bind=engine)
    logger.info("✅ 权限数据表创建完成")
    logger.info("  - roles: 角色表")
    logger.info("  - permissions: 权限表")
    logger.info("  - role_permissions: 角色权限关联表")
    logger.info("  - user_roles: 用户角色关联表")
    logger.info("  - permission_operation_logs: 权限操作日志表")


def initialize_system_permissions(session):
    """初始化系统权限"""
    logger.info("开始初始化系统权限...")
    
    # 系统权限定义
    system_permissions = [
        # 用户管理权限
        {"code": "user.profile.read", "name": "查看用户资料", "module": "user", "resource": "profile", "action": "read"},
        {"code": "user.profile.update", "name": "更新用户资料", "module": "user", "resource": "profile", "action": "update"},
        {"code": "user.list.read", "name": "查看用户列表", "module": "user", "resource": "list", "action": "read"},
        {"code": "user.create", "name": "创建用户", "module": "user", "resource": "user", "action": "create"},
        {"code": "user.update", "name": "更新用户", "module": "user", "resource": "user", "action": "update"},
        {"code": "user.delete", "name": "删除用户", "module": "user", "resource": "user", "action": "delete"},
        {"code": "user.role.assign", "name": "分配用户角色", "module": "user", "resource": "role", "action": "assign"},
        
        # 数据采集权限 - 数据源管理
        {"code": "data_source.data_source.create", "name": "创建数据源", "module": "data_source", "resource": "data_source", "action": "create"},
        {"code": "data_source.data_source.read", "name": "查看数据源", "module": "data_source", "resource": "data_source", "action": "read"},
        {"code": "data_source.data_source.update", "name": "更新数据源", "module": "data_source", "resource": "data_source", "action": "update"},
        {"code": "data_source.data_source.delete", "name": "删除数据源", "module": "data_source", "resource": "data_source", "action": "delete"},
        {"code": "data_source.list.read", "name": "查看数据源列表", "module": "data_source", "resource": "list", "action": "read"},
        {"code": "data_source.stats.read", "name": "查看数据源统计", "module": "data_source", "resource": "stats", "action": "read"},
        
        # 数据采集权限 - 数据源配置管理
        {"code": "data_source.config.create", "name": "创建数据源配置", "module": "data_source", "resource": "config", "action": "create"},
        {"code": "data_source.config.read", "name": "查看数据源配置", "module": "data_source", "resource": "config", "action": "read"},
        {"code": "data_source.config.update", "name": "更新数据源配置", "module": "data_source", "resource": "config", "action": "update"},
        {"code": "data_source.config.delete", "name": "删除数据源配置", "module": "data_source", "resource": "config", "action": "delete"},
        {"code": "data_source.config.manage", "name": "管理数据源配置", "module": "data_source", "resource": "config", "action": "manage"},
        
        # 数据采集权限 - 原始数据记录管理
        {"code": "raw_data_record.raw_data_record.create", "name": "创建原始数据记录", "module": "raw_data_record", "resource": "raw_data_record", "action": "create"},
        {"code": "raw_data_record.raw_data_record.read", "name": "查看原始数据记录", "module": "raw_data_record", "resource": "raw_data_record", "action": "read"},
        {"code": "raw_data_record.raw_data_record.update", "name": "更新原始数据记录", "module": "raw_data_record", "resource": "raw_data_record", "action": "update"},
        {"code": "raw_data_record.raw_data_record.delete", "name": "删除原始数据记录", "module": "raw_data_record", "resource": "raw_data_record", "action": "delete"},
        {"code": "raw_data_record.raw_data_record.manage", "name": "管理原始数据记录", "module": "raw_data_record", "resource": "raw_data_record", "action": "manage"},
        {"code": "raw_data_record.raw_data_record.analyze", "name": "分析原始数据记录", "module": "raw_data_record", "resource": "raw_data_record", "action": "analyze"},
        {"code": "raw_data_record.list.read", "name": "查看原始数据记录列表", "module": "raw_data_record", "resource": "list", "action": "read"},
        {"code": "raw_data_record.stats.read", "name": "查看原始数据记录统计", "module": "raw_data_record", "resource": "stats", "action": "read"},
        
        # 数据分析权限
        {"code": "data.query.execute", "name": "执行数据查询", "module": "data", "resource": "query", "action": "execute"},
        {"code": "data.report.read", "name": "查看分析报告", "module": "data", "resource": "report", "action": "read"},
        {"code": "data.report.create", "name": "创建分析报告", "module": "data", "resource": "report", "action": "create"},
        {"code": "data.report.update", "name": "更新分析报告", "module": "data", "resource": "report", "action": "update"},
        {"code": "data.report.publish", "name": "发布分析报告", "module": "data", "resource": "report", "action": "publish"},
        {"code": "data.report.delete", "name": "删除分析报告", "module": "data", "resource": "report", "action": "delete"},
        {"code": "data.export", "name": "导出数据", "module": "data", "resource": "data", "action": "export"},
        
        # 内容管理权限
        {"code": "content.article.read", "name": "查看文章", "module": "content", "resource": "article", "action": "read"},
        {"code": "content.article.create", "name": "创建文章", "module": "content", "resource": "article", "action": "create"},
        {"code": "content.article.update", "name": "更新文章", "module": "content", "resource": "article", "action": "update"},
        {"code": "content.article.publish", "name": "发布文章", "module": "content", "resource": "article", "action": "publish"},
        {"code": "content.article.delete", "name": "删除文章", "module": "content", "resource": "article", "action": "delete"},
        {"code": "content.comment.moderate", "name": "管理评论", "module": "content", "resource": "comment", "action": "moderate"},
        
        # 系统配置权限
        {"code": "system.config.read", "name": "查看系统配置", "module": "system", "resource": "config", "action": "read"},
        {"code": "system.config.update", "name": "更新系统配置", "module": "system", "resource": "config", "action": "update"},
        {"code": "system.log.read", "name": "查看系统日志", "module": "system", "resource": "log", "action": "read"},
        {"code": "system.backup", "name": "系统备份", "module": "system", "resource": "system", "action": "backup"},
        {"code": "system.permission.manage", "name": "管理权限设置", "module": "system", "resource": "permission", "action": "manage"},
        
        # 财经日历权限
        {"code": "financial.calendar.read", "name": "查看财经日历", "module": "financial", "resource": "calendar", "action": "read"},
        {"code": "financial.calendar.create", "name": "创建财经事件", "module": "financial", "resource": "calendar", "action": "create"},
        {"code": "financial.calendar.update", "name": "更新财经事件", "module": "financial", "resource": "calendar", "action": "update"},
        {"code": "financial.calendar.delete", "name": "删除财经事件", "module": "financial", "resource": "calendar", "action": "delete"},
        
        # 标签分类权限
        {"code": "tag.classification.read", "name": "查看标签分类", "module": "tag", "resource": "classification", "action": "read"},
        {"code": "tag.classification.create", "name": "创建标签分类", "module": "tag", "resource": "classification", "action": "create"},
        {"code": "tag.classification.update", "name": "更新标签分类", "module": "tag", "resource": "classification", "action": "update"},
        {"code": "tag.classification.delete", "name": "删除标签分类", "module": "tag", "resource": "classification", "action": "delete"},
        
        # 推送服务权限
        {"code": "push.message.send", "name": "发送推送消息", "module": "push", "resource": "message", "action": "send"},
        {"code": "push.template.create", "name": "创建推送模板", "module": "push", "resource": "template", "action": "create"},
        {"code": "push.template.update", "name": "更新推送模板", "module": "push", "resource": "template", "action": "update"},
        {"code": "push.log.read", "name": "查看推送日志", "module": "push", "resource": "log", "action": "read"},
        
        # AI 模型管理权限
        {"code": "ai_model.model.create", "name": "创建AI模型", "module": "ai_model", "resource": "model", "action": "create"},
        {"code": "ai_model.model.read", "name": "查看AI模型", "module": "ai_model", "resource": "model", "action": "read"},
        {"code": "ai_model.model.update", "name": "更新AI模型", "module": "ai_model", "resource": "model", "action": "update"},
        {"code": "ai_model.model.delete", "name": "删除AI模型", "module": "ai_model", "resource": "model", "action": "delete"},
        {"code": "ai_model.list.read", "name": "查看模型列表", "module": "ai_model", "resource": "list", "action": "read"},
        {"code": "ai_model.status.update", "name": "更新模型状态", "module": "ai_model", "resource": "status", "action": "update"},
        {"code": "ai_model.default.update", "name": "设置默认模型", "module": "ai_model", "resource": "default", "action": "update"},
        {"code": "ai_model.metrics.create", "name": "创建模型指标", "module": "ai_model", "resource": "metrics", "action": "create"},
        {"code": "ai_model.metrics.read", "name": "查看模型指标", "module": "ai_model", "resource": "metrics", "action": "read"},
        {"code": "ai_model.analytics.read", "name": "查看分析数据", "module": "ai_model", "resource": "analytics", "action": "read"},
        
        # 数据处理规则管理权限
        {"code": "data_processing.rule.create", "name": "创建数据处理规则", "module": "data_processing", "resource": "rule", "action": "create"},
        {"code": "data_processing.rule.read", "name": "查看数据处理规则", "module": "data_processing", "resource": "rule", "action": "read"},
        {"code": "data_processing.rule.update", "name": "更新数据处理规则", "module": "data_processing", "resource": "rule", "action": "update"},
        {"code": "data_processing.rule.delete", "name": "删除数据处理规则", "module": "data_processing", "resource": "rule", "action": "delete"},
        {"code": "data_processing.rule.manage", "name": "管理数据处理规则", "module": "data_processing", "resource": "rule", "action": "manage"},
        {"code": "data_processing.rule.test", "name": "测试数据处理规则", "module": "data_processing", "resource": "rule", "action": "test"},
        {"code": "data_processing.list.read", "name": "查看处理规则列表", "module": "data_processing", "resource": "list", "action": "read"},
        {"code": "data_processing.stats.read", "name": "查看处理规则统计", "module": "data_processing", "resource": "stats", "action": "read"},
        {"code": "data_processing.batch.update", "name": "批量更新处理规则", "module": "data_processing", "resource": "batch", "action": "update"},
        {"code": "data_processing.status.read", "name": "查看处理状态", "module": "data_processing", "resource": "status", "action": "read"},
        {"code": "data_processing.status.update", "name": "更新处理状态", "module": "data_processing", "resource": "status", "action": "update"},
        
        # 数据处理管道管理权限
        {"code": "data_processing.pipeline.create", "name": "创建处理管道", "module": "data_processing", "resource": "pipeline", "action": "create"},
        {"code": "data_processing.pipeline.read", "name": "查看处理管道", "module": "data_processing", "resource": "pipeline", "action": "read"},
        {"code": "data_processing.pipeline.update", "name": "更新处理管道", "module": "data_processing", "resource": "pipeline", "action": "update"},
        {"code": "data_processing.pipeline.delete", "name": "删除处理管道", "module": "data_processing", "resource": "pipeline", "action": "delete"},
        {"code": "data_processing.pipeline.manage", "name": "管理处理管道", "module": "data_processing", "resource": "pipeline", "action": "manage"},
        {"code": "data_processing.pipeline.test", "name": "测试处理管道", "module": "data_processing", "resource": "pipeline", "action": "test"},

        # 快讯管理权限
        {"code": "flash_news.flash_news.read", "name": "查看快讯", "module": "flash_news", "resource": "flash_news", "action": "read"},
        {"code": "flash_news.flash_news.update", "name": "更新快讯", "module": "flash_news", "resource": "flash_news", "action": "update"},
        {"code": "flash_news.flash_news.delete", "name": "删除快讯", "module": "flash_news", "resource": "flash_news", "action": "delete"},
        {"code": "flash_news.flash_news.stats", "name": "查看快讯统计", "module": "flash_news", "resource": "flash_news", "action": "stats"},

        # 新闻文章管理权限
        {"code": "news_article.news_article.read", "name": "查看新闻文章", "module": "news_article", "resource": "news_article", "action": "read"},
        {"code": "news_article.news_article.update", "name": "更新新闻文章", "module": "news_article", "resource": "news_article", "action": "update"},
        {"code": "news_article.news_article.delete", "name": "删除新闻文章", "module": "news_article", "resource": "news_article", "action": "delete"},
        {"code": "news_article.news_article.stats", "name": "查看新闻文章统计", "module": "news_article", "resource": "news_article", "action": "stats"},

        # 研究报告管理权限
        {"code": "research_report.research_report.read", "name": "查看研究报告", "module": "research_report", "resource": "research_report", "action": "read"},
        {"code": "research_report.research_report.update", "name": "更新研究报告", "module": "research_report", "resource": "research_report", "action": "update"},
        {"code": "research_report.research_report.delete", "name": "删除研究报告", "module": "research_report", "resource": "research_report", "action": "delete"},
        {"code": "research_report.research_report.stats", "name": "查看研究报告统计", "module": "research_report", "resource": "research_report", "action": "stats"},

        # 经济指标数据管理权限
        {"code": "economic_indicator_data.economic_indicator_data.read", "name": "查看经济指标数据", "module": "economic_indicator_data", "resource": "economic_indicator_data", "action": "read"},
        {"code": "economic_indicator_data.economic_indicator_data.update", "name": "更新经济指标数据", "module": "economic_indicator_data", "resource": "economic_indicator_data", "action": "update"},
        {"code": "economic_indicator_data.economic_indicator_data.delete", "name": "删除经济指标数据", "module": "economic_indicator_data", "resource": "economic_indicator_data", "action": "delete"},
        {"code": "economic_indicator_data.economic_indicator_data.stats", "name": "查看经济指标数据统计", "module": "economic_indicator_data", "resource": "economic_indicator_data", "action": "stats"},
    ]
    
    created_permissions = []
    for perm_data in system_permissions:
        # 检查权限是否已存在
        existing_perm = session.query(Permission).filter(Permission.code == perm_data["code"]).first()
        if not existing_perm:
            permission = Permission(
                code=perm_data["code"],
                name=perm_data["name"],
                description=f"{perm_data['name']} - {perm_data['module']}.{perm_data['resource']}.{perm_data['action']}",
                module=perm_data["module"],
                resource=perm_data["resource"],
                action=perm_data["action"]
            )
            session.add(permission)
            created_permissions.append(permission)
    
    if created_permissions:
        session.commit()
        logger.info(f"✅ 创建了 {len(created_permissions)} 个系统权限")
    else:
        logger.info("⚠️ 系统权限已存在，跳过创建")
    
    return created_permissions


def initialize_system_roles(session):
    """初始化系统角色"""
    logger.info("开始初始化系统角色...")
    
    # 系统角色定义
    system_roles = [
        {
            "name": "Admin",
            "description": "系统管理员，拥有所有权限",
            "is_system": True,
            "permissions": "*"  # 所有权限
        },
        {
            "name": "DataAdmin",
            "description": "数据管理员，负责数据源和采集任务管理",
            "is_system": True,
            "permissions": [
                "data.source.*", "data.task.*", "data.query.execute",
                "data.report.*", "data.export", "user.profile.*",
                "data_source.data_source.*", "data_source.config.*", "data_source.list.read", "data_source.stats.read",
                "raw_data_record.raw_data_record.*", "raw_data_record.list.read", "raw_data_record.stats.read",
                "ai_model.*", "data_processing.*",
                "flash_news.*", "news_article.*", "research_report.*", "economic_indicator_data.*"
            ]
        },
        {
            "name": "Analyst",
            "description": "数据分析师，负责数据分析和报告生成",
            "is_system": True,
            "permissions": [
                "data.source.read", "data.task.read", "data.query.execute",
                "data.report.*", "data.export", "user.profile.*",
                "data_source.data_source.read", "data_source.config.read", "data_source.list.read", "data_source.stats.read",
                "raw_data_record.raw_data_record.read", "raw_data_record.list.read", "raw_data_record.stats.read",
                "ai_model.model.read", "ai_model.list.read", "ai_model.metrics.read", "ai_model.analytics.read",
                "data_processing.rule.read", "data_processing.list.read", "data_processing.stats.read", "data_processing.status.read",
                "flash_news.read", "flash_news.stats", "news_article.read", "news_article.stats",
                "research_report.read", "research_report.stats", "economic_indicator_data.read", "economic_indicator_data.stats"
            ]
        },
        {
            "name": "Editor",
            "description": "内容编辑，负责内容创建和编辑",
            "is_system": True,
            "permissions": [
                "content.*", "financial.calendar.*", "tag.classification.*",
                "user.profile.*", "flash_news.*", "news_article.*", "research_report.*"
            ]
        },
        {
            "name": "AccountManager",
            "description": "客户经理，负责用户服务和账户管理",
            "is_system": True,
            "permissions": [
                "user.profile.*", "user.list.read", "user.create", "user.update",
                "content.article.read", "financial.calendar.read"
            ]
        },
        {
            "name": "RiskOfficer",
            "description": "风控专员，负责风险监控和合规检查",
            "is_system": True,
            "permissions": [
                "system.log.read", "data.report.read", "user.list.read",
                "user.profile.*"
            ]
        },
        {
            "name": "User",
            "description": "普通用户，基础功能权限",
            "is_system": True,
            "permissions": [
                "user.profile.*", "content.article.read", "financial.calendar.read",
                "data.report.read", "flash_news.read", "news_article.read", "research_report.read"
            ]
        },
        {
            "name": "Guest",
            "description": "访客，只能访问公开内容",
            "is_system": True,
            "permissions": [
                "content.article.read", "financial.calendar.read",
                "flash_news.read", "news_article.read", "research_report.read"
            ]
        },
        {
            "name": "AIEngineer",
            "description": "AI工程师，负责AI模型训练和调优",
            "is_system": True,
            "permissions": [
                "ai_model.model.*", "ai_model.list.read", "ai_model.status.update",
                "ai_model.metrics.*", "ai_model.analytics.read", "user.profile.*"
            ]
        }
    ]
    
    # 获取所有权限
    all_permissions = session.query(Permission).all()
    permission_map = {perm.code: perm for perm in all_permissions}
    
    created_roles = []
    for role_data in system_roles:
        # 检查角色是否已存在
        existing_role = session.query(Role).filter(Role.name == role_data["name"]).first()
        if existing_role:
            logger.info(f"⚠️ 角色 {role_data['name']} 已存在，跳过创建")
            continue
        
        # 创建角色
        role = Role(
            name=role_data["name"],
            description=role_data["description"],
            is_system=role_data["is_system"]
        )
        session.add(role)
        session.flush()  # 获取角色ID
        
        # 分配权限
        role_permissions = []
        if role_data["permissions"] == "*":
            # 分配所有权限
            role_permissions = all_permissions
        else:
            # 根据权限模式匹配
            for perm_pattern in role_data["permissions"]:
                if perm_pattern.endswith(".*"):
                    # 模块通配符
                    prefix = perm_pattern[:-2]
                    matching_perms = [p for p in all_permissions if p.code.startswith(prefix + ".")]
                    role_permissions.extend(matching_perms)
                elif perm_pattern in permission_map:
                    # 精确匹配
                    role_permissions.append(permission_map[perm_pattern])
        
        # 去重并创建角色权限关联
        unique_permissions = list(set(role_permissions))
        for permission in unique_permissions:
            role_permission = RolePermission(role_id=role.id, permission_id=permission.id)
            session.add(role_permission)
        
        created_roles.append((role, len(unique_permissions)))
    
    if created_roles:
        session.commit()
        for role, perm_count in created_roles:
            logger.info(f"✅ 创建角色: {role.name}，分配了 {perm_count} 个权限")
    
    return [role for role, _ in created_roles]


def create_admin_user_if_not_exists(session):
    """创建管理员用户（如果不存在）"""
    from ..user_service.models import User
    
    logger.info("检查管理员用户...")
    
    # 查找是否已有管理员
    admin_role = session.query(Role).filter(Role.name == "Admin").first()
    if not admin_role:
        logger.warning("⚠️ Admin角色不存在，跳过管理员用户创建")
        return
    
    # 检查是否已有管理员用户
    existing_admin = session.query(UserRole).filter(UserRole.role_id == admin_role.id).first()
    if existing_admin:
        logger.info("⚠️ 已存在管理员用户，跳过创建")
        return
    
    # 查找第一个用户作为管理员
    first_user = session.query(User).filter(User.id == 1).first()
    if first_user:
        # 为第一个用户分配管理员角色
        user_role = UserRole(user_id=first_user.id, role_id=admin_role.id)
        session.add(user_role)
        session.commit()
        logger.info(f"✅ 为用户 {first_user.phone} 分配了管理员权限")
    else:
        logger.warning("⚠️ 没有找到用户，请先创建用户后手动分配管理员角色")


def run_migration():
    """运行权限系统迁移"""
    logger.info("🚀 开始权限系统迁移...")
    
    engine = create_engine(settings.DATABASE_URL)
    
    # 创建表结构
    create_permission_tables(engine)
    
    # 初始化数据
    SessionLocal = sessionmaker(bind=engine)
    session = SessionLocal()
    
    try:
        # 初始化权限
        permissions = initialize_system_permissions(session)
        
        # 初始化角色
        roles = initialize_system_roles(session)
        
        # 创建管理员用户
        create_admin_user_if_not_exists(session)
        
        logger.info("✅ 权限系统迁移完成")
        logger.info(f"  - 权限数量: {len(session.query(Permission).all())}")
        logger.info(f"  - 角色数量: {len(session.query(Role).all())}")
        logger.info(f"  - 角色权限关联: {len(session.query(RolePermission).all())}")
        
    except Exception as e:
        logger.error(f"❌ 权限系统迁移失败: {e}")
        session.rollback()
        raise
    finally:
        session.close()


if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    run_migration() 