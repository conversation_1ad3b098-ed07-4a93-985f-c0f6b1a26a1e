"""
用户服务数据模型
定义用户相关的数据库模型
"""

from datetime import datetime, timezone

from sqlalchemy import (ARRAY, DECIMAL, Boolean, Column, DateTime, ForeignKey,
                        Index, Integer, SmallInteger, String, Text, TIMESTAMP)
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from ...core.database import Base


class User(Base):
    """
    用户基础信息表
    """

    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True, comment="用户ID")
    username = Column(String(50), nullable=True, comment="用户名")
    email = Column(String(100), unique=True, nullable=True, comment="邮箱")
    phone = Column(
        String(20), unique=True, nullable=False, index=True, comment="手机号"
    )
    password_hash = Column(String(255), nullable=True, comment="密码哈希")
    user_type = Column(
        SmallInteger,
        nullable=False,
        default=1,
        comment="用户类型 1:小白型 2:进阶型 3:焦虑型",
    )
    risk_level = Column(SmallInteger, nullable=False, default=3, comment="风险等级 1-5")
    knowledge_level = Column(
        SmallInteger, nullable=False, default=1, comment="知识水平 1-5"
    )
    is_active = Column(Boolean, default=True, comment="是否激活")
    is_verified = Column(Boolean, default=False, comment="是否已验证")
    is_admin = Column(Boolean, default=False, comment="是否是管理员")
    first_login_at = Column(DateTime, nullable=True, comment="首次登录时间")
    last_login_at = Column(DateTime, nullable=True, comment="最后登录时间")
    created_at = Column(TIMESTAMP, server_default=func.now(), comment="创建时间")
    updated_at = Column(
        TIMESTAMP,
        server_default=func.now(),
        onupdate=func.now(),
        comment="更新时间",
    )

    # 关系定义
    tags = relationship("UserTag", back_populates="user", cascade="all, delete-orphan")
    behaviors = relationship(
        "UserBehavior", back_populates="user", cascade="all, delete-orphan"
    )
    sessions = relationship(
        "UserSession", back_populates="user", cascade="all, delete-orphan"
    )
    # 权限相关关系 - 使用字符串引用避免循环导入
    # roles = relationship("Role", secondary="user_roles", back_populates="users")

    # 索引定义
    __table_args__ = (
        Index("idx_user_phone_active", "phone", "is_active"),
        Index("idx_user_type_risk", "user_type", "risk_level"),
        Index("idx_user_login_time", "last_login_at"),
        Index("idx_user_admin", "is_admin"),
    )


class SmsVerificationCode(Base):
    """
    短信验证码表
    """

    __tablename__ = "sms_verification_codes"

    id = Column(Integer, primary_key=True, index=True, comment="验证码ID")
    phone = Column(String(20), nullable=False, index=True, comment="手机号")
    code = Column(String(10), nullable=False, comment="验证码")
    purpose = Column(
        String(20), nullable=False, comment="验证码用途: login/register/reset_password"
    )
    is_used = Column(Boolean, default=False, comment="是否已使用")
    attempts = Column(Integer, default=0, comment="验证尝试次数")
    expires_at = Column(DateTime, nullable=False, comment="过期时间")
    created_at = Column(TIMESTAMP, server_default=func.now(), comment="创建时间")

    # 索引定义
    __table_args__ = (
        Index("idx_sms_phone_purpose", "phone", "purpose", "is_used"),
        Index("idx_sms_expires_at", "expires_at"),
    )


class UserTag(Base):
    """
    用户标签表
    """

    __tablename__ = "user_tags"

    id = Column(Integer, primary_key=True, index=True, comment="标签ID")
    user_id = Column(
        Integer,
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        comment="用户ID",
    )
    tag_category = Column(String(50), nullable=False, comment="标签分类 core/derived")
    tag_name = Column(String(100), nullable=False, comment="标签名称")
    tag_value = Column(Text, comment="标签值")
    weight = Column(DECIMAL(3, 2), default=1.0, comment="权重")
    created_at = Column(TIMESTAMP, server_default=func.now(), comment="创建时间")
    updated_at = Column(
        TIMESTAMP,
        server_default=func.now(),
        onupdate=func.now(),
        comment="更新时间",
    )

    # 关系定义
    user = relationship("User", back_populates="tags")

    # 索引定义
    __table_args__ = (
        Index("idx_user_tag_category", "user_id", "tag_category"),
        Index("idx_tag_name_weight", "tag_name", "weight"),
    )


class UserBehavior(Base):
    """
    用户行为追踪表
    """

    __tablename__ = "user_behaviors"

    id = Column(Integer, primary_key=True, index=True, comment="行为ID")
    user_id = Column(
        Integer,
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        comment="用户ID",
    )
    action_type = Column(String(50), nullable=False, comment="行为类型")
    action_target = Column(String(100), comment="行为目标")
    action_details = Column(Text, comment="行为详情（JSON格式）")
    ip_address = Column(String(45), comment="IP地址")
    user_agent = Column(Text, comment="用户代理")
    created_at = Column(TIMESTAMP, server_default=func.now(), comment="创建时间")

    # 关系定义
    user = relationship("User", back_populates="behaviors")

    # 索引定义
    __table_args__ = (
        Index("idx_behavior_user_action", "user_id", "action_type"),
        Index("idx_behavior_created_at", "created_at"),
    )


class UserSession(Base):
    """
    用户会话表
    """

    __tablename__ = "user_sessions"

    id = Column(Integer, primary_key=True, index=True, comment="会话ID")
    user_id = Column(
        Integer,
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        comment="用户ID",
    )
    session_token = Column(String(255), unique=True, nullable=False, comment="会话令牌")
    refresh_token = Column(String(255), unique=True, nullable=False, comment="刷新令牌")
    ip_address = Column(String(45), comment="IP地址")
    user_agent = Column(Text, comment="用户代理")
    is_active = Column(Boolean, default=True, comment="是否活跃")
    expires_at = Column(DateTime, nullable=False, comment="过期时间")
    last_used_at = Column(
        TIMESTAMP,
        server_default=func.now(),
        onupdate=func.now(),
        comment="最后使用时间",
    )
    created_at = Column(TIMESTAMP, server_default=func.now(), comment="创建时间")

    # 关系定义
    user = relationship("User", back_populates="sessions")

    # 索引定义
    __table_args__ = (
        Index("idx_session_token", "session_token"),
        Index("idx_session_user_active", "user_id", "is_active"),
        Index("idx_session_expires_at", "expires_at"),
    )
