"""
用户服务数据传输对象(DTO)
定义API请求和响应的数据格式
"""

import re
from datetime import datetime, timezone
from enum import IntEnum, Enum
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, EmailStr, Field, field_validator


class UserType(IntEnum):
    """用户类型枚举"""

    NOVICE = 1  # 小白型
    ADVANCED = 2  # 进阶型
    ANXIOUS = 3  # 焦虑型


class RiskLevel(IntEnum):
    """风险等级枚举"""

    CONSERVATIVE = 1  # 保守型
    MODERATE_CONSERVATIVE = 2  # 稳健偏保守型
    MODERATE = 3  # 稳健型
    MODERATE_AGGRESSIVE = 4  # 稳健偏进取型
    AGGRESSIVE = 5  # 进取型


class SmsCodePurpose(str):
    """短信验证码用途"""

    LOGIN = "login"
    REGISTER = "register"
    RESET_PASSWORD = "reset_password"


class PhoneLoginRequest(BaseModel):
    """手机号登录请求"""

    phone: str = Field(..., description="手机号")
    verification_code: str = Field(..., description="验证码")

    @field_validator("phone")
    def validate_phone(cls, v):
        """验证手机号格式"""
        if not re.match(r"^1[3-9]\d{9}$", v):
            raise ValueError("Invalid phone number format")
        return v

    @field_validator("verification_code")
    def validate_verification_code(cls, v):
        """验证验证码格式"""
        if not re.match(r"^\d{6}$", v):
            raise ValueError("Verification code must be 6 digits")
        return v


class SendSmsCodeRequest(BaseModel):
    """发送短信验证码请求"""

    phone: str = Field(..., description="手机号")
    purpose: str = Field(default=SmsCodePurpose.LOGIN, description="验证码用途")

    @field_validator("phone")
    def validate_phone(cls, v):
        """验证手机号格式"""
        if not re.match(r"^1[3-9]\d{9}$", v):
            raise ValueError("Invalid phone number format")
        return v

    @field_validator("purpose")
    def validate_purpose(cls, v):
        """验证用途"""
        if v not in [
            SmsCodePurpose.LOGIN,
            SmsCodePurpose.REGISTER,
            SmsCodePurpose.RESET_PASSWORD,
        ]:
            raise ValueError("Invalid purpose")
        return v


class UserResponse(BaseModel):
    """用户信息响应"""

    id: int
    phone: str
    username: Optional[str]
    email: Optional[str]
    user_type: UserType
    risk_level: RiskLevel
    knowledge_level: int
    is_active: bool
    is_verified: bool
    first_login_at: Optional[datetime]
    last_login_at: Optional[datetime]
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class UserTagCreate(BaseModel):
    """用户标签创建请求"""

    tag_category: str = Field(..., description="标签分类")
    tag_name: str = Field(..., description="标签名称")
    tag_value: Optional[str] = Field(None, description="标签值")
    weight: float = Field(default=1.0, description="权重", ge=0, le=5)

    @field_validator("tag_category")
    def validate_tag_category(cls, v):
        """验证标签分类"""
        if v not in ["core", "derived", "behavior", "preference"]:
            raise ValueError(
                "Tag category must be one of: core, derived, behavior, preference"
            )
        return v

    @field_validator("tag_name")
    def validate_tag_name(cls, v):
        """验证标签名称"""
        if len(v) < 1 or len(v) > 100:
            raise ValueError("Tag name must be between 1 and 100 characters")
        return v


class UserTagResponse(BaseModel):
    """用户标签响应"""

    id: int
    user_id: int
    tag_category: str
    tag_name: str
    tag_value: Optional[str]
    weight: float
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class UserTagUpdate(BaseModel):
    """用户标签更新请求"""

    tag_value: Optional[str] = None
    weight: Optional[float] = Field(None, ge=0, le=5)


class UserBehaviorCreate(BaseModel):
    """用户行为创建请求"""

    action_type: str = Field(..., description="行为类型")
    action_target: Optional[str] = Field(None, description="行为目标")
    action_details: Optional[Dict[str, Any]] = Field(None, description="行为详情")

    @field_validator("action_type")
    def validate_action_type(cls, v):
        """验证行为类型"""
        allowed_types = [
            "login",
            "logout",
            "admin_login",
            "create_user",
            "update_user_status",
            "view_article",
            "view_stock",
            "search",
            "bookmark",
            "share",
            "comment",
            "like",
            "follow",
            "profile_update",
        ]
        if v not in allowed_types:
            raise ValueError(f'Action type must be one of: {", ".join(allowed_types)}')
        return v


class UserBehaviorResponse(BaseModel):
    """用户行为响应"""

    id: int
    user_id: int
    action_type: str
    action_target: Optional[str]
    action_details: Optional[str]  # JSON string
    ip_address: Optional[str]
    user_agent: Optional[str]
    created_at: datetime

    class Config:
        from_attributes = True


class UserSessionResponse(BaseModel):
    """用户会话响应"""

    id: int
    user_id: int
    ip_address: Optional[str]
    user_agent: Optional[str]
    is_active: bool
    expires_at: datetime
    last_used_at: datetime
    created_at: datetime

    class Config:
        from_attributes = True


class TokenResponse(BaseModel):
    """令牌响应"""

    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    expires_in: int
    user: UserResponse


class SendSmsCodeResponse(BaseModel):
    """发送短信验证码响应"""

    message: str
    expires_in: int = 300  # 5分钟过期


class UserProfileUpdate(BaseModel):
    """用户信息更新请求"""

    username: Optional[str] = None
    email: Optional[EmailStr] = None
    user_type: Optional[UserType] = None
    risk_level: Optional[RiskLevel] = None
    knowledge_level: Optional[int] = None

    @field_validator("username")
    def validate_username(cls, v):
        """验证用户名格式"""
        if v is not None:
            if len(v) < 1 or len(v) > 50:
                raise ValueError("Username must be between 1 and 50 characters")
            if not re.match(r"^[a-zA-Z0-9_\u4e00-\u9fa5]+$", v):
                raise ValueError(
                    "Username can only contain letters, numbers, underscores and Chinese characters"
                )
        return v

    @field_validator("knowledge_level")
    def validate_knowledge_level(cls, v):
        if v is not None and not 1 <= v <= 5:
            raise ValueError("Knowledge level must be between 1 and 5")
        return v


class RefreshTokenRequest(BaseModel):
    """刷新令牌请求"""

    refresh_token: str = Field(..., description="刷新令牌")


class UserAnalyticsResponse(BaseModel):
    """用户分析响应"""

    user_id: int
    total_behaviors: int
    behavior_types: Dict[str, int]
    active_sessions: int
    tags_count: int
    last_activity: Optional[datetime]
    user_score: float


class UserCreate(BaseModel):
    """用户创建请求"""

    phone: str = Field(..., description="手机号")
    username: Optional[str] = Field(None, description="用户名")
    email: Optional[EmailStr] = Field(None, description="邮箱")
    user_type: UserType = Field(default=UserType.NOVICE, description="用户类型")
    risk_level: RiskLevel = Field(default=RiskLevel.MODERATE, description="风险等级")
    knowledge_level: int = Field(default=1, description="知识水平")
    is_active: bool = Field(default=True, description="是否激活")
    is_verified: bool = Field(default=False, description="是否已验证")
    is_admin: bool = Field(default=False, description="是否是管理员")

    @field_validator("phone")
    def validate_phone(cls, v):
        """验证手机号格式"""
        if not re.match(r"^1[3-9]\d{9}$", v):
            raise ValueError("Phone number must be a valid Chinese mobile number")
        return v

    @field_validator("username")
    def validate_username(cls, v):
        """验证用户名格式"""
        if v is not None:
            if len(v) < 1 or len(v) > 50:
                raise ValueError("Username must be between 1 and 50 characters")
            if not re.match(r"^[a-zA-Z0-9_\u4e00-\u9fa5]+$", v):
                raise ValueError(
                    "Username can only contain letters, numbers, underscores and Chinese characters"
                )
        return v

    @field_validator("knowledge_level")
    def validate_knowledge_level(cls, v):
        """验证知识水平"""
        if not 1 <= v <= 5:
            raise ValueError("Knowledge level must be between 1 and 5")
        return v


class UserUpdate(BaseModel):
    """用户更新请求"""

    username: Optional[str] = Field(None, description="用户名")
    email: Optional[EmailStr] = Field(None, description="邮箱")
    phone: Optional[str] = Field(None, description="手机号")
    user_type: Optional[UserType] = Field(None, description="用户类型")
    risk_level: Optional[RiskLevel] = Field(None, description="风险等级")
    knowledge_level: Optional[int] = Field(None, description="知识水平")

    @field_validator("phone")
    def validate_phone(cls, v):
        """验证手机号格式"""
        if v is not None and not re.match(r"^1[3-9]\d{9}$", v):
            raise ValueError("Phone number must be a valid Chinese mobile number")
        return v

    @field_validator("username")
    def validate_username(cls, v):
        """验证用户名格式"""
        if v is not None:
            if len(v) < 1 or len(v) > 50:
                raise ValueError("Username must be between 1 and 50 characters")
            if not re.match(r"^[a-zA-Z0-9_\u4e00-\u9fa5]+$", v):
                raise ValueError(
                    "Username can only contain letters, numbers, underscores and Chinese characters"
                )
        return v

    @field_validator("knowledge_level")
    def validate_knowledge_level(cls, v):
        """验证知识水平"""
        if v is not None and not 1 <= v <= 5:
            raise ValueError("Knowledge level must be between 1 and 5")
        return v


class UserStatusUpdate(BaseModel):
    """用户状态更新请求"""

    is_active: bool = Field(..., description="是否激活")


class UserRoleAssign(BaseModel):
    """用户角色分配请求"""

    role_id: int = Field(..., description="角色ID")


class AdminLoginRequest(BaseModel):
    """管理员登录请求"""

    phone: str = Field(..., description="手机号")
    verification_code: str = Field(..., description="验证码")

    @field_validator("phone")
    def validate_phone(cls, v):
        """验证手机号格式"""
        if not re.match(r"^1[3-9]\d{9}$", v):
            raise ValueError("Phone number must be a valid Chinese mobile number")
        return v

    @field_validator("verification_code")
    def validate_verification_code(cls, v):
        """验证验证码格式"""
        if not re.match(r"^\d{6}$", v):
            raise ValueError("Verification code must be 6 digits")
        return v


class RoleInfo(BaseModel):
    """角色信息"""

    id: int = Field(..., description="角色ID")
    name: str = Field(..., description="角色名称")
    description: Optional[str] = Field(None, description="角色描述")


class PermissionInfo(BaseModel):
    """权限信息"""

    id: int = Field(..., description="权限ID")
    code: str = Field(..., description="权限编码")
    name: str = Field(..., description="权限名称")
    module: str = Field(..., description="所属模块")
    resource: str = Field(..., description="资源类型")
    action: str = Field(..., description="操作类型")


class AdminUserResponse(BaseModel):
    """管理员用户信息响应"""

    id: int
    phone: str
    username: Optional[str]
    email: Optional[str]
    user_type: UserType
    risk_level: RiskLevel
    knowledge_level: int
    is_active: bool
    is_verified: bool
    is_admin: bool
    first_login_at: Optional[datetime]
    last_login_at: Optional[datetime]
    created_at: datetime
    updated_at: datetime
    roles: List[RoleInfo] = Field(default_factory=list, description="用户角色列表")
    permissions: List[PermissionInfo] = Field(
        default_factory=list, description="用户权限列表"
    )

    class Config:
        from_attributes = True


class AdminLoginResponse(BaseModel):
    """管理员登录响应"""

    access_token: str = Field(..., description="访问令牌")
    token_type: str = Field(..., description="令牌类型")
    expires_in: int = Field(..., description="访问令牌过期时间（秒）")
    user: AdminUserResponse = Field(..., description="用户信息")


class AdminSendSmsRequest(BaseModel):
    """管理员发送验证码请求"""

    phone: str = Field(..., description="手机号")
    purpose: str = Field(default=SmsCodePurpose.LOGIN, description="验证码用途")

    @field_validator("phone")
    def validate_phone(cls, v):
        """验证手机号格式"""
        if not re.match(r"^1[3-9]\d{9}$", v):
            raise ValueError("Phone number must be a valid Chinese mobile number")
        return v

    @field_validator("purpose")
    def validate_purpose(cls, v):
        """验证用途"""
        if v not in [
            SmsCodePurpose.LOGIN,
            SmsCodePurpose.REGISTER,
            SmsCodePurpose.RESET_PASSWORD,
        ]:
            raise ValueError("Invalid purpose")
        return v


class AdminSendSmsResponse(BaseModel):
    """管理员发送验证码响应"""

    message: str = Field(..., description="响应消息")
