"""
用户服务模块
包含用户认证、管理、会话管理等功能
"""

import json
import logging
import random
from datetime import datetime, timedelta, timezone
from functools import wraps
from typing import Any, Dict, List, Optional, Union

from jose import JWTError, jwt
from passlib.context import CryptContext
from sqlalchemy import and_, func
from sqlalchemy.orm import Session

import redis

from ...core.config import settings
from ..sms_service.service import SmsService
from .models import User, UserBehavior, UserSession, UserTag
from .schemas import SmsCodePurpose, UserBehaviorCreate, UserTagCreate, UserType


class RedisService:
    """Redis服务类 - 提供安全可靠的Redis操作"""

    def __init__(self):
        """初始化Redis连接"""
        try:
            # 从配置中获取Redis连接参数，添加默认值增强健壮性
            self.host = settings.REDIS_HOST or "localhost"
            self.port = settings.REDIS_PORT or 6379
            self.password = settings.REDIS_PASSWORD
            self.db = settings.REDIS_DB or 0

            # 统一连接参数，避免代码重复
            connection_params = {
                "host": self.host,
                "port": self.port,
                "db": self.db,
                "decode_responses": True,  # 统一使用字符串解码
                "socket_timeout": 5,  # 添加超时设置防止阻塞
                "retry_on_timeout": True,  # 超时自动重试
            }

            if self.password:
                connection_params["password"] = self.password

            self.redis_client = redis.Redis(**connection_params)

            # 测试连接并获取服务器信息，验证功能完整性
            server_info = self.redis_client.info()
            logging.info(
                f"✅ Redis connection established: {server_info['redis_version']}"
            )

        except Exception as e:
            logging.error(f"❌ Redis connection failed: {e}")
            self.redis_client = None

    # 装饰器：统一错误处理
    def _handle_redis_errors(func):
        @wraps(func)
        def wrapper(self, *args, **kwargs):
            if not self.redis_client:
                logging.warning("Redis client is not initialized")
                return None

            try:
                return func(self, *args, **kwargs)
            except redis.RedisError as e:
                logging.error(f"Redis operation failed: {e}")
                return None
            except Exception as e:
                logging.error(f"Unexpected error: {e}")
                return None

        return wrapper

    @_handle_redis_errors
    def set_value(
        self, key: str, value: Union[str, int, float], expire_time: int = None
    ) -> bool:
        """
        设置Redis值（支持字符串、整数、浮点数）

        Args:
            key: Redis键
            value: 值（支持字符串、整数、浮点数）
            expire_time: 过期时间(秒)

        Returns:
            操作是否成功
        """
        expire = expire_time or self.default_expire
        return self.redis_client.setex(key, expire, value)

    @_handle_redis_errors
    def get_value(self, key: str) -> Optional[str]:
        """
        获取Redis值

        Args:
            key: Redis键

        Returns:
            Redis值或None
        """
        return self.redis_client.get(key)

    @_handle_redis_errors
    def delete_key(self, key: str) -> bool:
        """
        删除Redis键

        Args:
            key: Redis键

        Returns:
            操作是否成功
        """
        return bool(self.redis_client.delete(key))

    @_handle_redis_errors
    def increment_value(self, key: str, expire_time: int = None) -> int:
        """
        增加Redis值（原子操作）

        Args:
            key: Redis键
            expire_time: 过期时间(秒)

        Returns:
            增加后的值
        """
        with self.redis_client.pipeline() as pipe:
            pipe.incr(key)
            if expire_time:
                pipe.expire(key, expire_time)
            result = pipe.execute()
            return result[0]

    @_handle_redis_errors
    def get_or_set(self, key: str, default_value: Any, expire_time: int = None) -> Any:
        """
        获取值，如果不存在则设置默认值（原子操作）

        Args:
            key: Redis键
            default_value: 默认值
            expire_time: 过期时间(秒)

        Returns:
            Redis值或设置的默认值
        """
        with self.redis_client.pipeline() as pipe:
            while True:
                try:
                    pipe.watch(key)
                    value = pipe.get(key)

                    if value is not None:
                        pipe.reset()
                        return value

                    pipe.multi()
                    pipe.setex(key, expire_time or self.default_expire, default_value)
                    pipe.execute()
                    return default_value
                except redis.WatchError:
                    continue
                finally:
                    pipe.reset()

    @_handle_redis_errors
    def get_all_keys(self, pattern: str = "*") -> list[str]:
        """
        获取所有匹配的键

        Args:
            pattern: 键匹配模式

        Returns:
            匹配的键列表
        """
        return self.redis_client.keys(pattern)

    @_handle_redis_errors
    def set_hash(
        self, key: str, field_value_map: dict, expire_time: int = None
    ) -> bool:
        """
        设置哈希值

        Args:
            key: Redis键
            field_value_map: 字段-值映射
            expire_time: 过期时间(秒)

        Returns:
            操作是否成功
        """
        result = self.redis_client.hset(key, mapping=field_value_map)

        if expire_time:
            self.redis_client.expire(key, expire_time)

        return bool(result)

    @_handle_redis_errors
    def get_hash(self, key: str, field: str) -> Optional[str]:
        """
        获取哈希字段值

        Args:
            key: Redis键
            field: 哈希字段

        Returns:
            哈希字段值或None
        """
        return self.redis_client.hget(key, field)

    @_handle_redis_errors
    def get_all_hash_fields(self, key: str) -> dict:
        """
        获取哈希所有字段和值

        Args:
            key: Redis键

        Returns:
            哈希所有字段和值的字典
        """
        return self.redis_client.hgetall(key)


class PasswordService:
    """密码服务类"""

    def __init__(self):
        self.pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

    def hash_password(self, password: str) -> str:
        """
        哈希密码

        Args:
            password: 明文密码

        Returns:
            哈希后的密码
        """
        return self.pwd_context.hash(password)

    def verify_password(self, plain_password: str, hashed_password: str) -> bool:
        """
        验证密码

        Args:
            plain_password: 明文密码
            hashed_password: 哈希密码

        Returns:
            密码是否匹配
        """
        return self.pwd_context.verify(plain_password, hashed_password)


class JWTService:
    """JWT令牌服务类"""

    def __init__(self):
        self.secret_key = settings.JWT_SECRET_KEY
        self.algorithm = settings.JWT_ALGORITHM
        self.access_token_expire_minutes = settings.JWT_ACCESS_TOKEN_EXPIRE_MINUTES
        self.refresh_token_expire_days = settings.JWT_REFRESH_TOKEN_EXPIRE_DAYS

    def create_access_token(self, data: Dict[str, Any]) -> str:
        """
        创建访问令牌

        Args:
            data: 令牌数据

        Returns:
            JWT访问令牌
        """
        to_encode = data.copy()
        expire = datetime.now(timezone.utc) + timedelta(
            minutes=self.access_token_expire_minutes
        )
        to_encode.update(
            {
                "exp": expire,
                "type": "access",
                "iat": datetime.now(timezone.utc).timestamp(),
            }
        )
        return jwt.encode(to_encode, self.secret_key, algorithm=self.algorithm)

    def create_refresh_token(self, data: Dict[str, Any]) -> str:
        """
        创建刷新令牌

        Args:
            data: 令牌数据

        Returns:
            JWT刷新令牌
        """
        to_encode = data.copy()
        expire = datetime.now(timezone.utc) + timedelta(
            days=self.refresh_token_expire_days
        )
        to_encode.update(
            {
                "exp": expire,
                "type": "refresh",
                "iat": datetime.now(timezone.utc).timestamp(),
            }
        )
        return jwt.encode(to_encode, self.secret_key, algorithm=self.algorithm)

    def verify_token(self, token: str) -> Optional[Dict[str, Any]]:
        """
        验证令牌

        Args:
            token: JWT令牌

        Returns:
            令牌载荷数据或None
        """
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            return payload
        except JWTError:
            return None


class SmsCodeService:
    """短信验证码服务类"""

    def __init__(self, redis_service: RedisService, sms_service: SmsService):
        self.redis_service = redis_service
        self.sms_service = sms_service
        self.expire_minutes = 5
        self.max_attempts = 3

    def generate_code(self) -> str:
        """生成6位数字验证码"""
        return str(random.randint(100000, 999999))

    def _get_code_key(self, phone: str, purpose: str, client_type: str = "user") -> str:
        """
        获取验证码Redis键

        Args:
            phone: 手机号
            purpose: 用途
            client_type: 客户端类型，"user"代表C端，"admin"代表B端

        Returns:
            Redis键
        """
        return f"sms:code:{client_type}:{phone}:{purpose}"

    def _get_attempts_key(
        self, phone: str, purpose: str, client_type: str = "user"
    ) -> str:
        """
        获取尝试次数Redis键

        Args:
            phone: 手机号
            purpose: 用途
            client_type: 客户端类型，"user"代表C端，"admin"代表B端

        Returns:
            Redis键
        """
        return f"sms:attempts:{client_type}:{phone}:{purpose}"

    def send_sms_code(
        self, phone: str, purpose: str = SmsCodePurpose.LOGIN, client_type: str = "user"
    ) -> str:
        """
        发送短信验证码（使用腾讯云短信服务）

        Args:
            phone: 手机号
            purpose: 验证码用途
            client_type: 客户端类型，"user"代表C端，"admin"代表B端

        Returns:
            验证码

        Raises:
            Exception: 发送失败时抛出异常
        """
        code = self.generate_code()
        code_key = self._get_code_key(phone, purpose, client_type)
        attempts_key = self._get_attempts_key(phone, purpose, client_type)

        # 存储验证码到Redis
        expire_time = self.expire_minutes * 60  # 转换为秒
        success = self.redis_service.set_value(code_key, code, expire_time)

        if success:
            # 重置尝试次数
            self.redis_service.delete_key(attempts_key)

            # 使用SMS服务发送验证码
            try:
                result = self.sms_service.send_verification_code(
                    phone=phone,
                    code=code,
                    business_name=settings.TENCENT_CLOUD_SMS_LOGIN,
                )

                if result.success:
                    logging.info(
                        f"✅ 短信验证码发送成功: {phone} (客户端类型: {client_type})"
                    )
                    return code
                else:
                    logging.error(f"❌ 短信验证码发送失败: {result.message}")
                    # 发送失败，删除已存储的验证码
                    self.redis_service.delete_key(code_key)
                    raise Exception(f"短信发送失败: {result.message}")

            except Exception as e:
                logging.error(f"❌ 短信服务异常: {str(e)}")
                # 发送失败，删除已存储的验证码
                self.redis_service.delete_key(code_key)
                raise Exception(f"短信发送异常: {str(e)}")
        else:
            raise Exception("验证码存储失败")

    def verify_code(
        self,
        phone: str,
        code: str,
        purpose: str = SmsCodePurpose.LOGIN,
        client_type: str = "user",
    ) -> bool:
        """
        验证短信验证码（从Redis读取）

        Args:
            phone: 手机号
            code: 验证码
            purpose: 用途
            client_type: 客户端类型，"user"代表C端，"admin"代表B端

        Returns:
            验证是否成功
        """
        code_key = self._get_code_key(phone, purpose, client_type)
        attempts_key = self._get_attempts_key(phone, purpose, client_type)

        # 获取存储的验证码
        stored_code = self.redis_service.get_value(code_key)
        if not stored_code:
            logging.warning(
                f"⚠️ 验证码不存在或已过期: {phone} (客户端类型: {client_type})"
            )
            return False

        # 检查尝试次数
        attempts = self.redis_service.increment_value(
            attempts_key, self.expire_minutes * 60
        )
        if attempts > self.max_attempts:
            # 删除验证码
            self.redis_service.delete_key(code_key)
            self.redis_service.delete_key(attempts_key)
            logging.warning(
                f"⚠️ 验证码尝试次数过多: {phone} (客户端类型: {client_type})"
            )
            return False

        # 验证码匹配
        if stored_code == code:
            # 删除验证码和尝试记录
            self.redis_service.delete_key(code_key)
            self.redis_service.delete_key(attempts_key)
            logging.info(f"✅ 验证码验证成功: {phone} (客户端类型: {client_type})")
            return True

        logging.warning(
            f"⚠️ 验证码错误: {phone} (客户端类型: {client_type}, 尝试次数: {attempts})"
        )
        return False


class SessionService:
    """会话管理服务类"""

    def __init__(self, redis_service: RedisService):
        self.redis_service = redis_service

    def create_session(
        self,
        db: Session,
        user: User,
        access_token: str,
        refresh_token: str,
        ip_address: str = None,
        user_agent: str = None,
    ) -> UserSession:
        """
        创建用户会话

        Args:
            db: 数据库会话
            user: 用户对象
            access_token: 访问令牌
            refresh_token: 刷新令牌
            ip_address: IP地址
            user_agent: 用户代理

        Returns:
            会话对象
        """
        session = UserSession(
            user_id=user.id,
            session_token=access_token,
            refresh_token=refresh_token,
            ip_address=ip_address,
            user_agent=user_agent,
            expires_at=datetime.now(timezone.utc)
            + timedelta(minutes=settings.JWT_ACCESS_TOKEN_EXPIRE_MINUTES),
        )

        db.add(session)
        db.commit()
        db.refresh(session)

        # 存储到Redis缓存
        session_key = f"session:{session.id}"
        session_data = {
            "user_id": user.id,
            "session_token": access_token,
            "ip_address": ip_address,
            "user_agent": user_agent,
        }
        self.redis_service.set_value(
            session_key,
            json.dumps(session_data),
            settings.JWT_ACCESS_TOKEN_EXPIRE_MINUTES * 60,
        )

        return session

    def get_active_sessions(self, db: Session, user_id: int) -> List[UserSession]:
        """
        获取用户活跃会话

        Args:
            db: 数据库会话
            user_id: 用户ID

        Returns:
            活跃会话列表
        """
        return (
            db.query(UserSession)
            .filter(
                and_(
                    UserSession.user_id == user_id,
                    UserSession.is_active.is_(True),
                    UserSession.expires_at > datetime.now(timezone.utc),
                )
            )
            .all()
        )

    def invalidate_session(self, db: Session, session_id: int) -> bool:
        """
        使会话失效

        Args:
            db: 数据库会话
            session_id: 会话ID

        Returns:
            操作是否成功
        """
        session = db.query(UserSession).filter(UserSession.id == session_id).first()
        if session:
            session.is_active = False
            db.commit()

            # 从Redis中删除
            session_key = f"session:{session_id}"
            self.redis_service.delete_key(session_key)
            return True
        return False


class UserService:
    """用户服务类"""

    def __init__(self, db_session: Session = None):
        self.redis_service = RedisService()
        self.password_service = PasswordService()
        self.jwt_service = JWTService()
        # 注意：这里需要传入数据库会话来初始化SMS服务
        if db_session:
            self.sms_service = SmsService(db_session)
            self.sms_code_service = SmsCodeService(self.redis_service, self.sms_service)
        else:
            self.sms_service = None
            self.sms_code_service = None
        self.session_service = SessionService(self.redis_service)

    def get_user_by_phone(self, db: Session, phone: str) -> Optional[User]:
        """
        根据手机号获取用户

        Args:
            db: 数据库会话
            phone: 手机号

        Returns:
            用户对象或None
        """
        return db.query(User).filter(User.phone == phone).first()

    def get_user_by_username(self, db: Session, username: str) -> Optional[User]:
        """
        根据用户名获取用户

        Args:
            db: 数据库会话
            username: 用户名

        Returns:
            用户对象或None
        """
        return db.query(User).filter(User.username == username).first()

    def get_user_by_email(self, db: Session, email: str) -> Optional[User]:
        """
        根据邮箱获取用户

        Args:
            db: 数据库会话
            email: 邮箱

        Returns:
            用户对象或None
        """
        return db.query(User).filter(User.email == email).first()

    def get_user_by_id(self, db: Session, user_id: int) -> Optional[User]:
        """
        根据ID获取用户

        Args:
            db: 数据库会话
            user_id: 用户ID

        Returns:
            用户对象或None
        """
        return db.query(User).filter(User.id == user_id).first()

    def authenticate_with_phone_code(
        self, db: Session, phone: str, verification_code: str
    ) -> Optional[User]:
        """
        使用手机号和验证码进行认证

        Args:
            db: 数据库会话
            phone: 手机号
            verification_code: 验证码

        Returns:
            认证成功的用户对象，失败返回None
        """
        # 验证短信验证码（C端用户登录）
        if not self.sms_code_service or not self.sms_code_service.verify_code(
            phone, verification_code, SmsCodePurpose.LOGIN, "user"
        ):
            return None

        # 查找用户
        user = self.get_user_by_phone(db, phone)

        # 如果用户不存在，创建新用户（首次登录即注册）
        if not user:
            # 直接创建用户，无需密码
            user = User(
                phone=phone,
                user_type=UserType.NOVICE,
                knowledge_level=1,
                is_verified=True,  # 通过短信验证码登录即认为已验证
                first_login_at=datetime.now(timezone.utc),
                last_login_at=datetime.now(timezone.utc),
            )
            db.add(user)
            db.commit()
            db.refresh(user)
        else:
            # 更新最后登录时间
            user.last_login_at = datetime.now(timezone.utc)
            if not user.first_login_at:
                user.first_login_at = datetime.now(timezone.utc)
            db.commit()

        return user

    def send_verification_code(
        self, phone: str, purpose: str = SmsCodePurpose.LOGIN, client_type: str = "user"
    ) -> str:
        """
        发送验证码

        Args:
            phone: 手机号
            purpose: 验证码用途
            client_type: 客户端类型，"user"代表C端，"admin"代表B端

        Returns:
            验证码

        Raises:
            Exception: 发送失败时抛出异常
        """
        if not self.sms_code_service:
            raise Exception("SMS服务未初始化")

        return self.sms_code_service.send_sms_code(phone, purpose, client_type)

    def get_user_roles_and_permissions(self, db: Session, user_id: int) -> tuple:
        """
        获取用户的角色和权限信息

        Args:
            db: 数据库会话
            user_id: 用户ID

        Returns:
            (roles, permissions) 元组
        """
        try:
            from ..permission_service.service import (
                UserRoleService,
                AuthorizationService,
            )
            from ..permission_service.cache import PermissionCacheService

            # 创建权限相关服务
            cache_service = PermissionCacheService(self.redis_service.redis_client)
            user_role_service = UserRoleService(db, cache_service)
            auth_service = AuthorizationService(db, cache_service)

            # 获取用户角色
            roles = user_role_service.get_user_roles(user_id)

            # 获取用户权限
            permissions = auth_service.get_user_permissions(user_id)

            return roles, permissions

        except Exception as e:
            logging.error(f"❌ 获取用户角色权限失败: {str(e)}")
            return [], []

    def create_user_tokens(
        self,
        user: User,
        ip_address: str = None,
        user_agent: str = None,
        db: Session = None,
    ) -> Dict[str, Any]:
        """
        为用户创建JWT令牌

        Args:
            user: 用户对象
            ip_address: IP地址
            user_agent: 用户代理
            db: 数据库会话

        Returns:
            包含访问令牌和刷新令牌的字典
        """
        token_data = {
            "sub": str(user.id),
            "phone": user.phone,
            "user_type": user.user_type,
        }

        access_token = self.jwt_service.create_access_token(token_data)
        refresh_token = self.jwt_service.create_refresh_token(token_data)

        # 创建会话记录
        if db:
            self.session_service.create_session(
                db, user, access_token, refresh_token, ip_address, user_agent
            )

        return {
            "access_token": access_token,
            "refresh_token": refresh_token,
            "token_type": "bearer",
            "expires_in": self.jwt_service.access_token_expire_minutes * 60,
        }

    def refresh_access_token(
        self,
        refresh_token: str,
        db: Session = None,
        ip_address: str = None,
        user_agent: str = None,
    ) -> Optional[Dict[str, Any]]:
        """
        刷新访问令牌

        Args:
            refresh_token: 刷新令牌
            db: 数据库会话
            ip_address: IP地址
            user_agent: 用户代理

        Returns:
            新的访问令牌或None
        """
        payload = self.jwt_service.verify_token(refresh_token)
        if not payload or payload.get("type") != "refresh":
            return None

        user_id = payload.get("sub")
        if not user_id or not db:
            return None

        # 根据refresh token查找对应的session
        session = (
            db.query(UserSession)
            .filter(
                and_(
                    UserSession.refresh_token == refresh_token,
                    UserSession.user_id == int(user_id),
                    UserSession.is_active.is_(True),
                    UserSession.expires_at > datetime.now(timezone.utc),
                )
            )
            .first()
        )

        if not session:
            return None

        # 获取用户信息
        user = self.get_user_by_id(db, int(user_id))
        if not user:
            return None

        # 创建新的访问令牌
        user_data = {
            "sub": user_id,
            "phone": payload["phone"],
            "user_type": payload["user_type"],
        }
        new_access_token = self.jwt_service.create_access_token(user_data)

        # 更新session记录
        session.session_token = new_access_token
        session.last_used_at = datetime.now(timezone.utc)

        # 如果提供了新的IP和用户代理，也更新这些信息
        if ip_address:
            session.ip_address = ip_address
        if user_agent:
            session.user_agent = user_agent

        db.commit()
        db.refresh(session)

        # 更新Redis缓存
        session_key = f"session:{session.id}"
        session_data = {
            "user_id": user.id,
            "session_token": new_access_token,
            "ip_address": session.ip_address,
            "user_agent": session.user_agent,
        }
        self.redis_service.set_value(
            session_key,
            json.dumps(session_data),
            settings.JWT_ACCESS_TOKEN_EXPIRE_MINUTES * 60,
        )

        return {
            "access_token": new_access_token,
            "token_type": "bearer",
            "expires_in": self.jwt_service.access_token_expire_minutes * 60,
        }

    def get_current_user(self, db: Session, token: str) -> Optional[User]:
        """
        根据JWT令牌获取当前用户

        Args:
            db: 数据库会话
            token: JWT令牌

        Returns:
            当前用户对象
        """
        payload = self.jwt_service.verify_token(token)
        if not payload:
            return None

        user_id = payload.get("sub")
        if not user_id:
            return None

        return self.get_user_by_id(db, int(user_id))

    def update_user_profile(
        self, db: Session, user: User, update_data: Dict[str, Any]
    ) -> User:
        """
        更新用户信息

        Args:
            db: 数据库会话
            user: 用户对象
            update_data: 更新数据

        Returns:
            更新后的用户对象
        """
        # 更新用户信息
        for field, value in update_data.items():
            if hasattr(user, field):
                setattr(user, field, value)

        # 移除手动设置updated_at，让数据库自动处理
        # user.updated_at = datetime.now(timezone.utc)

        try:
            db.commit()
            db.refresh(user)
            return user
        except Exception as e:
            db.rollback()
            raise ValueError(f"Failed to update user: {str(e)}")

    # 用户标签管理方法
    def create_user_tag(
        self, db: Session, user_id: int, tag_data: UserTagCreate
    ) -> UserTag:
        """
        创建用户标签

        Args:
            db: 数据库会话
            user_id: 用户ID
            tag_data: 标签数据

        Returns:
            创建的标签对象
        """
        # 检查是否已存在相同的标签
        existing_tag = (
            db.query(UserTag)
            .filter(
                and_(
                    UserTag.user_id == user_id,
                    UserTag.tag_category == tag_data.tag_category,
                    UserTag.tag_name == tag_data.tag_name,
                )
            )
            .first()
        )

        if existing_tag:
            raise ValueError("Tag already exists")

        tag = UserTag(
            user_id=user_id,
            tag_category=tag_data.tag_category,
            tag_name=tag_data.tag_name,
            tag_value=tag_data.tag_value,
            weight=tag_data.weight,
        )

        db.add(tag)
        db.commit()
        db.refresh(tag)

        return tag

    def get_user_tags(
        self, db: Session, user_id: int, category: str = None
    ) -> List[UserTag]:
        """
        获取用户标签

        Args:
            db: 数据库会话
            user_id: 用户ID
            category: 标签分类（可选）

        Returns:
            标签列表
        """
        query = db.query(UserTag).filter(UserTag.user_id == user_id)

        if category:
            query = query.filter(UserTag.tag_category == category)

        return query.order_by(UserTag.weight.desc(), UserTag.created_at.desc()).all()

    def update_user_tag(
        self, db: Session, tag_id: int, user_id: int, update_data: Dict[str, Any]
    ) -> Optional[UserTag]:
        """
        更新用户标签

        Args:
            db: 数据库会话
            tag_id: 标签ID
            user_id: 用户ID
            update_data: 更新数据

        Returns:
            更新后的标签对象或None
        """
        tag = (
            db.query(UserTag)
            .filter(and_(UserTag.id == tag_id, UserTag.user_id == user_id))
            .first()
        )

        if not tag:
            return None

        for field, value in update_data.items():
            if hasattr(tag, field) and value is not None:
                setattr(tag, field, value)

        db.commit()
        db.refresh(tag)

        return tag

    def delete_user_tag(self, db: Session, tag_id: int, user_id: int) -> bool:
        """
        删除用户标签

        Args:
            db: 数据库会话
            tag_id: 标签ID
            user_id: 用户ID

        Returns:
            操作是否成功
        """
        tag = (
            db.query(UserTag)
            .filter(and_(UserTag.id == tag_id, UserTag.user_id == user_id))
            .first()
        )

        if tag:
            db.delete(tag)
            db.commit()
            return True
        return False

    # 用户行为追踪方法
    def track_user_behavior(
        self,
        db: Session,
        user_id: int,
        behavior_data: UserBehaviorCreate,
        ip_address: str = None,
        user_agent: str = None,
    ) -> UserBehavior:
        """
        追踪用户行为

        Args:
            db: 数据库会话
            user_id: 用户ID
            behavior_data: 行为数据
            ip_address: IP地址
            user_agent: 用户代理

        Returns:
            行为记录对象
        """
        behavior = UserBehavior(
            user_id=user_id,
            action_type=behavior_data.action_type,
            action_target=behavior_data.action_target,
            action_details=(
                json.dumps(behavior_data.action_details)
                if behavior_data.action_details
                else None
            ),
            ip_address=ip_address,
            user_agent=user_agent,
        )

        db.add(behavior)
        db.commit()
        db.refresh(behavior)

        return behavior

    def get_user_behaviors(
        self, db: Session, user_id: int, action_type: str = None, limit: int = 100
    ) -> List[UserBehavior]:
        """
        获取用户行为记录

        Args:
            db: 数据库会话
            user_id: 用户ID
            action_type: 行为类型（可选）
            limit: 限制数量

        Returns:
            行为记录列表
        """
        query = db.query(UserBehavior).filter(UserBehavior.user_id == user_id)

        if action_type:
            query = query.filter(UserBehavior.action_type == action_type)

        return query.order_by(UserBehavior.created_at.desc()).limit(limit).all()

    def get_user_analytics(self, db: Session, user_id: int) -> Dict[str, Any]:
        """
        获取用户分析数据

        Args:
            db: 数据库会话
            user_id: 用户ID

        Returns:
            用户分析数据
        """
        # 行为统计
        behavior_stats = (
            db.query(
                UserBehavior.action_type, func.count(UserBehavior.id).label("count")
            )
            .filter(UserBehavior.user_id == user_id)
            .group_by(UserBehavior.action_type)
            .all()
        )

        behavior_types = {stat.action_type: stat.count for stat in behavior_stats}
        total_behaviors = sum(behavior_types.values())

        # 活跃会话数
        active_sessions = len(self.session_service.get_active_sessions(db, user_id))

        # 标签数量
        tags_count = db.query(UserTag).filter(UserTag.user_id == user_id).count()

        # 最后活动时间
        last_behavior = (
            db.query(UserBehavior)
            .filter(UserBehavior.user_id == user_id)
            .order_by(UserBehavior.created_at.desc())
            .first()
        )

        last_activity = last_behavior.created_at if last_behavior else None

        # 用户评分（简单算法）
        user_score = min(
            100.0, (total_behaviors * 0.1 + tags_count * 0.5 + active_sessions * 2.0)
        )

        return {
            "user_id": user_id,
            "total_behaviors": total_behaviors,
            "behavior_types": behavior_types,
            "active_sessions": active_sessions,
            "tags_count": tags_count,
            "last_activity": last_activity,
            "user_score": user_score,
        }

    # B端管理接口相关方法
    def get_users_paginated(
        self,
        db: Session,
        skip: int = 0,
        limit: int = 100,
        search: str = None,
        **filters,
    ) -> tuple[List[User], int]:
        """
        分页获取用户列表

        Args:
            db: 数据库会话
            skip: 跳过的记录数
            limit: 限制的记录数
            search: 搜索关键词（手机号/用户名）
            **filters: 其他过滤条件

        Returns:
            用户列表和总数的元组
        """
        query = db.query(User)

        # 应用搜索条件
        if search:
            query = query.filter(
                (User.phone.like(f"%{search}%")) | (User.username.like(f"%{search}%"))
            )

        # 应用其他过滤条件
        for field, value in filters.items():
            if hasattr(User, field) and value is not None:
                query = query.filter(getattr(User, field) == value)

        # 获取总数
        total = query.count()

        # 获取分页数据
        users = query.order_by(User.created_at.desc()).offset(skip).limit(limit).all()

        return users, total

    def create_user(self, db: Session, user_data) -> User:
        """
        创建用户

        Args:
            db: 数据库会话
            user_data: 用户创建数据（UserCreate schema）

        Returns:
            创建的用户对象

        Raises:
            ValueError: 当手机号已存在时抛出
        """
        # 检查手机号是否已存在
        existing_user = self.get_user_by_phone(db, user_data.phone)
        if existing_user:
            raise ValueError(f"Phone number {user_data.phone} already exists")


        # 检查邮箱是否已存在（如果提供了邮箱）
        if user_data.email:
            existing_user = self.get_user_by_email(db, user_data.email)
            if existing_user:
                raise ValueError(f"Email {user_data.email} already exists")

        # 创建用户对象
        user = User(
            phone=user_data.phone,
            username=user_data.username,
            email=user_data.email,
            user_type=user_data.user_type,
            risk_level=user_data.risk_level,
            knowledge_level=user_data.knowledge_level,
            is_active=user_data.is_active,
            is_verified=user_data.is_verified,
            is_admin=user_data.is_admin,
            created_at=datetime.now(timezone.utc),
            updated_at=datetime.now(timezone.utc),
        )

        db.add(user)
        db.commit()
        db.refresh(user)

        return user

    def update_user(self, db: Session, user: User, update_data: Dict[str, Any]) -> User:
        """
        更新用户信息

        Args:
            db: 数据库会话
            user: 要更新的用户对象
            update_data: 更新数据字典

        Returns:
            更新后的用户对象

        Raises:
            ValueError: 当更新的手机号/用户名/邮箱已存在时抛出
        """
        # 检查手机号是否已被其他用户使用
        if "phone" in update_data and update_data["phone"] != user.phone:
            existing_user = self.get_user_by_phone(db, update_data["phone"])
            if existing_user and existing_user.id != user.id:
                raise ValueError(f"Phone number {update_data['phone']} already exists")


        # 检查邮箱是否已被其他用户使用
        if "email" in update_data and update_data["email"] != user.email:
            existing_user = self.get_user_by_email(db, update_data["email"])
            if existing_user and existing_user.id != user.id:
                raise ValueError(f"Email {update_data['email']} already exists")

        # 更新用户字段
        for field, value in update_data.items():
            if hasattr(user, field) and value is not None:
                setattr(user, field, value)

        # 更新时间戳
        user.updated_at = datetime.now(timezone.utc)

        db.commit()
        db.refresh(user)

        return user
        
    def delete_user(self, db: Session, user_id: int) -> bool:
        """
        删除用户

        Args:
            db: 数据库会话
            user_id: 用户ID

        Returns:
            是否删除成功

        Raises:
            ValueError: 当用户是管理员时抛出异常，防止误删管理员账户
        """
        user = self.get_user_by_id(db, user_id)
        if not user:
            return False

        # 防止删除管理员账户
        if user.is_admin:
            raise ValueError("Cannot delete admin user")

        try:
            # 删除用户相关的会话记录
            db.query(UserSession).filter(UserSession.user_id == user_id).delete()
            
            # 删除用户相关的行为记录
            db.query(UserBehavior).filter(UserBehavior.user_id == user_id).delete()
            
            # 删除用户相关的标签
            db.query(UserTag).filter(UserTag.user_id == user_id).delete()
            
            # 删除用户
            db.delete(user)
            db.commit()
            
            # 清除Redis中的相关缓存
            self.redis_service.delete_key(f"user:{user_id}")
            
            return True
        except Exception as e:
            db.rollback()
            logging.error(f"删除用户失败: {str(e)}")
            raise e


# 全局服务实例
user_service = UserService()
