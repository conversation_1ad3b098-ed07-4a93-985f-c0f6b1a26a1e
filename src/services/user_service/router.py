"""
用户服务API路由
定义用户相关的HTTP接口
"""

from typing import Annotated, List, Optional

from fastapi import APIRouter, Depends, HTTPException, Request, status
from sqlalchemy.orm import Session

from ...core.database import get_db
from .dependencies import get_current_active_user, get_current_user
from .models import User
from .schemas import (PhoneLoginRequest, RefreshTokenRequest,
                      SendSmsCodeRequest, SendSmsCodeResponse, TokenResponse,
                      UserAnalyticsResponse, UserBehaviorCreate,
                      UserBehaviorResponse, UserProfileUpdate, UserResponse,
                      UserSessionResponse, UserTagCreate, UserTagResponse,
                      UserTagUpdate)
from .service import UserService

router = APIRouter()


def get_client_info(request: Request) -> tuple[str, str]:
    """
    获取客户端信息

    Args:
        request: FastAPI请求对象

    Returns:
        IP地址和用户代理的元组
    """
    ip_address = request.client.host if request.client else None
    user_agent = request.headers.get("User-Agent", "")
    return ip_address, user_agent


@router.post("/send-sms-code", response_model=SendSmsCodeResponse)
async def send_sms_verification_code(
    request: SendSmsCodeRequest, db: Annotated[Session, Depends(get_db)]
):
    """
    发送短信验证码

    Args:
        request: 发送验证码请求
        db: 数据库会话

    Returns:
        发送结果响应
    """
    try:
        # 创建带有数据库会话的用户服务实例
        user_service_instance = UserService(db_session=db)

        # 发送短信验证码（C端用户）
        code = user_service_instance.send_verification_code(
            request.phone, request.purpose, "user"
        )

        return SendSmsCodeResponse(
            message="Verification code sent successfully", expires_in=300
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to send SMS: {str(e)}",
        )


@router.post("/phone-login", response_model=TokenResponse)
async def phone_login(
    login_data: PhoneLoginRequest,
    request: Request,
    db: Annotated[Session, Depends(get_db)],
):
    """
    手机号+验证码登录（首次登录即注册）

    Args:
        login_data: 登录信息
        request: 请求对象
        db: 数据库会话

    Returns:
        访问令牌和用户信息
    """
    try:
        # 创建带有数据库会话的用户服务实例
        user_service_instance = UserService(db_session=db)

        # 使用手机号和验证码进行认证
        user = user_service_instance.authenticate_with_phone_code(
            db, login_data.phone, login_data.verification_code
        )

        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid phone number or verification code",
            )

        # 获取客户端信息
        ip_address, user_agent = get_client_info(request)

        # 创建JWT令牌
        tokens = user_service_instance.create_user_tokens(
            user, ip_address, user_agent, db
        )

        # 记录登录行为
        user_service_instance.track_user_behavior(
            db,
            user.id,
            UserBehaviorCreate(action_type="login", action_target="phone_login"),
            ip_address,
            user_agent,
        )

        return TokenResponse(
            access_token=tokens["access_token"],
            refresh_token=tokens["refresh_token"],
            token_type=tokens["token_type"],
            expires_in=tokens["expires_in"],
            user=UserResponse.model_validate(user),
        )
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Login failed: {str(e)}",
        )


@router.post("/refresh-token", response_model=dict)
async def refresh_token(
    refresh_data: RefreshTokenRequest,
    request: Request,
    db: Annotated[Session, Depends(get_db)],
):
    """
    刷新访问令牌

    Args:
        refresh_data: 刷新令牌请求
        request: 请求对象
        db: 数据库会话

    Returns:
        新的访问令牌
    """
    try:
        # 创建带有数据库会话的用户服务实例
        user_service_instance = UserService(db_session=db)

        # 获取客户端信息
        ip_address, user_agent = get_client_info(request)

        tokens = user_service_instance.refresh_access_token(
            refresh_data.refresh_token, db, ip_address, user_agent
        )

        if not tokens:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid refresh token",
            )

        return tokens
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Token refresh failed: {str(e)}",
        )


@router.get("/profile", response_model=UserResponse)
async def get_user_profile(
    current_user: Annotated[User, Depends(get_current_active_user)]
):
    """
    获取用户资料

    Args:
        current_user: 当前用户

    Returns:
        用户资料
    """
    return UserResponse.model_validate(current_user)


@router.get("/me", response_model=UserResponse)
async def get_current_user_info(
    current_user: Annotated[User, Depends(get_current_user)]
):
    """
    获取当前用户信息

    Args:
        current_user: 当前用户

    Returns:
        当前用户信息
    """
    return UserResponse.model_validate(current_user)


@router.put("/profile", response_model=UserResponse)
async def update_user_profile(
    user_data: UserProfileUpdate,
    current_user: Annotated[User, Depends(get_current_active_user)],
    request: Request,
    db: Annotated[Session, Depends(get_db)],
):
    """
    更新用户资料

    Args:
        user_data: 用户更新数据
        current_user: 当前用户
        request: 请求对象
        db: 数据库会话

    Returns:
        更新后的用户信息
    """
    try:
        # 创建带有数据库会话的用户服务实例
        user_service_instance = UserService(db_session=db)

        # 获取客户端信息
        ip_address, user_agent = get_client_info(request)

        # 更新用户资料
        updated_user = user_service_instance.update_user_profile(
            db, current_user, user_data.model_dump(exclude_unset=True)
        )

        # 记录更新行为
        user_service_instance.track_user_behavior(
            db,
            current_user.id,
            UserBehaviorCreate(action_type="update", action_target="user_profile"),
            ip_address,
            user_agent,
        )

        return UserResponse.model_validate(updated_user)
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Profile update failed: {str(e)}",
        )


@router.post(
    "/tags", response_model=UserTagResponse, status_code=status.HTTP_201_CREATED
)
async def create_user_tag(
    tag_data: UserTagCreate,
    current_user: Annotated[User, Depends(get_current_active_user)],
    request: Request,
    db: Annotated[Session, Depends(get_db)],
):
    """
    创建用户标签

    Args:
        tag_data: 标签数据
        current_user: 当前用户
        request: 请求对象
        db: 数据库会话

    Returns:
        创建的标签信息
    """
    try:
        # 创建带有数据库会话的用户服务实例
        user_service_instance = UserService(db_session=db)

        # 获取客户端信息
        ip_address, user_agent = get_client_info(request)

        tag = user_service_instance.create_user_tag(db, current_user.id, tag_data)

        # 记录创建标签行为
        user_service_instance.track_user_behavior(
            db,
            current_user.id,
            UserBehaviorCreate(action_type="create", action_target="user_tag"),
            ip_address,
            user_agent,
        )

        return UserTagResponse.model_validate(tag)
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Tag creation failed: {str(e)}",
        )


@router.get("/tags", response_model=List[UserTagResponse])
async def get_user_tags(
    current_user: Annotated[User, Depends(get_current_active_user)],
    db: Annotated[Session, Depends(get_db)],
    category: Optional[str] = None,
):
    """
    获取用户标签列表

    Args:
        current_user: 当前用户
        db: 数据库会话
        category: 标签分类（可选）

    Returns:
        用户标签列表
    """
    try:
        # 创建带有数据库会话的用户服务实例
        user_service_instance = UserService(db_session=db)

        tags = user_service_instance.get_user_tags(db, current_user.id, category)
        return [UserTagResponse.model_validate(tag) for tag in tags]
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get tags: {str(e)}",
        )


@router.put("/tags/{tag_id}", response_model=UserTagResponse)
async def update_user_tag(
    tag_id: int,
    tag_data: UserTagUpdate,
    current_user: Annotated[User, Depends(get_current_active_user)],
    request: Request,
    db: Annotated[Session, Depends(get_db)],
):
    """
    更新用户标签

    Args:
        tag_id: 标签ID
        tag_data: 标签更新数据
        current_user: 当前用户
        request: 请求对象
        db: 数据库会话

    Returns:
        更新后的标签信息
    """
    try:
        # 创建带有数据库会话的用户服务实例
        user_service_instance = UserService(db_session=db)

        # 获取客户端信息
        ip_address, user_agent = get_client_info(request)

        tag = user_service_instance.update_user_tag(
            db, tag_id, current_user.id, tag_data.model_dump(exclude_unset=True)
        )

        if not tag:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Tag not found"
            )

        # 记录更新标签行为
        user_service_instance.track_user_behavior(
            db,
            current_user.id,
            UserBehaviorCreate(action_type="update", action_target="user_tag"),
            ip_address,
            user_agent,
        )

        return UserTagResponse.model_validate(tag)
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Tag update failed: {str(e)}",
        )


@router.delete("/tags/{tag_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_user_tag(
    tag_id: int,
    current_user: Annotated[User, Depends(get_current_active_user)],
    request: Request,
    db: Annotated[Session, Depends(get_db)],
):
    """
    删除用户标签

    Args:
        tag_id: 标签ID
        current_user: 当前用户
        request: 请求对象
        db: 数据库会话
    """
    try:
        # 创建带有数据库会话的用户服务实例
        user_service_instance = UserService(db_session=db)

        # 获取客户端信息
        ip_address, user_agent = get_client_info(request)

        success = user_service_instance.delete_user_tag(db, tag_id, current_user.id)

        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Tag not found"
            )

        # 记录删除标签行为
        user_service_instance.track_user_behavior(
            db,
            current_user.id,
            UserBehaviorCreate(action_type="delete", action_target="user_tag"),
            ip_address,
            user_agent,
        )

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Tag deletion failed: {str(e)}",
        )


@router.post(
    "/behaviors",
    response_model=UserBehaviorResponse,
    status_code=status.HTTP_201_CREATED,
)
async def track_behavior(
    behavior_data: UserBehaviorCreate,
    current_user: Annotated[User, Depends(get_current_active_user)],
    request: Request,
    db: Annotated[Session, Depends(get_db)],
):
    """
    记录用户行为

    Args:
        behavior_data: 行为数据
        current_user: 当前用户
        request: 请求对象
        db: 数据库会话

    Returns:
        记录的行为信息
    """
    try:
        # 创建带有数据库会话的用户服务实例
        user_service_instance = UserService(db_session=db)

        # 获取客户端信息
        ip_address, user_agent = get_client_info(request)

        behavior = user_service_instance.track_user_behavior(
            db, current_user.id, behavior_data, ip_address, user_agent
        )

        return UserBehaviorResponse.model_validate(behavior)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Behavior tracking failed: {str(e)}",
        )


@router.get("/behaviors", response_model=List[UserBehaviorResponse])
async def get_user_behaviors(
    current_user: Annotated[User, Depends(get_current_active_user)],
    db: Annotated[Session, Depends(get_db)],
    action_type: Optional[str] = None,
    limit: int = 100,
):
    """
    获取用户行为列表

    Args:
        current_user: 当前用户
        db: 数据库会话
        action_type: 行为类型（可选）
        limit: 返回记录数限制

    Returns:
        用户行为列表
    """
    try:
        # 创建带有数据库会话的用户服务实例
        user_service_instance = UserService(db_session=db)

        behaviors = user_service_instance.get_user_behaviors(
            db, current_user.id, action_type, limit
        )
        return [UserBehaviorResponse.model_validate(behavior) for behavior in behaviors]
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get behaviors: {str(e)}",
        )


@router.get("/sessions", response_model=List[UserSessionResponse])
async def get_active_sessions(
    current_user: Annotated[User, Depends(get_current_active_user)],
    db: Annotated[Session, Depends(get_db)],
):
    """
    获取活跃会话列表

    Args:
        current_user: 当前用户
        db: 数据库会话

    Returns:
        活跃会话列表
    """
    try:
        # 创建带有数据库会话的用户服务实例
        user_service_instance = UserService(db_session=db)

        sessions = user_service_instance.session_service.get_active_sessions(
            db, current_user.id
        )
        return [UserSessionResponse.model_validate(session) for session in sessions]
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get sessions: {str(e)}",
        )


@router.delete("/sessions/{session_id}", status_code=status.HTTP_204_NO_CONTENT)
async def invalidate_session(
    session_id: int,
    current_user: Annotated[User, Depends(get_current_active_user)],
    db: Annotated[Session, Depends(get_db)],
):
    """
    使会话失效

    Args:
        session_id: 会话ID
        current_user: 当前用户
        db: 数据库会话
    """
    try:
        # 创建带有数据库会话的用户服务实例
        user_service_instance = UserService(db_session=db)

        success = user_service_instance.session_service.invalidate_session(
            db, session_id
        )

        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Session not found"
            )

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Session invalidation failed: {str(e)}",
        )


@router.get("/analytics", response_model=UserAnalyticsResponse)
async def get_user_analytics(
    current_user: Annotated[User, Depends(get_current_active_user)],
    db: Annotated[Session, Depends(get_db)],
):
    """
    获取用户分析数据

    Args:
        current_user: 当前用户
        db: 数据库会话

    Returns:
        用户分析数据
    """
    try:
        # 创建带有数据库会话的用户服务实例
        user_service_instance = UserService(db_session=db)

        analytics = user_service_instance.get_user_analytics(db, current_user.id)
        return UserAnalyticsResponse(**analytics)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get analytics: {str(e)}",
        )


@router.post("/logout", status_code=status.HTTP_204_NO_CONTENT)
async def logout_user(
    current_user: Annotated[User, Depends(get_current_user)],
    request: Request,
    db: Annotated[Session, Depends(get_db)],
):
    """
    用户登出

    Args:
        current_user: 当前用户
        request: 请求对象
        db: 数据库会话
    """
    try:
        # 创建带有数据库会话的用户服务实例
        user_service_instance = UserService(db_session=db)

        # 获取客户端信息
        ip_address, user_agent = get_client_info(request)

        # 记录登出行为
        user_service_instance.track_user_behavior(
            db,
            current_user.id,
            UserBehaviorCreate(action_type="logout", action_target="user_logout"),
            ip_address,
            user_agent,
        )

        # 注意：这里可能需要实现令牌黑名单功能来真正使令牌失效
        # 目前只记录行为，实际的令牌失效可以通过Redis黑名单实现

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Logout failed: {str(e)}",
        )
