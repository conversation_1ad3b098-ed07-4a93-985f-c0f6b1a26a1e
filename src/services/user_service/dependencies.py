"""
用户服务依赖注入
定义数据库连接、用户认证等依赖
"""

from typing import Annotated

from fastapi import Depends, HTTPException, status
from fastapi.security import HTTPAuthorizationCredentials, HTTPBearer
from sqlalchemy.orm import Session

from ...core.database import get_db
from .models import User
from .service import UserService

# HTTP Bearer认证
security = HTTPBearer()


async def get_current_user(
    credentials: Annotated[HTTPAuthorizationCredentials, Depends(security)],
    db: Annotated[Session, Depends(get_db)],
) -> User:
    """
    获取当前认证用户

    Args:
        credentials: HTTP Bearer认证凭据
        db: 数据库会话

    Returns:
        当前用户对象

    Raises:
        HTTPException: 认证失败时抛出401异常
    """
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )

    try:
        # 创建带有数据库会话的用户服务实例
        user_service_instance = UserService(db_session=db)

        # 验证JWT令牌
        user = user_service_instance.get_current_user(db, credentials.credentials)
        if user is None:
            raise credentials_exception

        # 检查用户状态
        if not user.is_active:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN, detail="User account is disabled"
            )

        return user
    except Exception:
        raise credentials_exception


async def get_current_active_user(
    current_user: Annotated[User, Depends(get_current_user)]
) -> User:
    """
    获取当前活跃用户（已验证且活跃）

    Args:
        current_user: 当前用户

    Returns:
        当前活跃用户对象

    Raises:
        HTTPException: 用户未验证时抛出403异常
    """
    if not current_user.is_verified:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, detail="User account is not verified"
        )
    return current_user


async def get_current_admin(
    credentials: Annotated[HTTPAuthorizationCredentials, Depends(security)],
    db: Annotated[Session, Depends(get_db)],
) -> User:
    """
    获取当前管理员用户

    Args:
        credentials: HTTP Bearer认证凭据
        db: 数据库会话

    Returns:
        当前管理员用户对象

    Raises:
        HTTPException: 认证失败或非管理员时抛出异常
    """
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )

    try:
        # 创建带有数据库会话的用户服务实例
        user_service_instance = UserService(db_session=db)

        # 验证JWT令牌
        user = user_service_instance.get_current_user(db, credentials.credentials)
        if user is None:
            raise credentials_exception

        # 检查用户状态
        if not user.is_active:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="User account is disabled"
            )

        # 检查管理员权限
        if not user.is_admin:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Not authorized for admin access"
            )

        return user
    except Exception:
        raise credentials_exception
