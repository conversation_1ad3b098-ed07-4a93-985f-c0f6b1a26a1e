"""
用户管理B端API路由
定义用户管理相关的HTTP接口
"""

from typing import Annotated, Optional

from fastapi import APIRouter, Depends, HTTPException, Request, Query, status
from sqlalchemy.orm import Session

from ...core.database import get_db
from ..permission_service.dependencies import require_permission
from .models import User
from .schemas import (
    AdminLoginRequest,
    AdminLoginResponse,
    AdminSendSmsRequest,
    AdminSendSmsResponse,
    AdminUserResponse,
    PermissionInfo,
    RoleInfo,
    SmsCodePurpose,
    UserBehaviorCreate,
    UserCreate,
    UserResponse,
    UserStatusUpdate,
    UserUpdate,
)
from .service import UserService

router = APIRouter()


def get_client_info(request: Request) -> tuple[str, str]:
    """
    获取客户端信息

    Args:
        request: FastAPI请求对象

    Returns:
        IP地址和用户代理的元组
    """
    ip_address = request.client.host if request.client else None
    user_agent = request.headers.get("User-Agent", "")
    return ip_address, user_agent


@router.post(
    "/auth/send-code",
    response_model=AdminSendSmsResponse,
    summary="发送管理员登录验证码",
    description="发送管理员登录的短信验证码",
)
async def send_admin_verification_code(
    request: Request,
    sms_request: AdminSendSmsRequest,
    db: Annotated[Session, Depends(get_db)],
) -> AdminSendSmsResponse:
    """
    发送管理员登录验证码

    Args:
        request: HTTP请求对象
        sms_request: 发送验证码请求
        db: 数据库会话

    Returns:
        发送验证码响应

    Raises:
        HTTPException: 用户不存在或不是管理员时抛出异常
    """
    user_service = UserService(db_session=db)

    # 检查用户是否存在且是管理员
    user = user_service.get_user_by_phone(db, sms_request.phone)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="User not found"
        )

    if not user.is_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="User is not authorized for admin access",
        )

    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, detail="User account is disabled"
        )

    # 发送验证码（B端管理员）
    user_service.send_verification_code(
        phone=sms_request.phone, purpose=sms_request.purpose, client_type="admin"
    )

    return AdminSendSmsResponse(message="Verification code sent successfully")


@router.post(
    "/auth/login",
    response_model=AdminLoginResponse,
    summary="管理员登录",
    description="管理员使用手机号和验证码登录",
)
async def admin_login(
    request: Request,
    login_request: AdminLoginRequest,
    db: Annotated[Session, Depends(get_db)],
) -> AdminLoginResponse:
    """
    管理员登录

    Args:
        request: HTTP请求对象
        login_request: 登录请求
        db: 数据库会话

    Returns:
        登录响应，包含访问令牌和用户信息

    Raises:
        HTTPException: 验证失败或非管理员时抛出异常
    """
    user_service = UserService(db_session=db)

    # 检查用户是否存在且是管理员
    user = user_service.get_user_by_phone(db, login_request.phone)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="User not found"
        )

    if not user.is_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="User is not authorized for admin access",
        )

    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, detail="User account is disabled"
        )

    # 验证验证码（B端管理员）
    if not user_service.sms_code_service.verify_code(
        phone=login_request.phone,
        code=login_request.verification_code,
        purpose=SmsCodePurpose.LOGIN,
        client_type="admin",
    ):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid verification code"
        )

    # 生成访问令牌
    ip_address, user_agent = get_client_info(request)
    tokens = user_service.create_user_tokens(
        user=user, ip_address=ip_address, user_agent=user_agent, db=db
    )

    # 获取用户角色和权限信息
    roles, permissions = user_service.get_user_roles_and_permissions(db, user.id)

    # 记录登录行为
    user_service.track_user_behavior(
        db,
        user.id,
        UserBehaviorCreate(
            action_type="admin_login",
            action_target="admin_panel",
            action_details={"login_method": "phone_verification"},
        ),
        ip_address,
        user_agent,
    )

    # 构建管理员用户响应
    admin_user = AdminUserResponse(
        id=user.id,
        phone=user.phone,
        username=user.username,
        email=user.email,
        user_type=user.user_type,
        risk_level=user.risk_level,
        knowledge_level=user.knowledge_level,
        is_active=user.is_active,
        is_verified=user.is_verified,
        is_admin=user.is_admin,
        first_login_at=user.first_login_at,
        last_login_at=user.last_login_at,
        created_at=user.created_at,
        updated_at=user.updated_at,
        roles=[
            RoleInfo(id=role.id, name=role.name, description=role.description)
            for role in roles
        ],
        permissions=[
            PermissionInfo(
                id=perm.id,
                code=perm.code,
                name=perm.name,
                module=perm.module,
                resource=perm.resource,
                action=perm.action,
            )
            for perm in permissions
        ],
    )

    return AdminLoginResponse(
        access_token=tokens["access_token"],
        token_type=tokens["token_type"],
        expires_in=tokens["expires_in"],
        user=admin_user,
    )


@router.get(
    "/", summary="获取用户列表", description="分页获取系统中的用户列表，支持筛选"
)
async def get_users(
    current_user: Annotated[User, Depends(require_permission("user.list.read"))],
    db: Annotated[Session, Depends(get_db)],
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(10, ge=1, le=100, description="每页数量"),
    user_type: Optional[int] = Query(None, description="用户类型过滤"),
    is_active: Optional[bool] = Query(None, description="是否激活过滤"),
    is_verified: Optional[bool] = Query(None, description="是否验证过滤"),
    search: Optional[str] = Query(None, description="搜索关键词（手机号/用户名）"),
):
    """
    获取用户列表

    Args:
        current_user: 当前用户（需要有user.list.read权限）
        db: 数据库会话
        page: 页码
        size: 每页数量
        user_type: 用户类型过滤
        is_active: 是否激活过滤
        is_verified: 是否验证过滤
        search: 搜索关键词

    Returns:
        分页用户列表
    """
    try:
        user_service = UserService(db_session=db)

        # 计算偏移量
        skip = (page - 1) * size

        # 构建查询条件
        filters = {}
        if user_type is not None:
            filters["user_type"] = user_type
        if is_active is not None:
            filters["is_active"] = is_active
        if is_verified is not None:
            filters["is_verified"] = is_verified

        # 获取用户列表和总数
        users, total = user_service.get_users_paginated(
            db, skip=skip, limit=size, search=search, **filters
        )

        return {
            "users": [UserResponse.model_validate(user) for user in users],
            "total": total,
            "page": page,
            "size": size,
            "pages": (total + size - 1) // size,
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get users: {str(e)}",
        )


@router.get(
    "/{user_id}",
    response_model=UserResponse,
    summary="获取用户详情",
    description="根据用户ID获取用户详细信息",
)
async def get_user_detail(
    user_id: int,
    current_user: Annotated[User, Depends(require_permission("user.profile.read"))],
    db: Annotated[Session, Depends(get_db)],
):
    """
    获取用户详情

    Args:
        user_id: 用户ID     
        current_user: 当前用户（需要有user.profile.read权限）
        db: 数据库会话

    Returns:
        用户详情
    """
    try:
        user_service = UserService(db_session=db)

        user = user_service.get_user_by_id(db, user_id)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="User not found"
            )

        return UserResponse.model_validate(user)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get user: {str(e)}",
        )


@router.post(
    "/",
    response_model=UserResponse,
    status_code=status.HTTP_201_CREATED,
    summary="创建用户",
    description="创建新用户",
)
async def create_user(
    user_data: UserCreate,
    current_user: Annotated[User, Depends(require_permission("user.user.create"))],
    request: Request,
    db: Annotated[Session, Depends(get_db)],
):
    """
    创建用户

    Args:
        user_data: 用户创建数据
        current_user: 当前用户（需要有user.create权限）
        request: 请求对象
        db: 数据库会话

    Returns:
        创建的用户信息
    """
    try:
        user_service = UserService(db_session=db)
        ip_address, user_agent = get_client_info(request)

        # 创建用户
        user = user_service.create_user(db, user_data)

        # 记录创建用户行为
        user_service.track_user_behavior(
            db,
            current_user.id,
            UserBehaviorCreate(
                action_type="create_user",
                action_target="user",
                action_details={
                    "created_user_id": user.id,
                    "created_phone": user.phone,
                },
            ),
            ip_address,
            user_agent,
        )

        return UserResponse.model_validate(user)
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"User creation failed: {str(e)}",
        )


@router.put(
    "/{user_id}",
    response_model=UserResponse,
    summary="更新用户信息",
    description="更新指定用户的信息",
)
async def update_user(
    user_id: int,
    user_data: UserUpdate,
    current_user: Annotated[User, Depends(require_permission("user.user.update"))],
    request: Request,
    db: Annotated[Session, Depends(get_db)],
):
    """
    更新用户信息

    Args:
        user_id: 用户ID
        user_data: 用户更新数据
        current_user: 当前用户（需要有user.update权限）
        request: 请求对象
        db: 数据库会话

    Returns:
        更新后的用户信息
    """
    try:
        user_service = UserService(db_session=db)
        ip_address, user_agent = get_client_info(request)

        # 获取要更新的用户
        user = user_service.get_user_by_id(db, user_id)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="User not found"
            )

        # 更新用户信息
        updated_user = user_service.update_user(
            db, user, user_data.model_dump(exclude_unset=True)
        )

        # 记录更新用户行为
        user_service.track_user_behavior(
            db,
            current_user.id,
            UserBehaviorCreate(
                action_type="update_user_status",
                action_target="user",
                action_details={"updated_user_id": user_id},
            ),
            ip_address,
            user_agent,
        )

        return UserResponse.model_validate(updated_user)
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"User update failed: {str(e)}",
        )


@router.put(
    "/{user_id}/status",
    response_model=UserResponse,
    summary="更新用户状态",
    description="更新用户的激活状态",
)
async def update_user_status(
    user_id: int,
    status_data: UserStatusUpdate,
    current_user: Annotated[User, Depends(require_permission("user.status.update"))],
    request: Request,
    db: Annotated[Session, Depends(get_db)],
):
    """
    更新用户状态

    Args:
        user_id: 用户ID
        status_data: 状态更新数据
        current_user: 当前用户（需要有user.status.update权限）
        request: 请求对象
        db: 数据库会话

    Returns:
        更新后的用户信息
    """
    try:
        user_service = UserService(db_session=db)
        ip_address, user_agent = get_client_info(request)

        # 获取要更新的用户
        user = user_service.get_user_by_id(db, user_id)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="User not found"
            )

        # 更新用户状态
        user.is_active = status_data.is_active
        db.commit()
        db.refresh(user)

        # 记录更新用户状态行为
        user_service.track_user_behavior(
            db,
            current_user.id,
            UserBehaviorCreate(
                action_type="update_user_status",
                action_target="user_status",
                action_details={
                    "user_id": user_id,
                    "new_status": status_data.is_active,
                },
            ),
            ip_address,
            user_agent,
        )

        return UserResponse.model_validate(user)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"User status update failed: {str(e)}",
        )


@router.delete(
    "/{user_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="删除用户",
    description="删除指定用户及其相关数据",
)
async def delete_user(
    user_id: int,
    current_user: Annotated[User, Depends(require_permission("user.user.delete"))],
    request: Request,
    db: Annotated[Session, Depends(get_db)],
):
    """
    删除用户

    Args:
        user_id: 用户ID
        current_user: 当前用户（需要有user.delete权限）
        request: 请求对象
        db: 数据库会话
    """
    try:
        user_service = UserService(db_session=db)
        ip_address, user_agent = get_client_info(request)

        # 获取要删除的用户
        user = user_service.get_user_by_id(db, user_id)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="User not found"
            )

        # 不允许删除自己
        if user_id == current_user.id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="Cannot delete your own account"
            )

        try:
            # 删除用户
            success = user_service.delete_user(db, user_id)
            
            if not success:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND, detail="User not found"
                )

            # 记录删除用户行为
            user_service.track_user_behavior(
                db,
                current_user.id,
                UserBehaviorCreate(
                    action_type="update_user_status",  # 使用已允许的行为类型
                    action_target="user_delete",
                    action_details={
                        "deleted_user_id": user_id,
                        "deleted_phone": user.phone,
                    },
                ),
                ip_address,
                user_agent,
            )
            
        except ValueError as e:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail=str(e)
            )
            
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"User deletion failed: {str(e)}",
        )
