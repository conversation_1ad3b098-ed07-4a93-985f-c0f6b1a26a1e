"""
数据处理服务Celery任务
提供异步数据处理功能
"""

import asyncio
import logging
from datetime import datetime, timedelta, timezone
from typing import Dict, Any, List, Optional

from celery import current_task
from sqlalchemy.orm import sessionmaker
from sqlalchemy import and_, desc

from ...core.celery_app import celery_app, data_processing_task, high_priority_task
from ...core.database import SessionLocal
from ..data_collection_service.models import RawDataRecord
from .models import DataProcessingStatus, ProcessingStage, ProcessingResult
from .engine import DataProcessingEngine

logger = logging.getLogger(__name__)


@data_processing_task
def process_single_record_task(self, record_id: int) -> Dict[str, Any]:
    """
    处理单个原始数据记录的异步任务
    
    Args:
        record_id: 原始数据记录ID
        
    Returns:
        Dict: 处理结果
    """
    
    db = SessionLocal()

    try:
        logger.info(f"开始处理记录 {record_id}")

        # 获取原始数据记录
        record = db.query(RawDataRecord).filter(
            RawDataRecord.id == record_id
        ).first()

        if not record:
            raise ValueError(f"未找到记录ID: {record_id}")

        # 创建处理引擎
        engine = DataProcessingEngine()

        # 执行异步处理
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        try:
            result = loop.run_until_complete(
                engine.process_single_record(record)
            )

            logger.info(f"成功处理记录 {record_id}: {result}")
            return result

        finally:
            loop.close()

    except Exception as e:
        logger.error(f"处理记录 {record_id} 失败: {e}")
        raise

    finally:
        db.close()


@data_processing_task
def process_pending_data_batch(self, batch_size: int = 10) -> Dict[str, Any]:
    """
    批量处理待处理数据的异步任务
    
    Args:
        batch_size: 批处理大小
        
    Returns:
        Dict: 处理统计结果
    """
    
    try:
        logger.info(f"开始批量处理，批量大小: {batch_size}")

        # 创建处理引擎
        engine = DataProcessingEngine()

        # 执行异步批量处理
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        try:
            stats = loop.run_until_complete(
                engine.process_pending_records(batch_size)
            )

            logger.info(f"批量处理完成: {stats}")
            return stats

        finally:
            loop.close()

    except Exception as e:
        logger.error(f"批量处理失败: {e}")
        raise


@high_priority_task
def retry_failed_records_task(self, max_retries: int = 3) -> Dict[str, Any]:
    """
    重试失败记录的高优先级异步任务
    
    Args:
        max_retries: 最大重试次数
        
    Returns:
        Dict: 重试统计结果
    """
    
    try:
        # 更新任务状态
        if current_task:
            current_task.update_state(
                state='STARTED',
                meta={'max_retries': max_retries, 'stage': 'finding_failed_records'}
            )
        
        # 创建处理引擎
        engine = DataProcessingEngine()
        
        # 执行异步重试
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            stats = loop.run_until_complete(
                engine.retry_failed_records(max_retries)
            )
            
            # 更新任务状态
            if current_task:
                current_task.update_state(
                    state='SUCCESS',
                    meta={
                        'max_retries': max_retries,
                        'stats': stats,
                        'completed_at': datetime.now(timezone.utc).isoformat()
                    }
                )
            
            logger.info(f"重试完成: {stats}")
            return stats
            
        finally:
            loop.close()
            
    except Exception as e:
        logger.error(f"重试失败: {e}")
        
        # 更新任务状态
        if current_task:
            current_task.update_state(
                state='FAILURE',
                meta={
                    'max_retries': max_retries,
                    'error': str(e),
                    'failed_at': datetime.now(timezone.utc).isoformat()
                }
            )
        
        raise


@celery_app.task(bind=True, autoretry_for=(Exception,), retry_kwargs={'max_retries': 3, 'countdown': 60})
def cleanup_old_processing_status(self, days_old: int = 30) -> Dict[str, Any]:
    """
    清理旧的处理状态记录

    Args:
        days_old: 保留天数

    Returns:
        Dict: 清理统计结果
    """

    db = SessionLocal()

    try:
        logger.info(f"开始清理 {days_old} 天前的处理状态记录")

        # 计算截止日期
        cutoff_date = datetime.now(timezone.utc) - timedelta(days=days_old)

        # 查找旧的已完成记录
        old_statuses = db.query(DataProcessingStatus).filter(
            and_(
                DataProcessingStatus.completed_at < cutoff_date,
                DataProcessingStatus.processing_result.in_([
                    ProcessingResult.SUCCESS,
                    ProcessingResult.DELETED
                ])
            )
        ).all()

        # 删除旧记录
        deleted_count = 0
        for status in old_statuses:
            db.delete(status)
            deleted_count += 1

        db.commit()

        result = {
            'deleted_count': deleted_count,
            'cutoff_date': cutoff_date.isoformat()
        }

        logger.info(f"清理完成: {result}")
        return result

    except Exception as e:
        logger.error(f"清理失败: {e}")
        db.rollback()
        raise

    finally:
        db.close()


@data_processing_task
def process_records_by_source(self, source_id: int, limit: int = 50) -> Dict[str, Any]:
    """
    按数据源处理记录
    
    Args:
        source_id: 数据源ID
        limit: 处理限制
        
    Returns:
        Dict: 处理统计结果
    """
    
    try:
        # 更新任务状态
        if current_task:
            current_task.update_state(
                state='STARTED',
                meta={'source_id': source_id, 'limit': limit, 'stage': 'finding_records'}
            )
        
        db = SessionLocal()
        
        try:
            # 获取指定数据源的待处理记录
            records = db.query(RawDataRecord).filter(
                and_(
                    RawDataRecord.source_id == source_id,
                    RawDataRecord.processing_status == "pending"
                )
            ).order_by(
                desc(RawDataRecord.processing_priority),
                RawDataRecord.created_at
            ).limit(limit).all()
            
            if not records:
                result = {'processed': 0, 'message': '没有待处理记录'}
                return result
            
            # 创建处理引擎
            engine = DataProcessingEngine()
            
            # 处理每个记录
            stats = {'processed': 0, 'success': 0, 'failed': 0}
            
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                for i, record in enumerate(records):
                    try:
                        # 更新进度
                        if current_task:
                            current_task.update_state(
                                state='PROGRESS',
                                meta={
                                    'source_id': source_id,
                                    'progress': f"{i+1}/{len(records)}",
                                    'current_record': record.id
                                }
                            )
                        
                        result = loop.run_until_complete(
                            engine.process_single_record(record)
                        )
                        
                        stats['processed'] += 1
                        if result.get('success'):
                            stats['success'] += 1
                        else:
                            stats['failed'] += 1
                            
                    except Exception as e:
                        logger.error(f"处理记录 {record.id} 失败: {e}")
                        stats['failed'] += 1
                
                # 更新任务状态
                if current_task:
                    current_task.update_state(
                        state='SUCCESS',
                        meta={
                            'source_id': source_id,
                            'stats': stats,
                            'completed_at': datetime.now(timezone.utc).isoformat()
                        }
                    )
                
                logger.info(f"按数据源处理完成: {stats}")
                return stats
                
            finally:
                loop.close()
                
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"按数据源处理失败: {e}")
        
        # 更新任务状态
        if current_task:
            current_task.update_state(
                state='FAILURE',
                meta={
                    'source_id': source_id,
                    'error': str(e),
                    'failed_at': datetime.now(timezone.utc).isoformat()
                }
            )
        
        raise


# 任务工具函数
def submit_processing_task(record_id: int, priority: int = 5) -> str:
    """
    提交处理任务
    
    Args:
        record_id: 记录ID
        priority: 任务优先级
        
    Returns:
        str: 任务ID
    """
    
    task = process_single_record_task.apply_async(
        args=[record_id],
        priority=priority,
        queue='data_processing'
    )
    
    return task.id


def submit_batch_processing_task(batch_size: int = 10) -> str:
    """
    提交批量处理任务
    
    Args:
        batch_size: 批处理大小
        
    Returns:
        str: 任务ID
    """
    
    task = process_pending_data_batch.apply_async(
        args=[batch_size],
        queue='data_processing'
    )
    
    return task.id


def submit_retry_task(max_retries: int = 3) -> str:
    """
    提交重试任务
    
    Args:
        max_retries: 最大重试次数
        
    Returns:
        str: 任务ID
    """
    
    task = retry_failed_records_task.apply_async(
        args=[max_retries],
        priority=8,  # 高优先级
        queue='data_processing'
    )
    
    return task.id
