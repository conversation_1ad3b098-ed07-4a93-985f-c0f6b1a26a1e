"""
数据处理服务管理器
负责数据处理服务的启动、停止和状态管理
"""

import asyncio
import logging
from datetime import datetime, timezone
from typing import Dict, Any, Optional

from sqlalchemy import func, text
from sqlalchemy.orm import Session

from ...core.database import SessionLocal
from ..data_collection_service.models import RawDataRecord
from .models import DataProcessingPipeline, DataProcessingStatus
from .engine import DataProcessingEngine
from .task_manager import DataProcessingTaskManager
from .performance_monitor import PerformanceMonitor
from .processing_scheduler import get_data_processing_scheduler

logger = logging.getLogger(__name__)


class DataProcessingManager:
    """
    数据处理服务管理器
    负责协调数据处理服务的各个组件
    """

    def __init__(self):
        self.is_running = False
        self.start_time: Optional[datetime] = None
        self.engine = DataProcessingEngine()
        self.task_manager = DataProcessingTaskManager()
        self.performance_monitor = PerformanceMonitor()
        self.scheduler = get_data_processing_scheduler()

    async def start(self):
        """
        启动数据处理服务
        """
        if self.is_running:
            logger.warning("数据处理服务已在运行")
            return

        logger.info("🔄 启动数据处理服务...")
        self.is_running = True
        self.start_time = datetime.now(timezone.utc)

        try:
            # 1. 检查服务依赖
            await self._check_dependencies()

            # 2. 初始化数据处理管道
            await self._initialize_pipelines()

            # 3. 启动性能监控
            self._start_performance_monitoring()

            # 4. 启动数据处理调度器
            await self.scheduler.start()

            # 5. 检查待处理数据并触发初始处理
            await self._trigger_initial_processing()

            # 6. 启动健康检查
            asyncio.create_task(self._health_check_loop())

            logger.info("✅ 数据处理服务启动成功")

        except Exception as e:
            logger.error(f"❌ 数据处理服务启动失败: {e}")
            self.is_running = False
            raise

    async def stop(self):
        """
        停止数据处理服务
        """
        if not self.is_running:
            return

        logger.info("🛑 停止数据处理服务...")
        self.is_running = False

        try:
            # 停止调度器
            await self.scheduler.stop()

            # 停止性能监控
            self.performance_monitor.stop_monitoring()

            logger.info("✅ 数据处理服务已停止")

        except Exception as e:
            logger.error(f"❌ 停止数据处理服务时出错: {e}")

    async def get_service_status(self) -> Dict[str, Any]:
        """
        获取服务状态信息
        """
        db = SessionLocal()
        try:
            # 统计待处理数据
            pending_count = db.query(RawDataRecord).filter(
                RawDataRecord.processing_status == 'pending'
            ).count()

            # 统计处理中数据
            processing_count = db.query(DataProcessingStatus).filter(
                DataProcessingStatus.processing_stage.in_(['parsing', 'classifying', 'tagging', 'validating'])
            ).count()

            # 获取调度器状态
            scheduler_status = self.scheduler.get_scheduler_status()

            return {
                "service_running": self.is_running,
                "start_time": self.start_time.isoformat() if self.start_time else None,
                "uptime_seconds": (
                    (datetime.now(timezone.utc) - self.start_time).total_seconds()
                    if self.start_time else 0
                ),
                "pending_records": pending_count,
                "processing_records": processing_count,
                "scheduler_status": scheduler_status,
                "performance_monitoring": self.performance_monitor.monitoring_active
            }

        finally:
            db.close()

    async def _check_dependencies(self):
        """
        检查服务依赖
        """
        logger.info("检查服务依赖...")

        # 检查数据库连接
        db = SessionLocal()
        try:
            db.execute(text("SELECT 1"))
            logger.info("✅ 数据库连接正常")
        except Exception as e:
            logger.error(f"❌ 数据库连接失败: {e}")
            raise
        finally:
            db.close()

        # 检查调度器状态
        try:
            scheduler_status = self.scheduler.get_scheduler_status()
            if scheduler_status["is_running"]:
                logger.info("✅ 数据处理调度器正常")
            else:
                logger.warning("⚠️ 数据处理调度器未运行")
        except Exception as e:
            logger.warning(f"⚠️ 调度器状态检查失败: {e}")

    async def _initialize_pipelines(self):
        """
        初始化数据处理管道
        """
        logger.info("初始化数据处理管道...")

        db = SessionLocal()
        try:
            # 检查是否有活跃的处理管道
            active_pipelines = db.query(DataProcessingPipeline).filter(
                DataProcessingPipeline.is_active == True
            ).count()

            if active_pipelines == 0:
                logger.warning("⚠️ 未找到活跃的数据处理管道，请检查管道配置")
            else:
                logger.info(f"✅ 找到 {active_pipelines} 个活跃的数据处理管道")

        finally:
            db.close()

    def _start_performance_monitoring(self):
        """
        启动性能监控
        """
        try:
            self.performance_monitor.start_monitoring(interval=60.0)  # 每分钟监控一次
            logger.info("✅ 性能监控已启动")
        except Exception as e:
            logger.warning(f"⚠️ 性能监控启动失败: {e}")

    async def _trigger_initial_processing(self):
        """
        触发初始数据处理
        """
        logger.info("检查待处理数据...")

        db = SessionLocal()
        try:
            # 统计待处理数据
            pending_count = db.query(RawDataRecord).filter(
                RawDataRecord.processing_status == 'pending'
            ).count()

            if pending_count > 0:
                logger.info(f"发现 {pending_count} 条待处理数据，触发批量处理...")

                # 提交批量处理任务
                task_id = await self.task_manager.submit_batch_processing_task(batch_size=20)
                if task_id:
                    logger.info(f"✅ 已提交批量处理任务: {task_id}")
                else:
                    logger.warning("⚠️ 批量处理任务提交失败")
            else:
                logger.info("✅ 暂无待处理数据")

        except Exception as e:
            logger.error(f"❌ 触发初始处理失败: {e}")
        finally:
            db.close()



    async def _health_check_loop(self):
        """
        健康检查循环
        """
        while self.is_running:
            try:
                await asyncio.sleep(300)  # 每5分钟检查一次
                
                if not self.is_running:
                    break
                
                # 执行健康检查
                status = await self.get_service_status()
                logger.debug(f"数据处理服务健康检查: {status}")
                
            except Exception as e:
                logger.error(f"健康检查失败: {e}")


# 全局管理器实例
_manager_instance = None


def get_data_processing_manager() -> DataProcessingManager:
    """
    获取全局数据处理管理器实例
    """
    global _manager_instance
    if _manager_instance is None:
        _manager_instance = DataProcessingManager()
    return _manager_instance


async def start_data_processing_service():
    """
    启动全局数据处理服务
    """
    manager = get_data_processing_manager()
    await manager.start()


async def stop_data_processing_service():
    """
    停止全局数据处理服务
    """
    manager = get_data_processing_manager()
    await manager.stop()


async def get_data_processing_service_status() -> Dict[str, Any]:
    """
    获取数据处理服务状态
    """
    manager = get_data_processing_manager()
    return await manager.get_service_status()
