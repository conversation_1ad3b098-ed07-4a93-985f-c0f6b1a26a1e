"""
数据处理服务数据模型
定义数据处理管道、数据处理状态和业务数据表相关的数据库模型
"""


from sqlalchemy import (
    Enum as SQLEnum,
    Index,
    Column, BigInteger, String, Text, Boolean, TIMESTAMP,
    Integer, DECIMAL, JSON, SMALLINT, ForeignKey, Date
)
from sqlalchemy.dialects.postgresql import JSONB, ARRAY
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from enum import Enum

from ...core.database import Base
from ..data_collection_service.models import BusinessDataType


class DataProcessingPipeline(Base):
    """
    数据处理管道表（统一版本）
    合并原processing_pipelines和data_processing_rules功能，提供统一的数据处理配置
    """

    __tablename__ = "data_processing_pipelines"

    id = Column(
        BigInteger, primary_key=True, index=True, comment="处理管道唯一标识符"
    )
    
    # 基础信息
    pipeline_code = Column(
        String(50),
        nullable=False,
        comment="管道代码，如jin10_flash_news_specific",
    )
    version = Column(
        Integer,
        default=1,
        comment="版本号，用于版本管理",
    )
    pipeline_name = Column(
        String(100),
        nullable=False,
        comment="管道名称",
    )
    description = Column(Text, comment="管道描述")

    # 适用范围（明确的匹配条件）
    business_data_type = Column(
        SQLEnum(BusinessDataType, values_callable=lambda obj: [e.value for e in obj]),
        nullable=False,
        comment="适用的业务数据类型"
    )
    source_id = Column(
        BigInteger,
        ForeignKey("data_sources.id"),
        comment="专用数据源ID（为空表示通用管道）"
    )
    url_pattern = Column(
        String(500),
        comment="URL匹配模式（正则表达式）"
    )
    domain_pattern = Column(
        String(200),
        comment="域名匹配模式"
    )
    content_type_pattern = Column(
        String(100),
        comment="内容类型匹配模式"
    )
    content_pattern = Column(
        JSONB,
        comment="内容匹配模式"
    )

    # 适用条件
    min_content_length = Column(Integer, comment="最小内容长度")
    max_content_length = Column(Integer, comment="最大内容长度")
    quality_threshold = Column(
        DECIMAL(3, 2),
        default=0.5,
        comment="质量阈值"
    )
    required_fields = Column(
        ARRAY(String),
        default=[],
        comment="必需字段"
    )

    # 统一的处理配置（合并原来的重复配置）
    field_mapping = Column(
        JSONB,
        nullable=False,
        default={},
        comment="字段映射规则：原始字段 -> 目标字段"
    )
    data_extraction_config = Column(
        JSONB,
        default={},
        comment="数据提取配置：CSS选择器、XPath等"
    )
    data_transformation_config = Column(
        JSONB,
        default={},
        comment="数据转换配置：格式转换、数据清洗、计算等"
    )
    data_validation_config = Column(
        JSONB,
        default={},
        comment="数据验证配置：必填检查、格式验证、范围验证等"
    )
    data_enrichment_config = Column(
        JSONB,
        default={},
        comment="数据增强配置：标签提取、分类识别、实体识别等"
    )

    # 管道控制
    priority = Column(
        Integer,
        default=5,
        comment="优先级 1-10，数值越大优先级越高"
    )
    execution_order = Column(Integer, default=1, comment="执行顺序")
    is_active = Column(Boolean, default=True, comment="是否启用")
    is_default = Column(Boolean, default=False, comment="是否为默认管道")

    # 版本管理
    change_description = Column(Text, comment="版本变更说明")
    test_result = Column(JSONB, comment="测试结果")
    parent_version_id = Column(
        BigInteger,
        ForeignKey("data_processing_pipelines.id"),
        comment="父版本ID，用于追踪版本继承关系"
    )

    # 生效时间控制
    effective_date_start = Column(Date, comment="生效开始日期")
    effective_date_end = Column(Date, comment="生效结束日期")

    # 执行统计
    execution_count = Column(Integer, default=0, comment="执行次数")
    success_count = Column(Integer, default=0, comment="成功次数")
    failure_count = Column(Integer, default=0, comment="失败次数")
    last_executed_at = Column(TIMESTAMP, comment="最后执行时间")
    avg_execution_time_ms = Column(Integer, comment="平均执行时间（毫秒）")

    # 管理信息
    created_at = Column(TIMESTAMP, server_default=func.now(), comment="创建时间")
    updated_at = Column(
        TIMESTAMP, server_default=func.now(), onupdate=func.now(), comment="更新时间"
    )
    created_by = Column(String(100), comment="创建者")
    updated_by = Column(String(100), comment="更新者")

    # 索引
    __table_args__ = (
        # 唯一约束
        Index("uq_pipeline_code_version", "pipeline_code", "version", unique=True),
        
        # 基础索引
        Index("idx_data_processing_pipelines_code_version", "pipeline_code", "version"),
        Index("idx_data_processing_pipelines_code_active", "pipeline_code", "is_active"),
        Index("idx_data_processing_pipelines_type", "business_data_type"),
        Index("idx_data_processing_pipelines_source", "source_id"),
        Index("idx_data_processing_pipelines_priority", "priority"),
        Index("idx_data_processing_pipelines_active", "is_active"),
        Index("idx_data_processing_pipelines_updated", "updated_at"),
    )


class ProcessingStage(str, Enum):
    """数据处理阶段枚举"""

    PENDING = "pending"  # 待处理
    EXTRACTING = "extracting"  # 数据提取
    CLEANING = "cleaning"  # 数据清洗
    TRANSFORMING = "transforming"  # 数据转换
    PARSING = "parsing"  # 解析中
    CLASSIFYING = "classifying"  # 分类中
    TAGGING = "tagging"  # 标签提取中
    VALIDATING = "validating"  # 验证中
    ENHANCING = "enhancing"  # AI增强
    SAVING = "saving"  # 保存数据
    COMPLETED = "completed"  # 完成
    FAILED = "failed"  # 失败


class ProcessingResult(str, Enum):
    """处理结果枚举"""

    SUCCESS = "success"  # 成功
    PARTIAL_SUCCESS = "partial_success"  # 部分成功
    FAILED = "failed"  # 失败
    SKIPPED = "skipped"  # 跳过


class DataProcessingStatus(Base):
    """
    数据处理状态表
    跟踪原始数据记录的处理状态和进度
    """
    __tablename__ = "data_processing_status"

    id = Column(BigInteger, primary_key=True, index=True, comment="处理状态唯一标识符")
    raw_data_id = Column(
        BigInteger,
        ForeignKey("raw_data_records.id", ondelete="CASCADE"),
        nullable=False,
        comment="原始数据记录ID"
    )
    target_business_type = Column(
        SQLEnum(BusinessDataType, values_callable=lambda obj: [e.value for e in obj]),
        nullable=False,
        comment="目标业务类型"
    )

    # 处理流程状态
    processing_stage = Column(
        SQLEnum(ProcessingStage, values_callable=lambda obj: [e.value for e in obj]),
        default=ProcessingStage.PENDING,
        comment="处理阶段"
    )
    stage_details = Column(JSONB, default={}, comment="阶段详情")

    # 处理进度
    progress_percentage = Column(
        Integer,
        default=0,
        comment="处理进度百分比，0-100"
    )
    current_step = Column(String(100), comment="当前处理步骤")

    # 时间跟踪
    started_at = Column(TIMESTAMP, default=func.now(), comment="开始处理时间")
    completed_at = Column(TIMESTAMP, comment="完成处理时间")
    processing_duration_seconds = Column(Integer, comment="处理耗时（秒）")

    # 处理结果
    target_table_id = Column(BigInteger, comment="目标表记录ID")
    target_table_name = Column(String(100), comment="目标表名")
    processing_result = Column(
        SQLEnum(ProcessingResult, values_callable=lambda obj: [e.value for e in obj]),
        comment="处理结果"
    )

    # 质量评估
    data_quality_score = Column(DECIMAL(3, 2), comment="数据质量评分")
    tag_extraction_count = Column(Integer, default=0, comment="提取标签数量")
    classification_count = Column(Integer, default=0, comment="分类数量")

    # 错误处理
    error_count = Column(Integer, default=0, comment="错误次数")
    error_message = Column(Text, comment="错误信息")
    retry_count = Column(Integer, default=0, comment="重试次数")

    # 时间字段
    created_at = Column(
        TIMESTAMP,
        server_default=func.now(),
        comment="创建时间"
    )
    updated_at = Column(
        TIMESTAMP,
        server_default=func.now(),
        onupdate=func.now(),
        comment="更新时间"
    )

    # 索引
    __table_args__ = (
        Index('idx_data_processing_status_raw_data', 'raw_data_id'),
        Index('idx_data_processing_status_stage', 'processing_stage'),
        Index('idx_data_processing_status_type', 'target_business_type'),
        Index('idx_data_processing_status_result', 'processing_result'),
    )


# ============================================================================
# 业务数据表模型
# ============================================================================

class FlashNews(Base):
    """
    快讯表
    存储处理后的财经快讯数据
    """
    __tablename__ = "flash_news"

    id = Column(BigInteger, primary_key=True, index=True, comment="快讯唯一标识符")
    raw_data_id = Column(
        BigInteger,
        ForeignKey("raw_data_records.id", ondelete="CASCADE"),
        nullable=False,
        comment="关联原始数据ID"
    )

    # 基础信息
    title = Column(String(500), nullable=False, comment="快讯标题")
    content = Column(Text, nullable=False, comment="快讯内容")
    summary = Column(String(1000), comment="AI生成摘要")

    # 时间信息
    publish_time = Column(TIMESTAMP, nullable=False, comment="发布时间")
    process_time = Column(TIMESTAMP, server_default=func.now(), comment="处理时间")

    # 重要性和紧急度
    urgency_level = Column(
        Integer,
        nullable=False,
        default=2,
        comment="紧急度级别：1普通/2重要/3紧急"
    )
    importance_score = Column(DECIMAL(3, 2), default=0.5, comment="重要性评分")
    impact_scope = Column(
        String(50),
        default="domestic",
        comment="影响范围：domestic/international/global"
    )

    # 分类信息
    news_category = Column(String(100), comment="新闻分类")

    # 状态管理
    status = Column(String(20), default="published", comment="状态：draft/published/updated/archived")
    is_breaking = Column(Boolean, default=False, comment="是否突发新闻")
    push_immediately = Column(Boolean, default=False, comment="是否立即推送")

    # 统计信息
    view_count = Column(Integer, default=0, comment="浏览次数")
    share_count = Column(Integer, default=0, comment="分享次数")

    created_at = Column(TIMESTAMP, server_default=func.now(), comment="创建时间")
    updated_at = Column(
        TIMESTAMP,
        server_default=func.now(),
        onupdate=func.now(),
        comment="更新时间"
    )

    # 索引
    __table_args__ = (
        Index('idx_flash_news_publish_time', 'publish_time'),
        Index('idx_flash_news_urgency', 'urgency_level'),
        Index('idx_flash_news_status', 'status'),
        Index('idx_flash_news_category', 'news_category'),
        Index('idx_flash_news_breaking', 'is_breaking'),
    )


class NewsArticle(Base):
    """
    新闻文章表
    存储处理后的新闻文章数据
    """
    __tablename__ = "news_articles"

    id = Column(BigInteger, primary_key=True, index=True, comment="新闻文章唯一标识符")
    raw_data_id = Column(
        BigInteger,
        ForeignKey("raw_data_records.id", ondelete="CASCADE"),
        nullable=False,
        comment="关联原始数据ID"
    )

    # 基础内容
    title = Column(String(1000), nullable=False, comment="文章标题")
    subtitle = Column(String(1000), comment="副标题")
    abstract = Column(Text, comment="文章摘要")
    content = Column(Text, nullable=False, comment="文章正文")
    content_html = Column(Text, comment="HTML内容")

    # 作者和来源
    author = Column(String(200), comment="作者姓名")
    source_media = Column(String(200), comment="发布媒体")
    source_column = Column(String(200), comment="所属栏目")
    original_url = Column(String(1000), comment="原文链接")

    # 时间信息
    publish_time = Column(TIMESTAMP, nullable=False, comment="发布时间")
    update_time = Column(TIMESTAMP, comment="更新时间")
    process_time = Column(TIMESTAMP, server_default=func.now(), comment="处理时间")

    # 内容特征
    word_count = Column(Integer, comment="字数统计")
    reading_time_minutes = Column(Integer, comment="预估阅读时长")
    content_quality_score = Column(DECIMAL(3, 2), default=0.5, comment="内容质量评分")
    readability_score = Column(DECIMAL(3, 2), comment="可读性评分")

    # 分类和实体
    primary_category = Column(String(100), comment="主要分类")
    secondary_categories = Column(ARRAY(String), default=[], comment="次要分类")
    mentioned_companies = Column(ARRAY(String), default=[], comment="提及公司")
    mentioned_people = Column(ARRAY(String), default=[], comment="提及人物")
    mentioned_locations = Column(ARRAY(String), default=[], comment="提及地点")

    # 市场相关
    market_impact_prediction = Column(String(50), comment="市场影响预测")

    # 媒体资源
    featured_image_url = Column(String(1000), comment="特色图片URL")
    images_urls = Column(ARRAY(String), default=[], comment="图片URL数组")
    video_urls = Column(ARRAY(String), default=[], comment="视频URL数组")

    # 平台统计
    internal_view_count = Column(Integer, default=0, comment="平台浏览量")
    internal_favorite_count = Column(Integer, default=0, comment="收藏数")
    internal_comment_count = Column(Integer, default=0, comment="评论数")
    internal_share_count = Column(Integer, default=0, comment="分享数")

    # 状态管理
    status = Column(
        String(20),
        default="published",
        comment="状态：draft/review/published/featured/archived"
    )
    is_featured = Column(Boolean, default=False, comment="是否精选")
    is_trending = Column(Boolean, default=False, comment="是否热门")
    editorial_rating = Column(Integer, comment="编辑评分 1-5")

    # 版权信息
    copyright_info = Column(Text, comment="版权声明")
    reproduction_rights = Column(
        String(50),
        default="fair_use",
        comment="转载权限"
    )

    created_at = Column(TIMESTAMP, server_default=func.now(), comment="创建时间")
    updated_at = Column(
        TIMESTAMP,
        server_default=func.now(),
        onupdate=func.now(),
        comment="更新时间"
    )

    # 索引
    __table_args__ = (
        Index('idx_news_articles_publish_time', 'publish_time'),
        Index('idx_news_articles_status', 'status'),
        Index('idx_news_articles_category', 'primary_category'),
        Index('idx_news_articles_featured', 'is_featured'),
        Index('idx_news_articles_trending', 'is_trending'),
        Index('idx_news_articles_author', 'author'),
        Index('idx_news_articles_source', 'source_media'),
    )


class ResearchReport(Base):
    """
    研究报告表
    存储处理后的研究报告数据
    """
    __tablename__ = "research_reports"

    id = Column(BigInteger, primary_key=True, index=True, comment="研究报告唯一标识符")
    raw_data_id = Column(
        BigInteger,
        ForeignKey("raw_data_records.id", ondelete="CASCADE"),
        nullable=False,
        comment="关联原始数据ID"
    )

    # 基础信息
    title = Column(String(1000), nullable=False, comment="报告标题")
    report_type = Column(
        String(50),
        nullable=False,
        comment="报告类型：company_report/industry_report/macro_report/strategy_report"
    )
    report_subtitle = Column(String(1000), comment="报告副标题")
    executive_summary = Column(Text, comment="执行摘要")
    full_content = Column(Text, comment="报告全文")

    # 发布机构和分析师
    institution_name = Column(String(200), nullable=False, comment="发布机构")
    institution_code = Column(String(50), comment="机构代码")
    institution_rating = Column(String(20), comment="机构评级")
    analyst_name = Column(String(200), comment="首席分析师")
    analyst_team = Column(ARRAY(String), default=[], comment="分析师团队")

    # 研究标的
    target_stock_code = Column(String(20), comment="标的股票代码")
    target_stock_name = Column(String(200), comment="标的股票名称")
    target_industry = Column(String(100), comment="标的行业")
    coverage_scope = Column(Text, comment="覆盖范围")

    # 投资建议
    investment_rating = Column(String(50), comment="投资评级")
    previous_rating = Column(String(50), comment="前次评级")
    rating_change = Column(String(20), comment="评级变化")
    target_price = Column(DECIMAL(10, 2), comment="目标价格")
    target_price_currency = Column(String(10), default="CNY", comment="价格币种")
    price_forecast_period = Column(Integer, comment="预测周期（月）")

    # 时间信息
    publish_time = Column(TIMESTAMP, nullable=False, comment="发布时间")
    report_period = Column(String(50), comment="报告期间")
    data_cutoff_date = Column(Date, comment="数据截止日期")
    process_time = Column(TIMESTAMP, server_default=func.now(), comment="处理时间")

    # 财务预测
    revenue_forecast = Column(JSONB, comment="营收预测")
    profit_forecast = Column(JSONB, comment="利润预测")
    eps_forecast = Column(JSONB, comment="EPS预测")
    pe_valuation = Column(DECIMAL(8, 2), comment="PE估值")
    pb_valuation = Column(DECIMAL(8, 2), comment="PB估值")
    dcf_valuation = Column(DECIMAL(12, 2), comment="DCF估值")

    # 风险分析
    upside_risks = Column(ARRAY(String), default=[], comment="上行风险")
    downside_risks = Column(ARRAY(String), default=[], comment="下行风险")
    risk_rating = Column(String(20), default="medium", comment="风险等级")

    # 报告特征
    report_pages = Column(Integer, comment="报告页数")
    has_financial_model = Column(Boolean, default=False, comment="是否包含财务模型")
    has_charts = Column(Boolean, default=False, comment="是否包含图表")
    chart_count = Column(Integer, default=0, comment="图表数量")

    # 关键信息
    key_topics = Column(ARRAY(String), default=[], comment="关键主题")
    mentioned_companies = Column(ARRAY(String), default=[], comment="提及公司")
    comparable_companies = Column(ARRAY(String), default=[], comment="可比公司")

    # 市场数据
    stock_price_at_publish = Column(DECIMAL(10, 2), comment="发布时股价")
    market_cap_at_publish = Column(DECIMAL(15, 2), comment="发布时市值")

    # 影响分析
    price_impact_1d = Column(DECIMAL(5, 2), comment="1日价格影响")
    price_impact_5d = Column(DECIMAL(5, 2), comment="5日价格影响")
    price_impact_30d = Column(DECIMAL(5, 2), comment="30日价格影响")

    # 质量评估
    research_quality_score = Column(DECIMAL(3, 2), default=0.5, comment="研究质量评分")
    prediction_accuracy = Column(DECIMAL(3, 2), comment="历史预测准确率")
    report_completeness = Column(DECIMAL(3, 2), default=0.5, comment="报告完整性")

    # 状态管理
    status = Column(
        String(20),
        default="published",
        comment="状态：draft/review/published/updated/archived"
    )
    is_featured = Column(Boolean, default=False, comment="是否精选")
    access_level = Column(
        String(20),
        default="public",
        comment="访问级别：public/premium/restricted"
    )

    # 文档链接
    pdf_url = Column(String(1000), comment="PDF链接")
    original_url = Column(String(1000), comment="原始链接")
    backup_urls = Column(ARRAY(String), default=[], comment="备份链接")

    created_at = Column(TIMESTAMP, server_default=func.now(), comment="创建时间")
    updated_at = Column(
        TIMESTAMP,
        server_default=func.now(),
        onupdate=func.now(),
        comment="更新时间"
    )

    # 索引
    __table_args__ = (
        Index('idx_research_reports_publish_time', 'publish_time'),
        Index('idx_research_reports_type', 'report_type'),
        Index('idx_research_reports_institution', 'institution_name'),
        Index('idx_research_reports_stock', 'target_stock_code'),
        Index('idx_research_reports_rating', 'investment_rating'),
        Index('idx_research_reports_status', 'status'),
        Index('idx_research_reports_featured', 'is_featured'),
    )


class EconomicIndicatorBase(Base):
    """
    经济指标基础信息表
    存储经济指标的基本信息和元数据
    """
    __tablename__ = "economic_indicator_base"

    id = Column(Integer, primary_key=True, comment="指标唯一标识")
    indicator_name = Column(String(200), nullable=False, comment="指标名称")
    indicator_paraphrase = Column(Text, comment="指标含义")
    country = Column(String(50), nullable=False, comment="发布国家/地区")
    release_institution = Column(String(100), nullable=False, comment="发布机构")
    release_frequency = Column(String(50), comment="发布频率")
    first_release_time = Column(TIMESTAMP, comment="首次公布时间")
    importance_star = Column(SMALLINT, comment="重要性星级（1-5星）")
    data_source_url = Column(String(500), comment="数据来源链接")
    related_video_url = Column(String(500), comment="相关视频链接")

    # 扩展信息
    indicator_code = Column(String(100), comment="标准化指标代码")
    indicator_name_en = Column(String(200), comment="英文指标名称")
    category = Column(String(100), comment="指标分类")
    data_unit = Column(String(50), comment="数据单位")

    # 管理字段
    is_active = Column(Boolean, default=True, comment="是否启用")
    created_at = Column(TIMESTAMP, server_default=func.now(), comment="记录创建时间")
    updated_at = Column(
        TIMESTAMP,
        server_default=func.now(),
        onupdate=func.now(),
        comment="记录更新时间"
    )

    # 关系
    indicator_data = relationship("EconomicIndicatorData", back_populates="indicator_base")

    # 索引
    __table_args__ = (
        Index('idx_economic_indicator_base_country', 'country', 'category'),
        Index('idx_economic_indicator_base_institution', 'release_institution'),
        Index('idx_economic_indicator_base_importance', 'importance_star'),
        Index('idx_economic_indicator_base_active', 'is_active'),
    )


class EconomicIndicatorData(Base):
    """
    经济指标数据记录表
    存储具体的经济指标数据发布记录
    """
    __tablename__ = "economic_indicator_data"

    id = Column(BigInteger, primary_key=True, index=True, comment="记录唯一标识")
    indicator_id = Column(
        Integer,
        ForeignKey("economic_indicator_base.id", ondelete="CASCADE"),
        nullable=False,
        comment="关联的指标ID"
    )
    raw_data_id = Column(
        BigInteger,
        ForeignKey("raw_data_records.id", ondelete="CASCADE"),
        comment="关联原始数据ID"
    )

    # 时间信息
    time_period = Column(String(100), comment="数据统计周期")
    current_release_time = Column(TIMESTAMP, nullable=False, comment="本期公布时间")
    last_release_time = Column(TIMESTAMP, comment="上一次公布时间")
    next_release_time = Column(TIMESTAMP, comment="下一次公布时间")

    # 数据值
    previous_value = Column(DECIMAL(20, 6), comment="前值")
    revised_previous = Column(DECIMAL(20, 6), comment="修正后的前值")
    consensus_value = Column(DECIMAL(20, 6), comment="预期值")
    actual_value = Column(DECIMAL(20, 6), comment="实际值")

    # 数据属性
    unit = Column(String(20), comment="数据单位")
    data_type = Column(
        String(30),
        default="absolute",
        comment="数据类型：absolute绝对值/rate比率/index指数/growth_rate增长率"
    )

    # 市场影响分析
    impact_logic = Column(String(500), comment="数据影响逻辑")
    market_concern = Column(Text, comment="市场关注度解读")

    # 计算字段
    surprise_index = Column(DECIMAL(5, 2), comment="意外指数，实际值偏离预期的程度")
    mom_change = Column(DECIMAL(10, 4), comment="环比变化")
    mom_change_pct = Column(DECIMAL(8, 4), comment="环比变化百分比")
    yoy_change = Column(DECIMAL(10, 4), comment="同比变化")
    yoy_change_pct = Column(DECIMAL(8, 4), comment="同比变化百分比")

    # 数据质量
    data_quality = Column(
        String(20),
        default="normal",
        comment="数据质量：preliminary初步/normal正常/revised修正/final最终"
    )
    reliability_score = Column(DECIMAL(3, 2), default=0.8, comment="可靠性评分")

    # 市场反应
    market_reaction = Column(JSONB, comment="市场反应数据，包含股市、汇率、债券等反应")
    analyst_comments = Column(ARRAY(String), default=[], comment="分析师评论数组")

    # 状态管理
    status = Column(
        String(20),
        default="published",
        comment="状态：preliminary初步/published已发布/revised已修正/archived已归档"
    )
    is_breaking_data = Column(Boolean, default=False, comment="是否为突破性数据")

    # 处理信息
    process_time = Column(TIMESTAMP, server_default=func.now(), comment="处理时间")

    created_at = Column(TIMESTAMP, server_default=func.now(), comment="记录创建时间")
    updated_at = Column(
        TIMESTAMP,
        server_default=func.now(),
        onupdate=func.now(),
        comment="记录更新时间"
    )

    # 关系
    indicator_base = relationship("EconomicIndicatorBase", back_populates="indicator_data")

    # 索引
    __table_args__ = (
        Index('idx_economic_indicator_data_indicator', 'indicator_id', 'current_release_time'),
        Index('idx_economic_indicator_data_publish_time', 'current_release_time'),
        Index('idx_economic_indicator_data_status', 'status'),
        Index('idx_economic_indicator_data_period', 'indicator_id', 'time_period'),
        Index('idx_economic_indicator_data_breaking', 'is_breaking_data'),
        # 唯一约束：同一指标的同一时间周期只能有一条记录
        Index('idx_economic_indicator_data_unique', 'indicator_id', 'time_period', 'current_release_time', unique=True),
    )


# ============================================================================
# 统一标签关联系统模型
# ============================================================================

class AITagMatches(Base):
    """
    AI标签匹配表
    存储AI生成标签与标准标签的匹配关系
    """
    __tablename__ = "ai_tag_matches"

    id = Column(BigInteger, primary_key=True, index=True, comment="AI标签匹配唯一标识符")
    ai_tag_text = Column(String(100), nullable=False, comment="AI生成的原始标签文本")
    standard_tag_id = Column(
        BigInteger,
        ForeignKey("tags.id", ondelete="CASCADE"),
        comment="匹配的标准化标签ID"
    )
    confidence_score = Column(DECIMAL(3, 2), nullable=False, comment="匹配置信度 0-1")
    match_method = Column(
        String(50),
        nullable=False,
        comment="匹配方法：exact/synonym/semantic/new"
    )

    # 匹配详情
    similarity_score = Column(DECIMAL(5, 4), comment="相似度分数")
    match_context = Column(JSONB, comment="匹配上下文信息")

    # 验证状态
    is_verified = Column(Boolean, default=False, comment="是否已验证")
    verified_by = Column(String(100), comment="验证人")
    verified_at = Column(TIMESTAMP, comment="验证时间")

    # 使用统计
    usage_count = Column(Integer, default=0, comment="使用次数")
    success_rate = Column(DECIMAL(3, 2), comment="成功率")

    created_at = Column(TIMESTAMP, server_default=func.now(), comment="创建时间")
    updated_at = Column(
        TIMESTAMP,
        server_default=func.now(),
        onupdate=func.now(),
        comment="更新时间"
    )

    # 索引
    __table_args__ = (
        Index('idx_ai_tag_matches_text', 'ai_tag_text'),
        Index('idx_ai_tag_matches_standard_tag', 'standard_tag_id'),
        Index('idx_ai_tag_matches_confidence', 'confidence_score'),
        Index('idx_ai_tag_matches_method', 'match_method'),
    )


class UnifiedContentTags(Base):
    """
    统一内容标签关联表
    支持所有业务表的标签关联
    """
    __tablename__ = "unified_content_tags"

    id = Column(BigInteger, primary_key=True, index=True, comment="统一内容标签关联唯一标识符")

    # 内容标识（支持多种业务表）
    content_type = Column(
        SQLEnum(BusinessDataType, values_callable=lambda obj: [e.value for e in obj]),
        nullable=False,
        comment="内容类型"
    )
    content_id = Column(BigInteger, nullable=False, comment="内容ID（业务表主键）")
    tag_id = Column(
        BigInteger,
        ForeignKey("tags.id", ondelete="CASCADE"),
        nullable=False,
        comment="标签ID"
    )

    # 评分信息
    relevance_score = Column(DECIMAL(3, 2), default=1.00, comment="相关性评分 0-1")
    confidence_score = Column(DECIMAL(3, 2), default=1.00, comment="置信度评分 0-1")
    importance_score = Column(DECIMAL(3, 2), default=0.50, comment="重要性评分 0-1")
    final_score = Column(
        DECIMAL(3, 2),
        default=0.83,
        comment="综合评分 = relevance_score * 0.5 + confidence_score * 0.3 + importance_score * 0.2"
    )

    # 来源信息
    source = Column(String(50), default="ai", comment="标签来源：ai/manual/rule/system")
    ai_tag_match_id = Column(
        BigInteger,
        ForeignKey("ai_tag_matches.id", ondelete="SET NULL"),
        comment="关联AI标签匹配记录"
    )
    extractor_name = Column(String(100), comment="提取器名称")
    extractor_version = Column(String(20), comment="提取器版本")

    # 位置和上下文
    position_start = Column(Integer, comment="起始位置")
    position_end = Column(Integer, comment="结束位置")
    context = Column(Text, comment="标签上下文")
    mention_count = Column(Integer, default=1, comment="提及次数")

    # 验证状态
    is_verified = Column(Boolean, default=False, comment="是否已验证")
    verified_by = Column(String(100), comment="验证人")
    verified_at = Column(TIMESTAMP, comment="验证时间")

    created_at = Column(TIMESTAMP, server_default=func.now(), comment="创建时间")

    # 索引
    __table_args__ = (
        Index('idx_unified_content_tags_content', 'content_type', 'content_id'),
        Index('idx_unified_content_tags_tag', 'tag_id'),
        Index('idx_unified_content_tags_final_score', 'final_score'),
        Index('idx_unified_content_tags_source', 'source'),
        # 唯一约束：同一内容的同一标签只能有一条记录
        Index('idx_unified_content_tags_unique', 'content_type', 'content_id', 'tag_id', unique=True),
    )


class UnifiedContentClassifications(Base):
    """
    统一内容分类关联表
    支持所有业务表的分类关联
    """
    __tablename__ = "unified_content_classifications"

    id = Column(BigInteger, primary_key=True, index=True, comment="统一内容分类关联唯一标识符")

    # 内容标识
    content_type = Column(
        SQLEnum(BusinessDataType, values_callable=lambda obj: [e.value for e in obj]),
        nullable=False,
        comment="内容类型"
    )
    content_id = Column(BigInteger, nullable=False, comment="内容ID")
    dimension_id = Column(
        BigInteger,
        ForeignKey("classification_dimensions.id", ondelete="CASCADE"),
        nullable=False,
        comment="分类维度ID"
    )
    value_id = Column(
        BigInteger,
        ForeignKey("classification_values.id", ondelete="CASCADE"),
        nullable=False,
        comment="分类值ID"
    )

    confidence_score = Column(DECIMAL(3, 2), default=1.00, comment="置信度评分")
    source = Column(String(50), default="ai", comment="分类来源：ai/manual/rule/system")

    created_at = Column(TIMESTAMP, server_default=func.now(), comment="创建时间")

    # 索引
    __table_args__ = (
        Index('idx_unified_content_classifications_content', 'content_type', 'content_id'),
        Index('idx_unified_content_classifications_dimension', 'dimension_id', 'value_id'),
        Index('idx_unified_content_classifications_source', 'source'),
        # 唯一约束：同一内容的同一维度同一值只能有一条记录
        Index('idx_unified_content_classifications_unique', 'content_type', 'content_id', 'dimension_id', 'value_id', unique=True),
    )
