"""
数据处理调度器模块
负责管理数据处理任务的调度和执行
参考数据采集服务的任务调度模式
"""

import asyncio
from dataclasses import dataclass
from datetime import datetime, timezone
from typing import Dict, Optional

from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.interval import IntervalTrigger
from loguru import logger

from ...core.database import SessionLocal
from ..data_collection_service.models import RawDataRecord
from .models import DataProcessingStatus
from .engine import DataProcessingEngine
from .task_manager import DataProcessingTaskManager


@dataclass
class ScheduledProcessingJob:
    """调度处理任务信息"""

    job_id: str
    job_name: str
    schedule_type: str  # interval, cron, once
    schedule_config: dict
    next_run_time: Optional[datetime] = None
    last_run_time: Optional[datetime] = None
    run_count: int = 0
    is_active: bool = True


class DataProcessingScheduler:
    """
    数据处理调度器
    负责管理数据处理任务的定时调度和执行
    """

    def __init__(self):
        """
        初始化数据处理调度器
        """
        self.scheduler = AsyncIOScheduler()
        self.scheduled_jobs: Dict[str, ScheduledProcessingJob] = {}
        self.is_running = False
        self.start_time: Optional[datetime] = None
        
        # 处理引擎和任务管理器
        self.processing_engine = DataProcessingEngine()
        self.task_manager = DataProcessingTaskManager()

        # 配置调度器
        self._configure_scheduler()

    def _configure_scheduler(self):
        """
        配置调度器参数
        """
        self.scheduler.configure(
            timezone="UTC",
            job_defaults={
                "coalesce": True,  # 合并错过的任务
                "max_instances": 1,  # 同一任务最多运行1个实例
                "misfire_grace_time": 300,  # 错过任务的宽限时间（秒）
            },
        )

    async def start(self):
        """
        启动调度器
        """
        if self.is_running:
            logger.warning("数据处理调度器已在运行")
            return

        self.is_running = True
        self.start_time = datetime.now(timezone.utc)
        self.scheduler.start()

        # 添加默认调度任务
        await self._setup_default_schedules()

        # 启动监控任务
        self._schedule_health_check()

        logger.info("数据处理调度器已启动")

    async def stop(self):
        """
        停止调度器
        """
        if not self.is_running:
            return

        logger.info("停止数据处理调度器...")
        self.is_running = False

        # 停止调度器
        self.scheduler.shutdown(wait=True)

        # 清理调度任务
        self.scheduled_jobs.clear()

        logger.info("数据处理调度器已停止")

    async def _setup_default_schedules(self):
        """
        设置默认的调度任务
        """
        # 1. 批量处理待处理数据 - 每5分钟执行一次（避免重复处理）
        await self.add_processing_schedule(
            job_id="batch_processing",
            job_name="批量处理待处理数据",
            schedule_type="interval",
            schedule_config={"minutes": 5},  # 改为5分钟，给处理足够时间
            func=self._execute_batch_processing
        )

        # 2. 清理旧的处理状态 - 每小时执行一次
        await self.add_processing_schedule(
            job_id="cleanup_old_status",
            job_name="清理旧的处理状态",
            schedule_type="interval",
            schedule_config={"hours": 1},
            func=self._execute_cleanup_old_status
        )

        # 3. 处理失败重试 - 每5分钟执行一次
        await self.add_processing_schedule(
            job_id="retry_failed_processing",
            job_name="重试失败的处理任务",
            schedule_type="interval",
            schedule_config={"minutes": 5},
            func=self._execute_retry_failed_processing
        )

        logger.info("默认调度任务已设置")

    async def add_processing_schedule(
        self,
        job_id: str,
        job_name: str,
        schedule_type: str,
        schedule_config: dict,
        func,
        **kwargs
    ) -> bool:
        """
        添加数据处理调度任务

        Args:
            job_id: 任务ID
            job_name: 任务名称
            schedule_type: 调度类型 (interval, cron)
            schedule_config: 调度配置
            func: 执行函数
            **kwargs: 其他参数

        Returns:
            bool: 是否成功添加
        """
        try:
            # 检查是否已存在
            if job_id in self.scheduled_jobs:
                logger.warning(f"调度任务已存在: {job_name}")
                return False

            # 创建触发器
            trigger = self._create_trigger(schedule_type, schedule_config)
            if not trigger:
                logger.error(f"无法为任务创建触发器: {job_name}")
                return False

            # 添加调度任务
            self.scheduler.add_job(
                func=func,
                trigger=trigger,
                id=job_id,
                name=job_name,
                replace_existing=True,
                **kwargs
            )

            # 记录调度任务信息
            scheduled_job = ScheduledProcessingJob(
                job_id=job_id,
                job_name=job_name,
                schedule_type=schedule_type,
                schedule_config=schedule_config,
                is_active=True
            )
            self.scheduled_jobs[job_id] = scheduled_job

            logger.info(f"已添加调度任务: {job_name}")
            return True

        except Exception as e:
            logger.error(f"添加调度任务失败: {job_name}, 错误: {e}")
            return False

    def _create_trigger(self, schedule_type: str, schedule_config: dict):
        """
        创建调度触发器

        Args:
            schedule_type: 调度类型
            schedule_config: 调度配置

        Returns:
            触发器实例
        """
        try:
            if schedule_type == "interval":
                return IntervalTrigger(**schedule_config)
            else:
                logger.error(f"不支持的调度类型: {schedule_type}")
                return None

        except Exception as e:
            logger.error(f"创建触发器失败: {e}")
            return None

    async def _execute_batch_processing(self):
        """
        执行批量处理任务
        """
        try:
            logger.debug("开始执行批量处理任务...")

            db = SessionLocal()
            try:
                # 查询待处理数据
                pending_records = db.query(RawDataRecord).filter(
                    RawDataRecord.processing_status == 'pending'
                ).limit(20).all()

                if not pending_records:
                    logger.debug("没有待处理的数据")
                    return

                logger.info(f"发现 {len(pending_records)} 条待处理数据，开始批量处理...")

                # 提交批量处理任务
                task_id = await self.task_manager.submit_batch_processing_task(
                    batch_size=len(pending_records)
                )

                if task_id:
                    logger.info(f"批量处理任务已提交: {task_id}")
                    # 更新调度任务统计
                    if "batch_processing" in self.scheduled_jobs:
                        self.scheduled_jobs["batch_processing"].run_count += 1
                        self.scheduled_jobs["batch_processing"].last_run_time = datetime.now(timezone.utc)
                else:
                    logger.error("批量处理任务提交失败")

            finally:
                db.close()

        except Exception as e:
            logger.error(f"执行批量处理任务失败: {e}")

    async def _execute_cleanup_old_status(self):
        """
        执行清理旧状态任务
        """
        try:
            logger.debug("开始执行清理旧状态任务...")

            # 提交清理任务
            task_id = await self.task_manager.submit_cleanup_task()

            if task_id:
                logger.info(f"清理任务已提交: {task_id}")
                # 更新调度任务统计
                if "cleanup_old_status" in self.scheduled_jobs:
                    self.scheduled_jobs["cleanup_old_status"].run_count += 1
                    self.scheduled_jobs["cleanup_old_status"].last_run_time = datetime.now(timezone.utc)
            else:
                logger.error("清理任务提交失败")

        except Exception as e:
            logger.error(f"执行清理任务失败: {e}")

    async def _execute_retry_failed_processing(self):
        """
        执行重试失败处理任务
        """
        try:
            logger.debug("开始执行重试失败处理任务...")

            db = SessionLocal()
            try:
                # 查询失败的处理状态
                failed_statuses = db.query(DataProcessingStatus).filter(
                    DataProcessingStatus.processing_stage == 'failed',
                    DataProcessingStatus.retry_count < 3
                ).limit(10).all()

                if not failed_statuses:
                    logger.debug("没有需要重试的失败任务")
                    return

                logger.info(f"发现 {len(failed_statuses)} 个失败任务需要重试...")

                # 提交重试任务
                for status in failed_statuses:
                    task_id = await self.task_manager.submit_retry_task(status.record_id)
                    if task_id:
                        logger.info(f"重试任务已提交: 记录ID={status.record_id}, 任务ID={task_id}")

                # 更新调度任务统计
                if "retry_failed_processing" in self.scheduled_jobs:
                    self.scheduled_jobs["retry_failed_processing"].run_count += 1
                    self.scheduled_jobs["retry_failed_processing"].last_run_time = datetime.now(timezone.utc)

            finally:
                db.close()

        except Exception as e:
            logger.error(f"执行重试失败处理任务失败: {e}")

    def _schedule_health_check(self):
        """
        调度健康检查任务
        """
        self.scheduler.add_job(
            func=self._health_check,
            trigger=IntervalTrigger(minutes=5),
            id="health_check",
            name="调度器健康检查",
            replace_existing=True,
        )

    async def _health_check(self):
        """
        健康检查
        """
        try:
            if not self.is_running:
                return

            # 检查调度器状态
            running_jobs = len(self.scheduler.get_jobs())
            logger.debug(f"调度器健康检查: 运行中任务={running_jobs}")

            # 更新调度任务的下次运行时间
            for job_id, scheduled_job in self.scheduled_jobs.items():
                job = self.scheduler.get_job(job_id)
                if job:
                    scheduled_job.next_run_time = job.next_run_time

        except Exception as e:
            logger.error(f"健康检查失败: {e}")

    def get_scheduler_status(self) -> dict:
        """
        获取调度器状态信息
        """
        return {
            "is_running": self.is_running,
            "start_time": self.start_time.isoformat() if self.start_time else None,
            "uptime_seconds": (
                (datetime.now(timezone.utc) - self.start_time).total_seconds()
                if self.start_time else 0
            ),
            "scheduled_jobs_count": len(self.scheduled_jobs),
            "scheduled_jobs": {
                job_id: {
                    "job_name": job.job_name,
                    "schedule_type": job.schedule_type,
                    "is_active": job.is_active,
                    "run_count": job.run_count,
                    "last_run_time": job.last_run_time.isoformat() if job.last_run_time else None,
                    "next_run_time": job.next_run_time.isoformat() if job.next_run_time else None,
                }
                for job_id, job in self.scheduled_jobs.items()
            }
        }


# 全局调度器实例
_scheduler_instance = None


def get_data_processing_scheduler() -> DataProcessingScheduler:
    """
    获取全局数据处理调度器实例
    """
    global _scheduler_instance
    if _scheduler_instance is None:
        _scheduler_instance = DataProcessingScheduler()
    return _scheduler_instance


async def start_data_processing_scheduler():
    """
    启动全局数据处理调度器
    """
    scheduler = get_data_processing_scheduler()
    await scheduler.start()


async def stop_data_processing_scheduler():
    """
    停止全局数据处理调度器
    """
    scheduler = get_data_processing_scheduler()
    await scheduler.stop()
