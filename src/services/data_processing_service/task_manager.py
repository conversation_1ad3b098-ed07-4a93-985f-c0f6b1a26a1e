"""
数据处理任务管理器
提供任务提交、监控和管理功能
"""

import logging
from datetime import datetime, timezone
from typing import Dict, Any, List, Optional
from enum import Enum

from celery.result import AsyncResult
from sqlalchemy.orm import Session

from ...core.celery_app import TaskPriority
from ...core.database import SessionLocal
from ..data_collection_service.models import RawDataRecord
from .models import DataProcessingStatus
from .tasks import (
    submit_processing_task, submit_batch_processing_task, submit_retry_task,
    process_records_by_source
)

logger = logging.getLogger(__name__)


class TaskType(str, Enum):
    """任务类型"""
    SINGLE_RECORD = "single_record"
    BATCH_PROCESSING = "batch_processing"
    RETRY_FAILED = "retry_failed"
    SOURCE_PROCESSING = "source_processing"
    CLEANUP = "cleanup"


class DataProcessingTaskManager:
    """数据处理任务管理器"""
    
    def __init__(self):
        pass
        
    async def submit_single_record_task(
        self,
        record_id: int,
        priority: int = TaskPriority.NORMAL
    ) -> Optional[str]:
        """
        提交单个记录处理任务
        
        Args:
            record_id: 记录ID
            priority: 任务优先级
            
        Returns:
            Dict: 任务信息
        """
        
        try:
            task_id = submit_processing_task(record_id, priority)

            logger.info(f"提交单记录处理任务: record_id={record_id}, task_id={task_id}")
            return task_id

        except Exception as e:
            logger.error(f"提交单记录处理任务失败: {e}")
            return None
    
    async def submit_batch_processing_task(
        self,
        batch_size: int = 10
    ) -> Optional[str]:
        """
        提交批量处理任务

        Args:
            batch_size: 批处理大小

        Returns:
            Optional[str]: 任务ID，如果提交失败则返回None
        """

        try:
            task_id = submit_batch_processing_task(batch_size)

            logger.info(f"提交批量处理任务: batch_size={batch_size}, task_id={task_id}")
            return task_id

        except Exception as e:
            logger.error(f"提交批量处理任务失败: {e}")
            return None
    
    async def submit_retry_task(
        self,
        record_id: int
    ) -> Optional[str]:
        """
        提交重试任务

        Args:
            record_id: 需要重试的记录ID

        Returns:
            Optional[str]: 任务ID，如果提交失败则返回None
        """

        try:
            task_id = submit_retry_task(record_id)

            logger.info(f"提交重试任务: record_id={record_id}, task_id={task_id}")
            return task_id

        except Exception as e:
            logger.error(f"提交重试任务失败: {e}")
            return None

    async def submit_cleanup_task(self) -> Optional[str]:
        """
        提交清理任务

        Returns:
            Optional[str]: 任务ID，如果提交失败则返回None
        """

        try:
            from .tasks import cleanup_old_processing_status

            # 使用Celery的apply_async方法提交任务
            task = cleanup_old_processing_status.apply_async(
                queue='data_processing'
            )
            task_id = task.id

            logger.info(f"提交清理任务: task_id={task_id}")
            return task_id

        except Exception as e:
            logger.error(f"提交清理任务失败: {e}")
            return None
    
    def submit_source_processing_task(
        self, 
        source_id: int, 
        limit: int = 50
    ) -> Dict[str, Any]:
        """
        提交按数据源处理任务
        
        Args:
            source_id: 数据源ID
            limit: 处理限制
            
        Returns:
            Dict: 任务信息
        """
        
        try:
            task = process_records_by_source.apply_async(
                args=[source_id, limit],
                queue='data_processing'
            )
            
            logger.info(f"提交数据源处理任务: source_id={source_id}, task_id={task.id}")
            
            return {
                "task_id": task.id,
                "task_type": TaskType.SOURCE_PROCESSING,
                "source_id": source_id,
                "limit": limit,
                "submitted_at": datetime.now(timezone.utc).isoformat(),
                "status": TaskStatus.PENDING
            }
            
        except Exception as e:
            logger.error(f"提交数据源处理任务失败: {e}")
            raise
    
    def get_task_status(self, task_id: str) -> Dict[str, Any]:
        """
        获取任务状态
        
        Args:
            task_id: 任务ID
            
        Returns:
            Dict: 任务状态信息
        """
        
        try:
            result = get_task_result(task_id)
            
            status_info = {
                "task_id": task_id,
                "status": result.status,
                "result": result.result if result.successful() else None,
                "error": str(result.result) if result.failed() else None,
                "traceback": result.traceback if result.failed() else None,
                "meta": result.info if hasattr(result, 'info') else None
            }
            
            # 添加时间信息
            if hasattr(result, 'date_done') and result.date_done:
                status_info["completed_at"] = result.date_done.isoformat()
            
            return status_info
            
        except Exception as e:
            logger.error(f"获取任务状态失败: {e}")
            return {
                "task_id": task_id,
                "status": "UNKNOWN",
                "error": str(e)
            }
    
    def cancel_task(self, task_id: str, terminate: bool = False) -> bool:
        """
        取消任务
        
        Args:
            task_id: 任务ID
            terminate: 是否终止正在运行的任务
            
        Returns:
            bool: 是否成功取消
        """
        
        try:
            revoke_task(task_id, terminate=terminate)
            logger.info(f"取消任务: task_id={task_id}, terminate={terminate}")
            return True
            
        except Exception as e:
            logger.error(f"取消任务失败: {e}")
            return False
    
    def get_active_tasks(self) -> Dict[str, Any]:
        """
        获取活跃任务列表
        
        Returns:
            Dict: 活跃任务信息
        """
        
        try:
            active_tasks = get_active_tasks()
            return {
                "active_tasks": active_tasks,
                "count": sum(len(tasks) for tasks in active_tasks.values()) if active_tasks else 0,
                "workers": list(active_tasks.keys()) if active_tasks else []
            }
            
        except Exception as e:
            logger.error(f"获取活跃任务失败: {e}")
            return {"active_tasks": {}, "count": 0, "workers": []}
    
    def get_scheduled_tasks(self) -> Dict[str, Any]:
        """
        获取计划任务列表
        
        Returns:
            Dict: 计划任务信息
        """
        
        try:
            scheduled_tasks = get_scheduled_tasks()
            return {
                "scheduled_tasks": scheduled_tasks,
                "count": sum(len(tasks) for tasks in scheduled_tasks.values()) if scheduled_tasks else 0,
                "workers": list(scheduled_tasks.keys()) if scheduled_tasks else []
            }
            
        except Exception as e:
            logger.error(f"获取计划任务失败: {e}")
            return {"scheduled_tasks": {}, "count": 0, "workers": []}
    
    def get_processing_statistics(self) -> Dict[str, Any]:
        """
        获取处理统计信息
        
        Returns:
            Dict: 统计信息
        """
        
        db = SessionLocal()
        
        try:
            # 统计处理状态
            from sqlalchemy import func
            from .models import ProcessingStage, ProcessingResult
            
            # 按阶段统计
            stage_stats = db.query(
                DataProcessingStatus.processing_stage,
                func.count(DataProcessingStatus.id)
            ).group_by(DataProcessingStatus.processing_stage).all()
            
            # 按结果统计
            result_stats = db.query(
                DataProcessingStatus.processing_result,
                func.count(DataProcessingStatus.id)
            ).filter(
                DataProcessingStatus.processing_result.isnot(None)
            ).group_by(DataProcessingStatus.processing_result).all()
            
            # 待处理记录数
            pending_count = db.query(RawDataRecord).filter(
                RawDataRecord.processing_status == "pending"
            ).count()
            
            return {
                "stage_statistics": {stage.value: count for stage, count in stage_stats},
                "result_statistics": {result.value: count for result, count in result_stats},
                "pending_records": pending_count,
                "generated_at": datetime.now(timezone.utc).isoformat()
            }
            
        except Exception as e:
            logger.error(f"获取处理统计失败: {e}")
            return {"error": str(e)}
            
        finally:
            db.close()
    
    def submit_multiple_records(
        self, 
        record_ids: List[int], 
        priority: int = TaskPriority.NORMAL
    ) -> List[Dict[str, Any]]:
        """
        提交多个记录处理任务
        
        Args:
            record_ids: 记录ID列表
            priority: 任务优先级
            
        Returns:
            List[Dict]: 任务信息列表
        """
        
        tasks = []
        
        for record_id in record_ids:
            try:
                task_info = self.submit_single_record_task(record_id, priority)
                tasks.append(task_info)
                
            except Exception as e:
                logger.error(f"提交记录 {record_id} 处理任务失败: {e}")
                tasks.append({
                    "record_id": record_id,
                    "error": str(e),
                    "status": "FAILED_TO_SUBMIT"
                })
        
        return tasks
    
    def get_task_history(
        self, 
        limit: int = 100, 
        task_type: Optional[TaskType] = None
    ) -> List[Dict[str, Any]]:
        """
        获取任务历史
        
        Args:
            limit: 返回数量限制
            task_type: 任务类型过滤
            
        Returns:
            List[Dict]: 任务历史列表
        """
        
        # 这里可以实现任务历史记录功能
        # 需要在数据库中存储任务历史信息
        
        # 暂时返回空列表，后续可以扩展
        return []


# 全局任务管理器实例
task_manager = DataProcessingTaskManager()


def get_task_manager() -> DataProcessingTaskManager:
    """获取任务管理器实例"""
    return task_manager
