"""
数据处理服务业务逻辑模块
包含数据处理管道的管理、智能匹配和操作功能
"""

import logging
import re
from decimal import Decimal
from datetime import datetime, timezone, date
from typing import Any, Dict, List, Optional, Tuple
from urllib.parse import urlparse

from sqlalchemy import and_, desc, func, or_
from sqlalchemy.exc import IntegrityError
from sqlalchemy.orm import Session

from .models import DataProcessingPipeline, DataProcessingStatus, ProcessingStage, ProcessingResult
from .schemas import (
    DataProcessingPipelineCreate,
    DataProcessingPipelineUpdate,
    DataProcessingPipelineFilter,
    BatchPipelineStatusUpdate,
    BatchPipelinePriorityUpdate,
    BatchOperationResponse,
    BusinessDataType,
)
from ..data_collection_service.models import DataSource, RawDataRecord


class ProcessingPipelineNotFoundError(Exception):
    """处理管道未找到异常"""
    pass


class DataProcessingPipelineService:
    """
    数据处理管道服务类
    提供统一的数据处理管道管理和智能匹配功能
    """

    def __init__(self, db_session: Session = None):
        """
        初始化数据处理管道服务

        Args:
            db_session: 数据库会话，可选参数
        """
        self.db = db_session
        self.logger = logging.getLogger(__name__)

    def create_pipeline(
        self,
        db: Session,
        pipeline_data: DataProcessingPipelineCreate
    ) -> DataProcessingPipeline:
        """
        创建数据处理管道

        Args:
            db: 数据库会话
            pipeline_data: 管道创建数据

        Returns:
            DataProcessingPipeline: 创建的管道实例

        Raises:
            IntegrityError: 管道代码和版本已存在
        """
        try:
            # 检查管道代码和版本是否已存在
            existing_pipeline = db.query(DataProcessingPipeline).filter(
                and_(
                    DataProcessingPipeline.pipeline_code == pipeline_data.pipeline_code,
                    DataProcessingPipeline.version == pipeline_data.version
                )
            ).first()

            if existing_pipeline:
                raise IntegrityError(
                    f"管道代码 '{pipeline_data.pipeline_code}' 版本 {pipeline_data.version} 已存在",
                    None, None
                )

            # 创建管道实例
            db_pipeline = DataProcessingPipeline(**pipeline_data.model_dump())
            
            db.add(db_pipeline)
            db.commit()
            db.refresh(db_pipeline)

            self.logger.info(f"成功创建处理管道: {db_pipeline.pipeline_code} v{db_pipeline.version}")
            return db_pipeline

        except IntegrityError as e:
            db.rollback()
            self.logger.error(f"创建管道失败，违反唯一约束: {e}")
            raise e
        except Exception as e:
            db.rollback()
            self.logger.error(f"创建管道时发生未知错误: {e}")
            raise e

    def get_pipeline_by_id(self, db: Session, pipeline_id: int) -> Optional[DataProcessingPipeline]:
        """
        根据ID获取管道

        Args:
            db: 数据库会话
            pipeline_id: 管道ID

        Returns:
            Optional[DataProcessingPipeline]: 管道实例或None
        """
        pipeline = db.query(DataProcessingPipeline).filter(
            DataProcessingPipeline.id == pipeline_id
        ).first()

        if pipeline:
            self._fix_pipeline_null_values(pipeline)

        return pipeline

    def get_pipeline_by_code_and_version(
        self, 
        db: Session, 
        pipeline_code: str, 
        version: int = None
    ) -> Optional[DataProcessingPipeline]:
        """
        根据管道代码和版本获取管道

        Args:
            db: 数据库会话
            pipeline_code: 管道代码
            version: 版本号，如果为None则获取最新活跃版本

        Returns:
            Optional[DataProcessingPipeline]: 管道实例或None
        """
        query = db.query(DataProcessingPipeline).filter(
            DataProcessingPipeline.pipeline_code == pipeline_code
        )

        if version is not None:
            query = query.filter(DataProcessingPipeline.version == version)
        else:
            # 获取最新活跃版本
            query = query.filter(DataProcessingPipeline.is_active == True).order_by(
                desc(DataProcessingPipeline.version)
            )

        return query.first()

    def find_processing_pipeline(
        self, 
        db: Session, 
        raw_data_record: RawDataRecord
    ) -> DataProcessingPipeline:
        """
        智能管道匹配算法
        根据多重条件自动选择最适合的处理管道

        Args:
            db: 数据库会话
            raw_data_record: 原始数据记录

        Returns:
            DataProcessingPipeline: 匹配的处理管道

        Raises:
            ProcessingPipelineNotFoundError: 未找到适用的管道
        """
        source_id = raw_data_record.source_id
        url = raw_data_record.source_url
        domain = self._extract_domain(url)
        business_type = self._get_business_type_from_source(db, source_id)

        # 1. 精确数据源匹配（最高优先级）
        pipeline = db.query(DataProcessingPipeline).filter(
            and_(
                DataProcessingPipeline.source_id == source_id,
                DataProcessingPipeline.is_active == True
            )
        ).order_by(desc(DataProcessingPipeline.priority)).first()

        if pipeline:
            self.logger.info(f"使用数据源专用管道: {pipeline.pipeline_code}")
            return pipeline

        # 2. URL模式匹配
        url_pipelines = db.query(DataProcessingPipeline).filter(
            and_(
                DataProcessingPipeline.url_pattern.isnot(None),
                DataProcessingPipeline.is_active == True,
                DataProcessingPipeline.business_data_type == business_type
            )
        ).order_by(desc(DataProcessingPipeline.priority)).all()

        for pipeline in url_pipelines:
            try:
                if re.match(pipeline.url_pattern, url):
                    self.logger.info(f"使用URL模式匹配管道: {pipeline.pipeline_code}")
                    return pipeline
            except re.error:
                self.logger.warning(f"管道 {pipeline.pipeline_code} 的URL模式无效: {pipeline.url_pattern}")
                continue

        # 3. 域名匹配
        pipeline = db.query(DataProcessingPipeline).filter(
            and_(
                DataProcessingPipeline.domain_pattern == domain,
                DataProcessingPipeline.is_active == True,
                DataProcessingPipeline.business_data_type == business_type
            )
        ).order_by(desc(DataProcessingPipeline.priority)).first()

        if pipeline:
            self.logger.info(f"使用域名匹配管道: {pipeline.pipeline_code}")
            return pipeline

        # 4. 默认管道（最后选择）
        pipeline = db.query(DataProcessingPipeline).filter(
            and_(
                DataProcessingPipeline.business_data_type == business_type,
                DataProcessingPipeline.is_default == True,
                DataProcessingPipeline.is_active == True
            )
        ).first()

        if pipeline:
            self.logger.info(f"使用默认管道: {pipeline.pipeline_code}")
            return pipeline

        raise ProcessingPipelineNotFoundError(
            f"未找到适用于业务类型 {business_type} 的处理管道"
        )

    def update_pipeline(
        self,
        db: Session,
        pipeline_id: int,
        pipeline_data: DataProcessingPipelineUpdate
    ) -> Optional[DataProcessingPipeline]:
        """
        更新处理管道

        Args:
            db: 数据库会话
            pipeline_id: 管道ID
            pipeline_data: 更新数据

        Returns:
            Optional[DataProcessingPipeline]: 更新后的管道实例或None
        """
        try:
            db_pipeline = self.get_pipeline_by_id(db, pipeline_id)
            if not db_pipeline:
                return None

            # 更新字段
            update_data = pipeline_data.model_dump(exclude_unset=True)
            for field, value in update_data.items():
                setattr(db_pipeline, field, value)

            # 更新时间戳
            db_pipeline.updated_at = datetime.now(timezone.utc)

            db.commit()
            db.refresh(db_pipeline)

            self.logger.info(f"成功更新处理管道: {db_pipeline.pipeline_code}")
            return db_pipeline

        except Exception as e:
            db.rollback()
            self.logger.error(f"更新管道时发生错误: {e}")
            raise e

    def delete_pipeline(self, db: Session, pipeline_id: int) -> bool:
        """
        删除处理管道

        Args:
            db: 数据库会话
            pipeline_id: 管道ID

        Returns:
            bool: 删除是否成功
        """
        try:
            db_pipeline = self.get_pipeline_by_id(db, pipeline_id)
            if not db_pipeline:
                return False

            db.delete(db_pipeline)
            db.commit()

            self.logger.info(f"成功删除处理管道: {db_pipeline.pipeline_code}")
            return True

        except Exception as e:
            db.rollback()
            self.logger.error(f"删除管道时发生错误: {e}")
            raise e

    def get_pipelines(
        self,
        db: Session,
        filter_params: Optional[DataProcessingPipelineFilter] = None,
        page: int = 1,
        size: int = 50
    ) -> Tuple[List[DataProcessingPipeline], int]:
        """
        获取管道列表

        Args:
            db: 数据库会话
            filter_params: 过滤参数
            page: 页码
            size: 每页大小

        Returns:
            Tuple[List[DataProcessingPipeline], int]: 管道列表和总数
        """
        query = db.query(DataProcessingPipeline)

        # 应用过滤条件
        if filter_params:
            if filter_params.pipeline_code:
                query = query.filter(
                    DataProcessingPipeline.pipeline_code.ilike(f"%{filter_params.pipeline_code}%")
                )
            if filter_params.business_data_type:
                query = query.filter(
                    DataProcessingPipeline.business_data_type == filter_params.business_data_type
                )
            if filter_params.source_id:
                query = query.filter(DataProcessingPipeline.source_id == filter_params.source_id)
            if filter_params.is_active is not None:
                query = query.filter(DataProcessingPipeline.is_active == filter_params.is_active)
            if filter_params.is_default is not None:
                query = query.filter(DataProcessingPipeline.is_default == filter_params.is_default)
            if filter_params.domain_pattern:
                query = query.filter(
                    DataProcessingPipeline.domain_pattern.ilike(f"%{filter_params.domain_pattern}%")
                )
            if filter_params.priority_min:
                query = query.filter(DataProcessingPipeline.priority >= filter_params.priority_min)
            if filter_params.priority_max:
                query = query.filter(DataProcessingPipeline.priority <= filter_params.priority_max)
            if filter_params.created_by:
                query = query.filter(
                    DataProcessingPipeline.created_by.ilike(f"%{filter_params.created_by}%")
                )

        # 获取总数
        total = query.count()

        # 分页和排序
        pipelines = query.order_by(
            desc(DataProcessingPipeline.priority),
            desc(DataProcessingPipeline.updated_at)
        ).offset((page - 1) * size).limit(size).all()

        # 处理NULL值
        for pipeline in pipelines:
            self._fix_pipeline_null_values(pipeline)

        return pipelines, total

    def _fix_pipeline_null_values(self, pipeline: DataProcessingPipeline):
        """修复管道对象中的NULL值"""
        if pipeline.required_fields is None:
            pipeline.required_fields = []
        if pipeline.execution_order is None:
            pipeline.execution_order = 1
        if pipeline.execution_count is None:
            pipeline.execution_count = 0
        if pipeline.success_count is None:
            pipeline.success_count = 0
        if pipeline.failure_count is None:
            pipeline.failure_count = 0
        if pipeline.field_mapping is None:
            pipeline.field_mapping = {}
        if pipeline.data_extraction_config is None:
            pipeline.data_extraction_config = {}
        if pipeline.data_transformation_config is None:
            pipeline.data_transformation_config = {}
        if pipeline.data_validation_config is None:
            pipeline.data_validation_config = {}
        if pipeline.data_enrichment_config is None:
            pipeline.data_enrichment_config = {}

    def batch_update_status(
        self,
        db: Session,
        batch_update: BatchPipelineStatusUpdate
    ) -> BatchOperationResponse:
        """
        批量更新管道状态

        Args:
            db: 数据库会话
            batch_update: 批量更新数据

        Returns:
            BatchOperationResponse: 批量操作结果
        """
        success_ids = []
        failure_details = []

        for pipeline_id in batch_update.pipeline_ids:
            try:
                pipeline = self.get_pipeline_by_id(db, pipeline_id)
                if pipeline:
                    pipeline.is_active = batch_update.is_active
                    if batch_update.updated_by:
                        pipeline.updated_by = batch_update.updated_by
                    pipeline.updated_at = datetime.now(timezone.utc)
                    success_ids.append(pipeline_id)
                else:
                    failure_details.append({
                        "id": pipeline_id,
                        "error": "管道不存在"
                    })
            except Exception as e:
                failure_details.append({
                    "id": pipeline_id,
                    "error": str(e)
                })

        try:
            db.commit()
        except Exception as e:
            db.rollback()
            self.logger.error(f"批量更新状态时发生错误: {e}")
            raise e

        return BatchOperationResponse(
            success_count=len(success_ids),
            failure_count=len(failure_details),
            success_ids=success_ids,
            failure_details=failure_details
        )

    def get_pipeline_stats(self, db: Session) -> Dict[str, Any]:
        """
        获取管道统计信息

        Args:
            db: 数据库会话

        Returns:
            Dict[str, Any]: 统计信息
        """
        # 基础统计
        total_pipelines = db.query(DataProcessingPipeline).count()
        active_pipelines = db.query(DataProcessingPipeline).filter(
            DataProcessingPipeline.is_active == True
        ).count()

        # 版本分布
        version_stats = db.query(
            DataProcessingPipeline.version,
            func.count(DataProcessingPipeline.id).label('count')
        ).group_by(DataProcessingPipeline.version).all()

        version_distribution = {str(version): count for version, count in version_stats}

        # 业务类型分布
        business_type_stats = db.query(
            DataProcessingPipeline.business_data_type,
            func.count(DataProcessingPipeline.id).label('count')
        ).group_by(DataProcessingPipeline.business_data_type).all()

        business_type_distribution = {str(business_type): count for business_type, count in business_type_stats}

        # 执行统计
        execution_stats = db.query(
            func.sum(DataProcessingPipeline.execution_count).label('total_executions'),
            func.sum(DataProcessingPipeline.success_count).label('total_successes'),
            func.sum(DataProcessingPipeline.failure_count).label('total_failures'),
            func.avg(DataProcessingPipeline.avg_execution_time_ms).label('avg_execution_time')
        ).first()

        # 计算执行统计
        total_executions = int(execution_stats.total_executions or 0)
        total_successes = int(execution_stats.total_successes or 0)
        total_failures = int(execution_stats.total_failures or 0)
        avg_execution_time = float(execution_stats.avg_execution_time or 0.0)

        success_rate = 0.0
        if total_executions > 0:
            success_rate = (total_successes / total_executions) * 100

        return {
            "total_pipelines": total_pipelines,
            "active_pipelines": active_pipelines,
            "version_distribution": version_distribution,
            "business_type_distribution": business_type_distribution,
            "execution_stats": {
                "total_executions": total_executions,
                "total_successes": total_successes,
                "total_failures": total_failures,
                "avg_execution_time_ms": avg_execution_time,
                "success_rate": success_rate
            }
        }

    def get_active_pipelines_by_type(
        self, 
        db: Session, 
        business_type: BusinessDataType
    ) -> List[DataProcessingPipeline]:
        """
        获取指定业务类型的活跃管道

        Args:
            db: 数据库会话
            business_type: 业务数据类型

        Returns:
            List[DataProcessingPipeline]: 活跃管道列表，按优先级排序
        """
        return db.query(DataProcessingPipeline).filter(
            and_(
                DataProcessingPipeline.business_data_type == business_type,
                DataProcessingPipeline.is_active == True
            )
        ).order_by(
            desc(DataProcessingPipeline.priority),
            desc(DataProcessingPipeline.updated_at)
        ).all()

    def find_processing_pipeline_by_params(
        self, 
        db: Session, 
        source_id: Optional[int] = None,
        url: Optional[str] = None,
        domain: Optional[str] = None,
        content_type: Optional[str] = None,
        business_data_type: Optional[BusinessDataType] = None
    ) -> Optional[DataProcessingPipeline]:
        """
        根据参数智能匹配处理管道

        Args:
            db: 数据库会话
            source_id: 数据源ID
            url: 内容URL
            domain: 域名
            content_type: 内容类型
            business_data_type: 业务数据类型

        Returns:
            Optional[DataProcessingPipeline]: 匹配的处理管道，未找到时返回None
        """
        # 如果没有提供域名，从URL中提取
        if not domain and url:
            domain = self._extract_domain(url)

        # 如果没有提供业务类型，从数据源获取
        if not business_data_type and source_id:
            business_type_str = self._get_business_type_from_source(db, source_id)
            business_data_type = BusinessDataType(business_type_str)

        # 1. 精确数据源匹配（最高优先级）
        if source_id:
            pipeline = db.query(DataProcessingPipeline).filter(
                and_(
                    DataProcessingPipeline.source_id == source_id,
                    DataProcessingPipeline.is_active == True
                )
            ).order_by(desc(DataProcessingPipeline.priority)).first()

            if pipeline:
                self.logger.info(f"使用数据源专用管道: {pipeline.pipeline_code}")
                return pipeline

        # 2. URL模式匹配
        if url and business_data_type:
            url_pipelines = db.query(DataProcessingPipeline).filter(
                and_(
                    DataProcessingPipeline.url_pattern.isnot(None),
                    DataProcessingPipeline.is_active == True,
                    DataProcessingPipeline.business_data_type == business_data_type
                )
            ).order_by(desc(DataProcessingPipeline.priority)).all()

            for pipeline in url_pipelines:
                try:
                    if re.match(pipeline.url_pattern, url):
                        self.logger.info(f"使用URL模式匹配管道: {pipeline.pipeline_code}")
                        return pipeline
                except re.error:
                    self.logger.warning(f"管道 {pipeline.pipeline_code} 的URL模式无效: {pipeline.url_pattern}")

        # 3. 域名模式匹配
        if domain and business_data_type:
            domain_pipelines = db.query(DataProcessingPipeline).filter(
                and_(
                    DataProcessingPipeline.domain_pattern.isnot(None),
                    DataProcessingPipeline.is_active == True,
                    DataProcessingPipeline.business_data_type == business_data_type
                )
            ).order_by(desc(DataProcessingPipeline.priority)).all()

            for pipeline in domain_pipelines:
                if pipeline.domain_pattern in domain:
                    self.logger.info(f"使用域名模式匹配管道: {pipeline.pipeline_code}")
                    return pipeline

        # 4. 业务类型默认管道
        if business_data_type:
            default_pipeline = db.query(DataProcessingPipeline).filter(
                and_(
                    DataProcessingPipeline.business_data_type == business_data_type,
                    DataProcessingPipeline.is_default == True,
                    DataProcessingPipeline.is_active == True
                )
            ).order_by(desc(DataProcessingPipeline.priority)).first()

            if default_pipeline:
                self.logger.info(f"使用业务类型默认管道: {default_pipeline.pipeline_code}")
                return default_pipeline

        # 5. 通用管道
        general_pipeline = db.query(DataProcessingPipeline).filter(
            and_(
                DataProcessingPipeline.source_id.is_(None),
                DataProcessingPipeline.url_pattern.is_(None),
                DataProcessingPipeline.domain_pattern.is_(None),
                DataProcessingPipeline.is_active == True
            )
        ).order_by(desc(DataProcessingPipeline.priority)).first()

        if general_pipeline:
            self.logger.info(f"使用通用管道: {general_pipeline.pipeline_code}")
            return general_pipeline

        return None

    def create_pipeline_version(
        self,
        db: Session,
        pipeline_id: int,
        pipeline_data: DataProcessingPipelineUpdate
    ) -> DataProcessingPipeline:
        """
        基于现有管道创建新版本

        Args:
            db: 数据库会话
            pipeline_id: 原管道ID
            pipeline_data: 新版本数据

        Returns:
            DataProcessingPipeline: 新版本管道实例
        """
        try:
            # 获取原管道
            original_pipeline = self.get_pipeline_by_id(db, pipeline_id)
            if not original_pipeline:
                raise ValueError(f"原管道不存在: {pipeline_id}")

            # 获取最新版本号
            latest_version = db.query(DataProcessingPipeline).filter(
                DataProcessingPipeline.pipeline_code == original_pipeline.pipeline_code
            ).order_by(desc(DataProcessingPipeline.version)).first()

            new_version_number = latest_version.version + 1

            # 创建新版本
            new_pipeline_data = DataProcessingPipelineCreate(
                pipeline_code=original_pipeline.pipeline_code,
                version=new_version_number,
                pipeline_name=pipeline_data.pipeline_name or original_pipeline.pipeline_name,
                description=pipeline_data.description or original_pipeline.description,
                business_data_type=pipeline_data.business_data_type or original_pipeline.business_data_type,
                source_id=pipeline_data.source_id or original_pipeline.source_id,
                url_pattern=pipeline_data.url_pattern or original_pipeline.url_pattern,
                domain_pattern=pipeline_data.domain_pattern or original_pipeline.domain_pattern,
                content_type_pattern=pipeline_data.content_type_pattern or original_pipeline.content_type_pattern,
                content_pattern=pipeline_data.content_pattern or original_pipeline.content_pattern,
                min_content_length=pipeline_data.min_content_length or original_pipeline.min_content_length,
                max_content_length=pipeline_data.max_content_length or original_pipeline.max_content_length,
                quality_threshold=pipeline_data.quality_threshold or original_pipeline.quality_threshold,
                required_fields=pipeline_data.required_fields or original_pipeline.required_fields,
                field_mapping=pipeline_data.field_mapping or original_pipeline.field_mapping,
                data_extraction_config=pipeline_data.data_extraction_config or original_pipeline.data_extraction_config,
                data_transformation_config=pipeline_data.data_transformation_config or original_pipeline.data_transformation_config,
                data_validation_config=pipeline_data.data_validation_config or original_pipeline.data_validation_config,
                data_enrichment_config=pipeline_data.data_enrichment_config or original_pipeline.data_enrichment_config,
                priority=pipeline_data.priority or original_pipeline.priority,
                execution_order=pipeline_data.execution_order or original_pipeline.execution_order,
                is_active=pipeline_data.is_active if pipeline_data.is_active is not None else original_pipeline.is_active,
                is_default=pipeline_data.is_default if pipeline_data.is_default is not None else original_pipeline.is_default,
                change_description=pipeline_data.change_description,
                parent_version_id=original_pipeline.id,
                effective_date_start=pipeline_data.effective_date_start or original_pipeline.effective_date_start,
                effective_date_end=pipeline_data.effective_date_end or original_pipeline.effective_date_end,
                created_by=pipeline_data.updated_by,  # 使用更新者作为创建者
            )

            return self.create_pipeline(db, new_pipeline_data)

        except Exception as e:
            self.logger.error(f"创建管道版本时发生错误: {e}")
            raise e

    def get_pipeline_versions(
        self,
        db: Session,
        pipeline_id: int
    ) -> List[DataProcessingPipeline]:
        """
        获取管道的所有版本

        Args:
            db: 数据库会话
            pipeline_id: 管道ID

        Returns:
            List[DataProcessingPipeline]: 版本列表，按版本号排序
        """
        original_pipeline = self.get_pipeline_by_id(db, pipeline_id)
        if not original_pipeline:
            return []

        return db.query(DataProcessingPipeline).filter(
            DataProcessingPipeline.pipeline_code == original_pipeline.pipeline_code
        ).order_by(desc(DataProcessingPipeline.version)).all()

    def batch_update_priority(
        self,
        db: Session,
        batch_update: BatchPipelinePriorityUpdate
    ) -> BatchOperationResponse:
        """
        批量更新管道优先级

        Args:
            db: 数据库会话
            batch_update: 批量更新数据

        Returns:
            BatchOperationResponse: 批量操作结果
        """
        success_ids = []
        failure_details = []

        for pipeline_id, new_priority in batch_update.pipeline_priorities.items():
            try:
                pipeline = self.get_pipeline_by_id(db, pipeline_id)
                if pipeline:
                    pipeline.priority = new_priority
                    if batch_update.updated_by:
                        pipeline.updated_by = batch_update.updated_by
                    pipeline.updated_at = datetime.now(timezone.utc)
                    success_ids.append(pipeline_id)
                else:
                    failure_details.append({
                        "id": pipeline_id,
                        "error": "管道不存在"
                    })
            except Exception as e:
                failure_details.append({
                    "id": pipeline_id,
                    "error": str(e)
                })

        try:
            db.commit()
        except Exception as e:
            db.rollback()
            self.logger.error(f"批量更新优先级时发生错误: {e}")
            raise e

        return BatchOperationResponse(
            success_count=len(success_ids),
            failure_count=len(failure_details),
            success_ids=success_ids,
            failure_details=failure_details
        )

    def update_execution_stats(
        self,
        db: Session,
        pipeline_id: int,
        execution_time_ms: int,
        success: bool
    ) -> Optional[DataProcessingPipeline]:
        """
        更新管道执行统计

        Args:
            db: 数据库会话
            pipeline_id: 管道ID
            execution_time_ms: 执行时间（毫秒）
            success: 是否执行成功

        Returns:
            Optional[DataProcessingPipeline]: 更新后的管道实例或None
        """
        try:
            pipeline = self.get_pipeline_by_id(db, pipeline_id)
            if not pipeline:
                return None

            # 更新统计信息
            pipeline.execution_count += 1
            if success:
                pipeline.success_count += 1
            else:
                pipeline.failure_count += 1

            # 更新平均执行时间
            if pipeline.avg_execution_time_ms is None:
                pipeline.avg_execution_time_ms = execution_time_ms
            else:
                pipeline.avg_execution_time_ms = (
                    (pipeline.avg_execution_time_ms * (pipeline.execution_count - 1) + execution_time_ms) 
                    // pipeline.execution_count
                )

            # 更新最后执行时间
            pipeline.last_executed_at = datetime.now(timezone.utc)

            db.commit()
            db.refresh(pipeline)

            self.logger.info(f"更新管道执行统计: {pipeline.pipeline_code}")
            return pipeline

        except Exception as e:
            db.rollback()
            self.logger.error(f"更新执行统计时发生错误: {e}")
            raise e

    def _extract_domain(self, url: str) -> str:
        """
        从URL中提取域名

        Args:
            url: 完整URL

        Returns:
            str: 域名
        """
        try:
            parsed_url = urlparse(url)
            domain = parsed_url.netloc.lower()
            # 移除 www. 前缀
            if domain.startswith('www.'):
                domain = domain[4:]
            return domain
        except Exception:
            return ""

    def _get_business_type_from_source(self, db: Session, source_id: int) -> str:
        """
        从数据源获取业务类型

        Args:
            db: 数据库会话
            source_id: 数据源ID

        Returns:
            str: 业务数据类型
        """
        data_source = db.query(DataSource).filter(DataSource.id == source_id).first()
        if data_source:
            return data_source.business_data_type.value
        return BusinessDataType.NEWS_ARTICLE.value  # 默认类型
