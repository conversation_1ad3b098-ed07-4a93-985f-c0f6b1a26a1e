"""
数据处理性能监控模块
提供性能指标收集、分析和报告功能
"""

import time
import psutil
import threading
from datetime import datetime, timezone, timedelta
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, asdict
from collections import defaultdict, deque

from ...core.logging_config import get_processing_logger, log_performance_metrics


@dataclass
class PerformanceMetrics:
    """性能指标数据类"""
    
    timestamp: datetime
    component: str
    operation: str
    duration_ms: float
    memory_usage_mb: float
    cpu_usage_percent: float
    record_id: Optional[str] = None
    task_id: Optional[str] = None
    success: bool = True
    error_message: Optional[str] = None
    additional_data: Optional[Dict[str, Any]] = None


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, max_metrics_history: int = 10000):
        self.max_metrics_history = max_metrics_history
        self.metrics_history = deque(maxlen=max_metrics_history)
        self.component_stats = defaultdict(list)
        self.operation_stats = defaultdict(list)
        self.lock = threading.Lock()
        self.logger = get_processing_logger("performance_monitor")
        
        # 系统监控
        self.system_metrics = deque(maxlen=1000)
        self.monitoring_active = False
        self.monitor_thread = None
        
    def start_monitoring(self, interval: float = 30.0):
        """
        开始系统监控
        
        Args:
            interval: 监控间隔（秒）
        """
        
        if self.monitoring_active:
            return
        
        self.monitoring_active = True
        self.monitor_thread = threading.Thread(
            target=self._system_monitor_loop,
            args=(interval,),
            daemon=True
        )
        self.monitor_thread.start()
        
        self.logger.info(f"性能监控已启动，间隔: {interval}秒")
    
    def stop_monitoring(self):
        """停止系统监控"""
        
        self.monitoring_active = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        
        self.logger.info("性能监控已停止")
    
    def _system_monitor_loop(self, interval: float):
        """系统监控循环"""
        
        while self.monitoring_active:
            try:
                # 收集系统指标
                cpu_percent = psutil.cpu_percent(interval=1)
                memory = psutil.virtual_memory()
                disk = psutil.disk_usage('/')
                
                system_metric = {
                    'timestamp': datetime.now(timezone.utc),
                    'cpu_percent': cpu_percent,
                    'memory_percent': memory.percent,
                    'memory_available_mb': memory.available / 1024 / 1024,
                    'disk_percent': disk.percent,
                    'disk_free_gb': disk.free / 1024 / 1024 / 1024
                }
                
                with self.lock:
                    self.system_metrics.append(system_metric)
                
                # 记录系统指标日志
                if cpu_percent > 80 or memory.percent > 80:
                    self.logger.warning(
                        f"系统资源使用率较高 - CPU: {cpu_percent:.1f}%, 内存: {memory.percent:.1f}%"
                    )
                
                time.sleep(interval)
                
            except Exception as e:
                self.logger.error(f"系统监控错误: {e}")
                time.sleep(interval)
    
    def record_metric(
        self,
        component: str,
        operation: str,
        duration_ms: float,
        record_id: str = None,
        task_id: str = None,
        success: bool = True,
        error_message: str = None,
        **additional_data
    ):
        """
        记录性能指标
        
        Args:
            component: 组件名称
            operation: 操作名称
            duration_ms: 持续时间（毫秒）
            record_id: 记录ID
            task_id: 任务ID
            success: 是否成功
            error_message: 错误消息
            **additional_data: 额外数据
        """
        
        try:
            # 获取当前系统资源使用情况
            memory_usage = psutil.virtual_memory().percent
            cpu_usage = psutil.cpu_percent()
            
            metric = PerformanceMetrics(
                timestamp=datetime.now(timezone.utc),
                component=component,
                operation=operation,
                duration_ms=duration_ms,
                memory_usage_mb=memory_usage,
                cpu_usage_percent=cpu_usage,
                record_id=record_id,
                task_id=task_id,
                success=success,
                error_message=error_message,
                additional_data=additional_data if additional_data else None
            )
            
            with self.lock:
                self.metrics_history.append(metric)
                self.component_stats[component].append(metric)
                self.operation_stats[operation].append(metric)
            
            # 记录性能日志
            log_performance_metrics(
                self.logger,
                {
                    'component': component,
                    'operation': operation,
                    'duration_ms': duration_ms,
                    'success': success
                },
                record_id=record_id,
                task_id=task_id
            )
            
            # 检查性能异常
            self._check_performance_alerts(metric)
            
        except Exception as e:
            self.logger.error(f"记录性能指标失败: {e}")
    
    def _check_performance_alerts(self, metric: PerformanceMetrics):
        """检查性能告警"""
        
        # 持续时间告警
        if metric.duration_ms > 30000:  # 30秒
            self.logger.warning(
                f"操作耗时过长: {metric.component}.{metric.operation} "
                f"耗时 {metric.duration_ms:.0f}ms",
                extra={'record_id': metric.record_id, 'task_id': metric.task_id}
            )
        
        # 内存使用告警
        if metric.memory_usage_mb > 80:
            self.logger.warning(
                f"内存使用率过高: {metric.memory_usage_mb:.1f}%",
                extra={'record_id': metric.record_id, 'task_id': metric.task_id}
            )
        
        # 失败告警
        if not metric.success:
            self.logger.error(
                f"操作失败: {metric.component}.{metric.operation} "
                f"错误: {metric.error_message}",
                extra={'record_id': metric.record_id, 'task_id': metric.task_id}
            )
    
    def get_component_stats(self, component: str, hours: int = 24) -> Dict[str, Any]:
        """
        获取组件统计信息
        
        Args:
            component: 组件名称
            hours: 统计时间范围（小时）
            
        Returns:
            Dict: 统计信息
        """
        
        cutoff_time = datetime.now(timezone.utc) - timedelta(hours=hours)
        
        with self.lock:
            metrics = [
                m for m in self.component_stats[component]
                if m.timestamp >= cutoff_time
            ]
        
        if not metrics:
            return {"component": component, "metrics_count": 0}
        
        durations = [m.duration_ms for m in metrics]
        success_count = sum(1 for m in metrics if m.success)
        
        return {
            "component": component,
            "metrics_count": len(metrics),
            "success_rate": success_count / len(metrics),
            "avg_duration_ms": sum(durations) / len(durations),
            "min_duration_ms": min(durations),
            "max_duration_ms": max(durations),
            "total_duration_ms": sum(durations),
            "time_range_hours": hours
        }
    
    def get_operation_stats(self, operation: str, hours: int = 24) -> Dict[str, Any]:
        """
        获取操作统计信息
        
        Args:
            operation: 操作名称
            hours: 统计时间范围（小时）
            
        Returns:
            Dict: 统计信息
        """
        
        cutoff_time = datetime.now(timezone.utc) - timedelta(hours=hours)
        
        with self.lock:
            metrics = [
                m for m in self.operation_stats[operation]
                if m.timestamp >= cutoff_time
            ]
        
        if not metrics:
            return {"operation": operation, "metrics_count": 0}
        
        durations = [m.duration_ms for m in metrics]
        success_count = sum(1 for m in metrics if m.success)
        
        return {
            "operation": operation,
            "metrics_count": len(metrics),
            "success_rate": success_count / len(metrics),
            "avg_duration_ms": sum(durations) / len(durations),
            "min_duration_ms": min(durations),
            "max_duration_ms": max(durations),
            "total_duration_ms": sum(durations),
            "time_range_hours": hours
        }


class PerformanceTimer:
    """性能计时器上下文管理器"""
    
    def __init__(
        self,
        monitor,
        component: str,
        operation: str,
        record_id: str = None,
        task_id: str = None,
        **additional_data
    ):
        self.monitor = monitor
        self.component = component
        self.operation = operation
        self.record_id = record_id
        self.task_id = task_id
        self.additional_data = additional_data
        self.start_time = None
        self.success = True
        self.error_message = None
    
    def __enter__(self):
        self.start_time = time.time()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        duration_ms = (time.time() - self.start_time) * 1000
        
        if exc_type is not None:
            self.success = False
            self.error_message = str(exc_val)
        
        self.monitor.record_metric(
            component=self.component,
            operation=self.operation,
            duration_ms=duration_ms,
            record_id=self.record_id,
            task_id=self.task_id,
            success=self.success,
            error_message=self.error_message,
            **self.additional_data
        )


# 全局性能监控器实例
performance_monitor = PerformanceMonitor()


def get_performance_monitor():
    """获取性能监控器实例"""
    return performance_monitor


def create_timer(
    component: str,
    operation: str,
    record_id: str = None,
    task_id: str = None,
    **additional_data
):
    """
    创建性能计时器
    
    Args:
        component: 组件名称
        operation: 操作名称
        record_id: 记录ID
        task_id: 任务ID
        **additional_data: 额外数据
        
    Returns:
        PerformanceTimer: 性能计时器
    """
    
    return PerformanceTimer(
        monitor=performance_monitor,
        component=component,
        operation=operation,
        record_id=record_id,
        task_id=task_id,
        **additional_data
    )
