"""
数据处理服务数据验证模型
定义数据处理管道相关的Pydantic数据模型，用于请求验证和响应序列化
"""

from datetime import datetime, date
from decimal import Decimal
from typing import Any, Dict, List, Optional
from enum import Enum

from pydantic import BaseModel, ConfigDict, Field, field_validator

from .models import ProcessingStage, ProcessingResult


class BusinessDataType(str, Enum):
    """业务数据类型枚举"""

    # 新闻类
    FLASH_NEWS = "flash_news"  # 快讯
    NEWS_ARTICLE = "news_article"  # 新闻文章
    MARKET_ANALYSIS = "market_analysis"  # 市场分析
    INDUSTRY_NEWS = "industry_news"  # 行业新闻

    # 数据类
    OFFICIAL_DATA = "official_data"  # 官方数据
    MARKET_DATA = "market_data"  # 市场数据
    ECONOMIC_DATA = "economic_data"  # 经济数据（统一使用ECONOMIC_DATA）
    ECONOMIC_INDICATOR = "economic_indicator"  # 经济指标（保留兼容性）
    FINANCIAL_STATEMENT = "financial_statement"  # 财务报表

    # 研究类
    RESEARCH_REPORT = "research_report"  # 研究报告
    ANALYST_OPINION = "analyst_opinion"  # 分析师观点
    RATING_CHANGE = "rating_change"  # 评级变动
    PRICE_TARGET = "price_target"  # 目标价格

    # 监管类
    REGULATORY_FILING = "regulatory_filing"  # 监管文件
    POLICY_ANNOUNCEMENT = "policy_announcement"  # 政策公告
    REGULATORY_NEWS = "regulatory_news"  # 监管新闻
    COMPLIANCE_UPDATE = "compliance_update"  # 合规更新

    # 社交类
    SOCIAL_SENTIMENT = "social_sentiment"  # 社交情绪
    SOCIAL_TREND = "social_trend"  # 社交趋势

    # 公司类
    COMPANY_ANNOUNCEMENT = "company_announcement"  # 公司公告
    OPINION_LEADER = "opinion_leader"  # 意见领袖


# ==================== 标签和分类信息响应模型 ====================

class ContentTagInfo(BaseModel):
    """内容标签信息"""

    tag_id: int = Field(..., description="标签ID")
    tag_name: str = Field(..., description="标签名称")
    tag_code: str = Field(..., description="标签代码")
    tag_slug: str = Field(..., description="标签URL标识符")
    color: Optional[str] = Field(None, description="标签颜色")
    icon: Optional[str] = Field(None, description="标签图标")

    # 评分信息
    relevance_score: Decimal = Field(..., description="相关性评分")
    confidence_score: Decimal = Field(..., description="置信度评分")
    importance_score: Decimal = Field(..., description="重要性评分")
    final_score: Decimal = Field(..., description="综合评分")

    # 来源和统计
    source: str = Field(..., description="标签来源")
    mention_count: int = Field(default=0, description="提及次数")

    model_config = ConfigDict(from_attributes=True)


class ContentClassificationInfo(BaseModel):
    """内容分类信息"""

    dimension_id: int = Field(..., description="分类维度ID")
    dimension_name: str = Field(..., description="维度名称")
    dimension_display_name: str = Field(..., description="维度显示名称")

    value_id: int = Field(..., description="分类值ID")
    value_code: str = Field(..., description="分类值代码")
    value_display_name: str = Field(..., description="分类值显示名称")

    confidence_score: Decimal = Field(..., description="置信度评分")
    source: str = Field(..., description="分类来源")

    model_config = ConfigDict(from_attributes=True)


# ==================== 数据处理管道相关 ====================

class DataProcessingPipelineBase(BaseModel):
    """数据处理管道基础模型"""

    pipeline_code: str = Field(
        ...,
        min_length=1,
        max_length=50,
        description="管道代码，如jin10_flash_news_specific"
    )
    version: int = Field(
        default=1,
        ge=1,
        description="版本号，用于版本管理"
    )
    pipeline_name: str = Field(
        ...,
        min_length=1,
        max_length=100,
        description="管道名称"
    )
    description: Optional[str] = Field(None, description="管道描述")

    # 适用范围
    business_data_type: BusinessDataType = Field(
        ...,
        description="适用的业务数据类型"
    )
    source_id: Optional[int] = Field(
        None,
        description="专用数据源ID（为空表示通用管道）"
    )
    url_pattern: Optional[str] = Field(
        None,
        max_length=500,
        description="URL匹配模式（正则表达式）"
    )
    domain_pattern: Optional[str] = Field(
        None,
        max_length=200,
        description="域名匹配模式"
    )
    content_type_pattern: Optional[str] = Field(
        None,
        max_length=100,
        description="内容类型匹配模式"
    )
    content_pattern: Optional[Dict[str, Any]] = Field(
        None,
        description="内容匹配模式"
    )

    # 适用条件
    min_content_length: Optional[int] = Field(
        None,
        ge=0,
        description="最小内容长度"
    )
    max_content_length: Optional[int] = Field(
        None,
        ge=1,
        description="最大内容长度"
    )
    quality_threshold: Optional[Decimal] = Field(
        default=Decimal("0.5"),
        ge=Decimal("0.0"),
        le=Decimal("1.0"),
        description="质量阈值"
    )
    required_fields: Optional[List[str]] = Field(
        default=None,
        description="必需字段"
    )

    # 统一的处理配置
    field_mapping: Dict[str, Any] = Field(
        ...,
        description="字段映射规则：原始字段 -> 目标字段"
    )
    data_extraction_config: Dict[str, Any] = Field(
        default_factory=dict,
        description="数据提取配置：CSS选择器、XPath等"
    )
    data_transformation_config: Dict[str, Any] = Field(
        default_factory=dict,
        description="数据转换配置：格式转换、数据清洗、计算等"
    )
    data_validation_config: Dict[str, Any] = Field(
        default_factory=dict,
        description="数据验证配置：必填检查、格式验证、范围验证等"
    )
    data_enrichment_config: Dict[str, Any] = Field(
        default_factory=dict,
        description="数据增强配置：标签提取、分类识别、实体识别等"
    )

    # 管道控制
    priority: int = Field(
        default=5,
        ge=1,
        le=10,
        description="优先级 1-10，数值越大优先级越高"
    )
    execution_order: Optional[int] = Field(
        default=None,
        description="执行顺序"
    )
    is_active: bool = Field(default=True, description="是否启用")
    is_default: bool = Field(default=False, description="是否为默认管道")

    # 版本管理
    change_description: Optional[str] = Field(None, description="版本变更说明")
    test_result: Optional[Dict[str, Any]] = Field(None, description="测试结果")
    parent_version_id: Optional[int] = Field(
        None,
        description="父版本ID，用于追踪版本继承关系"
    )

    # 生效时间控制
    effective_date_start: Optional[date] = Field(None, description="生效开始日期")
    effective_date_end: Optional[date] = Field(None, description="生效结束日期")

    # 管理信息
    created_by: Optional[str] = Field(
        None,
        max_length=100,
        description="创建者"
    )
    updated_by: Optional[str] = Field(
        None,
        max_length=100,
        description="更新者"
    )

    @field_validator('pipeline_code')
    @classmethod
    def validate_pipeline_code(cls, v: str) -> str:
        """验证管道代码"""
        if not v.replace('_', '').replace('-', '').isalnum():
            raise ValueError('管道代码只能包含字母、数字、下划线和横线')
        return v

    @field_validator('max_content_length')
    @classmethod
    def validate_content_length(cls, v: Optional[int], info) -> Optional[int]:
        """验证内容长度范围"""
        if v is not None and 'min_content_length' in info.data:
            min_length = info.data['min_content_length']
            if min_length is not None and v <= min_length:
                raise ValueError('最大内容长度必须大于最小内容长度')
        return v

    @field_validator('effective_date_end')
    @classmethod
    def validate_effective_date_range(cls, v: Optional[date], info) -> Optional[date]:
        """验证生效日期范围"""
        if v is not None and 'effective_date_start' in info.data:
            start_date = info.data['effective_date_start']
            if start_date is not None and v <= start_date:
                raise ValueError('生效结束日期必须晚于开始日期')
        return v


class DataProcessingPipelineCreate(DataProcessingPipelineBase):
    """创建数据处理管道请求模型"""
    pass


class DataProcessingPipelineUpdate(BaseModel):
    """更新数据处理管道请求模型"""

    pipeline_name: Optional[str] = Field(
        None,
        min_length=1,
        max_length=100,
        description="管道名称"
    )
    description: Optional[str] = Field(None, description="管道描述")

    # 适用范围
    business_data_type: Optional[BusinessDataType] = Field(
        None,
        description="适用的业务数据类型"
    )
    source_id: Optional[int] = Field(
        None,
        description="专用数据源ID（为空表示通用管道）"
    )
    url_pattern: Optional[str] = Field(
        None,
        max_length=500,
        description="URL匹配模式（正则表达式）"
    )
    domain_pattern: Optional[str] = Field(
        None,
        max_length=200,
        description="域名匹配模式"
    )
    content_type_pattern: Optional[str] = Field(
        None,
        max_length=100,
        description="内容类型匹配模式"
    )
    content_pattern: Optional[Dict[str, Any]] = Field(
        None,
        description="内容匹配模式"
    )

    # 适用条件
    min_content_length: Optional[int] = Field(
        None,
        ge=0,
        description="最小内容长度"
    )
    max_content_length: Optional[int] = Field(
        None,
        ge=1,
        description="最大内容长度"
    )
    quality_threshold: Optional[Decimal] = Field(
        None,
        ge=Decimal("0.0"),
        le=Decimal("1.0"),
        description="质量阈值"
    )
    required_fields: Optional[List[str]] = Field(
        None,
        description="必需字段"
    )

    # 统一的处理配置
    field_mapping: Optional[Dict[str, Any]] = Field(
        None,
        description="字段映射规则：原始字段 -> 目标字段"
    )
    data_extraction_config: Optional[Dict[str, Any]] = Field(
        None,
        description="数据提取配置：CSS选择器、XPath等"
    )
    data_transformation_config: Optional[Dict[str, Any]] = Field(
        None,
        description="数据转换配置：格式转换、数据清洗、计算等"
    )
    data_validation_config: Optional[Dict[str, Any]] = Field(
        None,
        description="数据验证配置：必填检查、格式验证、范围验证等"
    )
    data_enrichment_config: Optional[Dict[str, Any]] = Field(
        None,
        description="数据增强配置：标签提取、分类识别、实体识别等"
    )

    # 管道控制
    priority: Optional[int] = Field(
        None,
        ge=1,
        le=10,
        description="优先级 1-10，数值越大优先级越高"
    )
    execution_order: Optional[int] = Field(
        None,
        ge=1,
        description="执行顺序"
    )
    is_active: Optional[bool] = Field(None, description="是否启用")
    is_default: Optional[bool] = Field(None, description="是否为默认管道")

    # 版本管理
    change_description: Optional[str] = Field(None, description="版本变更说明")
    test_result: Optional[Dict[str, Any]] = Field(None, description="测试结果")

    # 生效时间控制
    effective_date_start: Optional[date] = Field(None, description="生效开始日期")
    effective_date_end: Optional[date] = Field(None, description="生效结束日期")

    # 管理信息
    updated_by: Optional[str] = Field(
        None,
        max_length=100,
        description="更新者"
    )


class DataProcessingPipelineResponse(DataProcessingPipelineBase):
    """数据处理管道响应模型"""

    id: int = Field(..., description="管道ID")

    # 执行统计 - 允许为空，提供默认值
    execution_order: Optional[int] = Field(None, description="执行顺序")
    execution_count: Optional[int] = Field(None, description="执行次数")
    success_count: Optional[int] = Field(None, description="成功次数")
    failure_count: Optional[int] = Field(None, description="失败次数")
    last_executed_at: Optional[datetime] = Field(None, description="最后执行时间")
    avg_execution_time_ms: Optional[int] = Field(None, description="平均执行时间（毫秒）")

    # 时间字段
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

    model_config = ConfigDict(from_attributes=True)

    @classmethod
    def model_validate(cls, obj, **kwargs):
        """自定义验证，处理NULL值"""
        try:
            return super().model_validate(obj, **kwargs)
        except Exception as e:
            # 如果验证失败，尝试处理NULL值
            if hasattr(obj, '__dict__'):
                data = {k: v for k, v in obj.__dict__.items() if not k.startswith('_')}

                # 为NULL字段提供默认值
                defaults = {
                    'required_fields': [],
                    'execution_order': 1,
                    'execution_count': 0,
                    'success_count': 0,
                    'failure_count': 0,
                    'field_mapping': {},
                    'data_extraction_config': {},
                    'data_transformation_config': {},
                    'data_validation_config': {},
                    'data_enrichment_config': {}
                }

                for key, default_value in defaults.items():
                    if data.get(key) is None:
                        data[key] = default_value

                return super().model_validate(data, **kwargs)
            else:
                raise e


class DataProcessingPipelineListResponse(BaseModel):
    """数据处理管道列表响应模型"""

    total: int = Field(..., description="总数量")
    page: int = Field(..., description="当前页码")
    size: int = Field(..., description="每页大小")
    pages: int = Field(..., description="总页数")
    items: List[DataProcessingPipelineResponse] = Field(..., description="管道列表")


class DataProcessingPipelineStatsResponse(BaseModel):
    """数据处理管道统计响应模型"""

    total_pipelines: int = Field(..., description="管道总数")
    active_pipelines: int = Field(..., description="启用的管道数")
    version_distribution: Dict[str, int] = Field(..., description="按版本分布")
    business_type_distribution: Dict[str, int] = Field(..., description="按业务类型分布")
    execution_stats: Dict[str, Any] = Field(..., description="执行统计")


class DataProcessingPipelineFilter(BaseModel):
    """数据处理管道过滤器"""

    pipeline_code: Optional[str] = Field(None, description="管道代码")
    business_data_type: Optional[BusinessDataType] = Field(None, description="业务数据类型")
    source_id: Optional[int] = Field(None, description="数据源ID")
    is_active: Optional[bool] = Field(None, description="是否启用")
    is_default: Optional[bool] = Field(None, description="是否为默认管道")
    domain_pattern: Optional[str] = Field(None, description="域名模式")
    priority_min: Optional[int] = Field(None, ge=1, le=10, description="最小优先级")
    priority_max: Optional[int] = Field(None, ge=1, le=10, description="最大优先级")
    created_by: Optional[str] = Field(None, description="创建者")
    

class BatchPipelineStatusUpdate(BaseModel):
    """批量管道状态更新"""

    pipeline_ids: List[int] = Field(..., min_length=1, description="管道ID列表")
    is_active: bool = Field(..., description="新的启用状态")
    updated_by: Optional[str] = Field(None, description="更新者")


class BatchPipelinePriorityUpdate(BaseModel):
    """批量管道优先级更新"""

    pipeline_priorities: Dict[int, int] = Field(
        ...,
        description="管道ID到优先级的映射"
    )
    updated_by: Optional[str] = Field(None, description="更新者")

    @field_validator('pipeline_priorities')
    @classmethod
    def validate_priorities(cls, v: Dict[int, int]) -> Dict[int, int]:
        """验证优先级范围"""
        for pipeline_id, priority in v.items():
            if not (1 <= priority <= 10):
                raise ValueError(f'管道 {pipeline_id} 的优先级 {priority} 必须在1-10范围内')
        return v


class BatchOperationResponse(BaseModel):
    """批量操作响应"""

    success_count: int = Field(..., description="成功数量")
    failure_count: int = Field(..., description="失败数量")
    success_ids: List[int] = Field(..., description="成功的ID列表")
    failure_details: List[Dict[str, Any]] = Field(..., description="失败详情")


# ==================== 数据处理状态相关 ====================

class DataProcessingStatusBase(BaseModel):
    """数据处理状态基础模型"""

    raw_data_id: int = Field(..., description="原始数据记录ID")
    target_business_type: BusinessDataType = Field(..., description="目标业务类型")
    processing_stage: ProcessingStage = Field(
        default=ProcessingStage.PENDING,
        description="处理阶段"
    )
    stage_details: Optional[Dict[str, Any]] = Field(
        default_factory=dict,
        description="阶段详情"
    )
    progress_percentage: int = Field(
        default=0,
        ge=0,
        le=100,
        description="处理进度百分比"
    )
    current_step: Optional[str] = Field(None, description="当前处理步骤")


class DataProcessingStatusResponse(DataProcessingStatusBase):
    """数据处理状态响应模型"""

    id: int = Field(..., description="状态记录ID")
    started_at: datetime = Field(..., description="开始处理时间")
    completed_at: Optional[datetime] = Field(None, description="完成处理时间")
    processing_duration_seconds: Optional[int] = Field(None, description="处理耗时（秒）")
    target_table_id: Optional[int] = Field(None, description="目标表记录ID")
    target_table_name: Optional[str] = Field(None, description="目标表名")
    processing_result: Optional[ProcessingResult] = Field(None, description="处理结果")
    data_quality_score: Optional[Decimal] = Field(None, description="数据质量评分")
    tag_extraction_count: int = Field(default=0, description="提取标签数量")
    classification_count: int = Field(default=0, description="分类数量")
    error_count: int = Field(default=0, description="错误次数")
    error_message: Optional[str] = Field(None, description="错误信息")
    retry_count: int = Field(default=0, description="重试次数")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

    model_config = ConfigDict(from_attributes=True)


# ==================== 业务数据相关 ====================

# FlashNews 快讯相关Schema

class FlashNewsBase(BaseModel):
    """快讯基础模型"""

    title: str = Field(..., min_length=1, max_length=500, description="快讯标题")
    content: str = Field(..., min_length=1, description="快讯内容")
    summary: Optional[str] = Field(None, max_length=1000, description="AI生成摘要")

    # 时间信息
    publish_time: datetime = Field(..., description="发布时间")

    # 重要性和紧急度
    urgency_level: int = Field(default=2, ge=1, le=3, description="紧急度级别：1普通/2重要/3紧急")
    importance_score: Optional[Decimal] = Field(
        default=Decimal("0.5"),
        ge=Decimal("0.0"),
        le=Decimal("1.0"),
        description="重要性评分"
    )
    impact_scope: str = Field(
        default="domestic",
        description="影响范围：domestic/international/global"
    )

    # 分类信息
    news_category: Optional[str] = Field(None, max_length=100, description="新闻分类")

    # 状态管理
    status: str = Field(default="published", description="状态：draft/published/updated/archived")
    is_breaking: bool = Field(default=False, description="是否突发新闻")
    push_immediately: bool = Field(default=False, description="是否立即推送")


class FlashNewsCreate(FlashNewsBase):
    """创建快讯请求模型"""

    raw_data_id: int = Field(..., description="关联原始数据ID")


class FlashNewsUpdate(BaseModel):
    """更新快讯请求模型"""

    title: Optional[str] = Field(None, min_length=1, max_length=500, description="快讯标题")
    content: Optional[str] = Field(None, min_length=1, description="快讯内容")
    summary: Optional[str] = Field(None, max_length=1000, description="AI生成摘要")

    # 时间信息
    publish_time: Optional[datetime] = Field(None, description="发布时间")

    # 重要性和紧急度
    urgency_level: Optional[int] = Field(None, ge=1, le=3, description="紧急度级别：1普通/2重要/3紧急")
    importance_score: Optional[Decimal] = Field(
        None,
        ge=Decimal("0.0"),
        le=Decimal("1.0"),
        description="重要性评分"
    )
    impact_scope: Optional[str] = Field(None, description="影响范围：domestic/international/global")

    # 分类信息
    news_category: Optional[str] = Field(None, max_length=100, description="新闻分类")

    # 状态管理
    status: Optional[str] = Field(None, description="状态：draft/published/updated/archived")
    is_breaking: Optional[bool] = Field(None, description="是否突发新闻")
    push_immediately: Optional[bool] = Field(None, description="是否立即推送")


class FlashNewsResponse(FlashNewsBase):
    """快讯响应模型"""

    id: int = Field(..., description="快讯唯一标识符")
    raw_data_id: int = Field(..., description="关联原始数据ID")
    process_time: datetime = Field(..., description="处理时间")

    # 统计信息
    view_count: int = Field(default=0, description="浏览次数")
    share_count: int = Field(default=0, description="分享次数")

    # 标签和分类信息
    tags: List[ContentTagInfo] = Field(default_factory=list, description="关联标签列表")
    classifications: List[ContentClassificationInfo] = Field(default_factory=list, description="分类信息列表")

    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

    model_config = ConfigDict(from_attributes=True)


class FlashNewsListResponse(BaseModel):
    """快讯列表响应模型"""

    items: List[FlashNewsResponse] = Field(..., description="快讯列表")
    total: int = Field(..., description="总数量")
    skip: int = Field(..., description="跳过数量")
    limit: int = Field(..., description="限制数量")

    model_config = ConfigDict(from_attributes=True)


# NewsArticle 新闻文章相关Schema

class NewsArticleBase(BaseModel):
    """新闻文章基础模型"""

    title: str = Field(..., min_length=1, max_length=1000, description="文章标题")
    subtitle: Optional[str] = Field(None, max_length=1000, description="副标题")
    abstract: Optional[str] = Field(None, description="文章摘要")
    content: str = Field(..., min_length=1, description="文章正文")
    content_html: Optional[str] = Field(None, description="HTML内容")

    # 作者和来源
    author: Optional[str] = Field(None, max_length=200, description="作者姓名")
    source_media: Optional[str] = Field(None, max_length=200, description="发布媒体")
    source_column: Optional[str] = Field(None, max_length=200, description="所属栏目")
    original_url: Optional[str] = Field(None, max_length=1000, description="原文链接")

    # 时间信息
    publish_time: datetime = Field(..., description="发布时间")
    update_time: Optional[datetime] = Field(None, description="更新时间")

    # 内容特征
    word_count: Optional[int] = Field(None, description="字数统计")
    reading_time_minutes: Optional[int] = Field(None, description="预估阅读时长")
    content_quality_score: Optional[Decimal] = Field(
        default=Decimal("0.5"),
        ge=Decimal("0.0"),
        le=Decimal("1.0"),
        description="内容质量评分"
    )

    # 分类和实体
    primary_category: Optional[str] = Field(None, max_length=100, description="主要分类")
    secondary_categories: List[str] = Field(default_factory=list, description="次要分类")
    mentioned_companies: List[str] = Field(default_factory=list, description="提及公司")
    mentioned_people: List[str] = Field(default_factory=list, description="提及人物")
    mentioned_locations: List[str] = Field(default_factory=list, description="提及地点")

    # 市场相关
    market_impact_prediction: Optional[str] = Field(None, max_length=50, description="市场影响预测")

    # 媒体资源
    featured_image_url: Optional[str] = Field(None, max_length=1000, description="特色图片URL")
    images_urls: List[str] = Field(default_factory=list, description="图片URL数组")
    video_urls: List[str] = Field(default_factory=list, description="视频URL数组")

    # 状态管理
    status: str = Field(default="published", description="状态：draft/review/published/featured/archived")
    is_featured: bool = Field(default=False, description="是否精选")
    is_trending: bool = Field(default=False, description="是否热门")
    editorial_rating: Optional[int] = Field(None, ge=1, le=5, description="编辑评分 1-5")

    # 版权信息
    copyright_info: Optional[str] = Field(None, description="版权声明")
    reproduction_rights: str = Field(default="fair_use", description="转载权限")


class NewsArticleCreate(NewsArticleBase):
    """创建新闻文章请求模型"""

    raw_data_id: int = Field(..., description="关联原始数据ID")


class NewsArticleUpdate(BaseModel):
    """更新新闻文章请求模型"""

    title: Optional[str] = Field(None, min_length=1, max_length=1000, description="文章标题")
    subtitle: Optional[str] = Field(None, max_length=1000, description="副标题")
    abstract: Optional[str] = Field(None, description="文章摘要")
    content: Optional[str] = Field(None, min_length=1, description="文章正文")
    content_html: Optional[str] = Field(None, description="HTML内容")

    # 作者和来源
    author: Optional[str] = Field(None, max_length=200, description="作者姓名")
    source_media: Optional[str] = Field(None, max_length=200, description="发布媒体")
    source_column: Optional[str] = Field(None, max_length=200, description="所属栏目")
    original_url: Optional[str] = Field(None, max_length=1000, description="原文链接")

    # 时间信息
    publish_time: Optional[datetime] = Field(None, description="发布时间")
    update_time: Optional[datetime] = Field(None, description="更新时间")

    # 内容特征
    word_count: Optional[int] = Field(None, description="字数统计")
    reading_time_minutes: Optional[int] = Field(None, description="预估阅读时长")
    content_quality_score: Optional[Decimal] = Field(
        None,
        ge=Decimal("0.0"),
        le=Decimal("1.0"),
        description="内容质量评分"
    )

    # 分类和实体
    primary_category: Optional[str] = Field(None, max_length=100, description="主要分类")
    secondary_categories: Optional[List[str]] = Field(None, description="次要分类")
    mentioned_companies: Optional[List[str]] = Field(None, description="提及公司")
    mentioned_people: Optional[List[str]] = Field(None, description="提及人物")
    mentioned_locations: Optional[List[str]] = Field(None, description="提及地点")

    # 市场相关
    market_impact_prediction: Optional[str] = Field(None, max_length=50, description="市场影响预测")

    # 媒体资源
    featured_image_url: Optional[str] = Field(None, max_length=1000, description="特色图片URL")
    images_urls: Optional[List[str]] = Field(None, description="图片URL数组")
    video_urls: Optional[List[str]] = Field(None, description="视频URL数组")

    # 状态管理
    status: Optional[str] = Field(None, description="状态：draft/review/published/featured/archived")
    is_featured: Optional[bool] = Field(None, description="是否精选")
    is_trending: Optional[bool] = Field(None, description="是否热门")
    editorial_rating: Optional[int] = Field(None, ge=1, le=5, description="编辑评分 1-5")

    # 版权信息
    copyright_info: Optional[str] = Field(None, description="版权声明")
    reproduction_rights: Optional[str] = Field(None, description="转载权限")


class NewsArticleResponse(NewsArticleBase):
    """新闻文章响应模型"""

    id: int = Field(..., description="新闻文章唯一标识符")
    raw_data_id: int = Field(..., description="关联原始数据ID")
    process_time: datetime = Field(..., description="处理时间")

    # 可读性评分
    readability_score: Optional[Decimal] = Field(None, description="可读性评分")

    # 平台统计
    internal_view_count: int = Field(default=0, description="平台浏览量")
    internal_favorite_count: int = Field(default=0, description="收藏数")
    internal_comment_count: int = Field(default=0, description="评论数")
    internal_share_count: int = Field(default=0, description="分享数")

    # 标签和分类信息
    tags: List[ContentTagInfo] = Field(default_factory=list, description="关联标签列表")
    classifications: List[ContentClassificationInfo] = Field(default_factory=list, description="分类信息列表")

    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

    model_config = ConfigDict(from_attributes=True)


class NewsArticleListResponse(BaseModel):
    """新闻文章列表响应模型"""

    items: List[NewsArticleResponse] = Field(..., description="新闻文章列表")
    total: int = Field(..., description="总数量")
    skip: int = Field(..., description="跳过数量")
    limit: int = Field(..., description="限制数量")

    model_config = ConfigDict(from_attributes=True)
