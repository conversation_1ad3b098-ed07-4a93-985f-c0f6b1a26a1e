"""
数据处理引擎
负责执行完整的数据处理流程，将原始数据转换为业务数据
"""

import asyncio
import json
import logging
import traceback
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional, Tuple, Type, Union

from sqlalchemy import and_, desc, func
from sqlalchemy.exc import IntegrityError
from sqlalchemy.orm import Session

from ...core.database import SessionLocal
from ...core.mongodb import mongodb_manager
from ..data_collection_service.models import RawDataRecord, BusinessDataType, DataSource
from .models import (
    DataProcessingPipeline, DataProcessingStatus, ProcessingStage, ProcessingResult,
    FlashNews, NewsArticle, ResearchReport, EconomicIndicatorData,
    UnifiedContentTags, UnifiedContentClassifications, AITagMatches
)
from .service import DataProcessingPipelineService
from .ai_services import AIContentAnalyzer
from .data_cleaners import DataCleaner, ContentFilter

logger = logging.getLogger(__name__)


class DataProcessingEngine:
    """
    数据处理引擎
    负责协调整个数据处理流程
    """

    def __init__(self):
        self.pipeline_service = DataProcessingPipelineService()
        self.ai_analyzer = AIContentAnalyzer()
        self.data_cleaner = DataCleaner()
        self.content_filter = ContentFilter()
        
        # 业务表映射
        self.business_table_mapping = {
            BusinessDataType.FLASH_NEWS: FlashNews,
            BusinessDataType.NEWS_ARTICLE: NewsArticle,
            BusinessDataType.RESEARCH_REPORT: ResearchReport,
            BusinessDataType.ECONOMIC_DATA: EconomicIndicatorData,  # 使用ECONOMIC_DATA而不是ECONOMIC_INDICATOR
        }

    async def process_pending_records(self, batch_size: int = 10) -> Dict[str, int]:
        """
        处理待处理的原始数据记录
        
        Args:
            batch_size: 批处理大小
            
        Returns:
            Dict: 处理结果统计
        """
        stats = {
            "processed": 0,
            "success": 0,
            "failed": 0,
            "skipped": 0
        }
        
        db = SessionLocal()
        try:
            # 获取待处理的记录
            pending_records = db.query(RawDataRecord).filter(
                RawDataRecord.processing_status == "pending"
            ).order_by(
                desc(RawDataRecord.processing_priority),
                RawDataRecord.created_at
            ).limit(batch_size).all()
            
            logger.info(f"找到 {len(pending_records)} 条待处理记录")
            
            for record in pending_records:
                try:
                    result = await self.process_single_record(record)
                    stats["processed"] += 1
                    
                    if result["success"]:
                        stats["success"] += 1
                    else:
                        stats["failed"] += 1
                        
                except Exception as e:
                    logger.error(f"处理记录失败 {record.id}: {e}")
                    stats["failed"] += 1
                    
        finally:
            db.close()
            
        logger.info(f"批处理完成: {stats}")
        return stats

    async def process_single_record(self, record: RawDataRecord) -> Dict[str, Any]:
        """
        处理单个原始数据记录

        Args:
            record: 原始数据记录

        Returns:
            Dict: 处理结果
        """
        db = SessionLocal()
        processing_status = None

        try:
            # 重新获取记录以避免会话冲突
            record_id = record.id
            record = db.query(RawDataRecord).filter(RawDataRecord.id == record_id).first()
            if not record:
                logger.error(f"记录 {record_id} 不存在")
                return {"success": False, "error": "记录不存在"}

            # 检查记录是否已经被处理过
            if record.processing_status == "processed":
                logger.info(f"记录 {record.id} 已经被处理过，跳过")
                return {"success": True, "skipped": True, "reason": "already_processed"}

            # 检查是否已经有对应的业务数据
            from .models import FlashNews
            existing_business_data = db.query(FlashNews).filter(
                FlashNews.raw_data_id == record.id
            ).first()

            if existing_business_data:
                logger.info(f"记录 {record.id} 已有对应的业务数据，更新状态为processed")
                record.processing_status = "processed"
                record.updated_at = datetime.now(timezone.utc)
                db.commit()
                return {"success": True, "skipped": True, "reason": "business_data_exists"}

            # 1. 创建处理状态记录
            processing_status = await self._create_processing_status(db, record)

            # 2. 获取MongoDB中的原始内容
            raw_content = await self._get_raw_content(record)
            if not raw_content:
                await self._update_processing_status(
                    db, processing_status, ProcessingStage.FAILED,
                    error_message="无法获取MongoDB原始内容"
                )
                # 确保失败状态也被提交
                db.commit()
                return {"success": False, "error": "无法获取MongoDB原始内容"}

            # 3. 匹配处理管道
            pipeline = await self._find_processing_pipeline(db, record)
            if not pipeline:
                await self._update_processing_status(
                    db, processing_status, ProcessingStage.FAILED,
                    error_message="未找到适用的处理管道"
                )
                # 确保失败状态也被提交
                db.commit()
                return {"success": False, "error": "未找到适用的处理管道"}

            # 4. 执行数据处理流程
            result = await self._execute_processing_pipeline(
                db, record, raw_content, pipeline, processing_status
            )

            # 确保成功结果被提交
            if result.get("success"):
                db.commit()
                logger.info(f"✅ 记录 {record.id} 处理成功并已提交到数据库")
            else:
                db.commit()  # 即使处理失败，也要提交状态更新

            return result

        except Exception as e:
            logger.error(f"处理记录异常 {record.id}: {e}")
            logger.error(traceback.format_exc())

            try:
                if processing_status:
                    await self._update_processing_status(
                        db, processing_status, ProcessingStage.FAILED,
                        error_message=str(e)
                    )
                db.commit()  # 提交错误状态
            except Exception as commit_error:
                logger.error(f"提交错误状态失败: {commit_error}")
                db.rollback()

            return {"success": False, "error": str(e)}

        finally:
            db.close()

    async def _create_processing_status(
        self, 
        db: Session, 
        record: RawDataRecord
    ) -> DataProcessingStatus:
        """创建处理状态记录"""
        
        # 确定目标业务类型
        data_source = db.query(DataSource).filter(
            DataSource.id == record.source_id
        ).first()
        
        target_business_type = data_source.business_data_type if data_source else BusinessDataType.NEWS_ARTICLE
        
        processing_status = DataProcessingStatus(
            raw_data_id=record.id,
            target_business_type=target_business_type,
            processing_stage=ProcessingStage.PENDING,
            progress_percentage=0,
            current_step="开始处理",
            started_at=datetime.now(timezone.utc)
        )
        
        db.add(processing_status)
        db.commit()
        db.refresh(processing_status)
        
        # 更新原始记录状态
        record.processing_status = "processing"
        db.commit()
        
        logger.info(f"创建处理状态记录: {processing_status.id}")
        return processing_status

    async def _get_raw_content(self, record: RawDataRecord) -> Optional[Dict[str, Any]]:
        """从MongoDB获取原始内容"""
        if not record.mongodb_id:
            logger.error(f"记录 {record.id} 没有MongoDB ID")
            return None
            
        try:
            content = await mongodb_manager.get_raw_content(record.mongodb_id)
            if not content:
                logger.error(f"MongoDB中未找到内容: {record.mongodb_id}")
                return None
                
            logger.debug(f"成功获取MongoDB内容: {record.mongodb_id}")
            return content
            
        except Exception as e:
            logger.error(f"获取MongoDB内容失败: {e}")
            return None

    async def _find_processing_pipeline(
        self, 
        db: Session, 
        record: RawDataRecord
    ) -> Optional[DataProcessingPipeline]:
        """查找适用的处理管道"""
        try:
            pipeline = self.pipeline_service.find_processing_pipeline(db, record)
            if pipeline:
                logger.info(f"找到处理管道: {pipeline.pipeline_code}")
            else:
                logger.warning(f"未找到适用的处理管道: record_id={record.id}")
            return pipeline
            
        except Exception as e:
            logger.error(f"查找处理管道失败: {e}")
            return None

    async def _execute_processing_pipeline(
        self,
        db: Session,
        record: RawDataRecord,
        raw_content: Dict[str, Any],
        pipeline: DataProcessingPipeline,
        processing_status: DataProcessingStatus
    ) -> Dict[str, Any]:
        """执行处理管道"""
        
        try:
            # 阶段1: 数据提取
            await self._update_processing_status(
                db, processing_status, ProcessingStage.EXTRACTING,
                progress=10, current_step="数据提取"
            )

            extracted_data = await self._extract_data(raw_content, pipeline)

            # 阶段2: 数据清洗
            await self._update_processing_status(
                db, processing_status, ProcessingStage.CLEANING,
                progress=30, current_step="数据清洗"
            )
            
            cleaned_data = await self._clean_data(extracted_data, pipeline)
            
            # 检查是否为垃圾内容（广告、推广等）
            if await self._is_spam_content(cleaned_data):
                # 删除垃圾内容
                await self._delete_spam_content(db, record)
                await self._update_processing_status(
                    db, processing_status, ProcessingStage.COMPLETED,
                    progress=100, current_step="垃圾内容已删除",
                    result=ProcessingResult.DELETED
                )
                return {"success": True, "action": "deleted", "reason": "spam_content"}
            
            # 阶段3: 数据转换
            await self._update_processing_status(
                db, processing_status, ProcessingStage.TRANSFORMING,
                progress=50, current_step="数据转换"
            )

            transformed_data = await self._transform_data(cleaned_data, pipeline)

            # 阶段4: 数据验证
            await self._update_processing_status(
                db, processing_status, ProcessingStage.VALIDATING,
                progress=70, current_step="数据验证"
            )
            
            validation_result = await self._validate_data(transformed_data, pipeline)
            if not validation_result["valid"]:
                await self._update_processing_status(
                    db, processing_status, ProcessingStage.FAILED,
                    error_message=f"数据验证失败: {validation_result['errors']}"
                )
                return {"success": False, "error": "数据验证失败"}
            
            # 阶段5: AI增强
            await self._update_processing_status(
                db, processing_status, ProcessingStage.ENHANCING,
                progress=80, current_step="AI内容增强"
            )

            enhanced_data = await self._enhance_data(transformed_data, pipeline)

            # 阶段6: 保存到业务表
            await self._update_processing_status(
                db, processing_status, ProcessingStage.SAVING,
                progress=90, current_step="保存业务数据"
            )
            
            business_record = await self._save_business_data(
                db, record, enhanced_data, processing_status.target_business_type
            )

            # 阶段7: 标签处理
            await self._update_processing_status(
                db, processing_status, ProcessingStage.TAGGING,
                progress=95, current_step="处理标签关联"
            )

            await self._process_tags_after_save(
                db, enhanced_data, business_record, processing_status.target_business_type, pipeline
            )

            # 阶段8: 完成
            await self._update_processing_status(
                db, processing_status, ProcessingStage.COMPLETED,
                progress=100, current_step="处理完成",
                result=ProcessingResult.SUCCESS,
                target_table_id=business_record.id,
                target_table_name=business_record.__tablename__
            )
            
            # 更新原始记录状态
            record.processing_status = "processed"
            record.updated_at = datetime.now(timezone.utc)
            # 记录已经在当前会话中，不需要再次添加

            logger.info(f"成功处理记录 {record.id} -> {business_record.__tablename__}:{business_record.id}")
            return {
                "success": True,
                "business_record_id": business_record.id,
                "business_table": business_record.__tablename__
            }
            
        except Exception as e:
            logger.error(f"执行处理管道失败: {e}")
            await self._update_processing_status(
                db, processing_status, ProcessingStage.FAILED,
                error_message=str(e)
            )
            raise

    async def _update_processing_status(
        self,
        db: Session,
        processing_status: DataProcessingStatus,
        stage: ProcessingStage,
        progress: Optional[int] = None,
        current_step: Optional[str] = None,
        error_message: Optional[str] = None,
        result: Optional[ProcessingResult] = None,
        target_table_id: Optional[int] = None,
        target_table_name: Optional[str] = None,
        auto_commit: bool = False
    ):
        """更新处理状态"""

        processing_status.processing_stage = stage

        if progress is not None:
            processing_status.progress_percentage = progress

        if current_step:
            processing_status.current_step = current_step

        if error_message:
            processing_status.error_message = error_message
            processing_status.error_count += 1

        if result:
            processing_status.processing_result = result
            processing_status.completed_at = datetime.now(timezone.utc)

        if target_table_id:
            processing_status.target_table_id = target_table_id

        if target_table_name:
            processing_status.target_table_name = target_table_name

        processing_status.updated_at = datetime.now(timezone.utc)

        # 只在明确要求时才自动提交，否则让调用者控制事务
        if auto_commit:
            db.commit()

        logger.debug(f"更新处理状态: {stage.value} - {current_step}")

    async def _extract_data(
        self,
        raw_content: Dict[str, Any],
        pipeline: DataProcessingPipeline
    ) -> Dict[str, Any]:
        """数据提取阶段"""

        extraction_config = pipeline.data_extraction_config or {}
        field_mapping = pipeline.field_mapping or {}

        extracted_data = {}

        # 基础字段提取
        for target_field, source_config in field_mapping.items():
            if isinstance(source_config, dict):
                source_field = source_config.get("source_field", target_field)
                required = source_config.get("required", False)
            else:
                source_field = source_config
                required = False

            value = raw_content.get(source_field)

            if value is not None:
                extracted_data[target_field] = value
            elif required:
                logger.warning(f"必需字段缺失: {target_field}")

        # 应用提取配置
        if extraction_config:
            extracted_data = await self.data_cleaner.apply_extraction_rules(
                extracted_data, extraction_config
            )

        logger.debug(f"数据提取完成，提取字段: {list(extracted_data.keys())}")
        return extracted_data

    async def _clean_data(
        self,
        extracted_data: Dict[str, Any],
        pipeline: DataProcessingPipeline
    ) -> Dict[str, Any]:
        """数据清洗阶段"""

        transformation_config = pipeline.data_transformation_config or {}

        # 应用清洗规则
        cleaned_data = await self.data_cleaner.clean_content(
            extracted_data, transformation_config
        )

        logger.debug("数据清洗完成")
        return cleaned_data

    async def _is_spam_content(self, data: Dict[str, Any]) -> bool:
        """检查是否为垃圾内容"""

        return await self.content_filter.is_spam(data)

    async def _delete_spam_content(self, db: Session, record: RawDataRecord):
        """删除垃圾内容"""

        try:
            # 从MongoDB删除
            if record.mongodb_id:
                await mongodb_manager.delete_raw_content(record.mongodb_id)

            # 从PostgreSQL删除
            db.delete(record)
            db.commit()

            logger.info(f"已删除垃圾内容: record_id={record.id}")

        except Exception as e:
            logger.error(f"删除垃圾内容失败: {e}")
            db.rollback()
            raise

    async def _transform_data(
        self,
        cleaned_data: Dict[str, Any],
        pipeline: DataProcessingPipeline
    ) -> Dict[str, Any]:
        """数据转换阶段"""

        transformation_config = pipeline.data_transformation_config or {}

        # 应用转换规则
        transformed_data = await self.data_cleaner.transform_data(
            cleaned_data, transformation_config
        )

        logger.debug("数据转换完成")
        return transformed_data

    async def _validate_data(
        self,
        data: Dict[str, Any],
        pipeline: DataProcessingPipeline
    ) -> Dict[str, Any]:
        """数据验证阶段"""

        validation_config = pipeline.data_validation_config or {}

        # 执行验证
        validation_result = await self.data_cleaner.validate_data(
            data, validation_config
        )

        logger.debug(f"数据验证结果: {validation_result}")
        return validation_result

    async def _enhance_data(
        self,
        data: Dict[str, Any],
        pipeline: DataProcessingPipeline
    ) -> Dict[str, Any]:
        """AI数据增强阶段"""

        enrichment_config = pipeline.data_enrichment_config or {}

        # AI内容分析
        enhanced_data = await self.ai_analyzer.analyze_content(
            data, enrichment_config
        )

        logger.debug("AI数据增强完成")
        return enhanced_data

    async def _process_tags_after_save(
        self,
        db: Session,
        enhanced_data: Dict[str, Any],
        business_record,
        business_type: str,
        pipeline: DataProcessingPipeline
    ):
        """在保存业务数据后处理标签和分类关联"""

        try:
            # 获取数据增强配置
            enrichment_config = pipeline.data_enrichment_config or {}
            tag_confidence_threshold = enrichment_config.get("tag_confidence_threshold", 0.6)
            classification_confidence_threshold = enrichment_config.get("classification_confidence_threshold", 0.7)

            # 1. 处理标签关联
            ai_tags = enhanced_data.get("ai_extracted_tags", [])
            if ai_tags:
                try:
                    matched_tags = await self.ai_analyzer.match_tags_to_standard(
                        ai_tags, business_type, business_record.id, tag_confidence_threshold
                    )

                    # 更新处理状态中的标签统计
                    if hasattr(self, '_current_processing_status'):
                        self._current_processing_status.tag_extraction_count = len(matched_tags)

                    logger.info(f"为 {business_type}:{business_record.id} 处理了 {len(matched_tags)} 个标签")
                except Exception as e:
                    logger.error(f"标签关联处理失败: {e}")
            else:
                logger.debug("没有AI提取的标签，跳过标签处理")

            # 2. 处理分类关联
            ai_classifications = enhanced_data.get("ai_classifications", {})
            if ai_classifications:
                try:
                    matched_classifications = await self.ai_analyzer.match_classifications_to_standard(
                        ai_classifications, business_type, business_record.id, classification_confidence_threshold
                    )
                    logger.info(f"为 {business_type}:{business_record.id} 处理了 {len(matched_classifications)} 个分类")
                except Exception as e:
                    logger.error(f"分类关联处理失败: {e}")
            else:
                logger.debug("没有AI提取的分类，跳过分类处理")

        except Exception as e:
            logger.error(f"标签和分类处理失败: {e}")
            # 不抛出异常，避免影响主流程

    async def _save_business_data(
        self,
        db: Session,
        record: RawDataRecord,
        data: Dict[str, Any],
        business_type: BusinessDataType
    ) -> Any:
        """保存到业务表"""

        business_model = self.business_table_mapping.get(business_type)
        if not business_model:
            raise ValueError(f"不支持的业务类型: {business_type}")

        # 准备业务数据，进行字段映射
        business_data = {
            "raw_data_id": record.id,
        }

        # 字段映射逻辑
        if business_model.__name__ == "FlashNews":
            business_data.update(self._map_to_flash_news_fields(data))
        else:
            # 对于其他业务模型，直接使用原数据
            business_data.update(data)

        # 创建业务记录（不自动提交，让调用者控制事务）
        business_record = business_model(**business_data)
        db.add(business_record)
        db.flush()  # 刷新以获取ID，但不提交事务
        db.refresh(business_record)

        logger.info(f"保存业务数据: {business_model.__tablename__}:{business_record.id}")
        return business_record

    def _map_to_flash_news_fields(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """将AI分析结果映射到FlashNews模型字段"""

        mapped_data = {}

        # 基础字段直接映射
        basic_fields = [
            "title", "content", "summary", "publish_time", "process_time",
            "urgency_level", "importance_score", "impact_scope", "news_category",
            "status", "is_breaking", "push_immediately", "view_count", "share_count"
        ]

        for field in basic_fields:
            if field in data:
                mapped_data[field] = data[field]

        # AI分析结果字段映射
        # 注意：AI提取的标签现在通过统一标签系统处理，不再直接映射到字段

        if "ai_classifications" in data:
            # 处理AI分类结果
            classifications = data["ai_classifications"]
            if isinstance(classifications, dict):
                # 映射主要分类到news_category
                if "primary_category" in classifications:
                    mapped_data["news_category"] = classifications["primary_category"]

                # 映射影响范围
                if "impact_scope" in classifications:
                    mapped_data["impact_scope"] = classifications["impact_scope"]

        if "sentiment_analysis" in data:
            # 情感分析结果可以影响重要性评分
            sentiment = data["sentiment_analysis"]
            if isinstance(sentiment, dict) and "confidence" in sentiment:
                # 如果没有其他重要性评分，使用情感分析的置信度
                if "importance_score" not in mapped_data:
                    mapped_data["importance_score"] = sentiment["confidence"]

        # 确保必需字段有默认值
        if "title" not in mapped_data:
            mapped_data["title"] = "未知标题"

        if "content" not in mapped_data:
            mapped_data["content"] = "内容为空"

        if "publish_time" not in mapped_data:
            from datetime import datetime, timezone
            mapped_data["publish_time"] = datetime.now(timezone.utc)

        if "status" not in mapped_data:
            mapped_data["status"] = "published"

        if "urgency_level" not in mapped_data:
            mapped_data["urgency_level"] = 2

        if "importance_score" not in mapped_data:
            mapped_data["importance_score"] = 0.5

        if "impact_scope" not in mapped_data:
            mapped_data["impact_scope"] = "domestic"

        return mapped_data

    async def retry_failed_records(self, max_retries: int = 3) -> Dict[str, int]:
        """重试失败的记录"""

        stats = {"retried": 0, "success": 0, "failed": 0}

        db = SessionLocal()
        try:
            # 查找失败的处理状态记录
            failed_statuses = db.query(DataProcessingStatus).filter(
                and_(
                    DataProcessingStatus.processing_stage == ProcessingStage.FAILED,
                    DataProcessingStatus.retry_count < max_retries
                )
            ).all()

            for status in failed_statuses:
                try:
                    # 获取原始记录
                    record = db.query(RawDataRecord).filter(
                        RawDataRecord.id == status.raw_data_id
                    ).first()

                    if not record:
                        continue

                    # 重置状态
                    record.processing_status = "pending"
                    status.retry_count += 1
                    status.processing_stage = ProcessingStage.PENDING
                    status.error_message = None
                    status.updated_at = datetime.now(timezone.utc)

                    db.commit()

                    # 重新处理
                    result = await self.process_single_record(record)
                    stats["retried"] += 1

                    if result["success"]:
                        stats["success"] += 1
                    else:
                        stats["failed"] += 1

                except Exception as e:
                    logger.error(f"重试记录失败 {status.id}: {e}")
                    stats["failed"] += 1

        finally:
            db.close()

        logger.info(f"重试完成: {stats}")
        return stats
