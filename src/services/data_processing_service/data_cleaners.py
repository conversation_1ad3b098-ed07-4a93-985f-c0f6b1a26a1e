"""
数据清洗和过滤服务
提供数据清洗、内容过滤、垃圾检测等功能
"""

import re
import logging
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional, Tuple
from urllib.parse import urlparse

logger = logging.getLogger(__name__)


class DataCleaner:
    """
    数据清洗器
    负责清洗和转换原始数据
    """

    def __init__(self):
        # 常见的垃圾内容模式
        self.spam_patterns = [
            r'广告|推广|营销|代理|加盟',
            r'微信|QQ|电话|联系方式',
            r'点击|链接|网址|www\.',
            r'免费|优惠|折扣|特价',
            r'立即|马上|赶紧|快速'
        ]
        
        # 标题清洗规则
        self.title_clean_patterns = [
            (r'^【.*?】\s*', ''),  # 去除开头的【】标记
            (r'^\[.*?\]\s*', ''),  # 去除开头的[]标记
            (r'^快讯[:：]\s*', ''),  # 去除"快讯:"前缀
            (r'^财经[:：]\s*', ''),  # 去除"财经:"前缀
            (r'\s+', ' '),  # 多个空格合并为一个
            (r'[^\w\s\u4e00-\u9fff\-\(\)（）\[\]【】]', ''),  # 保留中文、英文、数字、常用标点
        ]

    async def apply_extraction_rules(
        self, 
        data: Dict[str, Any], 
        config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """应用数据提取规则"""
        
        extracted_data = data.copy()
        
        # 内容清理
        if "content_cleaning" in config:
            cleaning_rules = config["content_cleaning"]
            
            for field in ["title", "content"]:
                if field in extracted_data:
                    extracted_data[field] = self._clean_text(
                        extracted_data[field], cleaning_rules
                    )
        
        # 时间处理
        if "time_processing" in config:
            extracted_data = self._process_time_fields(extracted_data, config["time_processing"])
        
        # 元数据提取
        if "metadata_extraction" in config:
            extracted_data = self._extract_metadata(extracted_data, config["metadata_extraction"])
        
        return extracted_data

    def _clean_text(self, text: str, rules: Dict[str, Any]) -> str:
        """清洗文本内容"""
        
        if not text:
            return text
            
        cleaned_text = text
        
        # 去除HTML标签
        if rules.get("remove_html", True):
            cleaned_text = re.sub(r'<[^>]+>', '', cleaned_text)
        
        # 去除多余空格
        if rules.get("normalize_whitespace", True):
            cleaned_text = re.sub(r'\s+', ' ', cleaned_text).strip()
        
        # 去除特殊字符
        if rules.get("remove_special_chars", False):
            cleaned_text = re.sub(r'[^\w\s\u4e00-\u9fff]', '', cleaned_text)
        
        # 长度限制
        max_length = rules.get("max_length")
        if max_length and len(cleaned_text) > max_length:
            cleaned_text = cleaned_text[:max_length] + "..."
        
        return cleaned_text

    def _process_time_fields(
        self, 
        data: Dict[str, Any], 
        config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """处理时间字段"""
        
        processed_data = data.copy()
        
        for field, time_config in config.items():
            if field in data:
                processed_time = self._parse_time(data[field], time_config)
                if processed_time:
                    processed_data[field] = processed_time
        
        return processed_data

    def _parse_time(self, time_value: Any, config: Dict[str, Any]) -> Optional[datetime]:
        """解析时间值"""
        
        if not time_value:
            return None
            
        try:
            # 如果已经是datetime对象
            if isinstance(time_value, datetime):
                return time_value
            
            # 字符串时间解析
            if isinstance(time_value, str):
                # 常见时间格式
                time_formats = [
                    "%Y-%m-%d %H:%M:%S",
                    "%Y-%m-%d %H:%M",
                    "%Y/%m/%d %H:%M:%S",
                    "%Y/%m/%d %H:%M",
                    "%m-%d %H:%M",
                    "%H:%M:%S",
                    "%H:%M"
                ]
                
                for fmt in time_formats:
                    try:
                        parsed_time = datetime.strptime(time_value, fmt)
                        
                        # 如果只有时间没有日期，使用今天的日期
                        if fmt in ["%H:%M:%S", "%H:%M"]:
                            today = datetime.now().date()
                            parsed_time = datetime.combine(today, parsed_time.time())
                        
                        # 如果只有月日没有年份，使用今年
                        elif fmt == "%m-%d %H:%M":
                            current_year = datetime.now().year
                            parsed_time = parsed_time.replace(year=current_year)
                        
                        # 确保时区信息
                        if parsed_time.tzinfo is None:
                            parsed_time = parsed_time.replace(tzinfo=timezone.utc)
                        
                        return parsed_time
                        
                    except ValueError:
                        continue
            
            logger.warning(f"无法解析时间值: {time_value}")
            return None
            
        except Exception as e:
            logger.error(f"时间解析失败: {e}")
            return None

    def _extract_metadata(
        self, 
        data: Dict[str, Any], 
        config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """提取元数据"""
        
        enhanced_data = data.copy()
        
        # 计算字数
        if config.get("calculate_word_count", True):
            content = data.get("content", "")
            if content:
                enhanced_data["word_count"] = len(content)
        
        # 提取URL域名
        if config.get("extract_domain", True):
            url = data.get("source_url", "")
            if url:
                try:
                    parsed_url = urlparse(url)
                    enhanced_data["url_domain"] = parsed_url.netloc
                except Exception:
                    pass
        
        return enhanced_data

    async def clean_content(
        self, 
        data: Dict[str, Any], 
        config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """清洗内容数据"""
        
        cleaned_data = data.copy()
        
        # 标题清洗
        if "title" in cleaned_data:
            cleaned_data["title"] = self._clean_title(cleaned_data["title"], config)
        
        # 内容清洗
        if "content" in cleaned_data:
            cleaned_data["content"] = self._clean_content_text(cleaned_data["content"], config)
        
        return cleaned_data

    def _clean_title(self, title: str, config: Dict[str, Any]) -> str:
        """清洗标题"""
        
        if not title:
            return title
            
        cleaned_title = title
        
        # 应用标题清洗规则
        title_config = config.get("title_cleaning", {})
        
        if title_config.get("apply_patterns", True):
            for pattern, replacement in self.title_clean_patterns:
                cleaned_title = re.sub(pattern, replacement, cleaned_title)
        
        # 长度限制
        max_length = title_config.get("max_length", 500)
        if len(cleaned_title) > max_length:
            cleaned_title = cleaned_title[:max_length]
        
        return cleaned_title.strip()

    def _clean_content_text(self, content: str, config: Dict[str, Any]) -> str:
        """清洗正文内容"""
        
        if not content:
            return content
            
        cleaned_content = content
        
        content_config = config.get("content_cleaning", {})
        
        # 去除HTML标签
        if content_config.get("remove_html", True):
            cleaned_content = re.sub(r'<[^>]+>', '', cleaned_content)
        
        # 去除多余的换行和空格
        if content_config.get("normalize_whitespace", True):
            cleaned_content = re.sub(r'\n\s*\n', '\n', cleaned_content)
            cleaned_content = re.sub(r'\s+', ' ', cleaned_content)
        
        # 去除广告内容
        if content_config.get("remove_ads", True):
            cleaned_content = self._remove_ad_content(cleaned_content)
        
        return cleaned_content.strip()

    def _remove_ad_content(self, content: str) -> str:
        """去除广告内容"""
        
        # 去除常见的广告模式
        ad_patterns = [
            r'【广告】.*?【/广告】',
            r'\[广告\].*?\[/广告\]',
            r'广告位.*?广告结束',
            r'推广.*?推广结束',
        ]
        
        for pattern in ad_patterns:
            content = re.sub(pattern, '', content, flags=re.DOTALL)
        
        return content

    async def transform_data(
        self, 
        data: Dict[str, Any], 
        config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """数据转换"""
        
        transformed_data = data.copy()
        
        # 重要性检测
        if config.get("importance_detection", True):
            transformed_data["urgency_level"] = self._detect_urgency(data)
        
        # 分类映射
        if "category_mapping" in config:
            transformed_data = self._apply_category_mapping(
                transformed_data, config["category_mapping"]
            )
        
        return transformed_data

    def _detect_urgency(self, data: Dict[str, Any]) -> int:
        """检测紧急程度"""
        
        title = data.get("title", "")
        content = data.get("content", "")
        text = f"{title} {content}".lower()
        
        # 紧急关键词
        urgent_keywords = ["突发", "紧急", "重大", "立即", "马上"]
        important_keywords = ["央行", "降准", "加息", "IPO", "重组", "监管"]
        
        for keyword in urgent_keywords:
            if keyword in text:
                return 3  # 紧急
        
        for keyword in important_keywords:
            if keyword in text:
                return 2  # 重要
        
        return 1  # 普通

    def _apply_category_mapping(
        self, 
        data: Dict[str, Any], 
        mapping: Dict[str, Any]
    ) -> Dict[str, Any]:
        """应用分类映射"""
        
        mapped_data = data.copy()
        
        for target_field, source_config in mapping.items():
            if isinstance(source_config, dict):
                source_field = source_config.get("source_field")
                mapping_rules = source_config.get("mapping", {})
                
                if source_field in data:
                    source_value = data[source_field]
                    mapped_value = mapping_rules.get(source_value, source_value)
                    mapped_data[target_field] = mapped_value
        
        return mapped_data

    async def validate_data(
        self, 
        data: Dict[str, Any], 
        config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """数据验证"""
        
        validation_result = {
            "valid": True,
            "errors": [],
            "warnings": []
        }
        
        # 必填字段检查
        required_fields = config.get("required_fields", [])
        for field in required_fields:
            if field not in data or not data[field]:
                validation_result["valid"] = False
                validation_result["errors"].append(f"必填字段缺失: {field}")
        
        # 字段长度检查
        field_lengths = config.get("field_lengths", {})
        for field, max_length in field_lengths.items():
            if field in data and data[field]:
                if len(str(data[field])) > max_length:
                    validation_result["warnings"].append(
                        f"字段 {field} 长度超过限制 {max_length}"
                    )
        
        # 数据格式检查
        format_checks = config.get("format_checks", {})
        for field, pattern in format_checks.items():
            if field in data and data[field]:
                if not re.match(pattern, str(data[field])):
                    validation_result["valid"] = False
                    validation_result["errors"].append(
                        f"字段 {field} 格式不正确"
                    )
        
        return validation_result


class ContentFilter:
    """
    内容过滤器
    负责检测和过滤垃圾内容、广告等
    """

    def __init__(self):
        # 垃圾内容关键词
        self.spam_keywords = [
            "广告", "推广", "营销", "代理", "加盟", "招商",
            "微信", "QQ", "电话", "联系方式", "咨询",
            "免费", "优惠", "折扣", "特价", "促销",
            "点击", "链接", "网址", "下载", "安装"
        ]
        
        # 低质量内容模式
        self.low_quality_patterns = [
            r'^.{0,20}$',  # 内容过短
            r'(.)\1{10,}',  # 重复字符
            r'[^\u4e00-\u9fff\w\s]{20,}',  # 过多特殊字符
        ]

    async def is_spam(self, data: Dict[str, Any]) -> bool:
        """检查是否为垃圾内容"""
        
        title = data.get("title", "")
        content = data.get("content", "")
        text = f"{title} {content}".lower()
        
        # 关键词检测
        spam_count = sum(1 for keyword in self.spam_keywords if keyword in text)
        if spam_count >= 3:  # 包含3个或以上垃圾关键词
            logger.info(f"检测到垃圾内容（关键词）: {spam_count} 个关键词")
            return True
        
        # 模式检测
        for pattern in self.low_quality_patterns:
            if re.search(pattern, text):
                logger.info(f"检测到垃圾内容（模式）: {pattern}")
                return True
        
        # 内容质量检测
        if self._is_low_quality_content(data):
            logger.info("检测到低质量内容")
            return True
        
        return False

    def _is_low_quality_content(self, data: Dict[str, Any]) -> bool:
        """检查是否为低质量内容"""
        
        content = data.get("content", "")
        title = data.get("title", "")
        
        # 内容过短
        if len(content) < 50 and len(title) < 10:
            return True
        
        # 重复内容比例过高
        if self._calculate_repetition_ratio(content) > 0.5:
            return True
        
        # 无意义内容
        if self._is_meaningless_content(content):
            return True
        
        return False

    def _calculate_repetition_ratio(self, text: str) -> float:
        """计算重复内容比例"""
        
        if len(text) < 100:
            return 0.0
        
        # 简单的重复检测：统计重复的句子
        sentences = re.split(r'[。！？\n]', text)
        sentences = [s.strip() for s in sentences if len(s.strip()) > 5]
        
        if len(sentences) < 2:
            return 0.0
        
        unique_sentences = set(sentences)
        repetition_ratio = 1 - (len(unique_sentences) / len(sentences))
        
        return repetition_ratio

    def _is_meaningless_content(self, content: str) -> bool:
        """检查是否为无意义内容"""
        
        # 检查是否主要由数字和符号组成
        meaningful_chars = re.sub(r'[\d\s\W]', '', content)
        if len(meaningful_chars) / max(len(content), 1) < 0.3:
            return True
        
        return False
