"""
数据处理服务依赖注入模块
提供数据处理服务相关的依赖注入功能
"""


from fastapi import Depends
from sqlalchemy.orm import Session

from ...core.database import get_db
from .service import DataProcessingPipelineService
from .business_service import FlashNewsService, NewsArticleService


def get_data_processing_pipeline_service(
    db: Session = Depends(get_db),
) -> DataProcessingPipelineService:
    """
    获取数据处理管道服务实例

    Args:
        db: 数据库会话

    Returns:
        DataProcessingPipelineService: 数据处理管道服务实例
    """
    return DataProcessingPipelineService(db_session=db)


def get_flash_news_service(
    db: Session = Depends(get_db),
) -> FlashNewsService:
    """
    获取快讯服务实例

    Args:
        db: 数据库会话

    Returns:
        FlashNewsService: 快讯服务实例
    """
    return FlashNewsService(db_session=db)


def get_news_article_service(
    db: Session = Depends(get_db),
) -> NewsArticleService:
    """
    获取新闻文章服务实例

    Args:
        db: 数据库会话

    Returns:
        NewsArticleService: 新闻文章服务实例
    """
    return NewsArticleService(db_session=db)
