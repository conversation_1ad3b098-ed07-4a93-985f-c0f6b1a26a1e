"""
数据处理服务B端管理API路由
定义数据处理管道管理相关的HTTP接口，带权限控制
"""

from typing import Annotated, List, Optional, Dict, Any

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session

from ...core.database import get_db
from ..permission_service.dependencies import require_permission
from ..user_service.models import User
from .dependencies import get_data_processing_pipeline_service, get_flash_news_service, get_news_article_service
from .schemas import (
    BatchOperationResponse,
    BatchPipelinePriorityUpdate,
    BatchPipelineStatusUpdate,
    DataProcessingPipelineCreate,
    DataProcessingPipelineFilter,
    DataProcessingPipelineListResponse,
    DataProcessingPipelineResponse,
    DataProcessingPipelineStatsResponse,
    DataProcessingPipelineUpdate,
    BusinessDataType,
    # 业务数据相关schema
    FlashNewsCreate,
    FlashNewsUpdate,
    FlashNewsResponse,
    FlashNewsListResponse,
    NewsArticleCreate,
    NewsArticleUpdate,
    NewsArticleResponse,
    NewsArticleListResponse,
)
from .service import DataProcessingPipelineService
from .business_service import FlashNewsService, NewsArticleService

# 创建路由器
router = APIRouter()


# ==================== 数据处理管道管理接口 ====================


@router.post(
    "/pipelines",
    response_model=DataProcessingPipelineResponse,
    status_code=status.HTTP_201_CREATED,
    summary="创建数据处理管道",
    description="创建新的数据处理管道配置",
)
async def create_processing_pipeline(
    pipeline_data: DataProcessingPipelineCreate,
    current_user: Annotated[User, Depends(require_permission("data_processing.pipeline.create"))],
    service: Annotated[DataProcessingPipelineService, Depends(get_data_processing_pipeline_service)],
    db: Annotated[Session, Depends(get_db)],
):
    """创建数据处理管道"""
    try:
        # 如果未指定创建者，使用当前用户
        if not pipeline_data.created_by and hasattr(current_user, 'username'):
            pipeline_data.created_by = current_user.username

        pipeline = service.create_pipeline(db, pipeline_data)
        return DataProcessingPipelineResponse.model_validate(pipeline)

    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="创建数据处理管道失败"
        )


@router.get(
    "/pipelines",
    response_model=DataProcessingPipelineListResponse,
    summary="获取数据处理管道列表",
    description="分页获取数据处理管道列表，支持多种过滤条件",
)
async def get_processing_pipelines(
    current_user: Annotated[User, Depends(require_permission("data_processing.pipeline.read"))],
    service: Annotated[DataProcessingPipelineService, Depends(get_data_processing_pipeline_service)],
    db: Annotated[Session, Depends(get_db)],
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回的记录数"),
    pipeline_code: Optional[str] = Query(None, description="管道代码过滤"),
    business_data_type: Optional[BusinessDataType] = Query(None, description="业务类型过滤"),
    source_id: Optional[int] = Query(None, description="数据源ID过滤"),
    is_active: Optional[bool] = Query(None, description="启用状态过滤"),
    is_default: Optional[bool] = Query(None, description="默认管道过滤"),
    domain_pattern: Optional[str] = Query(None, description="域名模式过滤"),
    priority_min: Optional[int] = Query(None, ge=1, le=10, description="最小优先级"),
    priority_max: Optional[int] = Query(None, ge=1, le=10, description="最大优先级"),
    created_by: Optional[str] = Query(None, description="创建者过滤"),
    # 忽略无效的params参数，防止前端传递错误格式参数导致500错误
    params: Optional[str] = Query(None, description="忽略的参数", include_in_schema=False),
):
    """获取数据处理管道列表"""
    try:
        # 构建过滤器
        filters = DataProcessingPipelineFilter(
            pipeline_code=pipeline_code,
            business_data_type=business_data_type,
            source_id=source_id,
            is_active=is_active,
            is_default=is_default,
            domain_pattern=domain_pattern,
            priority_min=priority_min,
            priority_max=priority_max,
            created_by=created_by,
        )

        # 计算分页参数
        page = skip // limit + 1
        size = limit

        pipelines, total = service.get_pipelines(db, filter_params=filters, page=page, size=size)

        # 计算分页信息
        pages = (total + limit - 1) // limit

        # 安全地转换管道数据
        pipeline_items = []
        for pipeline in pipelines:
            try:
                pipeline_item = DataProcessingPipelineResponse.model_validate(pipeline)
                pipeline_items.append(pipeline_item)
            except Exception as e:
                logger.error(f"转换管道数据失败 (ID: {getattr(pipeline, 'id', 'unknown')}): {str(e)}")
                # 跳过有问题的数据，继续处理其他数据
                continue

        return DataProcessingPipelineListResponse(
            items=pipeline_items,
            total=total,
            page=page,
            size=limit,
            pages=pages,
        )

    except Exception as e:
        # 记录详细错误信息
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"获取数据处理管道列表失败: {str(e)}", exc_info=True)

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取数据处理管道列表失败: {str(e)}"
        )


@router.get(
    "/pipelines/{pipeline_id}",
    response_model=DataProcessingPipelineResponse,
    summary="获取数据处理管道详情",
    description="根据ID获取数据处理管道的详细信息",
)
async def get_processing_pipeline(
    pipeline_id: int,
    current_user: Annotated[User, Depends(require_permission("data_processing.pipeline.read"))],
    service: Annotated[DataProcessingPipelineService, Depends(get_data_processing_pipeline_service)],
    db: Annotated[Session, Depends(get_db)],
):
    """获取数据处理管道详情"""
    pipeline = service.get_pipeline_by_id(db, pipeline_id)
    if not pipeline:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"数据处理管道不存在: {pipeline_id}"
        )

    return DataProcessingPipelineResponse.model_validate(pipeline)


@router.put(
    "/pipelines/{pipeline_id}",
    response_model=DataProcessingPipelineResponse,
    summary="更新数据处理管道",
    description="更新指定ID的数据处理管道",
)
async def update_processing_pipeline(
    pipeline_id: int,
    pipeline_data: DataProcessingPipelineUpdate,
    current_user: Annotated[User, Depends(require_permission("data_processing.pipeline.update"))],
    service: Annotated[DataProcessingPipelineService, Depends(get_data_processing_pipeline_service)],
    db: Annotated[Session, Depends(get_db)],
):
    """更新数据处理管道"""
    try:
        # 设置更新者
        if hasattr(current_user, 'username'):
            pipeline_data.updated_by = current_user.username

        pipeline = service.update_pipeline(db, pipeline_id, pipeline_data)
        if not pipeline:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"数据处理管道不存在: {pipeline_id}"
            )

        return DataProcessingPipelineResponse.model_validate(pipeline)

    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="更新数据处理管道失败"
        )


@router.delete(
    "/pipelines/{pipeline_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="删除数据处理管道",
    description="删除指定ID的数据处理管道",
)
async def delete_processing_pipeline(
    pipeline_id: int,
    current_user: Annotated[User, Depends(require_permission("data_processing.pipeline.delete"))],
    service: Annotated[DataProcessingPipelineService, Depends(get_data_processing_pipeline_service)],
    db: Annotated[Session, Depends(get_db)],
):
    """删除数据处理管道"""
    success = service.delete_pipeline(db, pipeline_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"数据处理管道不存在: {pipeline_id}"
        )


@router.get(
    "/pipelines/stats",
    response_model=DataProcessingPipelineStatsResponse,
    summary="获取数据处理管道统计",
    description="获取数据处理管道的统计信息",
)
async def get_processing_pipelines_stats(
    current_user: Annotated[User, Depends(require_permission("data_processing.pipeline.read"))],
    service: Annotated[DataProcessingPipelineService, Depends(get_data_processing_pipeline_service)],
    db: Annotated[Session, Depends(get_db)],
):
    """获取数据处理管道统计"""
    try:
        stats_data = service.get_pipeline_stats(db)
        return DataProcessingPipelineStatsResponse.model_validate(stats_data)
    except Exception as e:
        # 记录详细错误信息
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"获取管道统计失败: {str(e)}", exc_info=True)

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取管道统计失败: {str(e)}"
        )


@router.get(
    "/pipelines/business-type/{business_type}/active",
    response_model=List[DataProcessingPipelineResponse],
    summary="获取指定业务类型的活跃管道",
    description="获取指定业务类型的所有活跃管道，按优先级排序",
)
async def get_active_pipelines_by_type(
    business_type: BusinessDataType,
    current_user: Annotated[User, Depends(require_permission("data_processing.pipeline.read"))],
    service: Annotated[DataProcessingPipelineService, Depends(get_data_processing_pipeline_service)],
    db: Annotated[Session, Depends(get_db)],
):
    """获取指定业务类型的活跃管道"""
    try:
        pipelines = service.get_active_pipelines_by_type(db, business_type)
        return [DataProcessingPipelineResponse.model_validate(pipeline) for pipeline in pipelines]
    except Exception:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取活跃管道失败"
        )


@router.get(
    "/pipelines/match",
    response_model=DataProcessingPipelineResponse,
    summary="智能匹配数据处理管道",
    description="根据数据源和内容信息智能匹配最合适的数据处理管道",
)
async def find_matching_pipeline(
    current_user: Annotated[User, Depends(require_permission("data_processing.pipeline.read"))],
    service: Annotated[DataProcessingPipelineService, Depends(get_data_processing_pipeline_service)],
    db: Annotated[Session, Depends(get_db)],
    source_id: Optional[int] = Query(None, description="数据源ID"),
    url: Optional[str] = Query(None, description="内容URL"),
    domain: Optional[str] = Query(None, description="域名"),
    content_type: Optional[str] = Query(None, description="内容类型"),
    business_data_type: Optional[BusinessDataType] = Query(None, description="业务数据类型"),
):
    """智能匹配数据处理管道"""
    try:
        pipeline = service.find_processing_pipeline_by_params(
            db=db,
            source_id=source_id,
            url=url,
            domain=domain,
            content_type=content_type,
            business_data_type=business_data_type
        )
        
        if not pipeline:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="未找到匹配的数据处理管道"
            )

        return DataProcessingPipelineResponse.model_validate(pipeline)
    except HTTPException:
        raise
    except Exception:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="管道匹配失败"
        )


# ==================== 版本管理接口 ====================


@router.post(
    "/pipelines/{pipeline_id}/versions",
    response_model=DataProcessingPipelineResponse,
    status_code=status.HTTP_201_CREATED,
    summary="创建管道新版本",
    description="基于现有管道创建新版本",
)
async def create_pipeline_version(
    pipeline_id: int,
    pipeline_data: DataProcessingPipelineUpdate,
    current_user: Annotated[User, Depends(require_permission("data_processing.pipeline.create"))],
    service: Annotated[DataProcessingPipelineService, Depends(get_data_processing_pipeline_service)],
    db: Annotated[Session, Depends(get_db)],
):
    """创建管道新版本"""
    try:
        # 设置创建者和父版本信息
        if hasattr(current_user, 'username'):
            pipeline_data.updated_by = current_user.username

        new_version = service.create_pipeline_version(db, pipeline_id, pipeline_data)
        return DataProcessingPipelineResponse.model_validate(new_version)

    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="创建管道版本失败"
        )


@router.get(
    "/pipelines/{pipeline_id}/versions",
    response_model=List[DataProcessingPipelineResponse],
    summary="获取管道版本历史",
    description="获取指定管道的所有版本信息",
)
async def get_pipeline_versions(
    pipeline_id: int,
    current_user: Annotated[User, Depends(require_permission("data_processing.pipeline.read"))],
    service: Annotated[DataProcessingPipelineService, Depends(get_data_processing_pipeline_service)],
    db: Annotated[Session, Depends(get_db)],
):
    """获取管道版本历史"""
    try:
        versions = service.get_pipeline_versions(db, pipeline_id)
        return [DataProcessingPipelineResponse.model_validate(version) for version in versions]
    except Exception:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取管道版本失败"
        )


# ==================== 批量操作接口 ====================


@router.patch(
    "/pipelines/batch/status",
    response_model=BatchOperationResponse,
    summary="批量更新管道状态",
    description="批量启用或禁用数据处理管道",
)
async def batch_update_pipelines_status(
    update_data: BatchPipelineStatusUpdate,
    current_user: Annotated[User, Depends(require_permission("data_processing.pipeline.update"))],
    service: Annotated[DataProcessingPipelineService, Depends(get_data_processing_pipeline_service)],
    db: Annotated[Session, Depends(get_db)],
):
    """批量更新管道状态"""
    try:
        # 设置更新者
        if hasattr(current_user, 'username'):
            update_data.updated_by = current_user.username

        return service.batch_update_status(db, update_data)
    except Exception:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="批量更新状态失败"
        )


@router.patch(
    "/pipelines/batch/priority",
    response_model=BatchOperationResponse,
    summary="批量更新管道优先级",
    description="批量更新数据处理管道的优先级",
)
async def batch_update_pipelines_priority(
    update_data: BatchPipelinePriorityUpdate,
    current_user: Annotated[User, Depends(require_permission("data_processing.pipeline.update"))],
    service: Annotated[DataProcessingPipelineService, Depends(get_data_processing_pipeline_service)],
    db: Annotated[Session, Depends(get_db)],
):
    """批量更新管道优先级"""
    try:
        # 设置更新者
        if hasattr(current_user, 'username'):
            update_data.updated_by = current_user.username

        return service.batch_update_priority(db, update_data)
    except Exception:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="批量更新优先级失败"
        )


# ==================== 测试和调试接口 ====================


@router.post(
    "/pipelines/{pipeline_id}/test",
    summary="测试数据处理管道",
    description="测试数据处理管道的配置是否正确",
)
async def test_processing_pipeline(
    pipeline_id: int,
    test_data: dict,
    current_user: Annotated[User, Depends(require_permission("data_processing.pipeline.read"))],
    service: Annotated[DataProcessingPipelineService, Depends(get_data_processing_pipeline_service)],
    db: Annotated[Session, Depends(get_db)],
):
    """测试数据处理管道"""
    pipeline = service.get_pipeline_by_id(db, pipeline_id)
    if not pipeline:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"数据处理管道不存在: {pipeline_id}"
        )

    try:
        # 管道配置测试逻辑
        result = {
            "pipeline_id": pipeline_id,
            "pipeline_code": pipeline.pipeline_code,
            "pipeline_name": pipeline.pipeline_name,
            "test_result": "success",
            "message": "管道配置验证通过",
            "field_mapping_test": {},  # 字段映射测试结果
            "extraction_test": {},     # 数据提取测试结果
            "transformation_test": {}, # 数据转换测试结果
            "validation_test": {},     # 数据验证测试结果
            "enrichment_test": {},     # 数据增强测试结果
            "validation_errors": [],   # 验证错误
        }

        # 模拟字段映射测试
        if pipeline.field_mapping and test_data:
            mapped_fields = {}
            for target_field, source_path in pipeline.field_mapping.items():
                mapped_fields[target_field] = f"从 {source_path} 映射"
            result["field_mapping_test"] = mapped_fields

        # 模拟数据提取测试
        if pipeline.data_extraction_config:
            result["extraction_test"] = {
                "status": "success",
                "extracted_fields": len(pipeline.data_extraction_config),
                "message": "数据提取配置验证通过"
            }

        # 模拟数据转换测试
        if pipeline.data_transformation_config:
            result["transformation_test"] = {
                "status": "success",
                "transformation_rules": len(pipeline.data_transformation_config),
                "message": "数据转换配置验证通过"
            }

        # 模拟数据验证测试
        if pipeline.data_validation_config:
            result["validation_test"] = {
                "status": "success",
                "validation_rules": len(pipeline.data_validation_config),
                "message": "数据验证配置验证通过"
            }

        # 模拟数据增强测试
        if pipeline.data_enrichment_config:
            result["enrichment_test"] = {
                "status": "success",
                "enrichment_rules": len(pipeline.data_enrichment_config),
                "message": "数据增强配置验证通过"
            }

        return result

    except Exception:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="管道测试失败"
        )


@router.put(
    "/pipelines/{pipeline_id}/execution-stats",
    response_model=DataProcessingPipelineResponse,
    summary="更新管道执行统计",
    description="更新管道的执行统计信息（内部接口）",
)
async def update_pipeline_execution_stats(
    pipeline_id: int,
    current_user: Annotated[User, Depends(require_permission("data_processing.pipeline.update"))],
    service: Annotated[DataProcessingPipelineService, Depends(get_data_processing_pipeline_service)],
    db: Annotated[Session, Depends(get_db)],
    execution_time_ms: int = Query(..., description="执行时间（毫秒）"),
    success: bool = Query(..., description="是否执行成功"),
):
    """更新管道执行统计"""
    try:
        pipeline = service.update_execution_stats(db, pipeline_id, execution_time_ms, success)
        if not pipeline:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"数据处理管道不存在: {pipeline_id}"
            )

        return DataProcessingPipelineResponse.model_validate(pipeline)

    except HTTPException:
        raise
    except Exception:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="更新执行统计失败"
        )


# ==================== 业务数据管理接口 ====================

# FlashNews 快讯管理接口

# @router.post(
#     "/flash-news",
#     response_model=FlashNewsResponse,
#     status_code=status.HTTP_201_CREATED,
#     summary="创建快讯",
#     description="创建新的快讯记录",
# )
# async def create_flash_news(
#     flash_news_data: FlashNewsCreate,
#     current_user: Annotated[User, Depends(require_permission("flash_news.create"))],
#     service: Annotated[FlashNewsService, Depends(get_flash_news_service)],
#     db: Annotated[Session, Depends(get_db)],
# ):
#     """创建快讯"""
#     try:
#         flash_news = service.create_flash_news(db, flash_news_data)
#         return FlashNewsResponse.model_validate(flash_news)
#     except Exception as e:
#         raise HTTPException(
#             status_code=status.HTTP_400_BAD_REQUEST,
#             detail=f"创建快讯失败: {str(e)}"
#         )


@router.get(
    "/flash-news",
    response_model=FlashNewsListResponse,
    summary="获取快讯列表",
    description="获取快讯列表，支持多种过滤条件",
)
async def get_flash_news_list(
    current_user: Annotated[User, Depends(require_permission("flash_news.flash_news.read"))],
    service: Annotated[FlashNewsService, Depends(get_flash_news_service)],
    db: Annotated[Session, Depends(get_db)],
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回的记录数"),
    status: Optional[str] = Query(None, description="状态过滤"),
    urgency_level: Optional[int] = Query(None, ge=1, le=3, description="紧急度过滤"),
    news_category: Optional[str] = Query(None, description="新闻分类过滤"),
    is_breaking: Optional[bool] = Query(None, description="是否突发新闻过滤"),
):
    """获取快讯列表"""
    try:
        flash_news_list, total = service.get_flash_news_list_with_tags(
            db, skip, limit, status, urgency_level, news_category, is_breaking
        )
        return FlashNewsListResponse(
            items=flash_news_list,
            total=total,
            skip=skip,
            limit=limit,
        )
    except Exception:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取快讯列表失败"
        )


@router.get(
    "/flash-news/{flash_news_id}",
    response_model=FlashNewsResponse,
    summary="获取快讯详情",
    description="根据ID获取快讯详情",
)
async def get_flash_news_detail(
    flash_news_id: int,
    current_user: Annotated[User, Depends(require_permission("flash_news.flash_news.read"))],
    service: Annotated[FlashNewsService, Depends(get_flash_news_service)],
    db: Annotated[Session, Depends(get_db)],
):
    """获取快讯详情"""
    flash_news = service.get_flash_news_with_tags_by_id(db, flash_news_id)
    if not flash_news:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"快讯不存在: {flash_news_id}"
        )

    return flash_news


@router.put(
    "/flash-news/{flash_news_id}",
    response_model=FlashNewsResponse,
    summary="更新快讯",
    description="更新指定ID的快讯信息",
)
async def update_flash_news(
    flash_news_id: int,
    flash_news_data: FlashNewsUpdate,
    current_user: Annotated[User, Depends(require_permission("flash_news.flash_news.update"))],
    service: Annotated[FlashNewsService, Depends(get_flash_news_service)],
    db: Annotated[Session, Depends(get_db)],
):
    """更新快讯"""
    try:
        flash_news = service.update_flash_news(db, flash_news_id, flash_news_data)
        if not flash_news:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"快讯不存在: {flash_news_id}"
            )

        return FlashNewsResponse.model_validate(flash_news)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"更新快讯失败: {str(e)}"
        )


@router.delete(
    "/flash-news/{flash_news_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="删除快讯",
    description="删除指定ID的快讯",
)
async def delete_flash_news(
    flash_news_id: int,
    current_user: Annotated[User, Depends(require_permission("flash_news.flash_news.delete"))],
    service: Annotated[FlashNewsService, Depends(get_flash_news_service)],
    db: Annotated[Session, Depends(get_db)],
):
    """删除快讯"""
    success = service.delete_flash_news(db, flash_news_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"快讯不存在: {flash_news_id}"
        )


@router.get(
    "/flash-news/stats",
    response_model=Dict[str, Any],
    summary="获取快讯统计信息",
    description="获取快讯的统计信息",
)
async def get_flash_news_stats(
    current_user: Annotated[User, Depends(require_permission("flash_news.flash_news.stats"))],
    service: Annotated[FlashNewsService, Depends(get_flash_news_service)],
    db: Annotated[Session, Depends(get_db)],
):
    """获取快讯统计信息"""
    try:
        stats = service.get_flash_news_stats(db)
        return stats
    except Exception:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取快讯统计信息失败"
        )


# NewsArticle 新闻文章管理接口

# @router.post(
#     "/news-articles",
#     response_model=NewsArticleResponse,
#     status_code=status.HTTP_201_CREATED,
#     summary="创建新闻文章",
#     description="创建新的新闻文章记录",
# )
# async def create_news_article(
#     news_article_data: NewsArticleCreate,
#     current_user: Annotated[User, Depends(require_permission("news_article.news_article.create"))],
#     service: Annotated[NewsArticleService, Depends(get_news_article_service)],
#     db: Annotated[Session, Depends(get_db)],
# ):
#     """创建新闻文章"""
#     try:
#         news_article = service.create_news_article(db, news_article_data)
#         return NewsArticleResponse.model_validate(news_article)
#     except Exception as e:
#         raise HTTPException(
#             status_code=status.HTTP_400_BAD_REQUEST,
#             detail=f"创建新闻文章失败: {str(e)}"
#         )


@router.get(
    "/news-articles",
    response_model=NewsArticleListResponse,
    summary="获取新闻文章列表",
    description="获取新闻文章列表，支持多种过滤条件",
)
async def get_news_article_list(
    current_user: Annotated[User, Depends(require_permission("news_article.news_article.read"))],
    service: Annotated[NewsArticleService, Depends(get_news_article_service)],
    db: Annotated[Session, Depends(get_db)],
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回的记录数"),
    status: Optional[str] = Query(None, description="状态过滤"),
    primary_category: Optional[str] = Query(None, description="主要分类过滤"),
    author: Optional[str] = Query(None, description="作者过滤"),
    source_media: Optional[str] = Query(None, description="发布媒体过滤"),
    is_featured: Optional[bool] = Query(None, description="是否精选过滤"),
    is_trending: Optional[bool] = Query(None, description="是否热门过滤"),
):
    """获取新闻文章列表"""
    try:
        news_article_list, total = service.get_news_article_list_with_tags(
            db, skip, limit, status, primary_category, author, source_media, is_featured, is_trending
        )
        return NewsArticleListResponse(
            items=news_article_list,
            total=total,
            skip=skip,
            limit=limit,
        )
    except Exception:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取新闻文章列表失败"
        )


@router.get(
    "/news-articles/{news_article_id}",
    response_model=NewsArticleResponse,
    summary="获取新闻文章详情",
    description="根据ID获取新闻文章详情",
)
async def get_news_article_detail(
    news_article_id: int,
    current_user: Annotated[User, Depends(require_permission("news_article.news_article.read"))],
    service: Annotated[NewsArticleService, Depends(get_news_article_service)],
    db: Annotated[Session, Depends(get_db)],
):
    """获取新闻文章详情"""
    news_article = service.get_news_article_with_tags_by_id(db, news_article_id)
    if not news_article:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"新闻文章不存在: {news_article_id}"
        )

    return news_article


@router.put(
    "/news-articles/{news_article_id}",
    response_model=NewsArticleResponse,
    summary="更新新闻文章",
    description="更新指定ID的新闻文章信息",
)
async def update_news_article(
    news_article_id: int,
    news_article_data: NewsArticleUpdate,
    current_user: Annotated[User, Depends(require_permission("news_article.news_article.update"))],
    service: Annotated[NewsArticleService, Depends(get_news_article_service)],
    db: Annotated[Session, Depends(get_db)],
):
    """更新新闻文章"""
    try:
        news_article = service.update_news_article(db, news_article_id, news_article_data)
        if not news_article:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"新闻文章不存在: {news_article_id}"
            )

        return NewsArticleResponse.model_validate(news_article)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"更新新闻文章失败: {str(e)}"
        )


@router.delete(
    "/news-articles/{news_article_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="删除新闻文章",
    description="删除指定ID的新闻文章",
)
async def delete_news_article(
    news_article_id: int,
    current_user: Annotated[User, Depends(require_permission("news_article.news_article.delete"))],
    service: Annotated[NewsArticleService, Depends(get_news_article_service)],
    db: Annotated[Session, Depends(get_db)],
):
    """删除新闻文章"""
    success = service.delete_news_article(db, news_article_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"新闻文章不存在: {news_article_id}"
        )


@router.get(
    "/news-articles/stats",
    response_model=Dict[str, Any],
    summary="获取新闻文章统计信息",
    description="获取新闻文章的统计信息",
)
async def get_news_article_stats(
    current_user: Annotated[User, Depends(require_permission("news_article.news_article.stats"))],
    service: Annotated[NewsArticleService, Depends(get_news_article_service)],
    db: Annotated[Session, Depends(get_db)],
):
    """获取新闻文章统计信息"""
    try:
        stats = service.get_news_article_stats(db)
        return stats
    except Exception:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取新闻文章统计信息失败"
        )
