"""
数据处理管道配置
为不同数据源提供默认的处理管道配置
"""

from typing import Dict, Any, List
from ..data_collection_service.models import BusinessDataType


class PipelineConfigManager:
    """处理管道配置管理器"""
    
    def __init__(self):
        self.configs = self._load_default_configs()
    
    def _load_default_configs(self) -> Dict[str, Dict[str, Any]]:
        """加载默认配置"""
        
        return {
            # 快讯处理管道配置
            "flash_news_basic": {
                "pipeline_code": "flash_news_basic",
                "pipeline_name": "快讯基础处理",
                "target_business_type": BusinessDataType.FLASH_NEWS,
                "field_mapping": {
                    "title": "title",
                    "content": "content", 
                    "publish_time": "publish_time",
                    "author": "author",
                    "source_url": "source_url"
                },
                "data_extraction_config": {
                    "content_cleaning": {
                        "remove_html": True,
                        "normalize_whitespace": True,
                        "max_length": 10000
                    },
                    "time_processing": {
                        "publish_time": {
                            "required": True,
                            "formats": [
                                "%Y-%m-%d %H:%M:%S",
                                "%Y/%m/%d %H:%M:%S",
                                "%Y-%m-%d %H:%M",
                                "%m-%d %H:%M"
                            ]
                        }
                    },
                    "metadata_extraction": {
                        "calculate_word_count": True,
                        "extract_domain": True
                    }
                },
                "data_transformation_config": {
                    "title_cleaning": {
                        "apply_patterns": True,
                        "max_length": 500,
                        "remove_prefixes": ["【快讯】", "[快讯]", "快讯：", "财经："]
                    },
                    "content_cleaning": {
                        "remove_html": True,
                        "normalize_whitespace": True,
                        "remove_ads": True,
                        "min_length": 10
                    },
                    "importance_detection": True,
                    "urgency_detection": {
                        "urgent_keywords": ["突发", "紧急", "重大", "立即"],
                        "important_keywords": ["央行", "降准", "加息", "IPO", "重组", "监管"]
                    }
                },
                "data_validation_config": {
                    "required_fields": ["title", "content", "publish_time"],
                    "field_lengths": {
                        "title": 500,
                        "content": 10000,
                        "author": 200
                    },
                    "format_checks": {
                        "source_url": r"^https?://.*"
                    }
                },
                "data_enrichment_config": {
                    "generate_summary": True,
                    "extract_tags": True,
                    "classify_content": True,
                    "extract_entities": True,
                    "sentiment_analysis": True,
                    "importance_scoring": True
                }
            },
            
            # 新闻文章处理管道配置
            "news_article_comprehensive": {
                "pipeline_code": "news_article_comprehensive",
                "pipeline_name": "新闻文章综合处理",
                "target_business_type": BusinessDataType.NEWS_ARTICLE,
                "field_mapping": {
                    "title": "title",
                    "subtitle": "subtitle",
                    "content": "content",
                    "abstract": "abstract",
                    "author": "author",
                    "publish_time": "publish_time",
                    "update_time": "update_time",
                    "source_url": "source_url",
                    "source_media": "source_media",
                    "featured_image": "featured_image_url"
                },
                "data_extraction_config": {
                    "content_cleaning": {
                        "remove_html": True,
                        "normalize_whitespace": True,
                        "preserve_paragraphs": True
                    },
                    "time_processing": {
                        "publish_time": {"required": True},
                        "update_time": {"required": False}
                    },
                    "metadata_extraction": {
                        "calculate_word_count": True,
                        "extract_domain": True,
                        "estimate_reading_time": True
                    }
                },
                "data_transformation_config": {
                    "title_cleaning": {
                        "apply_patterns": True,
                        "max_length": 1000
                    },
                    "content_cleaning": {
                        "remove_html": True,
                        "normalize_whitespace": True,
                        "remove_ads": True,
                        "min_length": 100
                    },
                    "quality_assessment": {
                        "min_word_count": 50,
                        "max_repetition_ratio": 0.3
                    }
                },
                "data_validation_config": {
                    "required_fields": ["title", "content", "publish_time"],
                    "field_lengths": {
                        "title": 1000,
                        "subtitle": 1000,
                        "content": 50000,
                        "abstract": 2000
                    }
                },
                "data_enrichment_config": {
                    "generate_summary": True,
                    "extract_tags": True,
                    "classify_content": True,
                    "extract_entities": True,
                    "sentiment_analysis": True,
                    "importance_scoring": True,
                    "readability_analysis": True
                }
            },
            
            # 研究报告处理管道配置
            "research_report_professional": {
                "pipeline_code": "research_report_professional",
                "pipeline_name": "研究报告专业处理",
                "target_business_type": BusinessDataType.RESEARCH_REPORT,
                "field_mapping": {
                    "title": "title",
                    "content": "full_content",
                    "summary": "executive_summary",
                    "institution": "institution_name",
                    "analyst": "analyst_name",
                    "publish_time": "publish_time",
                    "target_stock": "target_stock_code",
                    "rating": "investment_rating",
                    "target_price": "target_price"
                },
                "data_extraction_config": {
                    "content_cleaning": {
                        "remove_html": True,
                        "preserve_structure": True,
                        "extract_tables": True
                    },
                    "financial_data_extraction": {
                        "extract_numbers": True,
                        "extract_ratios": True,
                        "extract_forecasts": True
                    }
                },
                "data_transformation_config": {
                    "report_type_detection": {
                        "company_keywords": ["公司", "股份", "集团"],
                        "industry_keywords": ["行业", "板块", "领域"],
                        "macro_keywords": ["宏观", "经济", "政策"]
                    },
                    "rating_standardization": {
                        "buy_synonyms": ["买入", "推荐", "强烈推荐"],
                        "hold_synonyms": ["持有", "中性", "观望"],
                        "sell_synonyms": ["卖出", "减持", "不推荐"]
                    }
                },
                "data_validation_config": {
                    "required_fields": ["title", "full_content", "institution_name"],
                    "field_lengths": {
                        "title": 1000,
                        "full_content": 100000
                    },
                    "format_checks": {
                        "target_stock_code": r"^[0-9]{6}\.(SH|SZ)$",
                        "target_price": r"^\d+(\.\d{1,2})?$"
                    }
                },
                "data_enrichment_config": {
                    "generate_summary": True,
                    "extract_tags": True,
                    "classify_content": True,
                    "extract_entities": True,
                    "financial_analysis": True,
                    "risk_assessment": True
                }
            },
            
            # 经济数据处理管道配置
            "economic_data_structured": {
                "pipeline_code": "economic_data_structured",
                "pipeline_name": "经济数据结构化处理",
                "target_business_type": BusinessDataType.ECONOMIC_DATA,
                "field_mapping": {
                    "indicator_name": "indicator_name",
                    "time_period": "time_period",
                    "release_time": "current_release_time",
                    "previous_value": "previous_value",
                    "forecast_value": "consensus_value",
                    "actual_value": "actual_value",
                    "unit": "unit"
                },
                "data_extraction_config": {
                    "numeric_extraction": {
                        "extract_decimals": True,
                        "handle_percentages": True,
                        "handle_currencies": True
                    },
                    "time_extraction": {
                        "extract_periods": True,
                        "standardize_formats": True
                    }
                },
                "data_transformation_config": {
                    "value_standardization": {
                        "convert_percentages": True,
                        "normalize_units": True,
                        "calculate_changes": True
                    },
                    "impact_assessment": {
                        "calculate_surprise_index": True,
                        "assess_market_impact": True
                    }
                },
                "data_validation_config": {
                    "required_fields": ["indicator_name", "current_release_time", "actual_value"],
                    "numeric_ranges": {
                        "actual_value": {"min": -999999, "max": 999999},
                        "previous_value": {"min": -999999, "max": 999999}
                    }
                },
                "data_enrichment_config": {
                    "generate_summary": True,
                    "classify_content": True,
                    "impact_analysis": True,
                    "trend_analysis": True
                }
            }
        }
    
    def get_config(self, pipeline_code: str) -> Dict[str, Any]:
        """获取指定管道配置"""
        return self.configs.get(pipeline_code, {})
    
    def get_configs_by_business_type(self, business_type: BusinessDataType) -> List[Dict[str, Any]]:
        """根据业务类型获取配置"""
        return [
            config for config in self.configs.values()
            if config.get("target_business_type") == business_type
        ]
    
    def list_available_configs(self) -> List[str]:
        """列出所有可用配置"""
        return list(self.configs.keys())
    
    def add_custom_config(self, pipeline_code: str, config: Dict[str, Any]):
        """添加自定义配置"""
        self.configs[pipeline_code] = config
    
    def get_default_config_for_source(self, source_name: str, business_type: BusinessDataType) -> Dict[str, Any]:
        """根据数据源和业务类型获取默认配置"""
        
        # 根据数据源特点选择合适的配置
        source_lower = source_name.lower()
        
        if business_type == BusinessDataType.FLASH_NEWS:
            return self.get_config("flash_news_basic")
        elif business_type == BusinessDataType.NEWS_ARTICLE:
            return self.get_config("news_article_comprehensive")
        elif business_type == BusinessDataType.RESEARCH_REPORT:
            return self.get_config("research_report_professional")
        elif business_type == BusinessDataType.ECONOMIC_DATA:
            return self.get_config("economic_data_structured")
        else:
            # 默认使用快讯配置
            return self.get_config("flash_news_basic")


# 全局配置管理器实例
pipeline_config_manager = PipelineConfigManager()
