"""
真实的AI客户端实现
支持DeepSeek、OpenAI、千问等多种AI服务提供商
"""

import asyncio
import json
import logging
import httpx
import os
from typing import Any, Dict, List, Optional
from abc import ABC, abstractmethod

from ...core.ai_config import get_default_model_config, ModelType, ModelProvider
from ...core.database import SessionLocal
from ..ai_service.service import AIModelService
from ..ai_service.schemas import ModelType as AIModelType

logger = logging.getLogger(__name__)


class BaseAIClient(ABC):
    """AI客户端抽象基类"""
    
    def __init__(self, model_config):
        self.model_config = model_config
        self.client = None
        
    @abstractmethod
    async def chat_completion(
        self, 
        messages: List[Dict[str, str]], 
        max_tokens: int = 1000,
        temperature: float = 0.7,
        **kwargs
    ) -> Dict[str, Any]:
        """聊天完成接口"""
        pass
        
    async def __aenter__(self):
        """异步上下文管理器入口"""
        if self.client is None:
            self.client = httpx.AsyncClient(
                timeout=httpx.Timeout(30.0),
                limits=httpx.Limits(max_keepalive_connections=5, max_connections=10)
            )
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.client:
            await self.client.aclose()


class DeepSeekClient(BaseAIClient):
    """DeepSeek AI客户端"""
    
    async def chat_completion(
        self, 
        messages: List[Dict[str, str]], 
        max_tokens: int = 1000,
        temperature: float = 0.7,
        **kwargs
    ) -> Dict[str, Any]:
        """DeepSeek聊天完成"""
        
        if not self.model_config or not self.model_config.api_key:
            logger.warning("DeepSeek API密钥未配置，使用模拟响应")
            return await self._mock_response(messages)
            
        try:
            headers = {
                "Authorization": f"Bearer {self.model_config.api_key}",
                "Content-Type": "application/json"
            }
            
            payload = {
                "model": self.model_config.model_id,
                "messages": messages,
                "max_tokens": max_tokens,
                "temperature": temperature,
                "stream": False
            }
            
            response = await self.client.post(
                f"{self.model_config.api_base}/chat/completions",
                headers=headers,
                json=payload
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result.get("choices", [{}])[0].get("message", {}).get("content", "")
                return {"content": content}
            else:
                logger.error(f"DeepSeek API错误: {response.status_code} - {response.text}")
                return await self._mock_response(messages)
                
        except Exception as e:
            logger.error(f"DeepSeek API调用失败: {e}")
            return await self._mock_response(messages)
    
    async def _mock_response(self, messages: List[Dict[str, str]]) -> Dict[str, Any]:
        """模拟响应"""
        if not messages:
            return {"content": ""}
            
        user_message = messages[-1].get("content", "").lower()
        
        if "摘要" in user_message:
            return {"content": "这是AI生成的摘要内容"}
        elif "标签" in user_message:
            return {"content": "央行,货币政策,降准,流动性,银行"}
        elif "分类" in user_message:
            return {"content": '{"primary_category": "monetary_policy", "impact_scope": "domestic"}'}
        elif "实体" in user_message:
            return {"content": '{"companies": ["中国人民银行"], "people": [], "locations": ["中国"]}'}
        elif "情感" in user_message:
            return {"content": '{"sentiment": "positive", "market_sentiment": "bullish", "confidence": 0.8}'}
        else:
            return {"content": "AI分析结果"}


class OpenAIClient(BaseAIClient):
    """OpenAI客户端"""
    
    async def chat_completion(
        self, 
        messages: List[Dict[str, str]], 
        max_tokens: int = 1000,
        temperature: float = 0.7,
        **kwargs
    ) -> Dict[str, Any]:
        """OpenAI聊天完成"""
        
        if not self.model_config or not self.model_config.api_key:
            logger.warning("OpenAI API密钥未配置，使用模拟响应")
            return await self._mock_response(messages)
            
        try:
            headers = {
                "Authorization": f"Bearer {self.model_config.api_key}",
                "Content-Type": "application/json"
            }
            
            payload = {
                "model": self.model_config.model_id,
                "messages": messages,
                "max_tokens": max_tokens,
                "temperature": temperature
            }
            
            response = await self.client.post(
                f"{self.model_config.api_base}/chat/completions",
                headers=headers,
                json=payload
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result.get("choices", [{}])[0].get("message", {}).get("content", "")
                return {"content": content}
            else:
                logger.error(f"OpenAI API错误: {response.status_code} - {response.text}")
                return await self._mock_response(messages)
                
        except Exception as e:
            logger.error(f"OpenAI API调用失败: {e}")
            return await self._mock_response(messages)
    
    async def _mock_response(self, messages: List[Dict[str, str]]) -> Dict[str, Any]:
        """模拟响应"""
        if not messages:
            return {"content": ""}
            
        user_message = messages[-1].get("content", "").lower()
        
        if "摘要" in user_message or "summary" in user_message:
            return {"content": "AI generated summary content"}
        elif "标签" in user_message or "tag" in user_message:
            return {"content": "central_bank,monetary_policy,reserve_requirement,liquidity,banking"}
        elif "分类" in user_message or "classif" in user_message:
            return {"content": '{"primary_category": "monetary_policy", "impact_scope": "domestic"}'}
        elif "实体" in user_message or "entity" in user_message:
            return {"content": '{"companies": ["People\'s Bank of China"], "people": [], "locations": ["China"]}'}
        elif "情感" in user_message or "sentiment" in user_message:
            return {"content": '{"sentiment": "positive", "market_sentiment": "bullish", "confidence": 0.8}'}
        else:
            return {"content": "AI analysis result"}


class QwenClient(BaseAIClient):
    """千问AI客户端"""

    async def chat_completion(
        self,
        messages: List[Dict[str, str]],
        max_tokens: int = 1000,
        temperature: float = 0.7,
        **kwargs
    ) -> Dict[str, Any]:
        """千问聊天完成"""

        if not self.model_config or not self.model_config.api_key:
            logger.warning("千问API密钥未配置，使用模拟响应")
            return await self._mock_response(messages)

        try:
            headers = {
                "Authorization": f"Bearer {self.model_config.api_key}",
                "Content-Type": "application/json"
            }

            payload = {
                "model": self.model_config.model_id,
                "messages": messages,
                "max_tokens": max_tokens,
                "temperature": temperature,
                "top_p": 0.8,
                "stream": False
            }

            response = await self.client.post(
                self.model_config.api_base,
                headers=headers,
                json=payload
            )

            if response.status_code == 200:
                result = response.json()
                # 千问API响应格式: {"choices": [{"message": {"content": "..."}}]}
                choices = result.get("choices", [])
                if choices:
                    content = choices[0].get("message", {}).get("content", "")
                    return {"content": content}
                else:
                    logger.warning(f"千问API响应格式异常: {result}")
                    return await self._mock_response(messages)
            else:
                logger.error(f"千问API错误: {response.status_code} - {response.text}")
                return await self._mock_response(messages)

        except Exception as e:
            logger.error(f"千问API调用失败: {e}")
            return await self._mock_response(messages)

    async def _mock_response(self, messages: List[Dict[str, str]]) -> Dict[str, Any]:
        """模拟响应"""
        if not messages:
            return {"content": ""}

        user_message = messages[-1].get("content", "").lower()

        if "摘要" in user_message:
            return {"content": "这是千问AI生成的摘要内容"}
        elif "标签" in user_message:
            return {"content": "央行,货币政策,降准,流动性,银行,金融市场"}
        elif "分类" in user_message:
            return {"content": '{"primary_category": "monetary_policy", "impact_scope": "domestic", "urgency": "high"}'}
        elif "实体" in user_message:
            return {"content": '{"companies": ["中国人民银行", "工商银行"], "people": ["易纲"], "locations": ["中国", "北京"]}'}
        elif "情感" in user_message:
            return {"content": '{"sentiment": "positive", "market_sentiment": "bullish", "confidence": 0.85}'}
        else:
            return {"content": "千问AI分析结果"}


class AIClientFactory:
    """AI客户端工厂"""

    @staticmethod
    def create_client(model_config=None, ai_model_service=None) -> BaseAIClient:
        """
        创建AI客户端

        Args:
            model_config: 模型配置，如果为None则使用默认配置
            ai_model_service: AI模型服务实例，用于获取数据库配置

        Returns:
            BaseAIClient: AI客户端实例
        """

        # 优先使用数据库配置
        if model_config is None:
            if ai_model_service is None:
                db = SessionLocal()
                try:
                    ai_model_service = AIModelService(db)
                    db_model = ai_model_service.get_default_model(AIModelType.LLM)
                    if db_model:
                        # 转换数据库模型配置为客户端配置
                        model_config = AIClientFactory._convert_db_model_to_config(db_model)
                        logger.info(f"使用数据库默认模型: {db_model.model_name} (提供商: {db_model.provider})")
                except Exception as e:
                    logger.warning(f"获取数据库模型配置失败: {e}")
                finally:
                    db.close()
            else:
                # 如果传入了ai_model_service，直接使用
                try:
                    db_model = ai_model_service.get_default_model(AIModelType.LLM)
                    if db_model:
                        model_config = AIClientFactory._convert_db_model_to_config(db_model)
                        logger.info(f"使用数据库默认模型: {db_model.model_name} (提供商: {db_model.provider})")
                except Exception as e:
                    logger.warning(f"获取数据库模型配置失败: {e}")

        # 如果仍然没有配置，使用硬编码默认配置
        if model_config is None:
            model_config = get_default_model_config(ModelType.CHAT)
            logger.info("使用硬编码默认模型配置")

        if not model_config:
            logger.warning("未找到可用的AI模型配置，使用DeepSeek模拟客户端")
            return DeepSeekClient(None)

        # 根据提供商创建对应客户端
        provider = getattr(model_config, 'provider', None)
        if hasattr(model_config, 'provider_name'):
            provider_name = model_config.provider_name.lower()
        elif hasattr(model_config, 'provider'):
            if hasattr(model_config.provider, 'value'):
                provider_name = model_config.provider.value.lower()
            else:
                provider_name = str(model_config.provider).lower()
        else:
            provider_name = "unknown"

        logger.info(f"创建AI客户端，提供商: {provider_name}")

        if provider_name == "qwen" or "qwen" in provider_name:
            return QwenClient(model_config)
        elif provider_name == "deepseek" or provider == ModelProvider.DEEPSEEK:
            return DeepSeekClient(model_config)
        elif provider_name == "openai" or provider == ModelProvider.OPENAI:
            return OpenAIClient(model_config)
        else:
            logger.warning(f"不支持的AI提供商: {provider_name}，使用DeepSeek客户端")
            return DeepSeekClient(model_config)

    @staticmethod
    def _convert_db_model_to_config(db_model):
        """将数据库模型配置转换为客户端配置"""
        from types import SimpleNamespace

        # 获取API密钥
        api_key = None
        if db_model.api_key_name:
            api_key = os.getenv(db_model.api_key_name)

        # 创建配置对象
        config = SimpleNamespace()
        config.name = db_model.model_name
        config.provider_name = db_model.provider
        config.model_id = db_model.model_name
        config.api_key = api_key
        config.api_base = db_model.api_endpoint
        config.max_tokens = db_model.max_tokens or 4096
        config.temperature = float(db_model.temperature or 0.7)
        config.top_p = float(db_model.top_p or 0.9)
        config.timeout = db_model.timeout_seconds or 30
        config.max_retries = db_model.max_retries or 3

        return config


class AIServiceManager:
    """AI服务管理器"""

    def __init__(self):
        self.client = None
        self.model_config = None

    async def initialize(self):
        """初始化AI服务"""

        # 优先使用数据库配置
        db = SessionLocal()
        try:
            ai_model_service = AIModelService(db)
            self.client = AIClientFactory.create_client(ai_model_service=ai_model_service)
        except Exception as e:
            logger.error(f"使用数据库配置初始化AI服务失败: {e}")
            # 回退到硬编码配置
            self.model_config = get_default_model_config(ModelType.CHAT)
            self.client = AIClientFactory.create_client(self.model_config)
        finally:
            db.close()
        
        # 测试连接
        try:
            async with self.client as client:
                test_response = await client.chat_completion(
                    messages=[{"role": "user", "content": "测试连接"}],
                    max_tokens=10,
                    temperature=0.1
                )
                
                if test_response.get("content"):
                    logger.info("AI服务连接测试成功")
                else:
                    logger.warning("AI服务连接测试失败，但将继续使用")
                    
        except Exception as e:
            logger.error(f"AI服务初始化失败: {e}")
            
        logger.info("AI服务管理器初始化完成")
        
    async def get_client(self) -> BaseAIClient:
        """获取AI客户端"""
        
        if not self.client:
            await self.initialize()
            
        return self.client
        
    def get_model_info(self) -> Dict[str, Any]:
        """获取当前模型信息"""
        
        if not self.model_config:
            return {"provider": "unknown", "model": "unknown"}
            
        return {
            "provider": self.model_config.provider.value,
            "model": self.model_config.model_id,
            "name": self.model_config.name,
            "max_tokens": self.model_config.max_tokens,
            "temperature": self.model_config.temperature
        }


# 全局AI服务管理器实例
ai_service_manager = AIServiceManager()


async def get_ai_client() -> BaseAIClient:
    """获取AI客户端的便捷函数"""
    return await ai_service_manager.get_client()


async def test_ai_service():
    """测试AI服务"""
    
    try:
        client = await get_ai_client()
        
        async with client as ai_client:
            # 测试摘要生成
            response = await ai_client.chat_completion(
                messages=[{
                    "role": "user", 
                    "content": "请为以下内容生成摘要：央行宣布降准政策"
                }],
                max_tokens=100,
                temperature=0.3
            )
            
            print(f"AI服务测试成功: {response.get('content', '')[:50]}...")
            return True
            
    except Exception as e:
        logger.error(f"AI服务测试失败: {e}")
        return False


if __name__ == "__main__":
    asyncio.run(test_ai_service())
