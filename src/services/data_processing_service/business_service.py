"""
业务数据服务模块
提供快讯、新闻文章、研究报告等业务数据的CRUD操作
"""

import logging
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional, Tuple
from decimal import Decimal

from sqlalchemy import and_, desc, func, or_
from sqlalchemy.exc import IntegrityError
from sqlalchemy.orm import Session

from .models import (
    FlashNews, NewsArticle, ResearchReport, EconomicIndicatorData,
    UnifiedContentTags, UnifiedContentClassifications
)
from .schemas import (
    FlashNewsCreate, FlashNewsUpdate, FlashNewsResponse,
    NewsArticleCreate, NewsArticleUpdate, NewsArticleResponse,
    ContentTagInfo, ContentClassificationInfo, BusinessDataType,
    # 后续添加其他业务数据的schema
)
from ..tag_classification_service.models import Tag, ClassificationDimension, ClassificationValue


class FlashNewsService:
    """
    快讯服务类
    提供快讯数据的CRUD操作
    """

    def __init__(self, db_session: Session = None):
        """
        初始化快讯服务

        Args:
            db_session: 数据库会话，可选参数
        """
        self.db = db_session
        self.logger = logging.getLogger(__name__)

    def _get_content_tags(self, db: Session, content_id: int, content_type: str) -> List[ContentTagInfo]:
        """
        获取内容的标签信息

        Args:
            db: 数据库会话
            content_id: 内容ID
            content_type: 内容类型

        Returns:
            List[ContentTagInfo]: 标签信息列表
        """
        try:
            # 查询标签关联信息
            tag_relations = db.query(
                UnifiedContentTags,
                Tag.tag_name,
                Tag.tag_code,
                Tag.tag_slug,
                Tag.color,
                Tag.icon
            ).join(
                Tag, UnifiedContentTags.tag_id == Tag.id
            ).filter(
                UnifiedContentTags.content_type == content_type,
                UnifiedContentTags.content_id == content_id,
                Tag.is_active == True
            ).all()

            tags = []
            for relation, tag_name, tag_code, tag_slug, color, icon in tag_relations:
                # 处理可能为 NULL 的字段，提供默认值
                from decimal import Decimal

                # 确保必填字段不为 None
                if not tag_name or not tag_code or not tag_slug:
                    self.logger.warning(f"跳过标签 {relation.tag_id}：缺少必填字段")
                    continue

                # 为评分字段提供默认值
                relevance_score = relation.relevance_score if relation.relevance_score is not None else Decimal('1.00')
                confidence_score = relation.confidence_score if relation.confidence_score is not None else Decimal('1.00')
                importance_score = relation.importance_score if relation.importance_score is not None else Decimal('0.50')
                final_score = relation.final_score if relation.final_score is not None else Decimal('0.83')  # 默认综合评分

                # 为其他字段提供默认值
                source = relation.source if relation.source else "ai"
                mention_count = relation.mention_count if relation.mention_count is not None else 1

                tag_info = ContentTagInfo(
                    tag_id=relation.tag_id,
                    tag_name=tag_name,
                    tag_code=tag_code,
                    tag_slug=tag_slug,
                    color=color,
                    icon=icon,
                    relevance_score=relevance_score,
                    confidence_score=confidence_score,
                    importance_score=importance_score,
                    final_score=final_score,
                    source=source,
                    mention_count=mention_count
                )
                tags.append(tag_info)

            return tags

        except Exception as e:
            self.logger.error(f"获取内容标签信息失败: {e}")
            return []

    def _get_content_classifications(self, db: Session, content_id: int, content_type: str) -> List[ContentClassificationInfo]:
        """
        获取内容的分类信息

        Args:
            db: 数据库会话
            content_id: 内容ID
            content_type: 内容类型

        Returns:
            List[ContentClassificationInfo]: 分类信息列表
        """
        try:
            # 查询分类关联信息
            classification_relations = db.query(
                UnifiedContentClassifications,
                ClassificationDimension.dimension_name,
                ClassificationDimension.display_name.label('dimension_display_name'),
                ClassificationValue.value_code,
                ClassificationValue.display_name.label('value_display_name')
            ).join(
                ClassificationDimension, UnifiedContentClassifications.dimension_id == ClassificationDimension.id
            ).join(
                ClassificationValue, UnifiedContentClassifications.value_id == ClassificationValue.id
            ).filter(
                UnifiedContentClassifications.content_type == content_type,
                UnifiedContentClassifications.content_id == content_id,
                ClassificationDimension.is_active == True,
                ClassificationValue.is_active == True
            ).all()

            classifications = []
            for relation, dimension_name, dimension_display_name, value_code, value_display_name in classification_relations:
                classification_info = ContentClassificationInfo(
                    dimension_id=relation.dimension_id,
                    dimension_name=dimension_name,
                    dimension_display_name=dimension_display_name,
                    value_id=relation.value_id,
                    value_code=value_code,
                    value_display_name=value_display_name,
                    confidence_score=relation.confidence_score,
                    source=relation.source
                )
                classifications.append(classification_info)

            return classifications

        except Exception as e:
            self.logger.error(f"获取内容分类信息失败: {e}")
            return []

    def create_flash_news(
        self,
        db: Session,
        flash_news_data: FlashNewsCreate
    ) -> FlashNews:
        """
        创建快讯

        Args:
            db: 数据库会话
            flash_news_data: 快讯创建数据

        Returns:
            FlashNews: 创建的快讯实例

        Raises:
            IntegrityError: 数据完整性错误
        """
        try:
            # 创建快讯实例
            db_flash_news = FlashNews(**flash_news_data.model_dump())
            
            db.add(db_flash_news)
            db.commit()
            db.refresh(db_flash_news)

            self.logger.info(f"成功创建快讯: {db_flash_news.id} - {db_flash_news.title}")
            return db_flash_news

        except IntegrityError as e:
            db.rollback()
            self.logger.error(f"创建快讯失败，违反完整性约束: {e}")
            raise e
        except Exception as e:
            db.rollback()
            self.logger.error(f"创建快讯时发生未知错误: {e}")
            raise e

    def get_flash_news_by_id(self, db: Session, flash_news_id: int) -> Optional[FlashNews]:
        """
        根据ID获取快讯

        Args:
            db: 数据库会话
            flash_news_id: 快讯ID

        Returns:
            Optional[FlashNews]: 快讯实例或None
        """
        return db.query(FlashNews).filter(FlashNews.id == flash_news_id).first()

    def get_flash_news_with_tags_by_id(self, db: Session, flash_news_id: int) -> Optional[FlashNewsResponse]:
        """
        根据ID获取快讯，包含标签和分类信息

        Args:
            db: 数据库会话
            flash_news_id: 快讯ID

        Returns:
            Optional[FlashNewsResponse]: 包含标签和分类信息的快讯响应或None
        """
        flash_news = self.get_flash_news_by_id(db, flash_news_id)
        if not flash_news:
            return None

        # 获取标签和分类信息
        tags = self._get_content_tags(db, flash_news_id, BusinessDataType.FLASH_NEWS.value)
        classifications = self._get_content_classifications(db, flash_news_id, BusinessDataType.FLASH_NEWS.value)

        # 创建响应对象
        flash_news_dict = {
            **flash_news.__dict__,
            'tags': tags,
            'classifications': classifications
        }

        return FlashNewsResponse.model_validate(flash_news_dict)

    def get_flash_news_list(
        self,
        db: Session,
        skip: int = 0,
        limit: int = 100,
        status: Optional[str] = None,
        urgency_level: Optional[int] = None,
        news_category: Optional[str] = None,
        is_breaking: Optional[bool] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> Tuple[List[FlashNews], int]:
        """
        获取快讯列表

        Args:
            db: 数据库会话
            skip: 跳过数量
            limit: 限制数量
            status: 状态过滤
            urgency_level: 紧急度过滤
            news_category: 新闻分类过滤
            is_breaking: 是否突发新闻过滤
            start_date: 开始日期过滤
            end_date: 结束日期过滤

        Returns:
            Tuple[List[FlashNews], int]: 快讯列表和总数
        """
        query = db.query(FlashNews)

        # 应用过滤条件
        if status:
            query = query.filter(FlashNews.status == status)
        if urgency_level is not None:
            query = query.filter(FlashNews.urgency_level == urgency_level)
        if news_category:
            query = query.filter(FlashNews.news_category == news_category)
        if is_breaking is not None:
            query = query.filter(FlashNews.is_breaking == is_breaking)
        if start_date:
            query = query.filter(FlashNews.publish_time >= start_date)
        if end_date:
            query = query.filter(FlashNews.publish_time <= end_date)

        # 获取总数
        total = query.count()

        # 应用分页和排序
        flash_news_list = query.order_by(desc(FlashNews.publish_time)).offset(skip).limit(limit).all()

        return flash_news_list, total

    def get_flash_news_list_with_tags(
        self,
        db: Session,
        skip: int = 0,
        limit: int = 100,
        status: Optional[str] = None,
        urgency_level: Optional[int] = None,
        news_category: Optional[str] = None,
        is_breaking: Optional[bool] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> Tuple[List[FlashNewsResponse], int]:
        """
        获取快讯列表，包含标签和分类信息

        Args:
            db: 数据库会话
            skip: 跳过数量
            limit: 限制数量
            status: 状态过滤
            urgency_level: 紧急度过滤
            news_category: 新闻分类过滤
            is_breaking: 是否突发新闻过滤
            start_date: 开始日期过滤
            end_date: 结束日期过滤

        Returns:
            Tuple[List[FlashNewsResponse], int]: 包含标签和分类信息的快讯列表和总数
        """
        # 先获取基础的快讯列表
        flash_news_list, total = self.get_flash_news_list(
            db, skip, limit, status, urgency_level, news_category, is_breaking, start_date, end_date
        )

        # 为每个快讯添加标签和分类信息
        flash_news_responses = []
        for flash_news in flash_news_list:
            tags = self._get_content_tags(db, flash_news.id, BusinessDataType.FLASH_NEWS.value)
            classifications = self._get_content_classifications(db, flash_news.id, BusinessDataType.FLASH_NEWS.value)

            flash_news_dict = {
                **flash_news.__dict__,
                'tags': tags,
                'classifications': classifications
            }

            flash_news_responses.append(FlashNewsResponse.model_validate(flash_news_dict))

        return flash_news_responses, total

    def update_flash_news(
        self,
        db: Session,
        flash_news_id: int,
        flash_news_data: FlashNewsUpdate
    ) -> Optional[FlashNews]:
        """
        更新快讯

        Args:
            db: 数据库会话
            flash_news_id: 快讯ID
            flash_news_data: 更新数据

        Returns:
            Optional[FlashNews]: 更新后的快讯实例或None
        """
        try:
            db_flash_news = self.get_flash_news_by_id(db, flash_news_id)
            if not db_flash_news:
                return None

            # 更新字段
            update_data = flash_news_data.model_dump(exclude_unset=True)
            for field, value in update_data.items():
                setattr(db_flash_news, field, value)

            # 更新时间戳
            db_flash_news.updated_at = datetime.now(timezone.utc)

            db.commit()
            db.refresh(db_flash_news)

            self.logger.info(f"成功更新快讯: {db_flash_news.id}")
            return db_flash_news

        except Exception as e:
            db.rollback()
            self.logger.error(f"更新快讯时发生错误: {e}")
            raise e

    def delete_flash_news(self, db: Session, flash_news_id: int) -> bool:
        """
        删除快讯

        Args:
            db: 数据库会话
            flash_news_id: 快讯ID

        Returns:
            bool: 删除是否成功
        """
        try:
            db_flash_news = self.get_flash_news_by_id(db, flash_news_id)
            if not db_flash_news:
                return False

            db.delete(db_flash_news)
            db.commit()

            self.logger.info(f"成功删除快讯: {flash_news_id}")
            return True

        except Exception as e:
            db.rollback()
            self.logger.error(f"删除快讯时发生错误: {e}")
            raise e

    def get_flash_news_stats(self, db: Session) -> Dict[str, Any]:
        """
        获取快讯统计信息

        Args:
            db: 数据库会话

        Returns:
            Dict[str, Any]: 统计信息
        """
        # 基础统计
        total_count = db.query(FlashNews).count()
        published_count = db.query(FlashNews).filter(FlashNews.status == "published").count()
        breaking_count = db.query(FlashNews).filter(FlashNews.is_breaking == True).count()

        # 紧急度分布
        urgency_stats = db.query(
            FlashNews.urgency_level,
            func.count(FlashNews.id).label('count')
        ).group_by(FlashNews.urgency_level).all()

        urgency_distribution = {f"level_{level}": count for level, count in urgency_stats}

        # 分类分布
        category_stats = db.query(
            FlashNews.news_category,
            func.count(FlashNews.id).label('count')
        ).filter(FlashNews.news_category.isnot(None)).group_by(FlashNews.news_category).all()

        category_distribution = {category or "未分类": count for category, count in category_stats}

        # 今日统计
        today = datetime.now(timezone.utc).date()
        today_count = db.query(FlashNews).filter(
            func.date(FlashNews.publish_time) == today
        ).count()

        return {
            "total_count": total_count,
            "published_count": published_count,
            "breaking_count": breaking_count,
            "today_count": today_count,
            "urgency_distribution": urgency_distribution,
            "category_distribution": category_distribution
        }


class NewsArticleService:
    """
    新闻文章服务类
    提供新闻文章数据的CRUD操作
    """

    def __init__(self, db_session: Session = None):
        """
        初始化新闻文章服务

        Args:
            db_session: 数据库会话，可选参数
        """
        self.db = db_session
        self.logger = logging.getLogger(__name__)

    def _get_content_tags(self, db: Session, content_id: int, content_type: str) -> List[ContentTagInfo]:
        """
        获取内容的标签信息

        Args:
            db: 数据库会话
            content_id: 内容ID
            content_type: 内容类型

        Returns:
            List[ContentTagInfo]: 标签信息列表
        """
        try:
            # 查询标签关联信息
            tag_relations = db.query(
                UnifiedContentTags,
                Tag.tag_name,
                Tag.tag_code,
                Tag.tag_slug,
                Tag.color,
                Tag.icon
            ).join(
                Tag, UnifiedContentTags.tag_id == Tag.id
            ).filter(
                UnifiedContentTags.content_type == content_type,
                UnifiedContentTags.content_id == content_id,
                Tag.is_active == True
            ).all()

            tags = []
            for relation, tag_name, tag_code, tag_slug, color, icon in tag_relations:
                # 处理可能为 NULL 的字段，提供默认值
                from decimal import Decimal

                # 确保必填字段不为 None
                if not tag_name or not tag_code or not tag_slug:
                    self.logger.warning(f"跳过标签 {relation.tag_id}：缺少必填字段")
                    continue

                # 为评分字段提供默认值
                relevance_score = relation.relevance_score if relation.relevance_score is not None else Decimal('1.00')
                confidence_score = relation.confidence_score if relation.confidence_score is not None else Decimal('1.00')
                importance_score = relation.importance_score if relation.importance_score is not None else Decimal('0.50')
                final_score = relation.final_score if relation.final_score is not None else Decimal('0.83')  # 默认综合评分

                # 为其他字段提供默认值
                source = relation.source if relation.source else "ai"
                mention_count = relation.mention_count if relation.mention_count is not None else 1

                tag_info = ContentTagInfo(
                    tag_id=relation.tag_id,
                    tag_name=tag_name,
                    tag_code=tag_code,
                    tag_slug=tag_slug,
                    color=color,
                    icon=icon,
                    relevance_score=relevance_score,
                    confidence_score=confidence_score,
                    importance_score=importance_score,
                    final_score=final_score,
                    source=source,
                    mention_count=mention_count
                )
                tags.append(tag_info)

            return tags

        except Exception as e:
            self.logger.error(f"获取内容标签信息失败: {e}")
            return []

    def _get_content_classifications(self, db: Session, content_id: int, content_type: str) -> List[ContentClassificationInfo]:
        """
        获取内容的分类信息

        Args:
            db: 数据库会话
            content_id: 内容ID
            content_type: 内容类型

        Returns:
            List[ContentClassificationInfo]: 分类信息列表
        """
        try:
            # 查询分类关联信息
            classification_relations = db.query(
                UnifiedContentClassifications,
                ClassificationDimension.dimension_name,
                ClassificationDimension.display_name.label('dimension_display_name'),
                ClassificationValue.value_code,
                ClassificationValue.display_name.label('value_display_name')
            ).join(
                ClassificationDimension, UnifiedContentClassifications.dimension_id == ClassificationDimension.id
            ).join(
                ClassificationValue, UnifiedContentClassifications.value_id == ClassificationValue.id
            ).filter(
                UnifiedContentClassifications.content_type == content_type,
                UnifiedContentClassifications.content_id == content_id,
                ClassificationDimension.is_active == True,
                ClassificationValue.is_active == True
            ).all()

            classifications = []
            for relation, dimension_name, dimension_display_name, value_code, value_display_name in classification_relations:
                classification_info = ContentClassificationInfo(
                    dimension_id=relation.dimension_id,
                    dimension_name=dimension_name,
                    dimension_display_name=dimension_display_name,
                    value_id=relation.value_id,
                    value_code=value_code,
                    value_display_name=value_display_name,
                    confidence_score=relation.confidence_score,
                    source=relation.source
                )
                classifications.append(classification_info)

            return classifications

        except Exception as e:
            self.logger.error(f"获取内容分类信息失败: {e}")
            return []

    def create_news_article(
        self,
        db: Session,
        news_article_data: NewsArticleCreate
    ) -> NewsArticle:
        """
        创建新闻文章

        Args:
            db: 数据库会话
            news_article_data: 新闻文章创建数据

        Returns:
            NewsArticle: 创建的新闻文章实例

        Raises:
            IntegrityError: 数据完整性错误
        """
        try:
            # 创建新闻文章实例
            db_news_article = NewsArticle(**news_article_data.model_dump())

            db.add(db_news_article)
            db.commit()
            db.refresh(db_news_article)

            self.logger.info(f"成功创建新闻文章: {db_news_article.id} - {db_news_article.title}")
            return db_news_article

        except IntegrityError as e:
            db.rollback()
            self.logger.error(f"创建新闻文章失败，违反完整性约束: {e}")
            raise e
        except Exception as e:
            db.rollback()
            self.logger.error(f"创建新闻文章时发生未知错误: {e}")
            raise e

    def get_news_article_by_id(self, db: Session, news_article_id: int) -> Optional[NewsArticle]:
        """
        根据ID获取新闻文章

        Args:
            db: 数据库会话
            news_article_id: 新闻文章ID

        Returns:
            Optional[NewsArticle]: 新闻文章实例或None
        """
        return db.query(NewsArticle).filter(NewsArticle.id == news_article_id).first()

    def get_news_article_with_tags_by_id(self, db: Session, news_article_id: int) -> Optional[NewsArticleResponse]:
        """
        根据ID获取新闻文章，包含标签和分类信息

        Args:
            db: 数据库会话
            news_article_id: 新闻文章ID

        Returns:
            Optional[NewsArticleResponse]: 包含标签和分类信息的新闻文章响应或None
        """
        news_article = self.get_news_article_by_id(db, news_article_id)
        if not news_article:
            return None

        # 获取标签和分类信息
        tags = self._get_content_tags(db, news_article_id, BusinessDataType.NEWS_ARTICLE.value)
        classifications = self._get_content_classifications(db, news_article_id, BusinessDataType.NEWS_ARTICLE.value)

        # 创建响应对象
        news_article_dict = {
            **news_article.__dict__,
            'tags': tags,
            'classifications': classifications
        }

        return NewsArticleResponse.model_validate(news_article_dict)

    def get_news_article_list(
        self,
        db: Session,
        skip: int = 0,
        limit: int = 100,
        status: Optional[str] = None,
        primary_category: Optional[str] = None,
        author: Optional[str] = None,
        source_media: Optional[str] = None,
        is_featured: Optional[bool] = None,
        is_trending: Optional[bool] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> Tuple[List[NewsArticle], int]:
        """
        获取新闻文章列表

        Args:
            db: 数据库会话
            skip: 跳过数量
            limit: 限制数量
            status: 状态过滤
            primary_category: 主要分类过滤
            author: 作者过滤
            source_media: 发布媒体过滤
            is_featured: 是否精选过滤
            is_trending: 是否热门过滤
            start_date: 开始日期过滤
            end_date: 结束日期过滤

        Returns:
            Tuple[List[NewsArticle], int]: 新闻文章列表和总数
        """
        query = db.query(NewsArticle)

        # 应用过滤条件
        if status:
            query = query.filter(NewsArticle.status == status)
        if primary_category:
            query = query.filter(NewsArticle.primary_category == primary_category)
        if author:
            query = query.filter(NewsArticle.author == author)
        if source_media:
            query = query.filter(NewsArticle.source_media == source_media)
        if is_featured is not None:
            query = query.filter(NewsArticle.is_featured == is_featured)
        if is_trending is not None:
            query = query.filter(NewsArticle.is_trending == is_trending)
        if start_date:
            query = query.filter(NewsArticle.publish_time >= start_date)
        if end_date:
            query = query.filter(NewsArticle.publish_time <= end_date)

        # 获取总数
        total = query.count()

        # 应用分页和排序
        news_article_list = query.order_by(desc(NewsArticle.publish_time)).offset(skip).limit(limit).all()

        return news_article_list, total

    def get_news_article_list_with_tags(
        self,
        db: Session,
        skip: int = 0,
        limit: int = 100,
        status: Optional[str] = None,
        primary_category: Optional[str] = None,
        author: Optional[str] = None,
        source_media: Optional[str] = None,
        is_featured: Optional[bool] = None,
        is_trending: Optional[bool] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> Tuple[List[NewsArticleResponse], int]:
        """
        获取新闻文章列表，包含标签和分类信息

        Args:
            db: 数据库会话
            skip: 跳过数量
            limit: 限制数量
            status: 状态过滤
            primary_category: 主要分类过滤
            author: 作者过滤
            source_media: 发布媒体过滤
            is_featured: 是否精选过滤
            is_trending: 是否热门过滤
            start_date: 开始日期过滤
            end_date: 结束日期过滤

        Returns:
            Tuple[List[NewsArticleResponse], int]: 包含标签和分类信息的新闻文章列表和总数
        """
        # 先获取基础的新闻文章列表
        news_article_list, total = self.get_news_article_list(
            db, skip, limit, status, primary_category, author, source_media, is_featured, is_trending, start_date, end_date
        )

        # 为每个新闻文章添加标签和分类信息
        news_article_responses = []
        for news_article in news_article_list:
            tags = self._get_content_tags(db, news_article.id, BusinessDataType.NEWS_ARTICLE.value)
            classifications = self._get_content_classifications(db, news_article.id, BusinessDataType.NEWS_ARTICLE.value)

            news_article_dict = {
                **news_article.__dict__,
                'tags': tags,
                'classifications': classifications
            }

            news_article_responses.append(NewsArticleResponse.model_validate(news_article_dict))

        return news_article_responses, total

    def update_news_article(
        self,
        db: Session,
        news_article_id: int,
        news_article_data: NewsArticleUpdate
    ) -> Optional[NewsArticle]:
        """
        更新新闻文章

        Args:
            db: 数据库会话
            news_article_id: 新闻文章ID
            news_article_data: 更新数据

        Returns:
            Optional[NewsArticle]: 更新后的新闻文章实例或None
        """
        try:
            db_news_article = self.get_news_article_by_id(db, news_article_id)
            if not db_news_article:
                return None

            # 更新字段
            update_data = news_article_data.model_dump(exclude_unset=True)
            for field, value in update_data.items():
                setattr(db_news_article, field, value)

            # 更新时间戳
            db_news_article.updated_at = datetime.now(timezone.utc)

            db.commit()
            db.refresh(db_news_article)

            self.logger.info(f"成功更新新闻文章: {db_news_article.id}")
            return db_news_article

        except Exception as e:
            db.rollback()
            self.logger.error(f"更新新闻文章时发生错误: {e}")
            raise e

    def delete_news_article(self, db: Session, news_article_id: int) -> bool:
        """
        删除新闻文章

        Args:
            db: 数据库会话
            news_article_id: 新闻文章ID

        Returns:
            bool: 删除是否成功
        """
        try:
            db_news_article = self.get_news_article_by_id(db, news_article_id)
            if not db_news_article:
                return False

            db.delete(db_news_article)
            db.commit()

            self.logger.info(f"成功删除新闻文章: {news_article_id}")
            return True

        except Exception as e:
            db.rollback()
            self.logger.error(f"删除新闻文章时发生错误: {e}")
            raise e

    def get_news_article_stats(self, db: Session) -> Dict[str, Any]:
        """
        获取新闻文章统计信息

        Args:
            db: 数据库会话

        Returns:
            Dict[str, Any]: 统计信息
        """
        # 基础统计
        total_count = db.query(NewsArticle).count()
        published_count = db.query(NewsArticle).filter(NewsArticle.status == "published").count()
        featured_count = db.query(NewsArticle).filter(NewsArticle.is_featured == True).count()
        trending_count = db.query(NewsArticle).filter(NewsArticle.is_trending == True).count()

        # 分类分布
        category_stats = db.query(
            NewsArticle.primary_category,
            func.count(NewsArticle.id).label('count')
        ).filter(NewsArticle.primary_category.isnot(None)).group_by(NewsArticle.primary_category).all()

        category_distribution = {category or "未分类": count for category, count in category_stats}

        # 媒体分布
        media_stats = db.query(
            NewsArticle.source_media,
            func.count(NewsArticle.id).label('count')
        ).filter(NewsArticle.source_media.isnot(None)).group_by(NewsArticle.source_media).limit(10).all()

        media_distribution = {media or "未知媒体": count for media, count in media_stats}

        # 今日统计
        today = datetime.now(timezone.utc).date()
        today_count = db.query(NewsArticle).filter(
            func.date(NewsArticle.publish_time) == today
        ).count()

        return {
            "total_count": total_count,
            "published_count": published_count,
            "featured_count": featured_count,
            "trending_count": trending_count,
            "today_count": today_count,
            "category_distribution": category_distribution,
            "media_distribution": media_distribution
        }


# 后续可以添加其他业务数据的Service类
# class ResearchReportService:
# class EconomicIndicatorDataService:
