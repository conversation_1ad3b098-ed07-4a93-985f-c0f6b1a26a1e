"""
查询参数处理中间件
用于处理和清理无效的URL查询参数，防止前端传递错误格式参数导致500错误
"""

import logging
import urllib.parse
from typing import Callable
from fastapi import Request, Response
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware

logger = logging.getLogger(__name__)


class QueryParamsCleanupMiddleware(BaseHTTPMiddleware):
    """
    查询参数清理中间件
    
    主要功能：
    1. 清理无效的查询参数格式
    2. 移除前端传递的错误参数
    3. 记录参数清理日志
    4. 防止参数解析错误导致的500错误
    """
    
    def __init__(self, app, excluded_paths: list = None):
        """
        初始化中间件
        
        Args:
            app: FastAPI应用实例
            excluded_paths: 排除的路径列表，这些路径不进行参数清理
        """
        super().__init__(app)
        self.excluded_paths = excluded_paths or []
        
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """
        处理请求
        
        Args:
            request: 请求对象
            call_next: 下一个处理函数
            
        Returns:
            Response: 响应对象
        """
        try:
            # 检查是否需要处理此路径
            if self._should_skip_path(request.url.path):
                return await call_next(request)
            
            # 清理查询参数
            cleaned_query_string = self._clean_query_params(request.url.query)
            
            # 如果查询参数被修改，更新请求URL
            if cleaned_query_string != request.url.query:
                # 构建新的URL
                new_url = request.url.replace(query=cleaned_query_string)
                
                # 创建新的请求对象
                request._url = new_url
                
                logger.info(
                    f"清理查询参数: {request.url.path} "
                    f"原始: {request.url.query} -> 清理后: {cleaned_query_string}"
                )
            
            # 继续处理请求
            response = await call_next(request)
            return response
            
        except Exception as e:
            logger.error(f"查询参数清理中间件处理失败: {str(e)}", exc_info=True)
            
            # 返回错误响应
            return JSONResponse(
                status_code=400,
                content={
                    "detail": "请求参数格式错误",
                    "error": "Invalid query parameters format"
                }
            )
    
    def _should_skip_path(self, path: str) -> bool:
        """
        检查是否应该跳过此路径
        
        Args:
            path: 请求路径
            
        Returns:
            bool: 是否跳过
        """
        for excluded_path in self.excluded_paths:
            if path.startswith(excluded_path):
                return True
        return False
    
    def _clean_query_params(self, query_string: str) -> str:
        """
        清理查询参数
        
        Args:
            query_string: 原始查询字符串
            
        Returns:
            str: 清理后的查询字符串
        """
        if not query_string:
            return query_string
        
        try:
            # 解析查询参数
            parsed_params = urllib.parse.parse_qs(query_string, keep_blank_values=True)
            cleaned_params = {}
            
            for key, values in parsed_params.items():
                # 清理无效的参数
                if self._is_valid_param(key, values):
                    cleaned_params[key] = values
                else:
                    logger.warning(f"移除无效参数: {key}={values}")
            
            # 重新构建查询字符串
            return urllib.parse.urlencode(cleaned_params, doseq=True)
            
        except Exception as e:
            logger.error(f"解析查询参数失败: {str(e)}")
            # 如果解析失败，返回空字符串
            return ""
    
    def _is_valid_param(self, key: str, values: list) -> bool:
        """
        检查参数是否有效
        
        Args:
            key: 参数名
            values: 参数值列表
            
        Returns:
            bool: 是否有效
        """
        # 检查参数名
        if not key or not isinstance(key, str):
            return False
        
        # 检查参数值
        if not values:
            return True  # 空值是允许的
        
        for value in values:
            if not isinstance(value, str):
                return False
            
            # 检查是否是无效的对象格式
            if self._is_invalid_object_format(value):
                return False
        
        return True
    
    def _is_invalid_object_format(self, value: str) -> bool:
        """
        检查是否是无效的对象格式
        
        Args:
            value: 参数值
            
        Returns:
            bool: 是否是无效格式
        """
        # 检查常见的无效格式
        invalid_patterns = [
            "[object Object]",
            "[object+Object]",
            "%5Bobject+Object%5D",
            "%5Bobject%20Object%5D",
            "undefined",
            "null",
            "[object HTMLElement]",
            "[object Window]"
        ]
        
        value_lower = value.lower()
        for pattern in invalid_patterns:
            if pattern.lower() in value_lower:
                return True
        
        return False


class SpecificParamCleanupMiddleware(BaseHTTPMiddleware):
    """
    特定参数清理中间件
    专门处理数据处理管道API的参数问题
    """
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """处理请求"""
        try:
            # 只处理数据处理管道相关的API
            if "/data-processing/pipelines" in request.url.path:
                # 清理特定的无效参数
                cleaned_query = self._clean_pipeline_params(request.url.query)
                
                if cleaned_query != request.url.query:
                    request._url = request.url.replace(query=cleaned_query)
                    logger.info(f"清理管道API参数: {request.url.path}")
            
            return await call_next(request)
            
        except Exception as e:
            logger.error(f"特定参数清理失败: {str(e)}", exc_info=True)
            return await call_next(request)
    
    def _clean_pipeline_params(self, query_string: str) -> str:
        """清理管道API参数"""
        if not query_string:
            return query_string
        
        try:
            # 移除params参数（通常是前端错误传递的）
            params = urllib.parse.parse_qs(query_string)
            
            # 移除无效参数
            invalid_keys = []
            for key in params.keys():
                if key == "params" or "[object" in str(params[key]):
                    invalid_keys.append(key)
            
            for key in invalid_keys:
                del params[key]
                logger.info(f"移除无效参数: {key}")
            
            return urllib.parse.urlencode(params, doseq=True)
            
        except Exception:
            return ""


def add_query_params_middleware(app):
    """
    添加查询参数处理中间件到应用
    
    Args:
        app: FastAPI应用实例
    """
    # 添加特定参数清理中间件
    app.add_middleware(SpecificParamCleanupMiddleware)
    
    # 添加通用查询参数清理中间件
    app.add_middleware(
        QueryParamsCleanupMiddleware,
        excluded_paths=[
            "/docs",
            "/redoc", 
            "/openapi.json",
            "/health",
            "/metrics"
        ]
    )
    
    logger.info("查询参数清理中间件已添加")
