---
description: 
globs: 
alwaysApply: true
---
# 安全规范

## 1. 认证授权

### 1.1 身份认证
1. **JWT认证**：使用JWT进行身份认证
2. **Token管理**：合理设置Token过期时间和刷新机制
3. **多因素认证**：对敏感操作实施多因素认证
4. **密码策略**：强制使用强密码并定期更换

### 1.2 访问控制
1. **RBAC模型**：实现基于角色的访问控制(RBAC)
2. **权限粒度**：细粒度的权限控制到具体操作
3. **最小权限原则**：用户和服务只分配必要的最小权限
4. **权限审查**：定期审查和清理不必要的权限

### 1.3 会话管理
1. **会话安全**：安全的会话管理机制
2. **自动登出**：闲置超时自动登出功能
3. **并发控制**：控制同一用户的并发会话数量
4. **会话固定防护**：防止会话固定攻击

### 1.4 审计日志
1. **操作记录**：敏感操作必须有审计日志
2. **日志完整性**：确保审计日志的完整性和不可篡改
3. **异常监控**：实时监控异常登录和操作行为
4. **合规要求**：满足相关合规性审计要求

## 2. 数据安全

### 2.1 数据传输安全
1. **HTTPS加密**：使用HTTPS进行数据传输
2. **TLS版本**：使用TLS 1.2或更高版本
3. **证书管理**：正确配置和管理SSL证书
4. **加密算法**：使用安全的加密算法和密钥长度

### 2.2 输入验证
1. **参数验证**：实现严格的请求参数验证
2. **数据类型检查**：验证输入数据的类型和格式
3. **长度限制**：对输入数据设置合理的长度限制
4. **特殊字符过滤**：过滤和转义特殊字符

### 2.3 攻击防护
1. **SQL注入防护**：使用参数化查询防止SQL注入
2. **XSS防护**：实施跨站脚本攻击防护
3. **CSRF防护**：实现跨站请求伪造防护
4. **点击劫持防护**：使用X-Frame-Options等安全头

### 2.4 敏感数据处理
1. **数据分类**：对敏感数据进行分类和标记
2. **加密存储**：敏感数据采用强加密算法存储
3. **密钥管理**：安全的密钥生成、存储和轮换
4. **数据脱敏**：非生产环境使用脱敏数据

### 2.5 漏洞防护
1. **定期扫描**：定期进行安全漏洞扫描
2. **依赖检查**：检查第三方依赖的安全漏洞
3. **安全更新**：及时应用安全补丁和更新
4. **渗透测试**：定期进行渗透测试

## 3. API安全

### 3.1 接口安全
1. **认证机制**：API接口必须有认证机制
2. **授权检查**：每个API调用都要进行授权检查
3. **限流控制**：实施API调用频率限制
4. **IP白名单**：敏感API使用IP白名单控制

### 3.2 数据保护
1. **响应过滤**：避免在API响应中泄露敏感信息
2. **错误处理**：错误信息不应泄露系统内部信息
3. **日志安全**：确保日志中不包含敏感数据
4. **调试信息**：生产环境禁用调试信息输出

## 4. 基础设施安全

### 4.1 服务器安全
1. **操作系统加固**：定期更新和加固操作系统
2. **防火墙配置**：合理配置防火墙规则
3. **服务最小化**：只运行必要的服务和端口
4. **入侵检测**：部署入侵检测和防护系统

### 4.2 网络安全
1. **网络隔离**：合理的网络分段和隔离
2. **VPN访问**：远程访问使用安全的VPN
3. **DNS安全**：使用安全的DNS解析服务
4. **DDoS防护**：部署DDoS攻击防护措施
