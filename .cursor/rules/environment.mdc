---
description: 
globs: 
alwaysApply: true
---
# 环境配置规则

## 1. 开发环境

### 1.1 基础环境要求
1. **虚拟环境激活**：运行程序前切换到虚拟环境执行 `source venv/finsight/bin/activate`
2. **Python版本要求**：Python 3.12.3
3. **环境变量配置**：本地开发环境必须配置好 `.env.develop` 文件，包含所有必要的环境变量
4. **安全要求**：禁止在代码中硬编码任何敏感信息（密钥、Token等）

### 1.2 依赖管理
1. **包管理**：使用 `requirements.txt` 管理项目依赖
2. **版本锁定**：生产环境依赖必须锁定具体版本号
3. **开发依赖**：开发工具依赖单独管理，不影响生产环境

### 1.3 环境变量规范
1. **命名规范**：使用大写字母，单词间用下划线分隔
2. **分类管理**：按功能模块对环境变量进行分类
3. **默认值**：为非敏感配置提供合理的默认值
4. **文档说明**：在 `.env.example` 中提供完整的环境变量说明

### 1.4 配置文件管理
1. **配置层级**：支持开发、测试、生产环境的配置覆盖
2. **敏感信息**：敏感配置项必须通过环境变量或密钥管理系统获取
3. **配置验证**：应用启动时验证必要配置项的完整性
