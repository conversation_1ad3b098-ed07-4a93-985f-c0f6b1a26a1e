---
description: 
globs: 
alwaysApply: false
---
# 测试规范

## 1. 单元测试

### 1.1 测试覆盖率要求
1. **核心业务逻辑**：核心业务逻辑的测试覆盖率不低于80%
2. **工具函数**：工具函数和公共模块覆盖率要求90%以上
3. **异常分支**：重要的异常处理分支必须被测试覆盖
4. **边界条件**：关键功能的边界条件测试

### 1.2 测试框架和工具
1. **测试框架**：使用pytest进行单元测试
2. **断言库**：充分利用pytest的断言功能
3. **参数化测试**：使用pytest.mark.parametrize进行参数化测试
4. **测试固件**：合理使用pytest fixture管理测试数据

### 1.3 模拟和存根
1. **外部依赖模拟**：使用mock进行外部依赖的模拟
2. **数据库模拟**：数据库操作使用内存数据库或mock
3. **网络请求模拟**：外部API调用必须被模拟
4. **时间模拟**：涉及时间的测试使用时间模拟

### 1.4 测试组织
1. **测试文件命名**：测试文件以`test_`开头或`_test.py`结尾
2. **测试类组织**：相关测试方法组织在同一测试类中
3. **测试方法命名**：测试方法名清楚描述测试目的
4. **测试文档**：复杂测试用例添加必要的文档说明

## 2. 集成测试

### 2.1 API集成测试
1. **主要流程**：主要API流程必须有集成测试
2. **端到端测试**：关键业务场景的端到端测试
3. **错误场景**：测试各种错误场景的处理
4. **性能测试**：重要接口的性能基线测试

### 2.2 数据库集成测试
1. **测试数据库**：测试数据库使用独立的测试库
2. **数据初始化**：每个测试用例独立的数据初始化
3. **事务隔离**：测试用例间的数据隔离
4. **数据清理**：测试完成后的数据清理

### 2.3 外部服务集成测试
1. **服务模拟**：外部服务使用WireMock等工具模拟
2. **契约测试**：使用契约测试验证服务间接口
3. **容错测试**：测试外部服务不可用时的处理
4. **超时测试**：测试网络超时等异常情况

## 3. 测试数据管理

### 3.1 测试数据原则
1. **数据隔离**：每个测试用例使用独立的测试数据
2. **数据最小化**：只准备测试所需的最小数据集
3. **数据真实性**：测试数据应尽量接近真实业务数据
4. **敏感数据处理**：测试环境禁止使用真实敏感数据

### 3.2 数据准备策略
1. **工厂模式**：使用工厂模式生成测试数据
2. **Builder模式**：复杂对象使用Builder模式构建
3. **数据模板**：预定义常用的数据模板
4. **随机数据**：适当使用随机数据增加测试覆盖面

### 3.3 数据库测试策略
1. **内存数据库**：简单测试使用SQLite内存数据库
2. **容器化数据库**：复杂测试使用Docker容器数据库
3. **数据迁移测试**：测试数据库迁移脚本
4. **种子数据**：维护标准的测试种子数据

## 4. 测试自动化

### 4.1 持续集成
1. **自动执行**：代码提交时自动执行测试套件
2. **快速反馈**：测试失败时及时通知开发者
3. **分级测试**：根据测试重要性分级执行
4. **并行执行**：支持测试用例的并行执行

### 4.2 测试环境
1. **环境一致性**：测试环境与生产环境保持一致
2. **环境隔离**：不同测试套件使用隔离的环境
3. **环境自动化**：测试环境的自动化部署和销毁
4. **配置管理**：测试环境配置的版本化管理

### 4.3 测试报告
1. **覆盖率报告**：生成详细的测试覆盖率报告
2. **测试结果**：清晰的测试结果展示和分析
3. **趋势分析**：测试结果的历史趋势分析
4. **质量门禁**：基于测试结果的质量门禁机制

## 5. 性能测试

### 5.1 性能测试类型
1. **负载测试**：正常负载下的性能表现
2. **压力测试**：极限负载下的系统行为
3. **容量测试**：确定系统的最大处理能力
4. **稳定性测试**：长时间运行的稳定性测试

### 5.2 性能指标
1. **响应时间**：API响应时间的基线和阈值
2. **吞吐量**：系统处理请求的吞吐量指标
3. **资源使用**：CPU、内存、磁盘、网络使用率
4. **错误率**：高负载下的错误率控制

### 5.3 性能测试工具
1. **工具选择**：选择合适的性能测试工具
2. **脚本开发**：开发可维护的性能测试脚本
3. **监控集成**：集成系统监控和性能测试
4. **报告分析**：详细的性能测试报告和分析

## 6. 测试最佳实践

### 6.1 测试设计原则
1. **独立性**：测试用例之间相互独立
2. **可重复性**：测试结果可重复和可预测
3. **简单性**：每个测试用例只验证一个功能点
4. **可维护性**：测试代码易于理解和维护

### 6.2 测试代码质量
1. **代码规范**：测试代码遵循与业务代码相同的规范
2. **重构原则**：及时重构重复的测试代码
3. **命名清晰**：测试方法和变量命名清晰明确
4. **注释文档**：复杂测试逻辑添加必要注释

### 6.3 测试维护
1. **定期审查**：定期审查和更新测试用例
2. **失效清理**：及时清理失效和过时的测试
3. **版本同步**：测试用例与功能版本同步更新
4. **知识分享**：团队内部的测试知识分享和培训
