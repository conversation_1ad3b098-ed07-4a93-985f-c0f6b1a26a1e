# FinSight Backend

金融信息智能推送系统后端API

## 📋 项目概述

FinSight是一个智能金融信息推送系统，为不同类型的投资者提供个性化的金融资讯和投资建议。

### 🎯 主要功能

- **用户管理**: 手机号+验证码登录，首次登录即注册
- **用户画像**: 基于行为和偏好的用户分类系统
- **财经日历**: 全球重要经济事件和数据发布时间表
- **信息处理**: 智能金融信息分析和处理
- **个性化推送**: 基于用户画像的精准内容推送
- **数据采集**: 多源金融数据实时采集

### 🏗️ 技术架构

- **后端框架**: FastAPI + SQLAlchemy
- **数据库**: PostgreSQL + Redis
- **认证方式**: JWT + 短信验证码
- **消息队列**: Kafka
- **搜索引擎**: Elasticsearch
- **文件存储**: MinIO
- **监控**: Prometheus + Grafana

## 🚀 快速开始

📖 **详细运行指南**: [docs/RUN_GUIDE.md](docs/RUN_GUIDE.md)

### 环境要求

- Python 3.8+
- PostgreSQL 12+
- Redis 6+
- 虚拟环境

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd app_finsight_backend
```

2. **创建并激活虚拟环境**
```bash
python -m venv venv/finsight
source venv/finsight/bin/activate  # Linux/Mac
# venv\finsight\Scripts\activate  # Windows
```

3. **安装依赖**
```bash
pip install -r requirements.txt
```

4. **配置环境变量**
```bash
cp env_example.txt .env.develop
# 编辑 .env.develop 文件，配置数据库等信息
```

5. **启动数据库服务**
```bash
# 确保 PostgreSQL 和 Redis 已启动
sudo systemctl start postgresql
sudo systemctl start redis
```

6. **运行应用**
```bash
python -m src.main
```

应用将在 http://localhost:8000 启动

## 📬 SMS服务更新

### 主要变更

1. **独立SMS服务模块** - 创建了独立的 `src/services/sms_service/` 模块
2. **基于业务名称的配置** - 不再使用 `TENCENT_CLOUD_SMS_SIGN_NAME` 和 `TENCENT_CLOUD_SMS_TEMPLATE_ID`，改为使用业务名称配置
3. **数据库驱动的配置管理** - 通过数据库表管理短信签名、模板和业务配置
4. **频率限制机制** - 实现了每日和每分钟的发送频率限制
5. **完整的测试覆盖** - 提供了全面的单元测试

### 快速配置

```bash
# 1. 初始化SMS服务数据库
python -c "from src.services.sms_service.migrations import run_migration; run_migration()"

# 2. 配置环境变量（在.env.develop中）
TENCENT_CLOUD_SMS_LOGIN=登录验证
TENCENT_CLOUD_SMS_REGISTER=注册验证
TENCENT_CLOUD_SMS_RESET_PASSWORD=密码重置

# 3. 运行SMS服务测试
python -m pytest tests/test_sms_service.py -v
```

📖 **详细SMS服务文档**: [docs/services/sms_service_integration.md](docs/services/sms_service_integration.md)

## 📅 财经日历功能

### 财经事件管理

FinSight提供完整的全球财经事件日历，包括重要经济指标、央行会议、政府报告等。

#### 主要特性

- **全球覆盖**: 支持美国、欧洲、日本、中国等主要国家和地区
- **事件分类**: 就业数据、通胀数据、央行会议、GDP数据等
- **重要性评级**: 1-3级重要性标记，帮助用户关注重点事件
- **实时更新**: 支持事件实际值的实时更新
- **影响分析**: 预测事件对不同市场的影响程度

#### API 使用示例

1. **获取今日重要事件**
```bash
curl -X GET "http://localhost:8000/api/financial-calendar/events?start_date=2024-01-05&end_date=2024-01-05&importance_level=3" \
     -H "Authorization: Bearer YOUR_TOKEN"
```

2. **获取即将到来的事件**
```bash
curl -X GET "http://localhost:8000/api/financial-calendar/events/upcoming?hours=24" \
     -H "Authorization: Bearer YOUR_TOKEN"
```

3. **按国家查询事件**
```bash
curl -X GET "http://localhost:8000/api/financial-calendar/events/by-country/1" \
     -H "Authorization: Bearer YOUR_TOKEN"
```

4. **获取日历摘要**
```bash
curl -X GET "http://localhost:8000/api/financial-calendar/calendar/summary?start_date=2024-01-01&end_date=2024-01-07" \
     -H "Authorization: Bearer YOUR_TOKEN"
```

📖 **详细财经日历API文档**: [docs/api/financial_calendar_api.md](docs/api/financial_calendar_api.md)

## 📱 用户服务功能

### 手机号+验证码登录

FinSight支持手机号+验证码的无密码登录方式，首次登录自动注册用户。

#### API 使用示例

1. **发送验证码**
```bash
curl -X POST "http://localhost:8000/api/v1/users/send-sms-code" \
     -H "Content-Type: application/json" \
     -d '{
       "phone": "13800138000",
       "purpose": "login"
     }'
```

2. **手机号登录**
```bash
curl -X POST "http://localhost:8000/api/v1/users/phone-login" \
     -H "Content-Type: application/json" \
     -d '{
       "phone": "13800138000",
       "verification_code": "123456"
     }'
```

3. **获取用户信息**
```bash
curl -X GET "http://localhost:8000/api/v1/users/profile" \
     -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### 用户类型分类

系统会根据用户行为自动分类：

- **小白型 (Type 1)**: 投资新手，需要基础指导
- **进阶型 (Type 2)**: 有经验的投资者，需要深度分析
- **焦虑型 (Type 3)**: 对市场敏感，需要及时提醒

## 🧪 测试

### 运行单元测试
```bash
pytest tests/test_user_service.py -v
```

### 运行演示脚本
```bash
# 交互式演示
python demo_test.py

# 自动演示（使用固定验证码）
python demo_test.py --auto
```

## 📊 API 文档

启动应用后，访问以下链接查看API文档：

- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc
- **健康检查**: http://localhost:8000/health

## 🔧 开发指南

### 项目结构
```
app_finsight_backend/
├── src/
│   ├── core/                 # 核心配置和数据库
│   │   ├── config.py        # 应用配置
│   │   └── database.py      # 数据库连接
│   └── services/            # 业务服务
│       ├── user_service/    # 用户服务
│       │   ├── models.py    # 数据模型
│   │   ├── schemas.py       # 请求响应模型
│   │   ├── service.py       # 业务逻辑
│   │   ├── router.py        # API路由
│   │   └── dependencies.py  # 依赖注入
│       ├── financial_calendar_service/ # 财经日历服务
│       ├── sms_service/            # 短信服务
│       ├── tag_classification_service/ # 标签分类服务
│   ├── data_collection_service/     # 数据采集服务
│   ├── information_processing_service/ # 信息处理服务
│   └── push_service/            # 推送服务
├── tests/                   # 测试文件
├── docs/                    # 文档
├── src/
│   ├── main.py              # 应用入口
└── requirements.txt         # 依赖列表
```

### 添加新功能

1. 在对应的服务模块中添加新的API端点
2. 更新数据模型和Schema
3. 编写业务逻辑
4. 添加测试用例
5. 更新文档

## 🔐 安全特性

- **JWT认证**: 访问令牌 + 刷新令牌机制
- **短信验证**: 6位数字验证码，5分钟有效期
- **输入验证**: 严格的请求参数验证
- **CORS保护**: 跨域请求安全配置
- **速率限制**: API调用频率控制

## 📝 环境配置

### 开发环境
```bash
# 数据库配置
DATABASE_URL=postgresql://postgres:password@localhost:5432/finsight_dev

# Redis配置  
REDIS_URL=redis://localhost:6379/0

# JWT配置
JWT_SECRET_KEY=your-secret-key
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30

# 短信服务配置（可选）
SMS_PROVIDER=aliyun
SMS_ACCESS_KEY_ID=your-access-key
SMS_ACCESS_KEY_SECRET=your-secret
```

### 生产环境部署

使用 Docker Compose 部署：

```bash
docker-compose up -d
```

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 📞 联系方式

- 项目负责人: [Your Name]
- 邮箱: [<EMAIL>]
- 项目链接: [https://github.com/yourorg/finsight-backend]

## 📄 许可证

本项目基于 MIT 许可证开源 - 查看 [LICENSE](LICENSE) 文件了解详情

## 🚧 开发状态

- ✅ 用户服务 - 手机号+验证码登录
- 🚧 数据采集服务 - 开发中
- 🚧 信息处理服务 - 开发中  
- 🚧 推送服务 - 计划中

## 📈 版本历史

### v0.1.0 (当前版本)
- 实现用户服务基础功能
- 支持手机号+验证码登录
- 首次登录自动注册
- JWT认证机制
- 用户信息管理
- 完整的测试覆盖 