# 应用配置
APP_NAME=FinSight Backend
APP_VERSION=0.1.0
DEBUG=true
ENVIRONMENT=development
# API配置
API_PREFIX=/api/v1
HOST=0.0.0.0
PORT=8000
# 数据库配置
DATABASE_URL=postgresql://user:pass@host:port/db
DB_HOST=
DB_PORT=
DB_NAME=
DB_USER=
DB_PASSWORD=

# MongoDB配置
MONGODB_URL: Optional[str] = None
MONGODB_HOST: str = "localhost"
MONGODB_PORT: int = 27017
MONGODB_DB_NAME: str = "finsight"
MONGODB_USERNAME: Optional[str] = None
MONGODB_PASSWORD: Optional[str] = None

# Redis配置
REDIS_URL=redis://localhost:6379/0
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=


# JWT配置
JWT_SECRET_KEY=YOUR_JWT_SECRET_KEY
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30
JWT_REFRESH_TOKEN_EXPIRE_DAYS=7
# 加密配置（32）位
ENCRYPTION_KEY=YOU_ENCRYPTION_KEY
# 短信服务配置（可选，用于真实短信发送）
TENCENT_CLOUD_SECRET_ID=your-dev-secret-id
TENCENT_CLOUD_SECRET_KEY=your-dev-secret-key
TENCENT_CLOUD_SMS_SDK_APP_ID=your-dev-app-id
TENCENT_CLOUD_SMS_SIGN_NAME=your-dev-sign
TENCENT_CLOUD_SMS_TEMPLATE_ID=your-dev-template-id
TENCENT_CLOUD_SMS_REGION=ap-guangzhou

# 安全配置
CORS_ORIGINS=["http://localhost:3000", "http://localhost:8080"]
RATE_LIMIT_PER_MINUTE=100