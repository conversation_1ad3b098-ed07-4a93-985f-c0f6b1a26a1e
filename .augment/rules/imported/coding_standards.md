---
type: "always_apply"
---

# 代码规范

## 1. Python代码规范

### 1.1 基础规范
1. **PEP 8遵循**：严格遵循PEP 8规范
2. **代码格式化**：使用Black进行代码格式化
3. **导入排序**：使用isort进行import排序
4. **代码质量检查**：使用pylint进行代码质量检查

### 1.2 代码复杂度要求
1. **函数长度**：单个函数不超过50行
2. **圈复杂度**：圈复杂度不超过10
3. **类的规模**：类的方法数不超过20
4. **嵌套深度**：避免过深的嵌套，建议不超过4层

### 1.3 代码质量标准
1. **可读性优先**：代码应该自解释，清晰易懂
2. **避免重复**：遵循DRY（Don't Repeat Yourself）原则
3. **单一职责**：每个函数和类应该只有一个明确的职责
4. **错误处理**：合理处理异常，避免裸露的try-except

## 2. 注释规范

### 2.1 文档字符串规范
1. **公共接口**：所有公共接口必须有文档字符串
2. **格式标准**：使用Google Style或NumPy Style文档字符串
3. **内容完整**：包含功能描述、参数说明、返回值和异常说明
4. **示例代码**：复杂接口提供使用示例

### 2.2 行内注释规范
1. **业务逻辑**：复杂的业务逻辑必须有详细注释
2. **语言选择**：使用英文注释，保持专业性和可读性
3. **位置规范**：注释应该在代码上方或右侧，保持对齐
4. **内容准确**：注释必须与代码同步更新，避免误导

### 2.3 注释维护
1. **及时更新**：代码修改时同步更新相关注释
2. **删除冗余**：代码提交前删除调试性注释
3. **TODO标记**：使用标准的TODO、FIXME、NOTE标记
4. **版权信息**：文件头部包含必要的版权和作者信息

## 3. 命名规范

### 3.1 变量命名
1. **变量名**：使用小写字母，单词间用下划线分隔（snake_case）
2. **描述性命名**：变量名应该清楚描述其用途
3. **避免缩写**：除非是约定俗成的缩写，否则使用完整单词
4. **作用域一致**：相似作用域的变量使用一致的命名模式

### 3.2 函数和方法命名
1. **函数名**：使用小写字母，单词间用下划线分隔
2. **动词开头**：函数名通常以动词开头，表明其行为
3. **返回值暗示**：函数名应该暗示返回值的类型或含义
4. **私有方法**：私有方法以单下划线开头

### 3.3 类和模块命名
1. **类名**：使用驼峰命名法（PascalCase）
2. **模块名**：使用小写字母，单词间用下划线分隔
3. **包名**：使用小写字母，避免使用下划线
4. **抽象类**：抽象类名通常以Abstract或Base开头

### 3.4 常量命名
1. **常量名**：使用大写字母，单词间用下划线分隔
2. **分组管理**：相关常量可以组织在类或模块中
3. **配置常量**：配置相关的常量统一管理
4. **魔法数字**：避免在代码中直接使用魔法数字

### 3.5 文件和目录命名
1. **文件名**：使用小写字母，单词间用下划线分隔
2. **目录名**：使用小写字母，保持简洁
3. **一致性**：项目内保持命名风格的一致性
4. **可读性**：名称应该清楚表达文件或目录的用途

## 4. 文件路径规范
### 4.1 路径表示方式
1.相对路径优先：在代码中优先使用相对路径，避免使用绝对路径提高可移植性
2.路径分隔符：使用正斜杠/作为路径分隔符，避免使用反斜杠\
3.路径拼接：使用os.path.join(Python) 或pathlib模块处理路径拼接，避免硬编码路径字符串
4.避免硬编码：禁止在代码中硬编码特定环境的路径

### 4.2 文件定位与引用
1.模块化路径：遵循项目的模块划分，相关文件应存放在同一模块目录下
2.配置文件管理：配置文件应集中存放在项目根目录下的config或settings目录
3.资源文件定位：静态资源文件（如图片、模板）应存放在assets或resources目录，并使用相对路径引用
4.测试文件定位：测试文件放在tests目录下，并按功能模块划分
5.避免深层嵌套：文件目录层级不宜过深，建议不超过 5 层

