---
type: "always_apply"
---

# FinSight 项目规则

## 1. 环境配置规则

### 1.1 开发环境
1. 运行程序前切换到虚拟环境执行 source venv/finsight/bin/activate
2. Python版本要求：3.8+
3. 本地开发环境必须配置好.env文件，包含所有必要的环境变量
4. 禁止在代码中硬编码任何敏感信息（密钥、Token等）

## 2. 代码规范

### 2.1 Python代码规范
1. 遵循PEP 8规范
2. 使用Black进行代码格式化
3. 使用isort进行import排序
4. 使用pylint进行代码质量检查
5. 代码复杂度要求：
   - 单个函数不超过50行
   - 圈复杂度不超过10
   - 类的方法数不超过20

### 2.2 注释规范
1. 所有公共接口必须有文档字符串
2. 复杂的业务逻辑必须有详细注释
3. 使用英文注释，保持专业性和可读性
4. 代码提交前删除调试性注释

### 2.3 命名规范
1. 变量名：使用小写字母，单词间用下划线分隔
2. 类名：使用驼峰命名法
3. 常量：使用大写字母，单词间用下划线分隔
4. 文件名：使用小写字母，单词间用下划线分隔

## 3. 架构规范

### 3.1 目录结构
1. 严格遵循领域驱动设计(DDD)的目录结构
2. 保持模块的高内聚、低耦合
3. 遵循依赖注入原则

### 3.2 API设计
1. 遵循RESTful API设计规范
2. 使用FastAPI框架的标准功能
3. 所有API必须有版本控制
4. 必须有完整的API文档

### 3.3 数据库操作
1. 使用SQLAlchemy ORM
2. 所有数据库操作必须有事务管理
3. 敏感数据必须加密存储
4. 建立合适的索引优化查询性能

## 4. 安全规范

### 4.1 认证授权
1. 使用JWT进行身份认证
2. 实现基于角色的访问控制(RBAC)
3. 敏感操作必须有审计日志

### 4.2 数据安全
1. 使用HTTPS进行数据传输
2. 实现请求参数验证
3. 防止SQL注入和XSS攻击
4. 定期进行安全漏洞扫描

## 5. 测试规范

### 5.1 单元测试
1. 核心业务逻辑的测试覆盖率不低于80%
2. 使用pytest进行测试
3. 使用mock进行外部依赖的模拟

### 5.2 集成测试
1. 主要API流程必须有集成测试
2. 测试数据库使用独立的测试库

## 6. 部署规范

### 6.1 环境管理
1. 严格区分开发、测试、生产环境
2. 使用Docker容器化部署
3. 使用Kubernetes进行容器编排

### 6.2 监控告警
1. 使用Prometheus进行系统监控
2. 使用ELK进行日志管理
3. 配置合理的告警阈值

## 7. 版本控制

### 7.1 Git使用规范
1. 遵循Git Flow工作流
2. commit信息必须清晰明确
3. 重要分支必须有保护机制

### 7.2 发布规范
1. 遵循语义化版本控制
2. 每次发布必须有更新日志
3. 建立回滚机制

## 8. 文档规范

### 8.1 技术文档
1. 及时更新API文档
2. 维护完整的部署文档
3. 记录重要的技术决策

### 8.2 业务文档
1. 维护最新的业务流程图
2. 记录核心业务规则
3. 保持与产品文档的同步更新